import { Node } from "@tiptap/core";
import { <PERSON><PERSON><PERSON><PERSON>w<PERSON><PERSON>per, ReactNodeViewRenderer } from "@tiptap/react";
import React, { useState, useCallback } from "react";
import { Edit3, Save, X, ChevronDown, ChevronRight } from "lucide-react";
import { Button } from "@/components/ui/button";
import { RichTextInput } from "@/components/ui/RichTextInput";
import { useToast } from "@/components/ToastProvider";
import type { NodeViewProps } from "@tiptap/react";

declare module "@tiptap/core" {
  interface Commands<ReturnType> {
    accordion: {
      /**
       * Insert an accordion with the specified attributes.
       */
      setAccordion: (attributes: AccordionAttrs) => ReturnType;
    };
  }
}

// Interface for accordion attributes
interface AccordionAttrs {
  title: string;
  description: string;
}

// Accordion Component
const AccordionComponent: React.FC<NodeViewProps> = ({
  node,
  updateAttributes,
  selected,
}) => {
  const [isEditing, setIsEditing] = useState(false);
  const [isExpanded, setIsExpanded] = useState(false);
  const { addToast } = useToast();
  const [formData, setFormData] = useState<AccordionAttrs>({
    title: node.attrs.title || "",
    description: node.attrs.description || "",
  });

  const handleSave = useCallback(() => {
    // Validate required fields
    if (!formData.title.trim()) {
      addToast("Title is required", "warning");
      return;
    }

    // Check if description has content (including HTML)
    const descriptionText = formData.description
      .replace(/<[^>]*>/g, "") // Remove HTML tags
      .trim();

    if (!descriptionText) {
      addToast("Description is required", "warning");
      return;
    }

    // Create a clean copy of formData to avoid mutation issues
    const cleanFormData = {
      ...formData,
      // Ensure description is properly formatted
      description: formData.description || "<p></p>",
    };

    updateAttributes(cleanFormData);
    setIsEditing(false);
    addToast("Accordion updated successfully", "success", "Success");
  }, [formData, updateAttributes, addToast]);

  const handleCancel = useCallback(() => {
    setFormData({
      title: node.attrs.title || "",
      description: node.attrs.description || "",
    });
    setIsEditing(false);
  }, [node.attrs]);

  const handleInputChange = useCallback(
    (field: keyof AccordionAttrs, value: string) => {
      setFormData((prev) => ({ ...prev, [field]: value }));
    },
    []
  );

  const toggleExpanded = useCallback(() => {
    setIsExpanded((prev) => !prev);
  }, []);

  if (isEditing) {
    return (
      <NodeViewWrapper
        className="accordion-node"
        as="div"
        data-drag-handle=""
        contentEditable={false}
      >
        <div className="my-4 p-4 pb-8 pt-2 bg-white dark:bg-slate-800 border-2 border-blue-400/50 dark:border-blue-600/50 rounded-lg shadow-lg transition-all duration-300 ease-in-out">
          <div className="flex items-center justify-between mb-4">
            <h4 className="text-sm font-semibold text-gray-900 dark:text-gray-100">
              Edit Accordion
            </h4>
            <div className="flex items-center space-x-2">
              <Button
                variant="ghost"
                size="sm"
                onClick={handleCancel}
                className="h-6 px-2 text-xs hover:bg-red-100/50 dark:hover:bg-red-900/30 text-red-600 dark:text-red-400"
              >
                <X className="w-3 h-3 mr-1" />
                Cancel
              </Button>
              <Button
                variant="default"
                size="sm"
                onClick={handleSave}
                className="h-6 px-2 text-xs bg-blue-600 hover:bg-blue-700 text-white"
              >
                <Save className="w-3 h-3 mr-1" />
                Save
              </Button>
            </div>
          </div>

          <div className="space-y-4">
            <div>
              <label className="block text-xs font-medium text-gray-700 dark:text-gray-300 mb-1">
                Title *
              </label>
              <input
                type="text"
                value={formData.title}
                onChange={(e) => handleInputChange("title", e.target.value)}
                placeholder="Enter accordion title"
                className="w-full px-3 py-2 text-sm bg-white dark:bg-slate-700 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:text-white"
                required
              />
            </div>

            <div>
              <label className="block text-xs font-medium text-gray-700 dark:text-gray-300 mb-1">
                Description *
              </label>
              <RichTextInput
                value={formData.description}
                onChange={(html) => handleInputChange("description", html)}
                placeholder="Enter accordion description"
                variant="default"
                className="w-full"
                enableCodeBlock={true}
                enableLink={true}
              />
            </div>
          </div>
        </div>
      </NodeViewWrapper>
    );
  }

  return (
    <NodeViewWrapper className="accordion-node">
      <div
        className={`my-4 border border-gray-200 dark:border-gray-700 rounded-lg bg-white dark:bg-slate-800 shadow-sm hover:shadow-md transition-all duration-200 relative group ${
          selected ? "ring-2 ring-blue-400/50" : ""
        }`}
      >
        {/* Edit button - only visible on hover */}
        {selected && (
          <Button
            variant="ghost"
            size="sm"
            onClick={(e) => {
              e.stopPropagation();
              setIsEditing(true);
            }}
            className="absolute top-2 right-2 h-6 px-2 text-xs opacity-0 group-hover:opacity-100 transition-opacity duration-200 bg-white/80 dark:bg-slate-800/80 hover:bg-gray-100 dark:hover:bg-slate-700"
          >
            <Edit3 className="w-3 h-3 mr-1" />
            Edit
          </Button>
        )}

        {/* Accordion Header */}
        <div
          className="flex items-center justify-center px-4 py-3 cursor-pointer hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors duration-200 rounded-lg"
          onClick={toggleExpanded}
        >
          <p className="text-base !font-extrabold font-medium text-gray-900 dark:text-gray-100 flex-1">
            {node.attrs.title || "Accordion Title"}
          </p>

          <div className="text-gray-400 dark:text-gray-500 ml-2">
            {isExpanded ? (
              <ChevronDown className="w-4 h-4" />
            ) : (
              <ChevronRight className="w-4 h-4" />
            )}
          </div>
        </div>

        {/* Accordion Content */}
        {isExpanded && (
          <div className="px-4 pb-4 border-gray-200 dark:border-gray-600">
            <div className="pt-1">
              {node.attrs.description && (
                <div
                  className="text-gray-600 dark:text-gray-400 leading-relaxed text-md prose-sm"
                  dangerouslySetInnerHTML={{
                    __html: node.attrs.description || "",
                  }}
                />
              )}
              {!node.attrs.description && (
                <p className="text-gray-600 dark:text-gray-400 leading-relaxed text-md">
                  Accordion description
                </p>
              )}
            </div>
          </div>
        )}
      </div>
    </NodeViewWrapper>
  );
};

// Accordion Node Extension
export const AccordionNode = Node.create({
  name: "accordion",
  group: "block",
  content: "",
  atom: true,
  draggable: false,

  addAttributes() {
    return {
      title: {
        default: "",
        parseHTML: (element) =>
          element.getAttribute("title") || element.getAttribute("data-title"),
        renderHTML: (attributes) => {
          if (!attributes.title) return {};
          return { title: attributes.title };
        },
      },
      description: {
        default: "",
        parseHTML: (element) =>
          element.getAttribute("description") ||
          element.getAttribute("data-description"),
        renderHTML: (attributes) => {
          if (!attributes.description) return {};
          return { description: attributes.description };
        },
      },
    };
  },

  parseHTML() {
    return [
      {
        tag: 'div[data-type="accordion"]',
      },
    ];
  },

  renderHTML({ HTMLAttributes }) {
    return [
      "div",
      {
        ...HTMLAttributes,
        "data-type": "accordion",
        class: "accordion-node",
      },
    ];
  },

  addNodeView() {
    return ReactNodeViewRenderer(AccordionComponent);
  },

  addCommands() {
    return {
      setAccordion:
        (attributes: AccordionAttrs) =>
        ({ commands }) => {
          return commands.insertContent({
            type: this.name,
            attrs: attributes,
          });
        },
    };
  },
});

export default AccordionNode;
