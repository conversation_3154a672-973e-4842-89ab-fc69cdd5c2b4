# Mock System

This system allows creating mock (simulated) data for specific projects, useful for demonstrations and client presentations.

## How to use

### Enable mocks for a project

1. Access the Supabase database
2. In the `projects` table, update the `enable_mock_data` column for the desired project:

```json
{
  "askAi": true,
  "webAnalytics": true
}
```

### Disable mocks for a project

1. Access the Supabase database
2. Update the `enable_mock_data` column:

```json
{
  "askAi": false,
  "webAnalytics": false
}
```

### Mock project characteristics

- **Ask AI**: Don't need DocsBot keys configured
- **Dashboard**: Don't need to exist in Cloudflare
- **Personalized data**: Mocks use the project name to personalize data
- **Realistic data**: Generate statistics and logs that look real

### File structure

- `mockProjects.ts` - Functions to check if projects should use mocks (based on Supabase data)
- `askAiMockData.ts` - Mock data for Ask AI (statistics and logs)
- `dashboardMockData.ts` - Mock data for Dashboard (analytics)
- `index.ts` - Centralized exports

### Usage example

For projects with mocks enabled in Supabase, the system generates:

#### Ask AI:
- Question statistics, positive/negative responses
- Question logs with personalized payment responses
- Functional pagination
- Functional filters

#### Dashboard:
- Visit and pageview metrics
- Country data (Brazil, USA, Argentina, etc.)
- Browsers and operating systems
- Most visited pages (/api-reference, /getting-started, etc.)

### Important ⚠️

This system is for demonstrations. Configure mocks through the Supabase database `enable_mock_data` column. No code changes or deployments are needed to enable/disable mocks for specific projects.