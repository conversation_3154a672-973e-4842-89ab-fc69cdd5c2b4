import { htmlToMdx } from "./htmlToMdx";
import { mdxToHtml } from "./mdxToHtml";
import {
  htmlToMdxCache,
  mdxToHtmlCache,
  createCacheKey,
} from "./conversionCache";

// Example usage with caching
export const convertHtmlToMdxWithCache = async (
  html: string
): Promise<string> => {
  const cacheKey = createCacheKey(html);

  // Check cache first
  const cached = htmlToMdxCache.get(cacheKey);
  if (cached) {
    console.log("📋 Cache hit for HTML to MDX conversion");
    return cached;
  }

  try {
    // Perform conversion
    const mdx = await htmlToMdx(html);

    // Cache the result
    htmlToMdxCache.set(cacheKey, mdx);

    return mdx;
  } catch (error) {
    console.error("❌ HTML to MDX conversion failed:", error);
    throw error;
  }
};

// Example usage with error handling
export const safeConvertMdxToHtml = async (mdx: string): Promise<string> => {
  const cacheKey = createCacheKey(mdx);

  // Check cache
  const cached = mdxToHtmlCache.get(cacheKey);
  if (cached) {
    console.log("📋 Cache hit for MDX to HTML conversion");
    return cached;
  }

  try {
    const html = await mdxToHtml(mdx);
    mdxToHtmlCache.set(cacheKey, html);
    return html;
  } catch (error) {
    console.error("❌ MDX to HTML conversion failed:", error);
    // Return a safe fallback
    return '<div class="error">Failed to render content</div>';
  }
};

// Batch conversion example
export const batchConvertHtmlToMdx = async (
  htmlArray: string[]
): Promise<string[]> => {
  return Promise.all(htmlArray.map((html) => convertHtmlToMdxWithCache(html)));
};
