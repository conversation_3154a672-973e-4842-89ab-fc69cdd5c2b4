import { Node, mergeAttributes } from '@tiptap/core';
import {
	ReactNode<PERSON><PERSON>w<PERSON><PERSON><PERSON>,
	NodeViewWrapper,
	NodeViewProps,
} from '@tiptap/react';

declare module '@tiptap/core' {
	interface Commands<ReturnType> {
		checklistNode: {
			/**
			 * Insert checklist with the specified attributes.
			 */
			setChecklist: (attributes?: { items?: ChecklistItem[] }) => ReturnType;
		};
	}
}
import React, { useState, useCallback } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Plus, X, Edit3, Check, Trash2 } from 'lucide-react';

interface ChecklistItem {
	id: string;
	text: string;
	checked: boolean;
}

interface ActionButtonsProps {
	onEdit: () => void;
	onDelete: () => void;
}

const ActionButtons: React.FC<ActionButtonsProps> = ({ onEdit, onDelete }) => (
	<div className='absolute top-1/2 -translate-y-1/2 right-2 flex items-center space-x-1 transition-all duration-200 z-30 bg-white dark:bg-slate-800 px-2 py-1 rounded-md shadow-lg border border-gray-200 dark:border-gray-600 backdrop-blur-sm'>
		<Button
			variant='ghost'
			size='sm'
			onClick={(e) => {
				e.stopPropagation();
				onEdit();
			}}
			className='h-6 px-2 text-xs hover:bg-gray-100 dark:hover:bg-slate-700'
			title='Edit'
		>
			<Edit3 className='w-3 h-3' />
			<span className='ml-1'>Edit</span>
		</Button>
		<Button
			variant='ghost'
			size='sm'
			onClick={(e) => {
				e.stopPropagation();
				onDelete();
			}}
			className='h-6 px-2 text-xs hover:bg-red-100 dark:hover:bg-red-900/30 text-red-600 dark:text-red-400'
			title='Delete'
		>
			<Trash2 className='w-3 h-3' />
			<span className='ml-1'>Delete</span>
		</Button>
	</div>
);

const generateDefaultItems = (): ChecklistItem[] => [
	{ id: Date.now().toString(), text: 'First item', checked: false },
	{ id: (Date.now() + 1).toString(), text: 'Second item', checked: false },
	{ id: (Date.now() + 2).toString(), text: 'Third item', checked: false },
];

const ChecklistComponent: React.FC<NodeViewProps> = ({
	node,
	updateAttributes,
	selected,
}) => {
	const [items, setItems] = useState<ChecklistItem[]>(() => {
		try {
			const nodeItems = node?.attrs?.items;

			if (!Array.isArray(nodeItems)) {
				console.warn('Invalid node items, using defaults:', nodeItems);
				return generateDefaultItems();
			}

			const validItems = nodeItems.filter((item) => {
				return (
					item &&
					typeof item === 'object' &&
					item !== null &&
					'id' in item &&
					typeof item.id === 'string'
				);
			}) as ChecklistItem[];

			if (validItems.length === 0) {
				console.warn('No valid items found, using defaults');
				return generateDefaultItems();
			}

			return validItems;
		} catch (error) {
			console.error('Error parsing checklist items:', error);
			return generateDefaultItems();
		}
	});

	const [editingItemId, setEditingItemId] = useState<string | null>(null);
	const [editingText, setEditingText] = useState('');
	const [hoveredItemId, setHoveredItemId] = useState<string | null>(null);
	const [newlyCreatedItemId, setNewlyCreatedItemId] = useState<string | null>(
		null
	);

	// Update node attributes when items change
	const updateNodeItems = useCallback(
		(newItems: ChecklistItem[]) => {
			setItems(newItems);
			updateAttributes({ items: newItems });
		},
		[updateAttributes]
	);

	const handleAddItem = useCallback(() => {
		// If there's currently an item being edited, save it first
		if (editingItemId && editingText.trim()) {
			const updatedItems = items.map((item) =>
				item.id === editingItemId ? { ...item, text: editingText.trim() } : item
			);
			updateNodeItems(updatedItems);
		}

		// Create new item
		const newItem: ChecklistItem = {
			id: Date.now().toString(),
			text: 'New item',
			checked: false,
		};
		const newItems = [
			...(editingItemId && editingText.trim()
				? items.map((item) =>
						item.id === editingItemId
							? { ...item, text: editingText.trim() }
							: item
					)
				: items),
			newItem,
		];

		updateNodeItems(newItems);
		setEditingItemId(newItem.id);
		setEditingText(newItem.text);
		setNewlyCreatedItemId(newItem.id); // Mark as newly created
	}, [items, updateNodeItems, editingItemId, editingText]);

	const handleDeleteItem = useCallback(
		(itemId: string) => {
			const newItems = items.filter((item) => item.id !== itemId);
			updateNodeItems(newItems);
		},
		[items, updateNodeItems]
	);

	const handleToggleCheck = useCallback(
		(itemId: string) => {
			const newItems = items.map((item) =>
				item.id === itemId ? { ...item, checked: !item.checked } : item
			);
			updateNodeItems(newItems);
		},
		[items, updateNodeItems]
	);

	const handleStartEdit = useCallback((item: ChecklistItem) => {
		setEditingItemId(item.id);
		setEditingText(item.text);
		// Clear newly created flag since we're editing an existing item
		setNewlyCreatedItemId(null);
	}, []);

	const handleSaveEdit = useCallback(() => {
		if (editingItemId) {
			const newItems = items.map((item) =>
				item.id === editingItemId ? { ...item, text: editingText.trim() } : item
			);
			updateNodeItems(newItems);
			setEditingItemId(null);
			setEditingText('');
			// Clear newly created flag since item is now saved
			if (newlyCreatedItemId === editingItemId) {
				setNewlyCreatedItemId(null);
			}
		}
	}, [editingItemId, editingText, items, updateNodeItems, newlyCreatedItemId]);

	const handleCancelEdit = useCallback(() => {
		if (editingItemId) {
			// Check if this is a newly created item that hasn't been saved yet
			if (newlyCreatedItemId === editingItemId) {
				// Remove the newly created item completely
				const newItems = items.filter((item) => item.id !== editingItemId);
				updateNodeItems(newItems);
				setNewlyCreatedItemId(null);
			}
			// For existing items, just exit edit mode (text reverts automatically)
		}

		setEditingItemId(null);
		setEditingText('');
	}, [editingItemId, newlyCreatedItemId, items, updateNodeItems]);

	const handleKeyDown = useCallback(
		(e: React.KeyboardEvent) => {
			if (e.key === 'Enter') {
				e.preventDefault();
				handleSaveEdit();
			} else if (e.key === 'Escape') {
				e.preventDefault();
				handleCancelEdit();
			}
		},
		[handleSaveEdit, handleCancelEdit]
	);

	return (
		<NodeViewWrapper
			className='checklist-node w-full border-dashed border-2 border-gray-300 dark:border-gray-600 rounded-lg hover:border-blue-400/50 dark:hover:border-blue-600/50 transition-colors duration-200'
			draggable={false}
			contentEditable={false}
			data-drag-handle=''
		>
			<div
				className={`checklist-node-container p-4 bg-white dark:bg-gray-50 relative w-full group ${
					selected ? 'ring-2 ring-blue-400/50 rounded-lg' : ''
				}`}
				draggable={false}
			>
				{/* Checklist Items */}
				<div className='space-y-2'>
					{items.map((item) => (
						<div key={item.id} className='checklist-item relative w-full'>
							{editingItemId === item.id ? (
								<div className='flex items-center gap-3 p-2'>
									{/* Checkbox */}
									<input
										type='checkbox'
										checked={item.checked}
										onChange={() => handleToggleCheck(item.id)}
										className='w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-gray-600 accent-blue-600'
									/>

									{/* Edit Form */}
									<div className='flex items-center gap-2 flex-1'>
										<Input
											value={editingText}
											onChange={(e) => setEditingText(e.target.value)}
											onKeyDown={handleKeyDown}
											className='h-8 text-sm'
											autoFocus
										/>
										<Button
											onClick={handleSaveEdit}
											size='sm'
											variant='outline'
											className='h-8 w-8 p-0'
										>
											<Check className='w-3 h-3 text-blue-600' />
										</Button>
										<Button
											onClick={handleCancelEdit}
											size='sm'
											variant='outline'
											className='h-8 w-8 p-0'
										>
											<X className='w-3 h-3' />
										</Button>
									</div>
								</div>
							) : (
								/* Container do item completo com hover isolado */
								<div
									className={`checklist-hover-zone relative rounded-lg transition-all duration-200 w-full ${
										hoveredItemId === item.id
											? 'bg-blue-50/50 dark:bg-blue-900/10 border border-blue-200/50 dark:border-blue-800/50'
											: 'border border-transparent'
									}`}
									onMouseEnter={(e) => {
										e.stopPropagation();
										setHoveredItemId(item.id);
									}}
									onMouseLeave={(e) => {
										e.stopPropagation();
										setHoveredItemId(null);
									}}
								>
									{hoveredItemId === item.id && (
										<ActionButtons
											onEdit={() => handleStartEdit(item)}
											onDelete={() => handleDeleteItem(item.id)}
										/>
									)}

									<div className='flex items-center gap-3 p-2'>
										{/* Checkbox */}
										<input
											type='checkbox'
											checked={item.checked}
											onChange={() => handleToggleCheck(item.id)}
											className='w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-gray-600 accent-blue-600'
										/>

										{/* Text */}
										<span className={'text-sm flex-1 select-none'}>
											{item.text}
										</span>
									</div>
								</div>
							)}
						</div>
					))}
				</div>

				{items.length === 0 && (
					<div className='text-center py-8'>
						<p className='text-gray-500 dark:text-gray-400 mb-4'>
							No items yet. Click &ldquo;Add Item&rdquo; to create your first
							checklist item.
						</p>
					</div>
				)}

				{/* Add Item Button - positioned at the bottom */}
				<div className='flex justify-center mt-4'>
					<Button
						onClick={handleAddItem}
						size='sm'
						variant='outline'
						className='h-8 px-3 text-xs border-gray-300 dark:border-gray-600 text-gray-600 dark:text-gray-400 hover:border-blue-400 hover:text-blue-600 dark:hover:border-blue-500 dark:hover:text-blue-400 opacity-0 group-hover:opacity-100 transition-all duration-200 bg-transparent hover:bg-blue-50 dark:hover:bg-blue-950/20'
					>
						<Plus className='w-3 h-3 mr-1' />
						Add Item
					</Button>
				</div>
			</div>
		</NodeViewWrapper>
	);
};

export const ChecklistNode = Node.create({
	name: 'checklistNode',

	group: 'block',

	content: '',

	addAttributes() {
		return {
			items: {
				default: [],
				parseHTML: (element) => {
					try {
						const itemsData =
							element.getAttribute('data-items') ||
							element.getAttribute('items');
						if (itemsData) {
							const parsed = JSON.parse(itemsData);
							console.log('🔧 ChecklistNode parseHTML parsed items:', parsed);
							return parsed;
						}

						// Extract from child elements if no data attributes
						const checkboxElements = element.querySelectorAll(
							'input[type="checkbox"]'
						);
						if (checkboxElements.length > 0) {
							return Array.from(checkboxElements).map((checkbox, index) => {
								const label =
									checkbox.nextElementSibling?.textContent ||
									`Item ${index + 1}`;
								return {
									id: Date.now().toString() + index,
									text: label.trim(),
									checked: (checkbox as HTMLInputElement).checked,
								};
							});
						}

						return [];
					} catch (error) {
						console.error('Error parsing checklist items:', error);
						return [];
					}
				},
				renderHTML: (attributes) => {
					if (!attributes.items || !Array.isArray(attributes.items)) {
						console.log(
							'🔧 ChecklistNode renderHTML no items, returning empty array'
						);
						return { 'data-items': '[]' };
					}
					console.log(
						'🔧 ChecklistNode renderHTML with items:',
						attributes.items
					);
					return { 'data-items': JSON.stringify(attributes.items) };
				},
			},
		};
	},

	parseHTML() {
		return [
			{
				tag: 'Checklist',
			},
			{
				tag: 'div[data-type="checklist"]',
			},
		];
	},

	renderHTML({ HTMLAttributes }) {
		const items = HTMLAttributes.items || HTMLAttributes['data-items'] || [];
		let itemsList: ChecklistItem[] = [];
		if (typeof items === 'string') {
			try {
				itemsList = JSON.parse(items);
			} catch {
				itemsList = [];
			}
		} else if (Array.isArray(items)) {
			itemsList = items;
		}

		const itemElements = itemsList.map((item: ChecklistItem) => [
			'div',
			{ class: 'checklist-item' },
			[
				'input',
				{
					type: 'checkbox',
					checked: item.checked ? 'checked' : null,
				},
			],
			['span', {}, item.text || ''],
		]);

		return [
			'div',
			mergeAttributes(HTMLAttributes, {
				'data-type': 'checklist',
				'data-items': JSON.stringify(itemsList),
				class: 'checklist-node',
			}),
			...itemElements,
		];
	},

	addNodeView() {
		return ReactNodeViewRenderer(ChecklistComponent);
	},

	addCommands() {
		return {
			setChecklist:
				(attributes?: { items?: ChecklistItem[] }) =>
				({ commands }) => {
					return commands.insertContent({
						type: this.name,
						attrs: attributes || { items: generateDefaultItems() },
					});
				},
		};
	},
});

export default ChecklistNode;
