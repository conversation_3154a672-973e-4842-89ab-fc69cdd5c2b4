import { NextRequest, NextResponse } from "next/server";
import <PERSON><PERSON> from "stripe";

export const runtime = 'edge';

const stripe = new Stripe(process.env.STRIPE_SECRET_KEY!, {
	apiVersion: "2025-05-28.basil",
});

export async function POST(request: NextRequest) {
	try {
		if (!process.env.STRIPE_SECRET_KEY) {
			throw new Error("STRIPE_SECRET_KEY environment variable is not set");
		}

		const body = (await request.json()) as {
			customerId: string;
			projectId?: string;
		};
		const { customerId, projectId } = body;

		if (!customerId) {
			return NextResponse.json(
				{ error: "Customer ID is required" },
				{ status: 400 }
			);
		}

		// Get the return URL from environment or construct it
		const baseUrl =
			process.env.NEXT_PUBLIC_BASE_URL || process.env.VERCEL_URL
				? `https://${process.env.VERCEL_URL}`
				: "http://localhost:3000";

		// Construct return URL based on whether projectId is provided
		let returnUrl: string;
		if (projectId) {
			returnUrl = `${baseUrl}/${projectId}/settings?tab=billing`;
		} else {
			// Fallback to generic settings page
			returnUrl = `${baseUrl}/settings?tab=billing`;
		}

		// Create a Stripe Customer Portal session
		const session = await stripe.billingPortal.sessions.create({
			customer: customerId,
			return_url: returnUrl,
		});

		return NextResponse.json({ url: session.url });
	} catch (error) {
		console.error("❌ [CUSTOMER_PORTAL] Error creating session:", error);

		const errorMessage =
			error instanceof Error ? error.message : "Unknown error";

		return NextResponse.json(
			{
				error: "Failed to create customer portal session",
				details: errorMessage,
			},
			{ status: 500 }
		);
	}
}
