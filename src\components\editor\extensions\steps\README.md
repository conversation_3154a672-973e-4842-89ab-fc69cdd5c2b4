# Steps Node - Refactored Structure

Este componente foi refatorado para melhor organização e manutenibilidade. O arquivo original `StepsNode.tsx` tinha quase 2000 linhas e foi dividido em vários arquivos menores.

## Estrutura de Arquivos

```
src/components/editor/extensions/steps/
├── README.md                    # Este arquivo
├── types.ts                     # Interfaces e tipos TypeScript
├── utils.ts                     # Funções utilitárias
├── styles.ts                    # Estilos CSS e injeção
└── components/
    ├── index.ts                 # Exports dos componentes
    ├── ActionButtons.tsx        # Botões de ação para cada step
    ├── VerticalLine.tsx         # Linha vertical de conexão
    ├── StepEditForm.tsx         # Formulário de edição de step
    └── StepItem.tsx             # Item individual e lista de steps
```

## Arquivos Principais

### `types.ts`
Contém todos os tipos e interfaces:
- `StepData` - Dados de um step individual
- `StepsAttrs` - Atributos do nó Steps
- `FlatStep` - Step com informações de nível (para drag-and-drop)
- Props de todos os componentes

### `utils.ts`
Funções utilitárias:
- `generateDefaultSteps()` - Gera steps padrão
- `getTitleSizeForLevel()` - Determina tamanho do título por nível
- `flattenSteps()` / `reconstructSteps()` - Conversão hierárquica
- Funções de drag-and-drop (temporariamente removidas)
- Funções de estilo (`getStepNumberClass`, `getStepTitleClass`, etc.)

### `styles.ts`
- CSS styles para efeitos visuais
- Função `injectStyles()` para injetar estilos no DOM

### `components/`
- **ActionButtons**: Botões de editar, adicionar sub-step e deletar
- **VerticalLine**: Linha de ligação entre steps com efeito fade
- **StepEditForm**: Formulário para editar título, conteúdo e tamanho
- **StepItem**: Renderiza um step individual
- **StepsList**: Renderiza uma lista de steps recursivamente

## Funcionalidades Mantidas

✅ **Funcionalidades Básicas**:
- Criar/editar/deletar steps
- Adicionar sub-steps
- Edição inline com RichTextInput
- Diferentes tamanhos de título (H2-H6)
- Interface visual com linhas de conexão

❌ **Funcionalidades Temporariamente Removidas**:
- Drag-and-drop complexo para reordenar steps
- EditMode avançado com hierarquia visual
- Reordenação com mudança de níveis

## StepsNode.tsx Principal

O arquivo principal agora tem ~380 linhas (vs ~2000 originais) e foca apenas em:
- Gerenciamento de estado dos steps
- Handlers para CRUD operations
- Integração com Tiptap
- Renderização do componente principal

## Benefícios da Refatoração

1. **Modularidade**: Cada arquivo tem uma responsabilidade específica
2. **Manutenibilidade**: Mais fácil de encontrar e modificar código
3. **Reutilização**: Componentes podem ser reutilizados individualmente
4. **Testabilidade**: Componentes menores são mais fáceis de testar
5. **Performance**: Imports mais específicos e componentes menores

## Próximos Passos

Para reativar as funcionalidades de drag-and-drop:
1. Implementar o componente `EditMode.tsx`
2. Restaurar as funções de drag-and-drop em `utils.ts`
3. Adicionar os handlers no componente principal
4. Testar a funcionalidade completa

## Uso

```tsx
import { StepsNode } from './extensions/StepsNode';

// No seu editor Tiptap
const extensions = [
  // ... outras extensões
  StepsNode,
];
```

O componente mantém a mesma API externa, então não requer mudanças no código que o utiliza. 