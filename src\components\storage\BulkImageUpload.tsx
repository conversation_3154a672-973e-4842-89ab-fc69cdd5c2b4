"use client";

import React, { useState, useRef, useCallback } from "react";
import { Upload, X, Loader2 } from "lucide-react";

interface ImageFile {
  id: string;
  file: File;
  preview: string;
  name: string;
  size: number;
  contentType: string;
  altText: string;
  tags: string[];
  metadata: Record<string, unknown>;
}

interface UploadResult {
  success: boolean;
  uploadedImages: Array<{
    name: string;
    path: string;
    url: string;
    size: number;
  }>;
  errors: string[];
  totalUploaded: number;
}

interface BulkImageUploadProps {
  projectId: number;
  organizationId: string;
  onUploadComplete?: (results: UploadResult) => void;
  maxFiles?: number;
  maxSizePerFile?: number; // in bytes
  allowedTypes?: string[];
}

export default function BulkImageUpload({
  projectId,
  organizationId,
  onUploadComplete,
  maxFiles = 20,
  maxSizePerFile = 10 * 1024 * 1024, // 10MB
  allowedTypes = [
    "image/jpeg",
    "image/jpg",
    "image/png",
    "image/webp",
    "image/gif",
  ],
}: BulkImageUploadProps) {
  const [images, setImages] = useState<ImageFile[]>([]);
  const [uploading, setUploading] = useState(false);
  const [uploadProgress, setUploadProgress] = useState<string>("");
  const fileInputRef = useRef<HTMLInputElement>(null);

  const generateId = () => Math.random().toString(36).substring(2, 15);

  const createImageFile = useCallback((file: File): Promise<ImageFile> => {
    return new Promise((resolve) => {
      const reader = new FileReader();
      reader.onload = (e) => {
        resolve({
          id: generateId(),
          file,
          preview: e.target?.result as string,
          name: file.name,
          size: file.size,
          contentType: file.type,
          altText: "",
          tags: [],
          metadata: {},
        });
      };
      reader.readAsDataURL(file);
    });
  }, []);

  const handleFileSelect = useCallback(
    async (files: FileList) => {
      const validFiles: File[] = [];
      const errors: string[] = [];

      // Validate files
      Array.from(files).forEach((file) => {
        if (!allowedTypes.includes(file.type)) {
          errors.push(`${file.name}: Tipo de arquivo não suportado`);
          return;
        }
        if (file.size > maxSizePerFile) {
          errors.push(
            `${file.name}: Arquivo muito grande (máx. ${Math.round(
              maxSizePerFile / 1024 / 1024
            )}MB)`
          );
          return;
        }
        if (images.length + validFiles.length >= maxFiles) {
          errors.push(`Máximo de ${maxFiles} arquivos permitidos`);
          return;
        }
        validFiles.push(file);
      });

      if (errors.length > 0) {
        alert(
          "Alguns arquivos não puderam ser adicionados:\n" + errors.join("\n")
        );
      }

      // Create image objects for valid files
      const newImages = await Promise.all(
        validFiles.map((file) => createImageFile(file))
      );

      setImages((prev) => [...prev, ...newImages]);
    },
    [images.length, maxFiles, maxSizePerFile, allowedTypes, createImageFile]
  );

  const handleDrop = useCallback(
    (e: React.DragEvent) => {
      e.preventDefault();
      const files = e.dataTransfer.files;
      if (files.length > 0) {
        handleFileSelect(files);
      }
    },
    [handleFileSelect]
  );

  const handleDragOver = useCallback((e: React.DragEvent) => {
    e.preventDefault();
  }, []);

  const removeImage = useCallback((id: string) => {
    setImages((prev) => {
      const imageToRemove = prev.find((img) => img.id === id);
      if (imageToRemove) {
        URL.revokeObjectURL(imageToRemove.preview);
      }
      return prev.filter((img) => img.id !== id);
    });
  }, []);

  const updateImageData = useCallback(
    (
      id: string,
      field: keyof ImageFile,
      value: string | string[] | Record<string, unknown>
    ) => {
      setImages((prev) =>
        prev.map((img) => (img.id === id ? { ...img, [field]: value } : img))
      );
    },
    []
  );

  const updateImageTags = useCallback(
    (id: string, tagsString: string) => {
      const tags = tagsString
        .split(",")
        .map((tag) => tag.trim())
        .filter((tag) => tag.length > 0);
      updateImageData(id, "tags", tags);
    },
    [updateImageData]
  );

  const convertFileToBase64 = (file: File): Promise<string> => {
    return new Promise((resolve, reject) => {
      const reader = new FileReader();
      reader.onload = () => {
        const result = reader.result as string;
        // Remove data:image/jpeg;base64, prefix
        const base64 = result.split(",")[1];
        resolve(base64);
      };
      reader.onerror = reject;
      reader.readAsDataURL(file);
    });
  };

  const handleBulkUpload = async () => {
    if (images.length === 0) {
      alert("Selecione pelo menos uma imagem para fazer upload");
      return;
    }

    setUploading(true);
    setUploadProgress("Preparando upload...");

    try {
      // Convert files to base64 for transmission
      const imagesData = await Promise.all(
        images.map(async (img, index) => {
          setUploadProgress(
            `Processando imagem ${index + 1} de ${images.length}...`
          );
          const base64File = await convertFileToBase64(img.file);

          return {
            name: img.name,
            file: base64File,
            contentType: img.contentType,
            altText: img.altText,
            tags: img.tags,
            metadata: {
              originalSize: img.size,
              uploadedAt: new Date().toISOString(),
              ...img.metadata,
            },
          };
        })
      );

      setUploadProgress("Enviando imagens...");

      // Get Supabase URL and anon key from your environment or context
      const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
      const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY;

      // Call our Edge Function
      const response = await fetch(
        `${supabaseUrl}/functions/v1/bulk-image-upload`,
        {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
            Authorization: `Bearer ${supabaseAnonKey}`,
          },
          body: JSON.stringify({
            projectId,
            organizationId,
            images: imagesData,
          }),
        }
      );

      const result = (await response.json()) as UploadResult;

      if (result.success) {
        setUploadProgress(
          `Upload concluído! ${result.totalUploaded} imagens enviadas.`
        );

        // Clear uploaded images
        images.forEach((img) => URL.revokeObjectURL(img.preview));
        setImages([]);

        // Call callback if provided
        if (onUploadComplete) {
          onUploadComplete(result);
        }

        setTimeout(() => {
          setUploadProgress("");
        }, 3000);
      } else {
        throw new Error("Upload falhou");
      }
    } catch (error) {
      console.error("Upload error:", error);
      setUploadProgress("");
      alert(
        `Erro no upload: ${
          error instanceof Error ? error.message : "Erro desconhecido"
        }`
      );
    } finally {
      setUploading(false);
    }
  };

  return (
    <div className="space-y-6">
      <div className="border-2 border-dashed border-gray-300 dark:border-gray-600 rounded-lg p-8">
        <div
          className="text-center space-y-4"
          onDrop={handleDrop}
          onDragOver={handleDragOver}
        >
          <div className="mx-auto w-12 h-12 text-gray-400">
            <Upload className="w-full h-full" />
          </div>
          <div>
            <button
              type="button"
              className="text-blue-600 hover:text-blue-500 font-medium"
              onClick={() => fileInputRef.current?.click()}
              disabled={uploading}
            >
              Clique para selecionar imagens
            </button>
            <p className="text-gray-500 text-sm mt-1">
              ou arraste e solte arquivos aqui
            </p>
          </div>
          <div className="text-xs text-gray-400 space-y-1">
            <p>
              Máximo {maxFiles} arquivos • Até{" "}
              {Math.round(maxSizePerFile / 1024 / 1024)}MB por arquivo
            </p>
            <p>Formatos suportados: JPG, PNG, WebP, GIF</p>
          </div>
          <input
            ref={fileInputRef}
            type="file"
            multiple
            accept={allowedTypes.join(",")}
            className="hidden"
            onChange={(e) => e.target.files && handleFileSelect(e.target.files)}
            disabled={uploading}
          />
        </div>
      </div>

      {images.length > 0 && (
        <div className="space-y-4">
          <div className="flex justify-between items-center">
            <h3 className="text-lg font-medium">
              Imagens Selecionadas ({images.length})
            </h3>
            <div className="space-x-2">
              <button
                type="button"
                onClick={() => {
                  images.forEach((img) => URL.revokeObjectURL(img.preview));
                  setImages([]);
                }}
                className="px-3 py-1 text-sm text-gray-600 hover:text-gray-800 border border-gray-300 rounded"
                disabled={uploading}
              >
                Limpar Tudo
              </button>
              <button
                type="button"
                onClick={handleBulkUpload}
                disabled={uploading || images.length === 0}
                className="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed flex items-center space-x-2"
              >
                {uploading && <Loader2 className="w-4 h-4 animate-spin" />}
                <span>{uploading ? "Enviando..." : "Fazer Upload"}</span>
              </button>
            </div>
          </div>

          {uploadProgress && (
            <div className="bg-blue-50 border border-blue-200 rounded-lg p-3">
              <p className="text-blue-800 text-sm">{uploadProgress}</p>
            </div>
          )}

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {images.map((image) => (
              <div
                key={image.id}
                className="border border-gray-200 dark:border-gray-700 rounded-lg p-4 space-y-3"
              >
                <div className="flex items-start space-x-3">
                  <img
                    src={image.preview}
                    alt={image.name}
                    className="w-16 h-16 object-cover rounded"
                  />
                  <div className="flex-1 min-w-0">
                    <p className="text-sm font-medium truncate">{image.name}</p>
                    <p className="text-xs text-gray-500">
                      {(image.size / 1024 / 1024).toFixed(2)} MB
                    </p>
                  </div>
                  <button
                    type="button"
                    onClick={() => removeImage(image.id)}
                    className="text-gray-400 hover:text-red-500"
                    disabled={uploading}
                  >
                    <X className="w-4 h-4" />
                  </button>
                </div>

                <div className="space-y-2">
                  <div>
                    <label className="block text-xs font-medium text-gray-700 dark:text-gray-300 mb-1">
                      Texto Alternativo
                    </label>
                    <input
                      type="text"
                      value={image.altText}
                      onChange={(e) =>
                        updateImageData(image.id, "altText", e.target.value)
                      }
                      placeholder="Descreva a imagem..."
                      className="w-full text-sm border border-gray-300 dark:border-gray-600 rounded px-2 py-1"
                      disabled={uploading}
                    />
                  </div>

                  <div>
                    <label className="block text-xs font-medium text-gray-700 dark:text-gray-300 mb-1">
                      Tags (separadas por vírgula)
                    </label>
                    <input
                      type="text"
                      value={image.tags.join(", ")}
                      onChange={(e) =>
                        updateImageTags(image.id, e.target.value)
                      }
                      placeholder="logo, header, produto..."
                      className="w-full text-sm border border-gray-300 dark:border-gray-600 rounded px-2 py-1"
                      disabled={uploading}
                    />
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      )}
    </div>
  );
}
