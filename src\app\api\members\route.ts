import { NextRequest, NextResponse } from "next/server";
import { createServerClient } from "@supabase/ssr";
import { createAdminClient } from "@/utils/supabase/admin";
import { cookies } from "next/headers";

export const runtime = 'edge';

interface PendingInvite {
	id: number;
	email: string;
	status: string;
	created_at: string;
}

export async function GET(request: NextRequest) {
	try {
		const { searchParams } = new URL(request.url);
		const projectId = searchParams.get("projectId");
		const userId = searchParams.get("userId"); // Optional - for fetching specific member

		if (!projectId) {
			return NextResponse.json(
				{ error: "Project ID is required" },
				{ status: 400 }
			);
		}

		// Convert projectId to number since it's stored as integer in database
		const projectIdNumber = parseInt(projectId, 10);
		if (isNaN(projectIdNumber)) {
			return NextResponse.json(
				{ error: "Invalid project ID format" },
				{ status: 400 }
			);
		}

		// Authenticate the user with regular client
		const cookieStore = await cookies();
		const supabase = createServerClient(
			process.env.NEXT_PUBLIC_SUPABASE_URL!,
			process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
			{
				cookies: {
					get(name: string) {
						return cookieStore.get(name)?.value;
					},
				},
			}
		);

		// Get current user
		const {
			data: { user },
			error: userError,
		} = await supabase.auth.getUser();

		if (userError || !user) {
			return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
		}

		// Use admin client for data operations
		const adminClient = await createAdminClient();

		// Security check: Verify user has access to this project
		const { data: userProject, error: userProjectError } = await adminClient
			.from("project_user")
			.select("role")
			.eq("project_id", projectIdNumber)
			.eq("user_id", user.id)
			.single();

		if (userProjectError || !userProject) {
			return NextResponse.json({ error: "Forbidden" }, { status: 403 });
		}

		// Build query for members
		let membersQuery = adminClient
			.from("project_user")
			.select("user_id, email, role, is_creator")
			.eq("project_id", projectIdNumber);

		// If userId is provided, filter for specific member
		if (userId) {
			membersQuery = membersQuery.eq("user_id", userId);
		}

		// Execute query with ordering (only when fetching all members)
		const { data: members, error: membersError } = userId
			? await membersQuery
			: await membersQuery
					.order("is_creator", { ascending: false })
					.order("role")
					.order("email");

		if (membersError) {
			console.error("❌ Error fetching project members:", membersError);
			return NextResponse.json(
				{ error: "Failed to fetch members" },
				{ status: 500 }
			);
		}

		// Also fetch pending invites (only when fetching all members, not for specific user)
		let pendingInvites: PendingInvite[] = [];
		if (!userId) {
			const { data: invites, error: invitesError } = await adminClient
				.from("project_invites")
				.select("id, email, status, created_at")
				.eq("project_id", projectIdNumber)
				.eq("status", "invited")
				.order("created_at", { ascending: false });

			if (invitesError) {
				console.error("❌ Error fetching pending invites:", invitesError);
				// Don't fail the whole request, just log the error
			} else {
				pendingInvites = invites || [];
			}
		}

		// Sort members by role hierarchy when fetching all
		if (!userId && members) {
			const roleOrder = { Owner: 0, Admin: 1, Editor: 2 };
			members.sort((a, b) => {
				// Sort by is_creator first (creators first)
				if (a.is_creator !== b.is_creator) {
					return a.is_creator ? -1 : 1;
				}
				// Then by role
				const aRoleOrder = roleOrder[a.role as keyof typeof roleOrder] ?? 3;
				const bRoleOrder = roleOrder[b.role as keyof typeof roleOrder] ?? 3;
				if (aRoleOrder !== bRoleOrder) {
					return aRoleOrder - bRoleOrder;
				}
				// Finally by email
				return a.email.localeCompare(b.email);
			});
		}

		return NextResponse.json({
			members: members || [],
			pending_invites: pendingInvites,
		});
	} catch (error) {
		console.error("💥 Unexpected error in GET members API:", error);
		return NextResponse.json(
			{ error: "Internal server error" },
			{ status: 500 }
		);
	}
}
