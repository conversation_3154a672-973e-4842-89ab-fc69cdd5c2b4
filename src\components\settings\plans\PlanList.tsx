import { ProcessedProduct } from "./types";
import { PlanCard } from "./PlanCard";
import { useProject } from "@/contexts/ProjectContext/ProjectContextProvider";
import { StripeCustomer } from "./types";

// Use the project type from the current context
type Project = ReturnType<typeof useProject>["selectedProject"];

interface PlanListProps {
	products: ProcessedProduct[];
	isAnnual: boolean;
	hasAdminPermission: boolean;
	selectedProject: Project | null;
	isCurrentPlan: (priceId: string) => boolean;
	customerId?: string;
	hasSubscription: boolean;
	subscriptionStatus: StripeCustomer["status"];
}

export function PlanList({
	products,
	isAnnual,
	hasAdminPermission,
	selectedProject,
	isCurrentPlan,
	customerId,
	hasSubscription,
	subscriptionStatus,
}: PlanListProps) {
	// Função local para obter o preço selecionado com base no isAnnual recebido como prop
	const getSelectedPrice = (product: ProcessedProduct) => {
		const interval = isAnnual ? "year" : "month";

		console.log("🎯 [PLAN_LIST] Selecionando preço para produto:", {
			productId: product.id,
			productName: product.name,
			isAnnual,
			interval,
			availablePrices: {
				monthly: product.display_prices.monthly
					? {
							id: product.display_prices.monthly.id,
							amount: product.display_prices.monthly.unit_amount,
					  }
					: null,
				yearly: product.display_prices.yearly
					? {
							id: product.display_prices.yearly.id,
							amount: product.display_prices.yearly.unit_amount,
					  }
					: null,
			},
		});

		// Use display_prices instead of searching through all prices
		let selectedPrice;
		if (interval === "year") {
			selectedPrice = product.display_prices.yearly || product.default_price;
		} else {
			selectedPrice = product.display_prices.monthly || product.default_price;
		}

		console.log("✅ [PLAN_LIST] Preço selecionado:", {
			productId: product.id,
			productName: product.name,
			selectedPriceId: selectedPrice?.id,
			selectedAmount: selectedPrice?.unit_amount,
			selectedInterval: selectedPrice?.recurring?.interval,
			wasFromDisplayPrices: selectedPrice !== product.default_price,
		});

		return selectedPrice;
	};

	return (
		<div className='grid gap-6 grid-cols-1 sm:grid-cols-1 md:grid-cols-1 lg:grid-cols-2 xl:grid-cols-3'>
			{products.map((product) => {
				// Obter o preço selecionado com base na preferência anual/mensal
				const selectedPrice = getSelectedPrice(product);

				return (
					<PlanCard
						key={`${product.id}-${selectedPrice?.id}`}
						product={product}
						selectedPrice={selectedPrice}
						isCurrentPlan={isCurrentPlan}
						selectedProject={selectedProject}
						customerId={customerId}
						hasSubscription={hasSubscription}
						hasAdminPermission={hasAdminPermission}
						subscriptionStatus={subscriptionStatus}
					/>
				);
			})}
		</div>
	);
}
