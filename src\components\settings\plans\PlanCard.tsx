import {
	<PERSON>,
	CardContent,
	Card<PERSON><PERSON><PERSON>,
	<PERSON><PERSON><PERSON>er,
	<PERSON><PERSON><PERSON><PERSON>,
	Card<PERSON><PERSON><PERSON>,
} from "@/components/ui/card";
import {
	ProcessedProduct,
	StripePrice,
	formatPrice,
	StripeCustomer,
} from "./types";
import { memo, useMemo, useState } from "react";
import { useProject } from "@/contexts/ProjectContext/ProjectContextProvider";
import { Check } from "lucide-react";
import { redirectToCustomerPortal } from "./hooks";
import { Button } from "@/components/ui/button";
import { useRouter } from "next/navigation";
import { useToast } from "@/hooks/use-toast";

// Use the project type from the current context
type Project = ReturnType<typeof useProject>["selectedProject"];

interface PlanCardProps {
	product: ProcessedProduct;
	selectedPrice: StripePrice | null;
	isCurrentPlan: (priceId: string) => boolean;
	selectedProject: Project | null;
	customerId?: string;
	hasSubscription: boolean;
	hasAdminPermission: boolean;
	subscriptionStatus: StripeCustomer["status"];
}

// Adding memo to prevent unnecessary re-renders
export const PlanCard = memo(function PlanCard({
	product,
	selectedPrice,
	isCurrentPlan,
	selectedProject,
	customerId,
	hasSubscription,
	hasAdminPermission,
	subscriptionStatus,
}: PlanCardProps) {
	const [isRedirecting, setIsRedirecting] = useState(false);
	const router = useRouter();
	const { toast } = useToast();
	const projectId = selectedProject?.id?.toString();

	// Check if this is the current plan based on price_id
	const isCurrent = useMemo(() => {
		if (!selectedPrice) return false;
		return isCurrentPlan(selectedPrice.id);
	}, [selectedPrice, isCurrentPlan]);

	// Handler for redirecting to customer portal
	const handleRedirectToPortal = async () => {
		if (customerId && !isRedirecting) {
			setIsRedirecting(true);
			try {
				console.log("🎯 [PLAN_CARD] Redirecionando para portal do Stripe:", {
					customerId,
					projectId,
				});
				await redirectToCustomerPortal(customerId, projectId);
			} catch (error) {
				console.error(
					"❌ [PLAN_CARD] Erro ao redirecionar para o portal:",
					error
				);
				setIsRedirecting(false);
			}
		}
	};

	// Define statuses that allow resubscribing/paying
	const resubscribableStatuses: StripeCustomer["status"][] = [
		"canceled",
		"incomplete_expired",
		"unpaid",
		"inactive",
	];

	// Check if the current status allows resubscription
	const canResubscribeStatus =
		hasSubscription && resubscribableStatuses.includes(subscriptionStatus);

	const handleNavigateToMembers = () => {
		const targetUrl = projectId
			? `/${projectId}/settings?tab=members`
			: "/settings?tab=members";
		router.push(targetUrl);
	};

	// Real PaymentButton component
	const PaymentButton = ({
		priceId,
		children,
	}: {
		priceId: string;
		children: React.ReactNode;
	}) => {
		const [isLoading, setIsLoading] = useState(false);

		const handlePayment = async () => {
			if (!selectedProject?.id || !priceId || isLoading) return;

			// Log detalhado da seleção do plano
			console.log("🎯 [PLAN_SELECTION] Usuário selecionou um plano:", {
				selectedPriceId: priceId,
				selectedPrice: selectedPrice,
				productName: product.name,
				productId: product.id,
				projectId,
				amount: selectedPrice?.unit_amount,
				currency: selectedPrice?.currency,
				interval: selectedPrice?.recurring?.interval,
				timestamp: new Date().toISOString(),
			});

			try {
				setIsLoading(true);

				// First, ensure customer exists
				console.log("🔍 [PLAN_SELECTION] Verificando se Customer existe:", {
					projectId,
				});

				const ensureCustomerResponse = await fetch(
					"/api/stripe/ensure-customer",
					{
						method: "POST",
						headers: {
							"Content-Type": "application/json",
						},
						body: JSON.stringify({
							projectId,
						}),
					}
				);

				if (!ensureCustomerResponse.ok) {
					const errorData = await ensureCustomerResponse.json();
					console.error(
						"❌ [PLAN_SELECTION] Erro ao verificar/criar Customer:",
						{
							status: ensureCustomerResponse.status,
							errorData,
							projectId,
						}
					);
					throw new Error("Failed to ensure customer exists");
				}

				const customerData = (await ensureCustomerResponse.json()) as {
					customerId: string;
					email: string;
					created: boolean;
				};

				console.log("✅ [PLAN_SELECTION] Customer verificado/criado:", {
					customerId: customerData.customerId,
					email: customerData.email,
					wasCreated: customerData.created,
					projectId,
				});

				// Show toast if customer was created
				if (customerData.created) {
					toast({
						title: "Account Setup Complete",
						description: `Billing account created for ${customerData.email}`,
						variant: "default",
					});
				}

				console.log(
					"🔄 [PLAN_SELECTION] Iniciando requisição para criar checkout:",
					{
						priceId,
						projectId,
						customerId: customerData.customerId,
					}
				);

				const response = await fetch("/api/stripe/create-checkout", {
					method: "POST",
					headers: {
						"Content-Type": "application/json",
					},
					body: JSON.stringify({
						priceId: priceId,
						projectId,
					}),
				});

				if (!response.ok) {
					const errorData = await response.json();
					console.error("❌ [PLAN_SELECTION] Erro na criação do checkout:", {
						status: response.status,
						errorData,
						priceId,
						projectId,
					});
					throw new Error("Failed to create checkout session");
				}

				const data = (await response.json()) as { url: string };

				console.log(
					"✅ [PLAN_SELECTION] Checkout criado com sucesso, redirecionando:",
					{
						checkoutUrl: data.url,
						priceId,
						projectId,
					}
				);

				// Redirect to Stripe Checkout
				window.location.href = data.url;
			} catch (error) {
				console.error("❌ [PLAN_SELECTION] Erro no pagamento:", {
					error: error instanceof Error ? error.message : error,
					priceId,
					projectId,
					timestamp: new Date().toISOString(),
				});
				setIsLoading(false);
			}
		};

		return (
			<Button
				onClick={handlePayment}
				className='w-full'
				disabled={isLoading || !priceId}
			>
				{isLoading ? "Redirecting..." : children}
			</Button>
		);
	};

	return (
		<Card
			className={`relative border pt-6 ${
				isCurrent ? "border-blue-500" : "border-gray-200"
			} hover:shadow-md transition-shadow h-full flex flex-col`}
		>
			{isCurrent && (
				<div className='absolute -top-3 right-4'>
					<span className='px-3 py-1 bg-blue-100 text-blue-700 text-xs font-medium rounded-full'>
						Current Plan
					</span>
				</div>
			)}
			<CardHeader className='pb-3 items-start'>
				<CardTitle className='text-lg sm:text-xl font-bold'>
					{product.name}
				</CardTitle>
				<CardDescription className='text-gray-600 text-sm text-justify'>
					{product.description || "No description available"}
				</CardDescription>
			</CardHeader>

			<CardContent className='py-3 flex flex-col flex-grow'>
				<div className='text-xl sm:text-2xl lg:text-3xl font-bold'>
					{selectedPrice
						? formatPrice(selectedPrice.unit_amount, selectedPrice.currency)
						: "Price not available"}
					<span className='text-sm text-gray-500 font-normal'>
						/per{" "}
						{selectedPrice?.recurring?.interval === "month" ? "month" : "year"}
					</span>
				</div>

				<div className='border-t border-gray-200 my-2'></div>

				{product.features && product.features.length > 0 && (
					<div className='space-y-2 flex-grow'>
						<h4 className='font-medium text-gray-700 text-sm'>INCLUDES</h4>
						<ul className='space-y-1.5'>
							{product.features.map((feature, index) => (
								<li key={index} className='flex items-start'>
									<span className='mr-2 text-xs sm:text-sm flex-shrink-0'>
										<Check className='w-4 h-4 text-blue-500' />
									</span>
									<span className='text-xs sm:text-sm text-gray-600'>
										{feature}
									</span>
								</li>
							))}
						</ul>
					</div>
				)}
			</CardContent>

			<CardFooter className='pt-2 pb-4 sm:pb-6'>
				{hasAdminPermission ? (
					hasSubscription && !canResubscribeStatus ? (
						<Button
							onClick={handleRedirectToPortal}
							disabled={isRedirecting || !customerId}
							className='w-full'
							variant='blue'
						>
							{isRedirecting
								? "Redirecting..."
								: isCurrent
								? "Manage Subscription"
								: "Switch Plan"}
						</Button>
					) : (
						<PaymentButton priceId={selectedPrice?.id || ""}>
							{canResubscribeStatus && isCurrent ? "Resubscribe" : "Subscribe"}
						</PaymentButton>
					)
				) : (
					<Button
						onClick={handleNavigateToMembers}
						variant='default'
						className='w-full'
					>
						Contact Project Owner
					</Button>
				)}
			</CardFooter>
		</Card>
	);
});
