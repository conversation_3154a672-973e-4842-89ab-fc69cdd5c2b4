"use client";
import PageDisplay from "@/components/editor/PageDisplay";
import { useParams } from "next/navigation";
export default function EditorRootPage() {
  const params = useParams<{ projectId: string }>();
  const projectId = params.projectId;
  const pagePath = "/";
  if (!projectId) {
    return <p>Loading project information...</p>;
  }
  return <PageDisplay projectId={projectId} pagePath={pagePath} />;
}
