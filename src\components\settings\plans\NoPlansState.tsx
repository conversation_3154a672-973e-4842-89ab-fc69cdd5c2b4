import { Button } from "@/components/ui/button";

interface NoPlansStateProps {
	loadProducts: () => Promise<void>;
}

export function NoPlansState({ loadProducts }: NoPlansStateProps) {
	return (
		<div className='text-center p-8'>
			<h3 className='text-lg font-medium mb-2'>No plans available</h3>
			<p className='text-gray-500 mb-4'>
				Could not find any available plans at the moment.
			</p>
			<Button
				variant='outline'
				onClick={loadProducts}
				className='flex items-center'
			>
				Try again
			</Button>
		</div>
	);
}
