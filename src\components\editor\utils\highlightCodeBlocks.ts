import hljs from "highlight.js/lib/core";

// Import only the languages we need to keep bundle size small
import javascript from "highlight.js/lib/languages/javascript";
import typescript from "highlight.js/lib/languages/typescript";
import python from "highlight.js/lib/languages/python";
import java from "highlight.js/lib/languages/java";
import xml from "highlight.js/lib/languages/xml"; // for HTML
import css from "highlight.js/lib/languages/css";
import json from "highlight.js/lib/languages/json";
import sql from "highlight.js/lib/languages/sql";
import bash from "highlight.js/lib/languages/bash";
import php from "highlight.js/lib/languages/php";
import go from "highlight.js/lib/languages/go";
import rust from "highlight.js/lib/languages/rust";
import cpp from "highlight.js/lib/languages/cpp";
import csharp from "highlight.js/lib/languages/csharp";
import yaml from "highlight.js/lib/languages/yaml";
import markdown from "highlight.js/lib/languages/markdown";
import dockerfile from "highlight.js/lib/languages/dockerfile";

// Register languages
hljs.registerLanguage("javascript", javascript);
hljs.registerLanguage("typescript", typescript);
hljs.registerLanguage("python", python);
hljs.registerLanguage("java", java);
hljs.registerLanguage("html", xml);
hljs.registerLanguage("xml", xml);
hljs.registerLanguage("css", css);
hljs.registerLanguage("json", json);
hljs.registerLanguage("sql", sql);
hljs.registerLanguage("bash", bash);
hljs.registerLanguage("php", php);
hljs.registerLanguage("go", go);
hljs.registerLanguage("rust", rust);
hljs.registerLanguage("cpp", cpp);
hljs.registerLanguage("csharp", csharp);
hljs.registerLanguage("yaml", yaml);
hljs.registerLanguage("markdown", markdown);
hljs.registerLanguage("docker", dockerfile);

/**
 * Apply syntax highlighting to code blocks in HTML content
 */
export function highlightCodeBlocks(html: string): string {
  // Create a temporary container to parse HTML
  const tempDiv = document.createElement("div");
  tempDiv.innerHTML = html;

  // Find all pre > code elements
  const codeBlocks = tempDiv.querySelectorAll(
    'pre[data-type="codeBlock"] code'
  );

  codeBlocks.forEach((codeElement) => {
    const className = codeElement.className || "";
    const language = className.match(/language-(\w+)/)?.[1] || "text";
    const code = codeElement.textContent || "";

    if (language !== "text" && code) {
      try {
        // Apply syntax highlighting
        const highlighted = hljs.highlight(code, { language }).value;
        codeElement.innerHTML = highlighted;
      } catch (error) {
        console.warn(
          `Failed to highlight code block with language ${language}:`,
          error
        );
        // Keep the original code if highlighting fails
      }
    }
  });

  return tempDiv.innerHTML;
}
