export type MarketingFeature = {
	name?: string;
};

export type StripePrice = {
	id: string;
	active: boolean;
	currency: string;
	unit_amount: number;
	recurring: {
		interval: string;
		interval_count: number;
		trial_period_days: number | null;
		usage_type: string;
	} | null;
	product: {
		id: string;
		name: string;
		description: string | null;
		active: boolean;
		images: string[];
		metadata: Record<string, string>;
		marketing_features?: MarketingFeature[];
	};
};

export type StripePayment = {
	id: string;
	customer_id: string;
	project_id: number;
	invoice_id?: string;
	subscription_id?: string;
	amount: number;
	currency: string;
	status: string;
	payment_method_id?: string;
	payment_method_type?: string;
	payment_method_brand?: string;
	payment_method_last4?: string;
	error_code?: string;
	error_message?: string;
	created: string;
	updated: string;
};

export type StripeInvoice = {
	id: string;
	customer_id: string;
	project_id: number;
	subscription_id?: string;
	status: string;
	amount_due: number;
	amount_paid: number;
	amount_remaining: number;
	currency: string;
	billing_reason?: string;
	invoice_pdf?: string;
	hosted_invoice_url?: string;
	period_start?: string;
	period_end?: string;
	created: string;
};

export type StripeCustomer = {
	id: string;
	project_id: number;
	email?: string;
	name?: string;
	status:
		| "active"
		| "inactive"
		| "past_due"
		| "canceled"
		| "incomplete"
		| "incomplete_expired"
		| "trialing"
		| "unpaid";
	subscription_id?: string;
	price_id?: string;
	product_id?: string;
	payment_method_id?: string;
	payment_method_type?: string;
	payment_method_brand?: string;
	payment_method_last4?: string;
	current_period_start?: string;
	current_period_end?: string;
	cancel_at_period_end?: boolean;
	created?: string;
	updated?: string;
	// Informações de pagamento e faturamento
	lastPayment?: StripePayment;
	lastInvoice?: StripeInvoice;
	billingInterval?: string;
	amount?: number;
};

export type ProcessedProduct = {
	id: string;
	name: string;
	description: string | null;
	active: boolean;
	_temp_default_price_id?: string | null; // Temporary field for processing
	default_price: StripePrice | null;
	prices: StripePrice[];
	features: string[] | null;
	// Prices to display based on complex logic
	display_prices: {
		monthly: StripePrice | null;
		yearly: StripePrice | null;
	};
};

export function formatPrice(amount: number, currency: string): string {
	if (amount === undefined || currency === undefined) {
		return "Price not available";
	}

	try {
		const formatter = new Intl.NumberFormat("en-US", {
			style: "currency",
			currency: currency.toUpperCase(),
			minimumFractionDigits: 2,
		});

		return formatter.format(amount / 100);
	} catch (error) {
		console.error("Error formatting price:", error);
		return "Error in price format";
	}
}

export function extractFeaturesFromMetadata(
	metadata: Record<string, string>
): string[] | null {
	// Extrair recursos do metadata, se existirem
	const features: string[] = [];

	for (const key in metadata) {
		if (key.startsWith("feature_") && metadata[key]) {
			features.push(metadata[key]);
		}
	}

	return features.length > 0 ? features : null;
}
