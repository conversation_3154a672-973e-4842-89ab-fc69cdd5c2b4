/**
 * Utility functions for checking user roles and permissions in Stripe billing context
 */

export type UserRole = "Owner" | "Admin" | "Editor";

/**
 * Get roles that have admin access for billing features
 */
export function getAdminRoles(): UserRole[] {
	return ["Owner", "Admin"];
}

/**
 * Check if a role has admin access for billing features
 */
export function isAdminRole(role: string): boolean {
	const adminRoles = getAdminRoles();
	return adminRoles.includes(role as UserRole);
}

/**
 * Check if a user role has admin access for billing operations
 */
export function hasAdminAccess(userRole?: string | null): boolean {
	if (!userRole) return false;
	return isAdminRole(userRole);
}

/**
 * Check if a user can manage billing (same as admin for now)
 */
export function canManageBilling(userRole?: string | null): boolean {
	return hasAdminAccess(userRole);
}

/**
 * Check if a user can view billing information
 */
export function canViewBilling(userRole?: string | null): boolean {
	// All project members can view billing info
	if (!userRole) return false;
	return ["Owner", "Admin", "Editor"].includes(userRole as UserRole);
}
