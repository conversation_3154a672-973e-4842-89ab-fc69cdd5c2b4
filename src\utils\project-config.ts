interface ConfigJson {
  websiteName: string;
  description: string;
  homepage: string;
  images: {
    logo: string;
    favicon: string;
  };
  navbar: NavbarItem[];
  styles: {
    mainColor: string;
  };
  apiFiles: string[];
  sidebars: DocusaurusSidebar[];
}

interface NavbarItem {
  label: string;
  dropdown: DropdownItem[];
}

interface DropdownItem {
  label: string;
  sidebarRef: string;
}

interface DocusaurusSidebar {
  sidebarRef: string;
  categories: SidebarCategory[];
}

interface SidebarCategory {
  categoryName: string;
  pages: string[];
}

export function generateDefaultConfig(projectName: string): ConfigJson {
  // Remove "-docs" suffix if present and clean project name
  const cleanProjectName = projectName.replace(/-docs$/, "");
  const displayName = cleanProjectName
    .split("-")
    .map((word) => word.charAt(0).toUpperCase() + word.slice(1))
    .join(" ");

  const sidebarRef = `first-steps-${Math.random().toString(36).substring(2, 12)}`;

  return {
    websiteName: `${displayName} Documentation`,
    description: `Documentation site for ${displayName} generated by WriteDocs`,
    homepage: "/",
    images: {
      logo: "media/logo.png",
      favicon: "media/favicon.ico",
    },
    navbar: [
      {
        label: "Welcome",
        dropdown: [
          {
            label: "First steps",
            sidebarRef: sidebarRef,
          },
        ],
      },
    ],
    styles: {
      mainColor: "#0029F5",
    },
    apiFiles: [],
    sidebars: [
      {
        sidebarRef: sidebarRef,
        categories: [
          {
            categoryName: "Editor",
            pages: [
              "Welcome/First steps/Editor/How to use",
              "Welcome/First steps/Editor/Filetree",
            ],
          },
        ],
      },
    ],
  };
}

export const defaultPageContents = {
  "Welcome/First steps/Editor/How to use": {
    title: "How to use the Editor",
    content: `# How to Use the WriteDocs Editor

Welcome to WriteDocs! Our powerful editor makes creating beautiful documentation simple and intuitive. This guide will show you everything you can do.

## Getting Started

The WriteDocs editor is a **rich text editor** that lets you create content visually without needing to know code. Simply click where you want to write and start typing!

## Basic Text Formatting

### Formatting Text
Select any text to see the **formatting bubble menu** with these options:
- **Bold** text (Ctrl+B)
- *Italic* text (Ctrl+I) 
- ~~Strikethrough~~ text
- \`Inline code\` formatting
- [Links](https://example.com) to other pages or websites

### Creating Structure
Use these elements to organize your content:
- **Headings** - Create H1, H2, and H3 headings for document structure
- **Lists** - Both bullet points and numbered lists
- **Blockquotes** - For important quotes or callouts
- **Horizontal rules** - Visual dividers between sections

## The Slash Menu - Your Content Toolkit

Type **/** anywhere to open the content menu with 25+ content types:

### Text Content
- **Paragraph** - Regular text content
- **Headings** (H1, H2, H3) - Structure your documentation

### Advanced Content Blocks
- **Code Blocks** - Syntax highlighting for 18+ programming languages
- **Tables** - Create and edit tables with drag-to-resize columns
- **Callouts** - Highlight important information in 6 different styles:
  - Info (blue), Success (green), Warning (yellow)
  - Danger (red), Support (orange), Training (gray)

### Interactive Elements
- **Accordions** - Collapsible content sections
- **Tabs** - Switch between different content areas
- **Steps** - Create numbered instruction sequences
- **Cards** - Visual content blocks with images and links

### Media & Visual Content
- **Images** - Upload and insert images with sizing options
- **Videos** - Embed videos and media content

## Working with Code

### Code Blocks
Create professional code examples with:
- **18+ languages** including JavaScript, Python, HTML, CSS, JSON, SQL, and more
- **Syntax highlighting** that updates as you type
- **Copy button** for easy code copying
- **Line numbers** for reference

### Inline Code
Use \`backticks\` or the formatting menu to highlight code within sentences.

## Advanced Features

### Tables Made Easy
- Click to create tables, then add/remove rows and columns
- Use **Tab** to navigate between cells
- Drag column borders to resize
- Right-click for table options

### Smart Content Organization
- **Drag and drop** content blocks to reorder them
- **Nest content** - put code blocks inside tabs, tables inside accordions
- **Auto-save** - your work is automatically saved as you type

### Links and Navigation
- Highlight text and click the link button to add links
- Links automatically open in new tabs
- Edit existing links by clicking on them

## Keyboard Shortcuts

Speed up your workflow with these shortcuts:
- **/** - Open the content menu
- **Ctrl+Z** - Undo changes
- **Ctrl+Y** - Redo changes
- **Tab** - Indent in lists or navigate table cells
- **Enter** - Create new blocks or paragraphs

## Pro Tips

1. **Start with structure** - Use headings to outline your content first
2. **Use callouts** for important information that needs to stand out
3. **Add code examples** - Code blocks make technical content much clearer
4. **Break up long content** - Use tabs or accordions for better organization
5. **Include visuals** - Images and videos make documentation more engaging

## Content Types You Can Create

The editor supports everything you need for professional documentation:
- Technical documentation with code examples
- User guides with step-by-step instructions
- API documentation with interactive elements
- Knowledge bases with organized content
- Training materials with visual elements

## Need Help?

The editor is designed to be intuitive - just start typing and explore the slash menu to discover all the content types available. Everything auto-saves, so feel free to experiment!`,
  },
  "Welcome/First steps/Editor/Filetree": {
    title: "Working with the File Tree",
    content: `# Organizing Your Documentation with the File Tree

The file tree is your command center for organizing and structuring your documentation. Think of it as a visual filing cabinet where you can create, organize, and manage all your content.

## Understanding the Structure

Your documentation is organized in a **hierarchical system** with different levels:

### Navigation Level
- **Navbar Dropdowns** - Top-level menus in your site's navigation bar
- **Navbar Items** - Individual links that appear in dropdowns or directly in the navbar

### Content Organization Level  
- **Categories** - Main folders that group related content (orange folder icons)
- **Groups** - Sub-folders within categories for further organization (indigo folder icons)
- **Pages** - Your actual documentation content (document icons)
- **Subpages** - Nested pages within groups for detailed topics

## Quick Actions Panel

At the top of the file tree, you'll find **Quick Actions** - your shortcut to creating new content:

- **Navbar Dropdown** - Create new dropdown menus for your navigation
- **Navbar Item** - Add individual navigation links
- **Import API Docs** - Bulk import documentation from API specifications

## Creating New Content

### Right-Click Context Menu
**Right-click on any item or empty space** to see available actions. The menu is smart - it only shows options that make sense for what you clicked on:

- Right-click on categories to create groups or pages
- Right-click on groups to create pages or subpages  
- Right-click in empty space to create top-level items

### Smart Creation Rules
The system prevents mistakes by only allowing valid combinations:
- Pages can only go inside categories or groups
- Navigation items maintain proper hierarchy
- You can't create invalid structures

## Organizing with Drag and Drop

### Visual Drag and Drop
**Click and drag** any item to move it around:
- **Blue highlights** show where you can drop items
- **Blue lines** indicate if items will go before, after, or inside containers
- **Auto-expansion** - folders open automatically when you drag items over them

### Smart Positioning
You can drop items in three ways:
- **Inside containers** - Drop pages into categories or groups
- **Before/after items** - Reorder items at the same level
- **Invalid drops** are prevented - you'll see visual feedback if a drop isn't allowed

## Managing Your Content

### Renaming Items
1. **Right-click** on any item and select "Rename"
2. **Type the new name** directly in the tree
3. **Press Enter** to confirm or **Escape** to cancel
4. **Paths update automatically** when you rename containers

### Deleting Items
1. **Right-click** and select "Delete"
2. **Confirm in the dialog** that appears
3. **Warning**: Deleting folders removes ALL content inside them
4. **This action cannot be undone** - deleted content is permanently removed

## Navigation and Interaction

### Expanding and Collapsing
- **Click the folder icons** or arrow chevrons to expand/collapse sections
- **Your preferences are saved** - the tree remembers what you had open
- **Visual hierarchy** - indentation and connecting lines show relationships

### Opening Pages
- **Click on any page** to open it in the editor
- **Loading indicators** show when pages are loading
- **Active page highlighting** - the current page is clearly marked

## Advanced Features

### API Documentation Import
- **Import entire API specifications** at once
- **Automatic organization** based on your API structure
- **Bulk page creation** saves time on large documentation projects

### Visual Organization
The file tree uses **color-coded icons** to help you understand your structure:
- **Blue icons** - Navigation elements (menus and nav items)
- **Orange folders** - Categories (main content containers)
- **Indigo folders** - Groups (sub-containers)
- **Gray documents** - Pages and subpages (your actual content)

## Best Practices for Organization

### Structure Planning
1. **Start with main topics** - Create categories for your major subjects
2. **Use groups for subtopics** - Break down complex categories
3. **Keep it shallow** - Avoid too many nested levels (3-4 levels maximum)
4. **Think like your users** - Organize by how people will look for information

### Naming Conventions
- **Use clear, descriptive names** that explain the content
- **Be consistent** - if you use "Getting Started" in one place, don't use "Quick Start" elsewhere
- **Avoid technical jargon** in navigation names
- **Keep names concise** but informative

### Content Organization
- **Group related pages** together in the same category or group
- **Use logical ordering** - arrange items from basic to advanced, or by workflow
- **Consider your audience** - organize by user type or use case if relevant

## Tips for Efficient Organization

1. **Plan your structure first** - sketch out your organization before creating lots of content
2. **Use drag and drop liberally** - it's easy to reorganize as your documentation grows
3. **Regular cleanup** - periodically review and reorganize outdated structure
4. **Test your navigation** - make sure users can find what they need easily
5. **Use descriptive names** - future you will thank you for clear naming

## Troubleshooting Common Issues

**Can't find where to create something?**
- Try right-clicking in different places - the context menu changes based on what you click

**Item won't move where you want?**
- Check if you're trying to move something into an invalid location
- Some items can only go in specific containers

**Lost in a complex structure?**
- Use the expand/collapse features to hide sections you're not working on
- The visual hierarchy helps show relationships between items

The file tree is designed to grow with your documentation. Start simple and add complexity as needed - you can always reorganize later!`,
  },
};
