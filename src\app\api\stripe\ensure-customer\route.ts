import { NextRequest, NextResponse } from "next/server";
import <PERSON><PERSON> from "stripe";
import { createClient } from "@/utils/supabase/server";

export const runtime = 'edge';

const stripe = new Stripe(process.env.STRIPE_SECRET_KEY!, {
	apiVersion: "2025-05-28.basil",
});

export async function POST(request: NextRequest) {
	try {
		if (!process.env.STRIPE_SECRET_KEY) {
			throw new Error("STRIPE_SECRET_KEY environment variable is not set");
		}

		const body = (await request.json()) as {
			projectId: string;
		};
		const { projectId } = body;

		if (!projectId) {
			console.error("❌ [ENSURE_CUSTOMER] Project ID não fornecido");
			return NextResponse.json(
				{ error: "Project ID is required" },
				{ status: 400 }
			);
		}

		// Get user from Supabase
		const supabase = await createClient();
		const {
			data: { user },
			error: userError,
		} = await supabase.auth.getUser();

		if (userError || !user) {
			console.error("❌ [ENSURE_CUSTOMER] Usuário não autorizado:", {
				userError: userError?.message,
				hasUser: !!user,
				projectId,
			});
			return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
		}

		// Get project data from database
		const { data: projectData, error: projectError } = await supabase
			.from("projects")
			.select("id, project_name, company_name")
			.eq("id", parseInt(projectId))
			.single();

		if (projectError || !projectData) {
			console.error("❌ [ENSURE_CUSTOMER] Projeto não encontrado:", {
				projectError: projectError?.message,
				projectId,
			});
			return NextResponse.json({ error: "Project not found" }, { status: 404 });
		}

		// Create customer name: Company Name if exists, otherwise Project Name
		const customerName = projectData.company_name || projectData.project_name;

		// Check if customer exists in our database first
		const { data: existingCustomer } = await supabase
			.from("stripe_customers")
			.select("id, email")
			.eq("project_id", parseInt(projectId))
			.single();

		if (existingCustomer) {
			// Verify the customer still exists in Stripe
			try {
				const stripeCustomer = await stripe.customers.retrieve(
					existingCustomer.id
				);
				if (!stripeCustomer.deleted) {
					return NextResponse.json({
						customerId: existingCustomer.id,
						email: existingCustomer.email,
						created: false,
					});
				} else {
					await supabase
						.from("stripe_customers")
						.delete()
						.eq("id", existingCustomer.id);
				}
			} catch {
				await supabase
					.from("stripe_customers")
					.delete()
					.eq("id", existingCustomer.id);
			}
		}

		// Look for existing customer in Stripe by email and project_id
		let stripeCustomer: Stripe.Customer | null = null;

		const customers = await stripe.customers.list({
			email: user.email!,
			limit: 100, // Increase limit to check all customers with this email
		});

		// Look for a customer with the same project_id in metadata
		for (const customer of customers.data) {
			if (customer.metadata?.project_id === projectId) {
				stripeCustomer = customer;

				break;
			}
		}

		// If no customer found, create a new one
		if (!stripeCustomer) {
			stripeCustomer = await stripe.customers.create({
				email: user.email!,
				name: customerName,
				metadata: {
					company_name: projectData.company_name || "",
					project_id: projectId,
					project_name: projectData.project_name,
				},
			});
		}

		// Save/update customer in our database
		const customerData = {
			id: stripeCustomer.id,
			project_id: parseInt(projectId),
			email: stripeCustomer.email,
			name: stripeCustomer.name,
			phone: stripeCustomer.phone,
			address: stripeCustomer.address
				? JSON.stringify(stripeCustomer.address)
				: null,
			created: new Date(stripeCustomer.created * 1000).toISOString(),
			updated: new Date().toISOString(),
		};

		const { error: dbError } = await supabase
			.from("stripe_customers")
			.upsert(customerData, {
				onConflict: "id",
			});

		if (dbError) {
			console.error("❌ [ENSURE_CUSTOMER] Erro ao salvar customer no banco:", {
				dbError,
				customerId: stripeCustomer.id,
				projectId,
			});
		} else {
		}

		return NextResponse.json({
			customerId: stripeCustomer.id,
			email: stripeCustomer.email,
			created: !existingCustomer,
		});
	} catch (error) {
		console.error("❌ [ENSURE_CUSTOMER] Erro ao verificar/criar customer:", {
			error: error instanceof Error ? error.message : error,
			stack: error instanceof Error ? error.stack : undefined,
			timestamp: new Date().toISOString(),
		});

		const errorMessage =
			error instanceof Error ? error.message : "Unknown error";

		return NextResponse.json(
			{
				error: "Failed to ensure customer exists",
				details: errorMessage,
			},
			{ status: 500 }
		);
	}
}
