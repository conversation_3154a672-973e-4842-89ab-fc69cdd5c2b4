import { <PERSON>, Card<PERSON>ontent, <PERSON><PERSON>eader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";

interface CloudflareProject {
	type?: string;
	project?: unknown;
	success?: boolean;
	data?: unknown;
}

interface DebugSectionProps {
	cloudflareProject: CloudflareProject | null;
	onFindProject: () => void;
	onListSites: () => void;
}

export function DebugSection({
	cloudflareProject,
	onFindProject,
	onListSites,
}: DebugSectionProps) {
	return (
		<div className='border-t pt-8 mt-12'>
			<h2 className='text-xl font-semibold mb-4'>
				Debug & Cloudflare Configuration
			</h2>
			<p className='text-muted-foreground mb-4'>
				Tools for debugging and configuration
			</p>

			<div className='flex gap-4 mb-4'>
				<Button variant='default' onClick={onFindProject}>
					Find Project
				</Button>

				<Button variant='outline' onClick={onListSites}>
					List Available Sites
				</Button>
			</div>

			{cloudflareProject && (
				<Card>
					<CardHeader>
						<CardTitle>
							{cloudflareProject.type === "sites_list"
								? "Available Sites"
								: "Project Data"}
						</CardTitle>
					</CardHeader>
					<CardContent>
						<pre className='text-xs bg-muted p-4 rounded-md overflow-auto max-h-96'>
							{JSON.stringify(cloudflareProject, null, 2)}
						</pre>
					</CardContent>
				</Card>
			)}
		</div>
	);
}
