import { Card, CardContent } from "@/components/ui/card";

interface ErrorCardProps {
	error: string;
}

export function ErrorCard({ error }: ErrorCardProps) {
	return (
		<Card className='mb-6 border-destructive'>
			<CardContent className='pt-6'>
				<div className='flex items-center gap-2 text-destructive'>
					<span className='font-medium'>Error:</span>
					<span>{error}</span>
				</div>
			</CardContent>
		</Card>
	);
}
