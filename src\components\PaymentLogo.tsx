import {
	Amex,
	Diners,
	Discover,
	Jcb,
	Mastercard,
	Unionpay,
	Visa,
} from "react-payment-logos/dist/flat-rounded";
import { CreditCard } from "lucide-react";

type PaymentLogoProps = {
	brand?: string;
	size?: number;
};

/**
 * Componente que exibe o logo de bandeiras de cartão oficialmente suportadas pelo Stripe
 *
 * Bandeiras suportadas:
 * - Visa
 * - Mastercard
 * - American Express (Amex)
 * - Discover & Diners Club
 * - China UnionPay (CUP)
 * - Japan Credit Bureau (JCB)
 *
 * Fonte: https://docs.stripe.com/payments/cards
 */
export default function PaymentLogo({ brand, size = 24 }: PaymentLogoProps) {
	if (!brand) return <CreditCard width={size} />;

	const normalizedBrand = brand.toLowerCase();

	switch (normalizedBrand) {
		// Bandeiras principais globais
		case "visa":
			return <Visa width={size} height={size} />;
		case "mastercard":
			return <Mastercard width={size} height={size} />;
		case "amex":
		case "american express":
			return <Amex width={size} height={size} />;

		// Discover e Diners são tratados como parte da mesma rede pelo Stripe
		case "discover":
			return <Discover width={size} height={size} />;
		case "diners":
		case "diners club":
			return <Diners width={size} height={size} />;

		// Redes regionais/internacionais
		case "jcb":
		case "japan credit bureau":
			return <Jcb width={size} height={size} />;
		case "unionpay":
		case "china unionpay":
		case "cup":
			return <Unionpay width={size} height={size} />;

		// Fallback para qualquer outra bandeira não suportada
		default:
			return <CreditCard width={size} />;
	}
}
