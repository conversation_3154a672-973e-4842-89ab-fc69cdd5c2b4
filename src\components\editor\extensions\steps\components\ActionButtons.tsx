import React from "react";
import { Edit3, Plus, Trash2 } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { ActionButtonsProps } from "../types";

export const ActionButtons: React.FC<ActionButtonsProps> = ({
	onEdit,
	onAddSubStep,
	onDelete,
}) => (
	<div className='absolute top-2 right-2 flex items-center space-x-1 transition-all duration-200 z-30 bg-white dark:bg-slate-800 px-2 py-1 rounded-md shadow-lg border border-gray-200 dark:border-gray-600 backdrop-blur-sm'>
		<Button
			variant='ghost'
			size='sm'
			onClick={(e) => {
				e.stopPropagation();
				onEdit();
			}}
			className='h-6 px-2 text-xs hover:bg-gray-100 dark:hover:bg-slate-700'
			title='Edit'
		>
			<Edit3 className='w-3 h-3' />
			<span className='ml-1'>Edit</span>
		</Button>
		<Button
			variant='ghost'
			size='sm'
			onClick={(e) => {
				e.stopPropagation();
				onAddSubStep();
			}}
			className='h-6 px-2 text-xs hover:bg-blue-100 dark:hover:bg-blue-900/30 text-blue-600 dark:text-blue-400'
			title='Add Sub-step'
		>
			<Plus className='w-3 h-3' />
			<span className='ml-1'>Add Sub-step</span>
		</Button>
		<Button
			variant='ghost'
			size='sm'
			onClick={(e) => {
				e.stopPropagation();
				onDelete();
			}}
			className='h-6 px-2 text-xs hover:bg-red-100 dark:hover:bg-red-900/30 text-red-600 dark:text-red-400'
			title='Delete'
		>
			<Trash2 className='w-3 h-3' />
			<span className='ml-1'>Delete</span>
		</Button>
	</div>
);
