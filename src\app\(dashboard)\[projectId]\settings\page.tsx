"use client";

import React, { useEffect, useState } from "react";
import { useProject } from "@/contexts/ProjectContext/ProjectContextProvider";
import { useSearchParams, useRouter, useParams } from "next/navigation";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogFooter,
} from "@/components/ui/dialog";
import { Skeleton } from "@/components/ui/skeleton";
import {
  TabButton,
  TabType,
  GeneralTab,
  MembersTab,
  BillingTab,
} from "@/components/settings";

const SettingsPage = () => {
  const { selectedProject, isLoadingProjects } = useProject();
  const searchParams = useSearchParams();
  const router = useRouter();
  const params = useParams();
  const projectId = params.projectId as string;

  const [activeTab, setActiveTab] = useState<TabType>("geral");
  const [isAddDomainOpen, setIsAddDomainOpen] = useState(false);
  const [domainName, setDomainName] = useState("");

  // Update activeTab based on URL params
  useEffect(() => {
    const tabFromUrl = searchParams.get("tab") as TabType;
    if (tabFromUrl && ["geral", "members", "billing"].includes(tabFromUrl)) {
      setActiveTab(tabFromUrl);
    } else {
      setActiveTab("geral");
    }
  }, [searchParams]);

  const handleTabChange = (tab: TabType) => {
    setActiveTab(tab);
    const newSearchParams = new URLSearchParams(searchParams.toString());
    newSearchParams.set("tab", tab);
    router.push(`/${projectId}/settings?${newSearchParams.toString()}`);
  };

  const handleAddDomain = () => {
    // Aqui você pode adicionar a lógica para salvar o domínio
    setIsAddDomainOpen(false);
    setDomainName("");
  };

  // Case: No project could be selected/loaded (and not in initial loading state anymore)
  if (!selectedProject && !isLoadingProjects) {
    return (
      <div className="w-full p-4 md:p-6 lg:p-8 text-center">
        No project selected or project data is unavailable. Please select a
        project or check your access.
      </div>
    );
  }

  // Loading state
  if (isLoadingProjects) {
    return (
      <div className="w-full pt-6 md:pt-10 px-4 md:px-6 overflow-x-hidden space-y-6">
        {/* Header Skeleton */}
        <div className="flex items-center justify-between pb-6 border-b">
          <Skeleton className="h-8 w-32" />
          <Skeleton className="h-10 w-16" />
        </div>

        {/* Tabs Skeleton */}
        <div className="border-b">
          <div className="flex space-x-8">
            <Skeleton className="h-8 w-16" />
            <Skeleton className="h-8 w-20" />
            <Skeleton className="h-8 w-16" />
          </div>
        </div>

        {/* Content Skeleton */}
        <div className="pt-6 space-y-8">
          {/* Project Info Row */}
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
            <div className="space-y-2">
              <Skeleton className="h-4 w-24" />
              <Skeleton className="h-8 w-full" />
            </div>
            <div className="space-y-2">
              <Skeleton className="h-4 w-20" />
              <Skeleton className="h-8 w-full" />
            </div>
            <div className="space-y-2">
              <Skeleton className="h-4 w-28" />
              <Skeleton className="h-4 w-36" />
              <Skeleton className="h-3 w-48" />
            </div>
          </div>

          {/* Table Skeleton */}
          <div className="border rounded-lg p-6 space-y-4">
            <div className="flex items-center justify-between">
              <Skeleton className="h-6 w-32" />
              <Skeleton className="h-8 w-28" />
            </div>
            <div className="grid grid-cols-6 gap-4 pb-3 border-b">
              <Skeleton className="h-4 w-12" />
              <Skeleton className="h-4 w-12" />
              <Skeleton className="h-4 w-12" />
              <Skeleton className="h-4 w-8" />
              <Skeleton className="h-4 w-12" />
              <Skeleton className="h-4 w-16" />
            </div>
            <div className="py-12 text-center">
              <Skeleton className="h-4 w-48 mx-auto" />
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="w-full pt-6 md:pt-10 px-4 md:px-6 overflow-x-hidden space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between pb-6 border-b">
        <h1 className="text-2xl font-semibold text-foreground">Settings</h1>
        {activeTab === "geral" && (
          <Button
            variant="outline"
            className="text-sm px-6 disabled cursor-not-allowed hover:bg-transparent opacity-60"
          >
            Save
          </Button>
        )}
      </div>

      {/* Tabs */}
      <div className="border-b">
        <div className="flex space-x-8">
          <TabButton
            tab="geral"
            label="General"
            activeTab={activeTab}
            onClick={handleTabChange}
          />
          <TabButton
            tab="members"
            label="Members"
            activeTab={activeTab}
            onClick={handleTabChange}
          />
          <TabButton
            tab="billing"
            label="Billing"
            activeTab={activeTab}
            onClick={handleTabChange}
          />
        </div>
      </div>

      {/* Tab Content */}
      <div className="pt-6">
        {activeTab === "geral" && (
          <GeneralTab selectedProject={selectedProject} />
        )}
        {activeTab === "members" && <MembersTab />}
        {activeTab === "billing" && <BillingTab />}
      </div>

      {/* Add Domain Dialog */}
      <Dialog open={isAddDomainOpen} onOpenChange={setIsAddDomainOpen}>
        <DialogContent className="sm:max-w-md">
          <DialogHeader>
            <DialogTitle>Setup a new domain</DialogTitle>
          </DialogHeader>
          <div className="space-y-4 py-4">
            <div className="space-y-2">
              <label htmlFor="domain-name">Domain name</label>
              <Input
                id="domain-name"
                value={domainName}
                onChange={(e) => setDomainName(e.target.value)}
                placeholder="docs.example.com"
                className="w-full"
              />
            </div>
          </div>
          <DialogFooter className="flex justify-end space-x-2">
            <Button
              variant="outline"
              onClick={() => {
                setIsAddDomainOpen(false);
                setDomainName("");
              }}
            >
              Cancel
            </Button>
            <Button onClick={handleAddDomain}>Add</Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default SettingsPage;
