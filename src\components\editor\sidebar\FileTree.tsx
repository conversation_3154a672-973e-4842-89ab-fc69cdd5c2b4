'use client';

import React from 'react';
import { Globe } from 'lucide-react';
import FileTreeItem from './FileTreeItem';
import ContextMenu from './ContextMenu';
import type { FileTreeProps } from './types';

const FileTree: React.FC<FileTreeProps> = ({
	tree,
	onToggle,
	onDrop,
	draggedItem,
	setDraggedItem,
	createPageLink,
	handlePageClick,
	getItemClasses,
	onCreateItem,
	onRenameItem,
	onDeleteItem,
	onSetAsHomepage,
	editingNodeId,
	setEditingNodeId,
	homepagePath,
	onCustomHomepageClick,
}) => {
	// Nó virtual para representar a raiz
	const rootNode = {
		id: 'root',
		name: '<PERSON><PERSON>',
		type: 'navbar' as const,
		parentId: undefined,
		children: tree,
		isExpanded: true,
		data: { type: 'root' },
	};

	if (!tree || tree.length === 0) {
		// Retorna um container vazio que ainda permite o menu de contexto
		// A mensagem de "Right-click to get started" foi removida para
		// evitar duplicação com o estado de carregamento/skeleton.
		return (
			<ContextMenu
				node={rootNode}
				onCreateItem={onCreateItem}
				onRenameItem={onRenameItem}
				onDeleteItem={onDeleteItem}
				onSetAsHomepage={onSetAsHomepage}
				editingNodeId={editingNodeId}
				setEditingNodeId={setEditingNodeId}
			>
				<div className='h-full w-full cursor-pointer' />
			</ContextMenu>
		);
	}

	return (
		<ContextMenu
			node={rootNode}
			onCreateItem={onCreateItem}
			onRenameItem={onRenameItem}
			onDeleteItem={onDeleteItem}
			onSetAsHomepage={onSetAsHomepage}
			editingNodeId={editingNodeId}
			setEditingNodeId={setEditingNodeId}
		>
			<div className='flex flex-col h-full w-full'>
				{/* Custom Homepage Indicator */}
				{homepagePath === 'homepage.html' && (
					<div 
						className='mb-2 p-2 px-4 bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 cursor-pointer hover:bg-green-100 dark:hover:bg-green-900/30 transition-colors duration-200'
						onClick={onCustomHomepageClick}
						title='Click to open Quick Actions and edit Custom Homepage'
					>
						<div className='flex items-center gap-2'>
							<Globe className='h-4 w-4 text-green-600 dark:text-green-400' />
							<span className='text-xs font-medium text-green-800 dark:text-green-200'>
								Custom Homepage Active
							</span>
						</div>
					</div>
				)}

				{/* Container dos itens da árvore */}
				<div className='p-2 space-y-0.5'>
					{tree.map((node) => (
						<FileTreeItem
							key={node.id}
							node={node}
							level={0}
							fullTree={tree}
							onToggle={onToggle}
							onDrop={onDrop}
							draggedItem={draggedItem}
							setDraggedItem={setDraggedItem}
							createPageLink={createPageLink}
							handlePageClick={handlePageClick}
							getItemClasses={getItemClasses}
							onCreateItem={onCreateItem}
							onRenameItem={onRenameItem}
							onDeleteItem={onDeleteItem}
							onSetAsHomepage={onSetAsHomepage}
							editingNodeId={editingNodeId}
							setEditingNodeId={setEditingNodeId}
							homepagePath={homepagePath}
						/>
					))}
				</div>

				{/* Área extra que ocupa todo o restante do espaço para context menu */}
				<div className='flex-1 cursor-pointer' />
			</div>
		</ContextMenu>
	);
};

export default FileTree;
