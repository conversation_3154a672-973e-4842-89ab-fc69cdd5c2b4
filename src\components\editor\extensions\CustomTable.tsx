import { mergeAttributes } from '@tiptap/core';
import Table from '@tiptap/extension-table';
import { ReactNodeViewRenderer } from '@tiptap/react';
import { TableWrapper } from './TableWrapper';

export const CustomTable = Table.extend({
  addAttributes() {
    return {
      ...this.parent?.(),
      searchbar: {
        default: false,
        parseHTML: (element) => element.getAttribute('data-searchbar') === 'true',
        renderHTML: (attributes) => {
          if (!attributes.searchbar) return {};
          return { 'data-searchbar': 'true' };
        },
      },
    };
  },

  renderHTML({ HTMLAttributes }) {
    return [
      'table',
      mergeAttributes(HTMLAttributes, {
        class: 'w-full border-collapse border border-gray-300 dark:border-gray-600'
      }),
      ['tbody', 0]
    ];
  },

  addNodeView() {
    return ReactNodeViewRenderer(TableWrapper);
  },
});

export default CustomTable;