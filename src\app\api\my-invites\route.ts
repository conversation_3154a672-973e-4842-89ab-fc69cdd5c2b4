import { createClient } from "@/utils/supabase/server";
import { NextResponse } from "next/server";

export const runtime = 'edge';

export async function GET() {
	try {
		const supabase = createClient();
		const {
			data: { user },
		} = await supabase.auth.getUser();

		if (!user) {
			return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
		}

		const { data: invites, error } = await supabase
			.from("project_invites")
			.select(
				`
        *,
        project:projects(id, project_name)
      `
			)
			.eq("email", user.email)
			.eq("status", "invited");

		if (error) {
			console.error("Query error:", error);
			throw error;
		}

		return NextResponse.json({ invites });
	} catch (error) {
		console.error("Error fetching invites:", error);
		return NextResponse.json(
			{ error: "Error on get invites" },
			{ status: 500 }
		);
	}
}

export async function PATCH(request: Request) {
	try {
		const supabase = createClient();
		const {
			data: { user },
		} = await supabase.auth.getUser();

		if (!user) {
			return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
		}

		const { inviteId, projectId } = (await request.json()) as {
			inviteId: number;
			projectId: number;
		};

		// Accept invite with default role of 'Editor'
		// Project owners can change the role later if needed
		const { data, error: rpcError } = await supabase.rpc(
			"accept_project_invite",
			{
				p_invite_id: inviteId,
				p_project_id: projectId,
				p_user_id: user.id,
				p_user_email: user.email,
				p_role: "Editor", // Default role for all invites
			}
		);

		if (rpcError) {
			console.error("RPC Error:", rpcError);
			throw rpcError;
		}

		return NextResponse.json({ success: true, data });
	} catch (error) {
		console.error("Error accepting invite:", error);
		return NextResponse.json(
			{
				error: "Erro ao aceitar convite",
				details: error instanceof Error ? error.message : "no hints",
			},
			{ status: 500 }
		);
	}
}

export async function DELETE(request: Request) {
	try {
		const { inviteId } = (await request.json()) as { inviteId: number };
		const supabase = createClient();

		const { error } = await supabase
			.from("project_invites")
			.delete()
			.eq("id", inviteId);

		if (error) throw error;

		// Retornar uma resposta vazia mas com status 200
		return new NextResponse(null, { status: 200 });
	} catch (error) {
		console.error("Error rejecting invite:", error);
		return NextResponse.json(
			{ error: "Error rejecting invite" },
			{ status: 500 }
		);
	}
}
