import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>Title } from "@/components/ui/card";
import { Skeleton } from "@/components/ui/skeleton";
import { BiLink } from "react-icons/bi";

interface Source {
	count: number;
	sum: { visits: number };
	dimensions: { metric: string };
}

interface TopSourcesProps {
	sources: Source[];
	isLoading?: boolean;
}

const formatNumber = (num: number): string => {
	return new Intl.NumberFormat("en-US").format(num);
};

function TopSourcesItemSkeleton() {
	return (
		<div className='flex items-center justify-between gap-3'>
			<div className='flex-1 min-w-0'>
				<Skeleton className='h-4 w-36' />
			</div>
			<div className='flex items-center gap-3 flex-shrink-0'>
				<Skeleton className='h-4 w-12' />
				<Skeleton className='w-20 h-2 rounded-full' />
			</div>
		</div>
	);
}

function TopSourcesSkeleton() {
	return (
		<Card className='h-full'>
			<CardHeader className='py-6'>
				<div className='flex items-center gap-2'>
					<Skeleton className='h-5 w-5 rounded-full' />
					<Skeleton className='h-5 w-32' />
				</div>
				<Skeleton className='h-4 w-40 mt-2' />
			</CardHeader>
			<CardContent className='pb-6'>
				<div className='space-y-4 max-h-80 overflow-y-auto pr-2'>
					{Array.from({ length: 6 }).map((_, index) => (
						<TopSourcesItemSkeleton key={index} />
					))}
				</div>
			</CardContent>
		</Card>
	);
}

export function TopSources({ sources, isLoading = false }: TopSourcesProps) {
	if (isLoading) {
		return <TopSourcesSkeleton />;
	}

	if (!sources || sources.length === 0) {
		return null;
	}

	const maxVisits = sources[0]?.sum.visits || 1;

	return (
		<Card className='h-full'>
			<CardHeader className='py-6'>
				<div className='flex items-center gap-2'>
					<BiLink className='h-5 w-5 text-green-500' />
					<CardTitle className='text-lg font-semibold flex items-center'>
						Visits by Source{" "}
						<span className='text-xs text-gray-500 ml-3'>
							({sources.length} sources)
						</span>
					</CardTitle>
				</div>
				{/* <CardDescription className='text-sm text-gray-500'>
					All traffic sources ({sources.length} sources)
				</CardDescription> */}
			</CardHeader>
			<CardContent className='pb-6'>
				<div className='space-y-4 max-h-96 overflow-y-auto pr-2'>
					{sources.map((referer, index) => {
						const percentage = (referer.sum.visits / maxVisits) * 100;
						const displayName =
							referer.dimensions.metric === ""
								? "🔗 Direct"
								: referer.dimensions.metric;

						return (
							<div
								key={index}
								className='flex items-center justify-between gap-3'
							>
								<div className='flex-1 min-w-0'>
									<span className='font-medium text-sm text-gray-900 truncate block'>
										{displayName}
									</span>
								</div>
								<div className='flex items-center gap-2 sm:gap-3 flex-shrink-0'>
									<span className='text-sm text-gray-600 min-w-[2.5rem] sm:min-w-[3rem] text-right'>
										{formatNumber(referer.sum.visits)}
									</span>
									<div className='w-16 sm:w-20 lg:w-24 h-2 bg-gray-200 rounded-full overflow-hidden'>
										<div
											className='h-full bg-green-500 transition-all duration-300'
											style={{
												width: `${percentage}%`,
											}}
										/>
									</div>
								</div>
							</div>
						);
					})}
				</div>
			</CardContent>
		</Card>
	);
}
