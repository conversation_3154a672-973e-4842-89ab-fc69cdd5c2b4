import { useProject } from "@/contexts";
import { useParams } from "next/navigation";

/**
 * Custom hook to access the siteTag for the current project
 *
 * This hook is READ-ONLY - it only returns data from the context.
 * The siteTag is fetched by the layout's SiteTagPrefetcher component.
 *
 * @returns Object containing siteTag info and availability state
 *
 * @example
 * ```tsx
 * const { siteTag, siteName, isAvailable } = useSiteTag();
 *
 * if (!isAvailable) return <div>Loading analytics configuration...</div>;
 * if (!siteTag) return <div>No analytics configured</div>;
 *
 * // Use siteTag for API calls
 * ```
 */
export function useSiteTag() {
	const { projectId } = useParams();
	const { projects } = useProject();

	const numericProjectId = projectId ? parseInt(projectId as string) : null;
	const currentProject = projects.find((p) => p.id === numericProjectId);

	// Check if project exists and has been processed for siteTag
	const isAvailable = !!currentProject;
	const hasSiteTag = !!currentProject?.siteTag;

	return {
		siteTag: currentProject?.siteTag,
		siteName: currentProject?.siteName,
		isAvailable,
		hasSiteTag,
		projectId: numericProjectId,
		// Note: No refetch function - fetch is handled by layout only
	};
}
