"use client";
import React, { useEffect } from "react";

const DocsBot = () => {
  useEffect(() => {
    const script1 = document.createElement("script");
    script1.type = "text/javascript";
    script1.innerHTML = `
      window.DocsBotAI = window.DocsBotAI || {};
      DocsBotAI.init = function(e) {
        return new Promise((t, r) => {
          var n = document.createElement("script");
          n.type = "text/javascript";
          n.async = !0;
          n.src = "https://widget.docsbot.ai/chat.js";
          let o = document.getElementsByTagName("script")[0];
          o.parentNode.insertBefore(n, o);
          n.addEventListener("load", () => {
            let n;
            Promise.all([
              new Promise((t, r) => {
                window.DocsBotAI.mount(Object.assign({}, e)).then(t).catch(r);
              }),
              (n = function e(t) {
                return new Promise(e => {
                  if (document.querySelector(t)) return e(document.querySelector(t));
                  let r = new MutationObserver(n => {
                    if (document.querySelector(t)) return e(document.querySelector(t)), r.disconnect();
                  });
                  r.observe(document.body, { childList: !0, subtree: !0 });
                });
              })("#docsbotai-root"),
            ])
            .then(() => t())
            .catch(r);
          });
          n.addEventListener("error", e => { r(e.message) });
        });
      };
    `;
    document.body.appendChild(script1);

    const script2 = document.createElement("script");
    script2.type = "text/javascript";

    const options = {
      labels: {
        poweredBy: "Powered by",
        inputPlaceholder: "Send a message...",
        firstMessage: "What can I help you with?",
        sources: "Sources",
        unhelpful: "Report as innacurate",
        getSupport: `Need help? Contact support`,
        floatingButton: "Ask AI",
        suggestions: "Not sure what to ask?",
        close: "Close",
        create: "Create your own!",
        thinking: "Thinking...",
      },
      customCSS: `
      .docsbot-chat-input {
        background-color: #fff !important;
      }
      .docsbot-chat-bot-message-support a {
        font-size: 14px;
        font-weight: 600;
        padding: 6px 12px !important;
        margin-bottom: 6px;
      }
    `,
    };

    script2.innerHTML = `
        DocsBotAI.init({
          id: "O8PMwjUFBgUo8miMVgJh/Qhq7H6n54htKfq6ag3oM",
          options: ${JSON.stringify(options)}
        });
      `;

    document.body.appendChild(script2);

    // Cleanup
    return () => {
      document.body.removeChild(script1);
      document.body.removeChild(script2);
    };
  }, []);

  return (
    <div>
      <div id="docsbotai-root"></div>
    </div>
  );
};

export default DocsBot;
