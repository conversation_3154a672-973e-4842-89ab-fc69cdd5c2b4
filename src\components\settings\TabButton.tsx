import React from "react";

export type TabType = "geral" | "members" | "billing";

interface TabButtonProps {
	tab: TabType;
	label: string;
	activeTab: TabType;
	onClick: (tab: TabType) => void;
}

export const TabButton: React.FC<TabButtonProps> = ({
	tab,
	label,
	activeTab,
	onClick,
}) => (
	<button
		onClick={() => onClick(tab)}
		className={`px-4 py-2 text-sm font-medium transition-colors focus:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 border-b-2 ${
			activeTab === tab
				? "border-blue-600 text-blue-600"
				: "border-transparent text-muted-foreground hover:text-foreground"
		}`}
	>
		{label}
	</button>
);
