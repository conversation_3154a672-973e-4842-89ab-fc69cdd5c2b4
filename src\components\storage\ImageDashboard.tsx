"use client";

import React from "react";
import {
  BarChart3,
  HardDrive,
  Image,
  Tag,
  Trash2,
  RefreshCw,
} from "lucide-react";
import { useOrganizationImages } from "../../hooks/useOrganizationImages";

interface ImageDashboardProps {
  organizationId: string;
  onManageImages?: () => void;
  onUploadImages?: () => void;
}

export default function ImageDashboard({
  organizationId,
  onManageImages,
  onUploadImages,
}: ImageDashboardProps) {
  const {
    stats,
    loading,
    error,
    loadOrganizationStats,
    cleanupOrphanedImages,
    clearError,
  } = useOrganizationImages({
    organizationId,
    autoLoad: true,
  });

  const handleCleanup = async () => {
    if (
      confirm(
        "Tem certeza que deseja limpar imagens órfãs? Esta ação não pode ser desfeita."
      )
    ) {
      try {
        const result = await cleanupOrphanedImages();
        alert(`Limpeza concluída: ${result.details}`);
        loadOrganizationStats(); // Refresh stats
      } catch (err) {
        console.error("Cleanup failed:", err);
      }
    }
  };

  const formatStorageSize = (sizeMB: number): string => {
    if (sizeMB >= 1024) {
      return `${(sizeMB / 1024).toFixed(1)} GB`;
    }
    return `${sizeMB.toFixed(1)} MB`;
  };

  if (loading && !stats) {
    return (
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {[...Array(4)].map((_, i) => (
          <div
            key={i}
            className="bg-white dark:bg-gray-800 rounded-lg shadow p-6"
          >
            <div className="animate-pulse">
              <div className="w-8 h-8 bg-gray-200 dark:bg-gray-700 rounded mb-4"></div>
              <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded mb-2"></div>
              <div className="h-6 bg-gray-200 dark:bg-gray-700 rounded"></div>
            </div>
          </div>
        ))}
      </div>
    );
  }

  if (error) {
    return (
      <div className="bg-red-50 border border-red-200 rounded-lg p-4">
        <div className="flex items-center justify-between">
          <div>
            <h3 className="text-red-800 font-medium">
              Erro ao carregar estatísticas
            </h3>
            <p className="text-red-600 text-sm mt-1">{error}</p>
          </div>
          <button
            onClick={() => {
              clearError();
              loadOrganizationStats();
            }}
            className="text-red-600 hover:text-red-800"
          >
            <RefreshCw className="w-4 h-4" />
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Statistics Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {/* Total Images */}
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <Image className="h-8 w-8 text-blue-600" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-500 dark:text-gray-400">
                Total de Imagens
              </p>
              <p className="text-2xl font-semibold text-gray-900 dark:text-gray-100">
                {stats?.total_images?.toLocaleString() || 0}
              </p>
            </div>
          </div>
        </div>

        {/* Total Storage */}
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <HardDrive className="h-8 w-8 text-green-600" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-500 dark:text-gray-400">
                Armazenamento
              </p>
              <p className="text-2xl font-semibold text-gray-900 dark:text-gray-100">
                {formatStorageSize(stats?.total_size_mb || 0)}
              </p>
            </div>
          </div>
        </div>

        {/* Projects with Images */}
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <BarChart3 className="h-8 w-8 text-purple-600" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-500 dark:text-gray-400">
                Projetos com Imagens
              </p>
              <p className="text-2xl font-semibold text-gray-900 dark:text-gray-100">
                {stats?.images_by_project?.length || 0}
              </p>
            </div>
          </div>
        </div>

        {/* Most Used Tags */}
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <Tag className="h-8 w-8 text-orange-600" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-500 dark:text-gray-400">
                Tags Populares
              </p>
              <p className="text-2xl font-semibold text-gray-900 dark:text-gray-100">
                {stats?.most_used_tags?.length || 0}
              </p>
            </div>
          </div>
        </div>
      </div>

      {/* Projects Breakdown */}
      {stats?.images_by_project && stats.images_by_project.length > 0 && (
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow">
          <div className="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
            <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100">
              Imagens por Projeto
            </h3>
          </div>
          <div className="p-6">
            <div className="space-y-4">
              {stats.images_by_project.map((project, index) => (
                <div key={index} className="flex items-center justify-between">
                  <div className="flex-1">
                    <p className="text-sm font-medium text-gray-900 dark:text-gray-100">
                      {project.project_name}
                    </p>
                    <p className="text-xs text-gray-500 dark:text-gray-400">
                      {project.image_count} imagens •{" "}
                      {formatStorageSize(project.total_size_mb)}
                    </p>
                  </div>
                  <div className="ml-4 flex-shrink-0">
                    <div className="flex items-center">
                      <div className="w-24 bg-gray-200 dark:bg-gray-700 rounded-full h-2 mr-2">
                        <div
                          className="bg-blue-600 h-2 rounded-full"
                          style={{
                            width: `${Math.max(
                              5,
                              (project.image_count /
                                (stats.total_images || 1)) *
                                100
                            )}%`,
                          }}
                        ></div>
                      </div>
                      <span className="text-xs text-gray-500 dark:text-gray-400 w-8 text-right">
                        {Math.round(
                          (project.image_count / (stats.total_images || 1)) *
                            100
                        )}
                        %
                      </span>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      )}

      {/* Popular Tags */}
      {stats?.most_used_tags && stats.most_used_tags.length > 0 && (
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow">
          <div className="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
            <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100">
              Tags Mais Utilizadas
            </h3>
          </div>
          <div className="p-6">
            <div className="flex flex-wrap gap-2">
              {stats.most_used_tags.map((tag, index) => (
                <span
                  key={index}
                  className="inline-flex items-center px-3 py-1 rounded-full text-sm bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200"
                >
                  <Tag className="w-3 h-3 mr-1" />
                  {tag}
                </span>
              ))}
            </div>
          </div>
        </div>
      )}

      {/* Action Buttons */}
      <div className="flex flex-wrap gap-4">
        {onUploadImages && (
          <button
            onClick={onUploadImages}
            className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
          >
            Fazer Upload de Imagens
          </button>
        )}

        {onManageImages && (
          <button
            onClick={onManageImages}
            className="px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors"
          >
            Gerenciar Imagens
          </button>
        )}

        <button
          onClick={loadOrganizationStats}
          disabled={loading}
          className="px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors flex items-center space-x-2"
        >
          <RefreshCw className={`w-4 h-4 ${loading ? "animate-spin" : ""}`} />
          <span>Atualizar</span>
        </button>

        <button
          onClick={handleCleanup}
          className="px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors flex items-center space-x-2"
        >
          <Trash2 className="w-4 h-4" />
          <span>Limpar Órfãs</span>
        </button>
      </div>
    </div>
  );
}
