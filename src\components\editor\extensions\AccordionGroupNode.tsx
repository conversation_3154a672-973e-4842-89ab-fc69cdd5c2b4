import { Node } from "@tiptap/core";
import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>per, ReactNodeViewRenderer } from "@tiptap/react";

declare module "@tiptap/core" {
  interface Commands<ReturnType> {
    accordionGroup: {
      /**
       * Insert an accordion group with the specified attributes.
       */
      setAccordionGroup: (attributes: AccordionGroupAttrs) => ReturnType;
    };
  }
}
import React, { useState, useCallback } from "react";
import {
  Edit3,
  Save,
  X,
  ChevronDown,
  ChevronRight,
  Plus,
  Trash2,
} from "lucide-react";
import { Button } from "@/components/ui/button";
import { RichTextInput } from "@/components/ui/RichTextInput";
import { useToast } from "@/components/ToastProvider";
import type { NodeViewProps } from "@tiptap/react";
// Import TiptapEditor styles for code blocks
import "../TiptapEditor.css";
import { highlightCodeBlocks } from "../utils/highlightCodeBlocks";

// Interface for accordion data
interface AccordionData {
  id: string;
  title: string;
  description: string;
  isExpanded?: boolean;
}

// Interface for accordion group attributes
interface AccordionGroupAttrs {
  accordions: AccordionData[];
}

// Individual Accordion Item Component for Display
const AccordionDisplayItem: React.FC<{
  accordion: AccordionData;
  onToggle: () => void;
  isLast?: boolean;
}> = ({ accordion, onToggle, isLast = false }) => {
  return (
    <div
      className={`border border-gray-200 dark:border-gray-700 bg-white dark:bg-slate-800 shadow-sm hover:shadow-md transition-all duration-200 ${
        isLast ? "rounded-lg" : "rounded-t-lg border-b-0"
      } ${!isLast ? "first:rounded-t-lg" : ""}`}
    >
      {/* Accordion Header */}
      <div
        className="flex items-center justify-center px-4 py-3 cursor-pointer hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors duration-200"
        onClick={onToggle}
      >
        <p className="text-base !font-extrabold font-medium text-gray-900 dark:text-gray-100 flex-1">
          {accordion.title || "Accordion Title"}
        </p>

        <div className="text-gray-400 dark:text-gray-500 ml-2">
          {accordion.isExpanded ? (
            <ChevronDown className="w-4 h-4" />
          ) : (
            <ChevronRight className="w-4 h-4" />
          )}
        </div>
      </div>

      {/* Accordion Content */}
      {accordion.isExpanded && (
        <div className="px-4 pb-4 border-gray-200 dark:border-gray-600">
          <div className="pt-1">
            {accordion.description && (
              <div
                className="text-gray-600 dark:text-gray-400 leading-relaxed text-md prose-sm accordion-content-wrapper"
                dangerouslySetInnerHTML={{
                  __html: highlightCodeBlocks(accordion.description || ""),
                }}
              />
            )}
            {!accordion.description && (
              <p className="text-gray-600 dark:text-gray-400 leading-relaxed text-md">
                Accordion description
              </p>
            )}
          </div>
        </div>
      )}
    </div>
  );
};

// Individual Accordion Item Component for Editing
const AccordionEditItem: React.FC<{
  accordion: AccordionData;
  onToggle: () => void;
  onEdit: () => void;
  onDelete: () => void;
  onDragStart?: (e: React.DragEvent) => void;
  onDragOver?: (e: React.DragEvent) => void;
  onDrop?: (e: React.DragEvent) => void;
  isDragging?: boolean;
  isDragOver?: boolean;
  isLast?: boolean;
}> = ({
  accordion,
  onToggle,
  onEdit,
  onDelete,
  onDragStart,
  onDragOver,
  onDrop,
  isDragging = false,
  isDragOver = false,
  isLast = false,
}) => {
  return (
    <div className="relative">
      {/* Drop indicator - top */}
      {isDragOver && (
        <div className="absolute -top-2 left-0 right-0 h-1 bg-gradient-to-r from-transparent via-blue-500 to-transparent rounded-full z-10 opacity-80" />
      )}

      <div
        className={`
          border border-gray-200 dark:border-gray-700 bg-white dark:bg-slate-800 
          shadow-sm hover:shadow-md transition-all duration-200 relative group cursor-move
          ${isLast ? "rounded-lg" : "rounded-t-lg border-b-0"}
          ${!isLast ? "first:rounded-t-lg" : ""}
          ${
            isDragging
              ? "opacity-60 scale-98 shadow-lg bg-blue-50 dark:bg-blue-900/20 border-blue-300 dark:border-blue-600"
              : ""
          }
          ${
            isDragOver
              ? "bg-blue-50/50 dark:bg-blue-900/10 border-blue-300 dark:border-blue-600"
              : ""
          }
        `}
        draggable
        onDragStart={onDragStart}
        onDragOver={onDragOver}
        onDrop={onDrop}
      >
        {/* Edit and Delete buttons - only visible on hover */}
        <div className="absolute top-2 right-2 flex space-x-1 opacity-0 group-hover:opacity-100 transition-opacity duration-200 z-10">
          <Button
            variant="ghost"
            size="sm"
            onClick={(e) => {
              e.stopPropagation();
              onEdit();
            }}
            className="h-6 px-2 text-xs bg-white/80 dark:bg-slate-800/80 hover:bg-gray-100 dark:hover:bg-slate-700"
          >
            <Edit3 className="w-3 h-3" />
          </Button>
          <Button
            variant="ghost"
            size="sm"
            onClick={(e) => {
              e.stopPropagation();
              onDelete();
            }}
            className="h-6 px-2 text-xs bg-white/80 dark:bg-slate-800/80 hover:bg-red-100 dark:hover:bg-red-900/30 text-red-600 dark:text-red-400"
          >
            <Trash2 className="w-3 h-3" />
          </Button>
        </div>

        {/* Drag handle */}
        <div className="absolute top-3 left-2 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 transition-colors duration-200">
          <svg
            className="w-4 h-4"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M4 8h16M4 16h16"
            />
          </svg>
        </div>

        {/* Accordion Header */}
        <div
          className="flex items-center justify-center px-8 py-3 cursor-pointer hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors duration-200"
          onClick={onToggle}
        >
          <p className="text-base !font-extrabold font-medium text-gray-900 dark:text-gray-100 flex-1">
            {accordion.title || "Accordion Title"}
          </p>

          <div className="text-gray-400 dark:text-gray-500 ml-2">
            {accordion.isExpanded ? (
              <ChevronDown className="w-4 h-4" />
            ) : (
              <ChevronRight className="w-4 h-4" />
            )}
          </div>
        </div>

        {/* Accordion Content */}
        {accordion.isExpanded && (
          <div className="px-8 pb-4 border-gray-200 dark:border-gray-600">
            <div className="pt-1">
              {accordion.description && (
                <div
                  className="text-gray-600 dark:text-gray-400 leading-relaxed text-md prose-sm accordion-content-wrapper"
                  dangerouslySetInnerHTML={{
                    __html: highlightCodeBlocks(accordion.description || ""),
                  }}
                />
              )}
              {!accordion.description && (
                <p className="text-gray-600 dark:text-gray-400 leading-relaxed text-md">
                  Accordion description
                </p>
              )}
            </div>
          </div>
        )}
      </div>

      {/* Drop indicator - bottom */}
      {isDragOver && (
        <div className="absolute -bottom-2 left-0 right-0 h-1 bg-gradient-to-r from-transparent via-blue-500 to-transparent rounded-full z-10 opacity-80" />
      )}
    </div>
  );
};

// Accordion Edit Form Component
const AccordionEditForm: React.FC<{
  accordion: AccordionData;
  onSave: (accordion: AccordionData) => void;
  onCancel: () => void;
}> = ({ accordion, onSave, onCancel }) => {
  const [formData, setFormData] = useState<AccordionData>(accordion);
  const { addToast } = useToast();

  const handleInputChange = useCallback(
    (field: keyof AccordionData, value: string) => {
      setFormData((prev) => ({ ...prev, [field]: value }));
    },
    []
  );

  const handleSave = useCallback(() => {
    if (!formData.title.trim()) {
      addToast("Title is required", "warning");
      return;
    }

    // Check if description has content (including HTML)
    const descriptionText = formData.description
      .replace(/<[^>]*>/g, "") // Remove HTML tags
      .trim();

    if (!descriptionText) {
      addToast("Description is required", "warning");
      return;
    }

    // Create a clean copy of formData to avoid mutation issues
    const cleanFormData = {
      ...formData,
      // Ensure description is properly formatted
      description: formData.description || "<p></p>",
    };

    onSave(cleanFormData);
  }, [formData, onSave, addToast]);

  return (
    <div className="border border-gray-200 dark:border-gray-700 rounded-lg p-4 bg-white dark:bg-gray-800">
      <div className="flex items-center justify-between mb-4">
        <h4 className="text-sm font-semibold text-gray-900 dark:text-gray-100">
          Edit Accordion
        </h4>
        <div className="flex items-center space-x-2">
          <Button
            variant="ghost"
            size="sm"
            onClick={onCancel}
            className="h-6 px-2 text-xs hover:bg-red-100/50 dark:hover:bg-red-900/30 text-red-600 dark:text-red-400"
          >
            <X className="w-3 h-3 mr-1" />
            Cancel
          </Button>
          <Button
            variant="default"
            size="sm"
            onClick={handleSave}
            className="h-6 px-2 text-xs bg-blue-600 hover:bg-blue-700 text-white"
          >
            <Save className="w-3 h-3 mr-1" />
            Save
          </Button>
        </div>
      </div>

      <div className="space-y-4">
        <div>
          <label className="block text-xs font-medium text-gray-700 dark:text-gray-300 mb-1">
            Title *
          </label>
          <input
            type="text"
            value={formData.title}
            onChange={(e) => handleInputChange("title", e.target.value)}
            placeholder="Enter accordion title"
            className="w-full px-3 py-2 text-sm bg-white dark:bg-slate-700 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:text-white"
            required
          />
        </div>

        <div>
          <label className="block text-xs font-medium text-gray-700 dark:text-gray-300 mb-1">
            Description *
          </label>
          <RichTextInput
            value={formData.description}
            onChange={(html) => handleInputChange("description", html)}
            placeholder="Enter accordion description"
            variant="default"
            className="w-full"
            enableCodeBlock={true}
            enableLink={true}
          />
        </div>
      </div>
    </div>
  );
};

// Accordion Group Component
const AccordionGroupComponent: React.FC<NodeViewProps> = ({
  node,
  updateAttributes,
  selected,
}) => {
  const [isEditing, setIsEditing] = useState(false);
  const [editingAccordionId, setEditingAccordionId] = useState<string | null>(
    null
  );
  const [accordions, setAccordions] = useState<AccordionData[]>(() => {
    const nodeAccordions = node.attrs.accordions;
    return Array.isArray(nodeAccordions) ? nodeAccordions : [];
  });
  const [draggedIndex, setDraggedIndex] = useState<number | null>(null);
  const [dragOverIndex, setDragOverIndex] = useState<number | null>(null);
  const { addToast } = useToast();

  const handleSave = useCallback(() => {
    updateAttributes({ accordions });
    setIsEditing(false);
    setEditingAccordionId(null);
    addToast("Accordion group updated successfully", "success", "Success");
  }, [accordions, updateAttributes, addToast]);

  const handleCancel = useCallback(() => {
    const nodeAccordions = node.attrs.accordions;
    setAccordions(Array.isArray(nodeAccordions) ? nodeAccordions : []);
    setIsEditing(false);
    setEditingAccordionId(null);
  }, [node.attrs.accordions]);

  const addAccordion = useCallback(() => {
    const newAccordion: AccordionData = {
      id: Date.now().toString(),
      title: "New Accordion",
      description: "<p>Description for the new accordion</p>",
      isExpanded: false,
    };
    setAccordions((prev) => [...prev, newAccordion]);
    setEditingAccordionId(newAccordion.id);
    addToast("New accordion added", "success", "Accordion Added");
  }, [addToast]);

  const deleteAccordion = useCallback(
    (id: string) => {
      setAccordions((prev) => prev.filter((accordion) => accordion.id !== id));
      addToast("Accordion deleted successfully", "info", "Accordion Removed");
    },
    [addToast]
  );

  const toggleAccordion = useCallback((id: string) => {
    setAccordions((prev) =>
      prev.map((accordion) =>
        accordion.id === id
          ? { ...accordion, isExpanded: !accordion.isExpanded }
          : accordion
      )
    );
  }, []);

  const editAccordion = useCallback((updatedAccordion: AccordionData) => {
    setAccordions((prev) =>
      prev.map((accordion) =>
        accordion.id === updatedAccordion.id ? updatedAccordion : accordion
      )
    );
    setEditingAccordionId(null);
  }, []);

  const handleDragStart = useCallback((index: number) => {
    return (e: React.DragEvent) => {
      setDraggedIndex(index);
      e.dataTransfer.effectAllowed = "move";
      e.dataTransfer.setData("text/plain", index.toString());
    };
  }, []);

  const handleDragOver = useCallback((targetIndex: number) => {
    return (e: React.DragEvent) => {
      e.preventDefault();
      e.dataTransfer.dropEffect = "move";
      setDragOverIndex(targetIndex);
    };
  }, []);

  const handleDragLeave = useCallback(() => {
    setDragOverIndex(null);
  }, []);

  const handleDrop = useCallback(
    (targetIndex: number) => {
      return (e: React.DragEvent) => {
        e.preventDefault();
        setDragOverIndex(null);

        if (draggedIndex === null || draggedIndex === targetIndex) {
          setDraggedIndex(null);
          return;
        }

        const newAccordions = [...accordions];
        const draggedAccordion = newAccordions[draggedIndex];
        newAccordions.splice(draggedIndex, 1);
        newAccordions.splice(targetIndex, 0, draggedAccordion);

        setAccordions(newAccordions);
        setDraggedIndex(null);
      };
    },
    [accordions, draggedIndex]
  );

  if (isEditing) {
    return (
      <NodeViewWrapper
        className="accordion-group-node"
        as="div"
        data-drag-handle=""
        contentEditable={false}
      >
        <div className="my-4 p-4 bg-white dark:bg-slate-800 border-2 border-blue-400/50 dark:border-blue-600/50 rounded-lg shadow-lg">
          <div className="flex items-center justify-between mb-4">
            <h4 className="text-sm font-semibold text-gray-900 dark:text-gray-100">
              Edit Accordion Group
            </h4>
            <div className="flex items-center space-x-2">
              <Button
                onClick={addAccordion}
                size="sm"
                className="h-6 px-2 text-xs bg-green-600 hover:bg-green-700 text-white"
              >
                <Plus className="w-3 h-3 mr-1" />
                Add Accordion
              </Button>
              <Button
                variant="ghost"
                size="sm"
                onClick={handleCancel}
                className="h-6 px-2 text-xs hover:bg-red-100/50 dark:hover:bg-red-900/30 text-red-600 dark:text-red-400"
              >
                <X className="w-3 h-3 mr-1" />
                Cancel
              </Button>
              <Button
                variant="default"
                size="sm"
                onClick={handleSave}
                className="h-6 px-2 text-xs bg-blue-600 hover:bg-blue-700 text-white"
              >
                <Save className="w-3 h-3 mr-1" />
                Save
              </Button>
            </div>
          </div>

          <div className="space-y-4">
            {accordions.map((accordion, index) => (
              <div key={accordion.id}>
                {editingAccordionId === accordion.id ? (
                  <AccordionEditForm
                    accordion={accordion}
                    onSave={editAccordion}
                    onCancel={() => setEditingAccordionId(null)}
                  />
                ) : (
                  <AccordionEditItem
                    accordion={accordion}
                    onToggle={() => toggleAccordion(accordion.id)}
                    onEdit={() => setEditingAccordionId(accordion.id)}
                    onDelete={() => deleteAccordion(accordion.id)}
                    onDragStart={handleDragStart(index)}
                    onDragOver={handleDragOver(index)}
                    onDrop={handleDrop(index)}
                    isDragging={draggedIndex === index}
                    isDragOver={
                      dragOverIndex === index &&
                      draggedIndex !== null &&
                      draggedIndex !== index
                    }
                    isLast={index === accordions.length - 1}
                  />
                )}
              </div>
            ))}

            {/* Drop zone at the end of the list */}
            {draggedIndex !== null && (
              <div
                className="h-12 flex items-center justify-center border-2 border-dashed border-blue-300 dark:border-blue-500 rounded-lg bg-blue-50/50 dark:bg-blue-900/10 transition-all duration-200 hover:bg-blue-100/50 dark:hover:bg-blue-900/20"
                onDragOver={(e) => {
                  e.preventDefault();
                  setDragOverIndex(accordions.length);
                }}
                onDragLeave={handleDragLeave}
                onDrop={(e) => {
                  e.preventDefault();
                  setDragOverIndex(null);

                  if (draggedIndex === null) return;

                  const newAccordions = [...accordions];
                  const draggedAccordion = newAccordions[draggedIndex];
                  newAccordions.splice(draggedIndex, 1);
                  newAccordions.push(draggedAccordion);

                  setAccordions(newAccordions);
                  setDraggedIndex(null);
                }}
              >
                <div className="flex items-center space-x-2 text-blue-600 dark:text-blue-400">
                  <svg
                    className="w-4 h-4"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M19 14l-7 7m0 0l-7-7m7 7V3"
                    />
                  </svg>
                  <span className="text-sm font-medium">
                    Drop here to move to end
                  </span>
                </div>
              </div>
            )}

            {accordions.length === 0 && (
              <div className="text-center py-8 text-gray-500 dark:text-gray-400">
                No accordions yet. Click &quot;Add Accordion&quot; to get
                started.
              </div>
            )}
          </div>
        </div>
      </NodeViewWrapper>
    );
  }

  return (
    <NodeViewWrapper className="accordion-group-node border-dashed border-2 dark:border-blue-600/50 rounded-lg hover:border-blue-400/50 dark:hover:border-blue-600/50">
      <div
        className={`m-2 relative group ${
          selected ? "ring-2 ring-blue-400/50 rounded-lg" : ""
        }`}
      >
        <Button
          variant="ghost"
          size="sm"
          onClick={(e) => {
            e.stopPropagation();
            setIsEditing(true);
          }}
          className="absolute -top-2 -right-2 h-6 px-2 text-xs opacity-0 group-hover:opacity-100 transition-opacity duration-200 bg-white/80 dark:bg-slate-800/80 hover:bg-gray-100 dark:hover:bg-slate-700 z-50"
        >
          <Edit3 className="w-3 h-3 mr-1" />
          Edit
        </Button>

        {/* Accordions List - no gaps, connected */}
        {/* remove conners but not from the first and last accordion */}
        <div className="accordion-group space-y-0">
          {accordions.map((accordion, index) => (
            <AccordionDisplayItem
              key={accordion.id}
              accordion={accordion}
              onToggle={() => toggleAccordion(accordion.id)}
              isLast={index === accordions.length - 1}
            />
          ))}

          {accordions.length === 0 && (
            <div className="border border-gray-200 dark:border-gray-700 rounded-lg p-8 bg-white dark:bg-slate-800 text-center">
              <p className="text-gray-500 dark:text-gray-400 mb-4">
                No accordions in this group. Edit to add accordions.
              </p>
            </div>
          )}
        </div>
      </div>
    </NodeViewWrapper>
  );
};

// Accordion Group Node Extension
export const AccordionGroupNode = Node.create({
  name: "accordionGroup",
  group: "block",
  content: "",
  atom: true,
  draggable: false,

  addAttributes() {
    return {
      accordions: {
        default: [],
        parseHTML: (element) => {
          try {
            // Try to get from data-accordions attribute first
            const accordionsData =
              element.getAttribute("data-accordions") ||
              element.getAttribute("accordions");
            if (accordionsData) {
              return JSON.parse(accordionsData);
            }

            // Extract from child Accordion elements
            const accordionElements = element.querySelectorAll(
              "Accordion, accordion, div[data-type='accordion']"
            );
            return Array.from(accordionElements).map((accordionEl, index) => ({
              id: Date.now().toString() + index,
              title:
                accordionEl.getAttribute("title") ||
                accordionEl.getAttribute("data-title") ||
                "",
              description:
                accordionEl.getAttribute("description") ||
                accordionEl.getAttribute("data-description") ||
                accordionEl.textContent ||
                accordionEl.innerHTML ||
                "",
              isExpanded: false,
            }));
          } catch {
            return [];
          }
        },
        renderHTML: (attributes) => {
          if (!attributes.accordions || !Array.isArray(attributes.accordions)) {
            return { "data-accordions": "[]" };
          }
          return { "data-accordions": JSON.stringify(attributes.accordions) };
        },
      },
    };
  },

  parseHTML() {
    return [
      {
        tag: "AccordionGroup",
      },
      {
        tag: 'div[data-type="accordion-group"]',
      },
    ];
  },

  renderHTML({ HTMLAttributes }) {
    // Get accordions from the node attributes - check both locations
    const accordions =
      HTMLAttributes.accordions || HTMLAttributes["data-accordions"] || [];
    console.log("🔧 AccordionGroup renderHTML called with:", HTMLAttributes);

    // Ensure accordions is always an array
    let accordionsList = [];
    if (typeof accordions === "string") {
      try {
        accordionsList = JSON.parse(accordions);
      } catch {
        accordionsList = [];
      }
    } else if (Array.isArray(accordions)) {
      accordionsList = accordions;
    }

    console.log("🔧 AccordionGroup final accordionsList:", accordionsList);

    // Generate the children Accordion elements as div elements with data-type
    const accordionElements = accordionsList.map((accordion: AccordionData) => {
      return [
        "div",
        {
          "data-type": "accordion",
          title: accordion.title,
          description: accordion.description || "",
        },
        "", // Empty content since description is in the attribute
      ];
    });

    return [
      "div",
      {
        "data-type": "accordion-group",
        "data-accordions": JSON.stringify(accordionsList), // Include the data for debugging
        class: "accordion-group-node",
      },
      ...accordionElements,
    ];
  },

  addNodeView() {
    return ReactNodeViewRenderer(AccordionGroupComponent);
  },

  addCommands() {
    return {
      setAccordionGroup:
        (attributes: AccordionGroupAttrs) =>
        ({ commands }) => {
          return commands.insertContent({
            type: this.name,
            attrs: attributes,
          });
        },
    };
  },
});

export default AccordionGroupNode;
