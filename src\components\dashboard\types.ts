export interface WebAnalyticsSite {
	site_tag: string;
	site_token: string;
	host: string;
	created: string;
	auto_install: boolean;
	snippet?: string;
	is_host_regex?: boolean;
}

export interface CloudflareProject {
	type?: string;
	project?: unknown;
	success?: boolean;
	data?: unknown;
}

export interface AnalyticsData {
	dashboard?: {
		timeseries?: Array<unknown>;
		totals?: {
			requests?: number;
			bandwidth?: number;
			uniques?: number;
			pageViews?: number;
			threats?: number;
		};
	};
	firewall?: Array<unknown>;
	zone?: unknown;
	zoneId?: string;
	zoneName?: string;
	siteTag?: string;
	siteName?: string;
	projectName?: string;
	timeRange?: {
		since: string;
		until: string;
	};
	apiVersion?: string;
	debug?: {
		availableSites?: WebAnalyticsSite[];
		searchedKeywords?: string[];
		message?: string;
		suggestion?: string;
	};
}

export interface WebAnalyticsTopNsResponse {
	success: boolean;
	siteTag: string;
	dateRange: { since: string; until: string };
	data: {
		total: Array<unknown>;
		countries: Array<{
			count: number;
			sum: { visits: number };
			dimensions: { metric: string };
		}>;
		topReferers: Array<{
			count: number;
			sum: { visits: number };
			dimensions: { metric: string };
		}>;
		topPaths: Array<{
			count: number;
			sum: { visits: number };
			dimensions: { metric: string };
		}>;
		topHosts: Array<{
			count: number;
			sum: { visits: number };
			dimensions: { metric: string };
		}>;
		topBrowsers: Array<{
			count: number;
			sum: { visits: number };
			dimensions: { metric: string };
		}>;
		topOSs: Array<{
			count: number;
			sum: { visits: number };
			dimensions: { metric: string };
		}>;
		topDeviceTypes: Array<{
			count: number;
			sum: { visits: number };
			dimensions: { metric: string };
		}>;
	};
}

export interface WebAnalyticsSparklineResponse {
	success: boolean;
	siteTag: string;
	dateRange: { since: string; until: string };
	data: {
		visits?: Array<{
			sum: { visits: number };
			dimensions: { ts: string };
		}>;
		pageviews?: Array<{
			count: number;
			dimensions: { ts: string };
		}>;
	};
}

export interface WebAnalyticsTimeseriesResponse {
	success: boolean;
	siteTag: string;
	dateRange: { since: string; until: string };
	groupBy?: string;
	data: {
		series?: Array<{
			count: number;
			sum: { visits: number };
			dimensions: { ts: string; metric: string };
		}>;
	};
}
