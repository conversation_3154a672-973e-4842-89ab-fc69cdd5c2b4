'use client';

import React, { useState } from 'react';
import { NodeViewWrapper, NodeViewContent } from '@tiptap/react';
import type { NodeViewProps } from '@tiptap/react';
import { Search } from 'lucide-react';

interface TableWrapperProps extends NodeViewProps {
	selected: boolean;
}

export const TableWrapper: React.FC<TableWrapperProps> = ({ node }) => {
	const hasSearchbar = node.attrs.searchbar;
	const [searchTerm, setSearchTerm] = useState('');

	return (
		<NodeViewWrapper className='table-wrapper relative'>
			{/* Search Input - appears above table when searchbar is enabled */}
			{hasSearchbar && (
				<div className='mb-3'>
					<div className='relative'>
						<input
							type='text'
							placeholder='Search table content...'
							value={searchTerm}
							onChange={(e) => setSearchTerm(e.target.value)}
							className='w-full pl-10 pr-4 py-2.5 border border-blue-300 dark:border-blue-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100 placeholder-gray-500 dark:placeholder-gray-400 shadow-sm'
						/>
						<Search className='absolute left-3.5 transform translate-y-[22px] w-4 h-4 text-blue-500 pointer-events-none' />
					</div>
				</div>
			)}

			{/* Table Content */}
			<div className='group'>
				<NodeViewContent
					as='table'
					className={`w-full border-collapse ${hasSearchbar ? 'border-2 border-blue-300 dark:border-blue-600' : 'border border-gray-300 dark:border-gray-600'}`}
					data-searchbar={hasSearchbar ? 'true' : undefined}
				/>
			</div>
		</NodeViewWrapper>
	);
};

export default TableWrapper;
