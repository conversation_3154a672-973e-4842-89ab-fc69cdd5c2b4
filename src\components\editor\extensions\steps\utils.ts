import { StepData, FlatStep } from "./types";

export const generateDefaultSteps = (): StepData[] => {
  const now = Date.now();
  return [
    {
      id: `${now}-1`,
      title: "First Step",
      content: "<p>First step content</p>",
      titleSize: "h3",
      subSteps: [],
    },
    {
      id: `${now}-2`,
      title: "Second Step",
      content: "<p>Second step content</p>",
      titleSize: "h3",
      subSteps: [],
    },
  ];
};

export const getTitleSizeForLevel = (
  level: number
): "h2" | "h3" | "h4" | "h5" | "h6" => {
  const sizes: ("h2" | "h3" | "h4" | "h5" | "h6")[] = [
    "h2",
    "h3",
    "h4",
    "h5",
    "h6",
  ];
  const index = Math.min(level + 1, sizes.length - 1);
  return sizes[index];
};

export const flattenSteps = (
  steps: StepData[],
  level: number = 0,
  parentId?: string
): FlatStep[] => {
  const result: FlatStep[] = [];

  if (!Array.isArray(steps)) {
    console.warn("flattenSteps received non-array:", steps);
    return result;
  }

  steps.forEach((step) => {
    if (!step || !step.id) {
      console.warn("Invalid step in flattenSteps:", step);
      return;
    }

    result.push({
      id: step.id,
      title: step.title || "",
      content: step.content || "",
      titleSize: step.titleSize || "h3",
      subSteps: step.subSteps || [],
      level,
      parentId,
    });

    if (
      step.subSteps &&
      Array.isArray(step.subSteps) &&
      step.subSteps.length > 0
    ) {
      result.push(...flattenSteps(step.subSteps, level + 1, step.id));
    }
  });

  return result;
};

export const reconstructSteps = (flatSteps: FlatStep[]): StepData[] => {
  if (!Array.isArray(flatSteps)) {
    console.warn("reconstructSteps received non-array:", flatSteps);
    return [];
  }

  const validSteps = flatSteps.filter(
    (step) => step && step.id && typeof step.level === "number"
  );

  const stepMap = new Map<string, StepData>();
  const rootSteps: StepData[] = [];

  const processedSteps = validSteps.map((flatStep) => {
    const finalLevel = flatStep.level || 0;

    const step: StepData = {
      id: flatStep.id,
      title: flatStep.title || "",
      content: flatStep.content || "",
      titleSize: getTitleSizeForLevel(finalLevel),
      subSteps: [],
    };

    stepMap.set(flatStep.id, step);

    return {
      ...flatStep,
      level: finalLevel,
      step: step,
    };
  });

  const parentStack: { step: StepData; level: number }[] = [];

  processedSteps.forEach(({ step, level }) => {
    while (
      parentStack.length > 0 &&
      parentStack[parentStack.length - 1].level >= level
    ) {
      parentStack.pop();
    }

    if (parentStack.length === 0 || level === 0) {
      rootSteps.push(step);
    } else {
      const parent = parentStack[parentStack.length - 1].step;
      parent.subSteps = parent.subSteps || [];
      parent.subSteps.push(step);
    }

    if (level < 4) {
      parentStack.push({ step, level });
    }
  });

  return rootSteps;
};

export const getDescendantIds = (
  step: FlatStep,
  allSteps: FlatStep[]
): string[] => {
  const descendants: string[] = [];
  const currentIndex = allSteps.findIndex((s) => s.id === step.id);

  if (currentIndex === -1) return descendants;

  for (let i = currentIndex + 1; i < allSteps.length; i++) {
    const nextStep = allSteps[i];
    if (nextStep.level > step.level) {
      descendants.push(nextStep.id);
    } else {
      break;
    }
  }

  return descendants;
};

export const moveStepWithDescendants = (
  flatSteps: FlatStep[],
  draggedIndex: number,
  targetIndex: number,
  forceRootLevel: boolean = false,
  targetParent?: FlatStep | null
): FlatStep[] => {
  const draggedStep = flatSteps[draggedIndex];
  const descendantIds = getDescendantIds(draggedStep, flatSteps);

  const stepsToMove = flatSteps.filter(
    (step) => step.id === draggedStep.id || descendantIds.includes(step.id)
  );

  const remainingSteps = flatSteps.filter(
    (step) => step.id !== draggedStep.id && !descendantIds.includes(step.id)
  );

  let insertIndex = targetIndex;
  const removedBeforeTarget = flatSteps
    .slice(0, targetIndex)
    .filter(
      (step) => step.id === draggedStep.id || descendantIds.includes(step.id)
    ).length;

  insertIndex = Math.max(0, targetIndex - removedBeforeTarget);
  insertIndex = Math.min(insertIndex, remainingSteps.length);

  let newLevel = 0;

  if (forceRootLevel) {
    newLevel = 0;
  } else if (targetParent) {
    if (targetParent.level < 4) {
      newLevel = targetParent.level + 1;
    } else {
      newLevel = targetParent.level;
    }
  } else {
    if (insertIndex > 0) {
      const stepBefore = remainingSteps[insertIndex - 1];
      if (stepBefore && stepBefore.level < 4) {
        newLevel = stepBefore.level + 1;
      } else if (stepBefore) {
        newLevel = stepBefore.level;
      }
    } else if (insertIndex < remainingSteps.length) {
      const stepAfter = remainingSteps[insertIndex];
      newLevel = stepAfter ? stepAfter.level : 0;
    } else {
      newLevel = 0;
    }
  }

  const levelDifference = newLevel - draggedStep.level;
  const adjustedStepsToMove = stepsToMove.map((step) => {
    const adjustedLevel = Math.max(
      0,
      Math.min(4, step.level + levelDifference)
    );
    return {
      ...step,
      level: adjustedLevel,
      titleSize: getTitleSizeForLevel(adjustedLevel),
    };
  });

  const result = [
    ...remainingSteps.slice(0, insertIndex),
    ...adjustedStepsToMove,
    ...remainingSteps.slice(insertIndex),
  ];

  return result;
};

export const moveStepToEndAsRoot = (
  flatSteps: FlatStep[],
  draggedIndex: number
): FlatStep[] => {
  return moveStepWithDescendants(
    flatSteps,
    draggedIndex,
    flatSteps.length,
    true,
    null
  );
};

export const getStepNumberClass = (titleSize: string) => {
  const baseClass =
    "step-number flex-shrink-0 rounded-md flex items-center justify-center font-bold text-black relative z-10";
  const sizeClasses = {
    h2: "w-10 h-10 text-base",
    h3: "w-8 h-8 text-sm",
    h4: "w-7 h-7 text-xs",
    h5: "w-6 h-6 text-xs",
    h6: "w-5 h-5 text-xs",
  };
  const bgClass = "bg-wd-blue-gray";
  return `${baseClass} ${
    sizeClasses[titleSize as keyof typeof sizeClasses] || sizeClasses.h3
  } ${bgClass}`;
};

export const getLinePosition = (titleSize: string) => {
  const sizeMap = {
    h2: 19,
    h3: 15,
    h4: 13,
    h5: 11,
    h6: 9,
  };
  return sizeMap[titleSize as keyof typeof sizeMap] || 15;
};

export const getStepTitleClass = (titleSize: string) => {
  const baseClass =
    "step-title font-bold text-gray-900 dark:text-gray-100 mb-2";
  const sizeClasses = {
    h2: "text-2xl",
    h3: "text-xl",
    h4: "text-lg",
    h5: "text-base",
    h6: "text-sm",
  };
  return `${baseClass} ${
    sizeClasses[titleSize as keyof typeof sizeClasses] || sizeClasses.h3
  }`;
};
