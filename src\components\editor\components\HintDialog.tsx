import React, { useState } from "react";
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  Di<PERSON><PERSON>ooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";

interface HintDialogProps {
  isOpen: boolean;
  onClose: () => void;
  onConfirm: (text: string, hint: string) => void;
  initialText?: string;
}

export const HintDialog: React.FC<HintDialogProps> = ({
  isOpen,
  onClose,
  onConfirm,
  initialText = "",
}) => {
  const [text, setText] = useState(initialText);
  const [hint, setHint] = useState("");

  // Update text when initialText changes (when dialog opens with selected text)
  React.useEffect(() => {
    setText(initialText);
  }, [initialText]);

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (text.trim() && hint.trim()) {
      onConfirm(text.trim(), hint.trim());
      handleClose();
    }
  };

  const handleClose = () => {
    setText("");
    setHint("");
    onClose();
  };

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle>Add Hint</DialogTitle>
          <DialogDescription>
            Enter the text and hint that will be displayed on hover.
          </DialogDescription>
        </DialogHeader>
        <form onSubmit={handleSubmit}>
          <div className="grid gap-4 py-4">
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="text" className="text-right">
                Text
              </Label>
              <Input
                id="text"
                placeholder="Text to display"
                value={text}
                onChange={(e) => setText(e.target.value)}
                className="col-span-3"
                autoFocus
                required
              />
            </div>
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="hint" className="text-right">
                Hint
              </Label>
              <Input
                id="hint"
                placeholder="Hint text to show on hover"
                value={hint}
                onChange={(e) => setHint(e.target.value)}
                className="col-span-3"
                required
              />
            </div>
          </div>
          <DialogFooter>
            <Button type="button" variant="outline" onClick={handleClose}>
              Cancel
            </Button>
            <Button type="submit" disabled={!text.trim() || !hint.trim()}>
              Add Hint
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  );
};

export default HintDialog;
