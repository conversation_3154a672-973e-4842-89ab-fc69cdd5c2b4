'use client';

import { useState, useCallback } from 'react';

import { useToast } from '@/components/ToastProvider';
import { type OrganizationImage } from '@/hooks/useOrganizationImages';

interface UseImageDeletionProps {
	onImagesDeleted?: (deletedImageIds: string[]) => void;
}

// Toast IDs for consistent messaging
const TOAST_IDS = {
	DELETE_SUCCESS: 'image-delete-success',
	DELETE_ERROR: 'image-delete-error',
};

export const useImageDeletion = ({
	onImagesDeleted,
}: UseImageDeletionProps = {}) => {
	const [isDeleting, setIsDeleting] = useState(false);
	const { addToast } = useToast();

	// Helper function to create user-friendly error messages
	const createUserFriendlyErrorMessage = useCallback(
		(error: string): string => {
			// Database/technical errors
			if (
				error.includes('Database error') ||
				error.includes('constraint') ||
				error.includes('foreign key')
			) {
				return 'Unable to delete image. It may be in use elsewhere.';
			}

			// Network/connection errors
			if (
				error.includes('fetch') ||
				error.includes('Network') ||
				error.includes('connection')
			) {
				return 'Connection problem. Please check your internet and try again.';
			}

			// Authentication errors
			if (
				error.includes('authentication') ||
				error.includes('unauthorized') ||
				error.includes('session')
			) {
				return 'Session expired. Please refresh the page and try again.';
			}

			// Permission errors
			if (
				error.includes('permission') ||
				error.includes('access denied') ||
				error.includes('forbidden')
			) {
				return "You don't have permission to delete this image.";
			}

			// Generic server errors
			if (error.includes('500') || error.includes('Internal Server Error')) {
				return 'Server temporarily unavailable. Please try again in a moment.';
			}

			// If error doesn't expose sensitive info, show it
			const sensitiveKeywords = [
				'database',
				'sql',
				'query',
				'table',
				'column',
				'constraint',
				'key',
				'index',
			];
			const lowerError = error.toLowerCase();
			const hasSensitiveInfo = sensitiveKeywords.some((keyword) =>
				lowerError.includes(keyword)
			);

			if (!hasSensitiveInfo && error.length < 100) {
				return error;
			}

			// Default fallback
			return 'Unable to delete image. Please try again.';
		},
		[]
	);

	// Mark single image for deletion (immediate UI feedback)
	const deleteSingleImage = useCallback(
		async (image: OrganizationImage): Promise<boolean> => {
			try {
				setIsDeleting(true);

				// Use server-side API for deletion
				const response = await fetch(`/api/images/${image.id}`, {
					method: 'DELETE',
				});

				if (!response.ok) {
					const errorData = (await response.json()) as { error?: string };
					throw new Error(errorData.error || 'Failed to delete image');
				}

				// Show immediate feedback
				addToast(
					`"${image.image_name}" is being deleted...`,
					'info',
					'Deleting Image',
					TOAST_IDS.DELETE_SUCCESS
				);

				// Notify parent component immediately
				onImagesDeleted?.([image.id]);

				return true;
			} catch (error) {
				const rawError =
					error instanceof Error ? error.message : 'Unknown error';
				const userFriendlyMessage = createUserFriendlyErrorMessage(rawError);

				addToast(
					userFriendlyMessage,
					'error',
					'Delete Failed',
					TOAST_IDS.DELETE_ERROR
				);

				return false;
			} finally {
				setIsDeleting(false);
			}
		},
		[addToast, createUserFriendlyErrorMessage, onImagesDeleted]
	);

	// Mark multiple images for deletion (immediate UI feedback)
	const deleteMultipleImages = useCallback(
		async (
			images: OrganizationImage[]
		): Promise<{
			success: boolean;
			deletedIds: string[];
			failedCount: number;
		}> => {
			if (images.length === 0) {
				return { success: true, deletedIds: [], failedCount: 0 };
			}

			try {
				setIsDeleting(true);

				// Use server-side API for bulk deletion
				const imageIds = images.map((img) => img.id);
				const response = await fetch('/api/images/bulk-delete', {
					method: 'POST',
					headers: {
						'Content-Type': 'application/json',
					},
					body: JSON.stringify({ imageIds }),
				});

				if (!response.ok) {
					const errorData = (await response.json()) as { error?: string };
					throw new Error(errorData.error || 'Failed to delete images');
				}

				// Show immediate feedback
				const message =
					images.length === 1
						? `1 image is being deleted...`
						: `${images.length} images are being deleted...`;

				addToast(message, 'info', 'Deleting Images', TOAST_IDS.DELETE_SUCCESS);

				// Notify parent component immediately
				onImagesDeleted?.(imageIds);

				return {
					success: true,
					deletedIds: imageIds,
					failedCount: 0,
				};
			} catch (error) {
				const rawError =
					error instanceof Error ? error.message : 'Unknown error';
				const userFriendlyMessage = createUserFriendlyErrorMessage(rawError);

				addToast(
					userFriendlyMessage,
					'error',
					'Delete Failed',
					TOAST_IDS.DELETE_ERROR
				);

				return {
					success: false,
					deletedIds: [],
					failedCount: images.length,
				};
			} finally {
				setIsDeleting(false);
			}
		},
		[addToast, createUserFriendlyErrorMessage, onImagesDeleted]
	);

	return {
		isDeleting,
		deleteSingleImage,
		deleteMultipleImages,
	};
};
