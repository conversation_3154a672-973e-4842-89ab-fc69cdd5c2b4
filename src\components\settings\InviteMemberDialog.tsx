import React, { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
	Dialog,
	DialogContent,
	DialogHeader,
	DialogTitle,
	DialogFooter,
} from "@/components/ui/dialog";
// import {
// 	Select,
// 	SelectContent,
// 	SelectItem,
// 	SelectTrigger,
// 	SelectValue,
// } from "@/components/ui/select";
// import { Crown, Shield, Edit } from "lucide-react";
import type { MemberRole } from "@/types/members";

interface InviteMemberDialogProps {
	isOpen: boolean;
	onOpenChange: (open: boolean) => void;
	onInvite: (email: string, role: MemberRole) => Promise<boolean>;
	currentUserRole: MemberRole | null;
}

export const InviteMemberDialog: React.FC<InviteMemberDialogProps> = ({
	isOpen,
	onOpenChange,
	onInvite,
	// currentUserRole,
}) => {
	const [email, setEmail] = useState("");
	const [role, setRole] = useState<MemberRole>("Editor");
	const [isInviting, setIsInviting] = useState(false);

	const handleInvite = async () => {
		if (!email.trim()) return;

		setIsInviting(true);
		const success = await onInvite(email, role);
		setIsInviting(false);

		if (success) {
			handleClose();
		}
	};

	const handleClose = () => {
		onOpenChange(false);
		setEmail("");
		setRole("Editor");
	};

	return (
		<Dialog open={isOpen} onOpenChange={onOpenChange}>
			<DialogContent className='sm:max-w-md'>
				<DialogHeader>
					<DialogTitle>Invite New Member</DialogTitle>
				</DialogHeader>
				<div className='space-y-4 py-4'>
					<div className='space-y-2'>
						<label htmlFor='member-email' className='text-sm font-medium'>
							Email address
						</label>
						<Input
							id='member-email'
							type='email'
							value={email}
							onChange={(e) => setEmail(e.target.value)}
							placeholder='<EMAIL>'
							className='w-full'
						/>
					</div>
					{/* <div className='space-y-2'>
						<label htmlFor='member-role' className='text-sm font-medium'>
							Role
						</label>
						<Select
							value={role}
							onValueChange={(value: MemberRole) => setRole(value)}
						>
							<SelectTrigger>
								<SelectValue />
							</SelectTrigger>
							<SelectContent>
								<SelectItem value='Editor'>
									<div className='flex items-center gap-2'>
										<Edit className='h-4 w-4' />
										Editor
									</div>
								</SelectItem>
								<SelectItem value='Admin'>
									<div className='flex items-center gap-2'>
										<Shield className='h-4 w-4' />
										Admin
									</div>
								</SelectItem>
								{currentUserRole === "Owner" && (
									<SelectItem value='Owner'>
										<div className='flex items-center gap-2'>
											<Crown className='h-4 w-4' />
											Owner
										</div>
									</SelectItem>
								)}
							</SelectContent>
						</Select>
					</div> */}
				</div>
				<DialogFooter>
					<Button variant='outline' onClick={handleClose} disabled={isInviting}>
						Cancel
					</Button>
					<Button onClick={handleInvite} disabled={!email.trim() || isInviting}>
						{isInviting ? "Sending..." : "Send Invite"}
					</Button>
				</DialogFooter>
			</DialogContent>
		</Dialog>
	);
};
