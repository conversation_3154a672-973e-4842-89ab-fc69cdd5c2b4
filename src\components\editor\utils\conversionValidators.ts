interface ValidationResult {
	isValid: boolean;
	errors: string[];
	warnings: string[];
}

export const validateHtmlForMdx = (html: string): ValidationResult => {
	const errors: string[] = [];
	const warnings: string[] = [];

	// Check for empty input
	if (!html || html.trim() === '') {
		errors.push('HTML input is empty');
		return { isValid: false, errors, warnings };
	}

	// Remove Custom HTML content from validation since it should preserve any HTML
	let htmlToValidate = html;

	// Remove content inside custom-html divs from validation
	// This handles the case where Custom HTML is already rendered as a div
	htmlToValidate = htmlToValidate.replace(
		/<div[^>]*data-type="custom-html"[^>]*>[\s\S]*?<\/div>/gi,
		'<div data-type="custom-html"></div>'
	);

	// Also handle the case where Custom HTML has data-html attribute
	htmlToValidate = htmlToValidate.replace(
		/<div[^>]*data-html="[^"]*"[^>]*>[\s\S]*?<\/div>/gi,
		'<div data-type="custom-html"></div>'
	);

	// Handle escaped quotes in data-html attribute
	htmlToValidate = htmlToValidate.replace(
		/<div[^>]*data-html='[^']*'[^>]*>[\s\S]*?<\/div>/gi,
		'<div data-type="custom-html"></div>'
	);

	// Check for malformed HTML
	const openTags = htmlToValidate.match(/<[^/][^>]*>/g) || [];
	const closeTags = htmlToValidate.match(/<\/[^>]+>/g) || [];

	if (openTags.length !== closeTags.length) {
		warnings.push('Possible unclosed HTML tags detected');
	}

	// Check for unsupported components (excluding Custom HTML content)
	const unsupportedComponents = ['script', 'style'];
	unsupportedComponents.forEach((tag) => {
		if (htmlToValidate.includes(`<${tag}`)) {
			// Double-check: if this is a style tag, make sure it's not inside Custom HTML
			if (tag === 'style') {
				// If the original HTML contains Custom HTML components, be more lenient with style tags
				if (
					html.includes('data-type="custom-html"') ||
					html.includes('data-html=')
				) {
					return; // Skip this error for style tags when Custom HTML is present
				}
			}
			errors.push(`Unsupported tag: <${tag}>`);
		}
	});

	// Check for iframe tags that are not part of video components
	const iframeMatches = html.match(/<iframe[^>]*>/g) || [];
	iframeMatches.forEach((iframe) => {
		// Allow iframes that are part of video components
		if (!html.includes('data-type="video"') && iframe.includes('<iframe')) {
			warnings.push(
				'Iframe detected outside of video component - may not be supported'
			);
		}
	});

	return {
		isValid: errors.length === 0,
		errors,
		warnings,
	};
};

export const validateMdxForHtml = (mdx: string): ValidationResult => {
	const errors: string[] = [];
	const warnings: string[] = [];

	// Check for empty input
	if (!mdx || mdx.trim() === '') {
		errors.push('MDX input is empty');
		return { isValid: false, errors, warnings };
	}

	// Check for valid component syntax
	const componentRegex = /<([A-Z][a-zA-Z]*)[^>]*>/g;
	const matches = mdx.match(componentRegex) || [];

	const supportedComponents = [
		'Card',
		'CardList',
		'Accordion',
		'AccordionGroup',
		'Callout',
		'Steps',
		'Tabs',
		'Tab',
		'Image',
		'Video',
		'CustomHtml',
	];
	matches.forEach((match) => {
		const componentName = match.match(/<([A-Z][a-zA-Z]*)/)?.[1];
		if (componentName && !supportedComponents.includes(componentName)) {
			warnings.push(`Unknown component: ${componentName}`);
		}
	});

	return {
		isValid: errors.length === 0,
		errors,
		warnings,
	};
};
