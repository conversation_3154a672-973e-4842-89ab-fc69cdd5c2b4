import type { Project } from '@/contexts/ProjectContext/types';

/**
 * Check if a project should use mock data for Ask AI functionality
 */
export const shouldUseMockDataForAskAi = (project: Project): boolean => {
	return project.enable_mock_data?.askAi || false;
};

/**
 * Check if a project should use mock data for Web Analytics functionality
 */
export const shouldUseMockDataForWebAnalytics = (project: Project): boolean => {
	return project.enable_mock_data?.webAnalytics || false;
};

/**
 * Get personalized project name for mocks
 */
export const getMockProjectDisplayName = (projectName: string): string => {
	// Capitalize first letter of each word for display
	return projectName
		.split('-')
		.map((word) => word.charAt(0).toUpperCase() + word.slice(1))
		.join(' ');
};
