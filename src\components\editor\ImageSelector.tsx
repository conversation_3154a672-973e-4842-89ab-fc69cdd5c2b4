'use client';

import React, { useState, useRef } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Search, X, ImageIcon, Trash2, Plus, Loader2 } from 'lucide-react';

import { type OrganizationImage } from '@/hooks/useOrganizationImages';
import { useImageUpload } from '@/hooks/useImageUpload';
import { useImageSelection } from '@/hooks/useImageSelection';
import { useImageDeletion } from '@/hooks/useImageDeletion';
import { DeleteConfirmationDialog } from './DeleteConfirmationDialog';

interface ImageSelectorProps {
	isOpen: boolean;
	onClose: () => void;
	onSelectImage: (imageUrl: string) => void;
	projectId?: number;
	organizationId?: string;
}

export const ImageSelector: React.FC<ImageSelectorProps> = ({
	isOpen,
	onClose,
	onSelectImage,
	projectId,
	organizationId,
}) => {
	const [searchTerm, setSearchTerm] = useState('');
	const [images, setImages] = useState<OrganizationImage[]>([]);
	const [loading, setLoading] = useState(false);
	const [error, setError] = useState<string | null>(null);
	const [showDeleteDialog, setShowDeleteDialog] = useState(false);
	const [imagesToDelete, setImagesToDelete] = useState<OrganizationImage[]>([]);
	const fileInputRef = useRef<HTMLInputElement>(null);

	// Use new hooks
	const { uploading, uploadingImages, handleFileUpload, LIMITS } =
		useImageUpload({
			projectId,
			organizationId,
			onUploadComplete: (newImages) => {
				setImages((prev) => [...newImages, ...prev]);
			},
		});

	// Filter images based on search (moved here for use in selection hook)
	const filteredImages = images.filter((image: OrganizationImage) => {
		const matchesSearch = image.image_name
			.toLowerCase()
			.includes(searchTerm.toLowerCase());
		return matchesSearch;
	});

	// Image selection hook
	const {
		selectedImages,
		selectionCount,
		isAllSelected,
		isIndeterminate,
		toggleImageSelection,
		isImageSelected,
		toggleSelectAll,
		clearSelection,
		removeFromSelection,
	} = useImageSelection({
		images: filteredImages,
	});

	// Image deletion hook
	const { isDeleting, deleteSingleImage, deleteMultipleImages } =
		useImageDeletion({
			onImagesDeleted: (deletedIds) => {
				// Remove deleted images from state
				setImages((prev) => prev.filter((img) => !deletedIds.includes(img.id)));
				// Remove from selection
				removeFromSelection(deletedIds);
				// Close dialog
				setShowDeleteDialog(false);
			},
		});

	// Load images function using server-side API
	const loadImages = React.useCallback(async () => {
		if (!projectId || !organizationId) return;

		setLoading(true);
		setError(null);

		try {
			const response = await fetch(
				`/api/images?projectId=${projectId}&organizationId=${organizationId}&limit=100&offset=0`
			);

			if (!response.ok) {
				const errorData = (await response.json()) as { error?: string };
				throw new Error(errorData.error || 'Failed to load images');
			}

			const result = (await response.json()) as { data: OrganizationImage[] };
			setImages(result.data || []);
		} catch (err) {
			console.error('Loading error:', err);
			setError(err instanceof Error ? err.message : 'Failed to load images');
		} finally {
			setLoading(false);
		}
	}, [projectId, organizationId]);

	// Load images when the modal opens
	React.useEffect(() => {
		if (isOpen && projectId && organizationId) {
			loadImages();
		}
	}, [isOpen, projectId, organizationId, loadImages]);

	// Deletion handlers
	const handleDeleteSelected = () => {
		if (selectedImages.length > 0) {
			setImagesToDelete(selectedImages);
			setShowDeleteDialog(true);
		}
	};

	const handleDeleteSingle = (image: OrganizationImage) => {
		setImagesToDelete([image]);
		setShowDeleteDialog(true);
	};

	const handleConfirmDelete = async () => {
		if (imagesToDelete.length === 1) {
			await deleteSingleImage(imagesToDelete[0]);
		} else if (imagesToDelete.length > 1) {
			await deleteMultipleImages(imagesToDelete);
		}
	};

	const handleCancelDelete = () => {
		setShowDeleteDialog(false);
		setImagesToDelete([]);
	};

	// Handle image selection for preview
	const handleImageSelect = (image: OrganizationImage) => {
		onSelectImage(image.image_url);
		onClose();
	};

	// Handle upload card click - triggers file picker
	const handleUploadCardClick = () => {
		if (uploading) return;
		fileInputRef.current?.click();
	};

	// Handle file selection using new hook
	const handleFileSelect = async (
		event: React.ChangeEvent<HTMLInputElement>
	) => {
		const files = event.target.files;
		if (!files || files.length === 0) return;

		// Convert FileList to Array IMMEDIATELY before doing anything else
		// This prevents Chrome/Edge from invalidating the FileList when we clear the input
		const filesArray = Array.from(files);

		// Clear the input value to allow selecting the same file again
		// (This is safe now because we already have the files in our array)
		event.target.value = '';

		// Use the new upload hook
		await handleFileUpload(filesArray);
	};

	if (!isOpen) return null;

	return (
		<div className='fixed inset-0 bg-black/50 flex items-center justify-center z-50'>
			<div className='bg-white dark:bg-slate-800 rounded-lg shadow-xl w-full max-w-4xl h-[80vh] flex flex-col mx-4'>
				{/* Header */}
				<div className='flex items-center justify-between px-4 border-b border-gray-200 dark:border-gray-700'>
					<h2 className='text-lg font-semibold text-gray-900 dark:text-white'>
						Select Image
					</h2>
					<div className='flex items-center space-x-2'>
						<Button
							variant='ghost'
							size='sm'
							onClick={onClose}
							disabled={uploading}
						>
							<X className='w-4 h-4' />
						</Button>
					</div>
				</div>

				{/* Search and Filters */}
				<div className='px-4 pt-4 border-b border-gray-200 dark:border-gray-700'>
					<div className='flex items-center space-x-4 mb-4'>
						<div className='relative flex-1'>
							<Search className='absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4' />
							<Input
								type='text'
								placeholder='Search images...'
								value={searchTerm}
								onChange={(e) => setSearchTerm(e.target.value)}
								className='pl-10'
							/>
						</div>
					</div>
				</div>

				{/* Hidden file input for upload card */}
				<input
					ref={fileInputRef}
					type='file'
					accept='image/*'
					multiple
					onChange={handleFileSelect}
					style={{ display: 'none' }}
				/>

				{/* Selection Controls - Fixed at top */}
				{filteredImages.length > 0 && !loading && !error && (
					<div className='px-4 py-3 border-b border-gray-200 dark:border-gray-700 bg-gray-50 dark:bg-slate-700/50'>
						<div className='flex items-center justify-between'>
							<div className='flex items-center space-x-4'>
								{/* Select All Checkbox */}
								<label className='flex items-center space-x-2 cursor-pointer'>
									<input
										type='checkbox'
										checked={isAllSelected}
										ref={(input) => {
											if (input) input.indeterminate = isIndeterminate;
										}}
										onChange={toggleSelectAll}
										className='w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-gray-600'
									/>
									<span className='text-sm font-medium text-gray-700 dark:text-gray-300'>
										{isAllSelected ? 'Deselect All' : 'Select All'}
									</span>
								</label>

								{/* Selection Count */}
								{selectionCount > 0 && (
									<span className='text-sm text-gray-600 dark:text-gray-400'>
										{selectionCount} image{selectionCount !== 1 ? 's' : ''}{' '}
										selected
									</span>
								)}
							</div>

							{/* Bulk Actions */}
							{selectionCount > 0 && (
								<div className='flex items-center space-x-2'>
									<Button
										variant='outline'
										size='sm'
										onClick={clearSelection}
										disabled={isDeleting}
									>
										Clear Selection
									</Button>
									<Button
										variant='destructive'
										size='sm'
										onClick={handleDeleteSelected}
										disabled={isDeleting}
										className='flex items-center space-x-1 text-white'
									>
										<Trash2 className='w-4 h-4' />
										<span>
											Delete{' '}
											{selectionCount > 1
												? `${selectionCount} Images`
												: 'Image'}
										</span>
									</Button>
								</div>
							)}
						</div>
					</div>
				)}

				{/* Content */}
				<div className='flex-1 overflow-y-auto p-4'>
					{loading ? (
						<div className='flex items-center justify-center h-full'>
							<div className='text-gray-500 dark:text-gray-400'>
								Loading images...
							</div>
						</div>
					) : error ? (
						<div className='flex items-center justify-center h-full'>
							<div className='text-red-500 dark:text-red-400'>
								Error: {error}
							</div>
						</div>
					) : (
						<div className='space-y-4'>
							{/* Always show upload card and grid */}
							<div className='grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-6 gap-4'>
								{/* Upload Card - First Item */}
								<div
									onClick={handleUploadCardClick}
									className='group relative aspect-square bg-gradient-to-br from-blue-50 to-indigo-100 dark:from-blue-900/20 dark:to-indigo-900/20 rounded-lg border-2 border-dashed border-blue-300 dark:border-blue-600 hover:border-blue-500 dark:hover:border-blue-400 cursor-pointer transition-all duration-300 ease-in-out hover:shadow-xl hover:scale-105 flex flex-col items-center justify-center p-4 overflow-hidden'
								>
									{/* Background Pattern */}
									<div className='absolute inset-0 opacity-5 dark:opacity-10'>
										<svg className='w-full h-full' viewBox='0 0 100 100'>
											<defs>
												<pattern
													id='upload-pattern'
													x='0'
													y='0'
													width='20'
													height='20'
													patternUnits='userSpaceOnUse'
												>
													<circle cx='10' cy='10' r='1' fill='currentColor' />
												</pattern>
											</defs>
											<rect
												width='100'
												height='100'
												fill='url(#upload-pattern)'
											/>
										</svg>
									</div>

									{/* Upload Icon with Animation */}
									<div className='relative mb-2 p-2 bg-blue-500 dark:bg-blue-600 rounded-full group-hover:bg-blue-600 dark:group-hover:bg-blue-500 transition-colors duration-300 group-hover:scale-110 transform z-10'>
										<Plus className='w-5 h-5 text-white' />
									</div>
									{/* Hover Effect Overlay */}
									<div className='absolute inset-0 bg-blue-500/5 dark:bg-blue-400/5 rounded-lg opacity-0 group-hover:opacity-100 transition-opacity duration-300' />
								</div>

								{/* Uploading Images - Loading Cards */}
								{uploadingImages.map((uploadingImg) => (
									<div
										key={uploadingImg.id}
										className='group/uploading relative aspect-square rounded-lg overflow-hidden border-2 border-blue-300 dark:border-blue-600 bg-gray-100 dark:bg-gray-700 transition-all'
									>
										{uploadingImg.status === 'uploading' && (
											<div className='absolute inset-0 flex flex-col items-center justify-center bg-blue-50 dark:bg-blue-900/20'>
												<Loader2 className='w-8 h-8 text-blue-500 animate-spin mb-2' />
												<p className='text-xs text-blue-700 dark:text-blue-300 text-center px-2 font-medium'>
													Uploading...
												</p>
												<p className='text-xs text-gray-600 dark:text-gray-400 text-center px-2 truncate w-full'>
													{uploadingImg.name}
												</p>
											</div>
										)}
										{uploadingImg.status === 'error' && (
											<div className='absolute inset-0 flex flex-col items-center justify-center bg-red-50 dark:bg-red-900/20'>
												<X className='w-8 h-8 text-red-500 mb-2' />
												<p className='text-xs text-red-700 dark:text-red-300 text-center px-2 font-medium'>
													Upload Failed
												</p>
												<p className='text-xs text-gray-600 dark:text-gray-400 text-center px-2 truncate w-full'>
													{uploadingImg.name}
												</p>
											</div>
										)}
									</div>
								))}

								{/* Existing Images */}
								{filteredImages.map((image) => (
									<div
										key={image.id}
										className={`group/image relative aspect-square bg-gray-100 dark:bg-gray-700 rounded-lg overflow-hidden transition-all ${
											isImageSelected(image.id)
												? 'ring-2 ring-blue-500 dark:ring-blue-400'
												: ''
										}`}
									>
										{/* Selection Checkbox */}
										<div className='absolute top-2 left-2 z-20'>
											<input
												type='checkbox'
												checked={isImageSelected(image.id)}
												onChange={() => toggleImageSelection(image.id)}
												onClick={(e) => e.stopPropagation()}
												className='w-4 h-4 text-blue-600 bg-white border-2 border-gray-300 rounded focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800 focus:ring-2 shadow-lg'
											/>
										</div>

										{/* Individual Delete Button */}
										<div className='absolute top-2 right-2 z-20'>
											<button
												onClick={(e) => {
													e.stopPropagation();
													handleDeleteSingle(image);
												}}
												disabled={isDeleting}
												className='w-6 h-6 bg-red-500 hover:bg-red-600 text-white rounded-full flex items-center justify-center shadow-lg transition-colors opacity-0 group-hover/image:opacity-100 disabled:opacity-50 disabled:cursor-not-allowed'
												title='Delete image'
											>
												<Trash2 className='w-4 h-4' />
											</button>
										</div>

										{/* Selection Overlay */}
										{isImageSelected(image.id) && (
											<div className='absolute inset-0 bg-blue-500/20 dark:bg-blue-400/20 z-10' />
										)}

										{/* Clickable area for image selection */}
										<div
											className='absolute inset-0 cursor-pointer hover:ring-2 hover:ring-blue-400 transition-all z-10'
											onClick={() => handleImageSelect(image)}
										>
											<img
												src={image.image_url}
												alt={image.alt_text || image.image_name}
												className='w-full h-full object-cover transition-transform group-hover/image:scale-105'
												onError={(e) => {
													const target = e.target as HTMLImageElement;
													target.src =
														"data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='100' height='100' viewBox='0 0 100 100'%3E%3Crect width='100' height='100' fill='%23f3f4f6'/%3E%3Ctext x='50' y='50' text-anchor='middle' dy='.3em' fill='%236b7280'%3ENo Image%3C/text%3E%3C/svg%3E";
												}}
											/>

											<div className='absolute inset-0 bg-black/0 group-hover/image:bg-black/20 transition-all' />
											<div className='absolute bottom-0 left-0 right-0 p-2 bg-gradient-to-t from-black/60 to-transparent opacity-0 group-hover/image:opacity-100 transition-opacity'>
												<p className='text-white text-xs font-medium truncate'>
													{image.alt_text || image.image_name}
												</p>
												{image.tags && image.tags.length > 0 && (
													<p className='text-white/80 text-xs truncate'>
														{image.tags.join(', ')}
													</p>
												)}
											</div>
										</div>
									</div>
								))}
							</div>

							{/* Show message when no images found */}
							{filteredImages.length === 0 && images.length > 0 && (
								<div className='flex flex-col items-center justify-center py-8 text-gray-500 dark:text-gray-400'>
									<ImageIcon className='w-12 h-12 mb-4 opacity-50' />
									<p className='text-lg font-medium mb-2'>
										No images match your filters
									</p>
									<p className='text-sm'>
										Try adjusting your search or filters
									</p>
								</div>
							)}

							{/* Show message when no images at all */}
							{images.length === 0 && uploadingImages.length === 0 && (
								<div className='flex flex-col items-center justify-center py-8 text-gray-500 dark:text-gray-400'>
									<ImageIcon className='w-12 h-12 mb-4 opacity-50' />
									<p className='text-lg font-medium mb-2'>No images yet</p>
									<p className='text-sm'>
										Click the upload button above to get started
									</p>
								</div>
							)}
						</div>
					)}
				</div>

				{/* Footer */}
				<div className='p-4 border-t border-gray-200 dark:border-gray-700'>
					<div className='flex items-center justify-between text-sm text-gray-500 dark:text-gray-400'>
						<span>
							{images.length} {images.length > 1 ? 'images' : 'image'}
						</span>
						<span>Maximum image size: {LIMITS.max_size_mb_per_image}MB</span>
					</div>
				</div>
			</div>

			{/* Delete Confirmation Dialog */}
			<DeleteConfirmationDialog
				isOpen={showDeleteDialog}
				onClose={handleCancelDelete}
				onConfirm={handleConfirmDelete}
				images={imagesToDelete}
				isDeleting={isDeleting}
			/>
		</div>
	);
};
