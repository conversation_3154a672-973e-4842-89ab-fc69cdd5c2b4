import axios from "axios";

async function slackNotifier(title: string, message: string) {
  const data = {
    blocks: [
      {
        type: "divider",
      },
      {
        type: "section",
        text: {
          type: "mrkdwn",
          text: title,
        },
      },
      {
        type: "section",
        text: {
          type: "mrkdwn",
          text: message,
        },
      },
      {
        type: "divider",
      },
    ],
  };
  await axios.post(
    "*********************************************************************************",
    data,
    {
      headers: {
        "Content-Type": "application/json",
      },
    }
  );
}

export default slackNotifier;
