import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>Title } from "@/components/ui/card";
import { Skeleton } from "@/components/ui/skeleton";
import { HiLocationMarker } from "react-icons/hi";
import { getCountryNameByCode } from "@/utils/countries";

interface Country {
	count: number;
	sum: { visits: number };
	dimensions: { metric: string };
}

interface TopCountriesProps {
	countries: Country[];
	isLoading?: boolean;
}

const formatNumber = (num: number): string => {
	return new Intl.NumberFormat("en-US").format(num);
};

function TopCountriesItemSkeleton() {
	return (
		<div className='flex items-center justify-between gap-3'>
			<div className='flex-1 min-w-0'>
				<Skeleton className='h-4 w-32' />
			</div>
			<div className='flex items-center gap-3 flex-shrink-0'>
				<Skeleton className='h-4 w-12' />
				<Skeleton className='w-20 h-2 rounded-full' />
			</div>
		</div>
	);
}

function TopCountriesSkeleton() {
	return (
		<Card className='h-full'>
			<CardHeader className='py-'>
				<div className='flex items-center gap-2'>
					<Skeleton className='h-5 w-5 rounded-full' />
					<Skeleton className='h-5 w-32' />
				</div>
				{/* <Skeleton className='h-4 w-48 mt-2' /> */}
			</CardHeader>
			<CardContent className='px-6 pb-6'>
				<div className='space-y-4 max-h-80 overflow-y-auto pr-2'>
					{Array.from({ length: 8 }).map((_, index) => (
						<TopCountriesItemSkeleton key={index} />
					))}
				</div>
			</CardContent>
		</Card>
	);
}

export function TopCountries({
	countries,
	isLoading = false,
}: TopCountriesProps) {
	if (isLoading) {
		return <TopCountriesSkeleton />;
	}

	if (!countries || countries.length === 0) {
		return null;
	}

	const maxVisits = countries[0]?.sum.visits || 1;

	return (
		<Card className='h-full'>
			<CardHeader className='py-6'>
				<div className='flex items-center gap-2'>
					<HiLocationMarker className='h-5 w-5 text-blue-500' />
					<CardTitle className='text-lg font-semibold flex items-center'>
						Visits by Country{" "}
						<span className='text-xs text-gray-500 ml-3'>
							({countries.length} countries)
						</span>
					</CardTitle>
				</div>
				{/* <CardDescription className='text-sm text-gray-500'>
					All countries by number of visits ({countries.length} countries)
				</CardDescription> */}
			</CardHeader>
			<CardContent className='pb-6'>
				<div className='space-y-4 max-h-96 overflow-y-auto pr-2'>
					{countries.map((country, index) => {
						const percentage = (country.sum.visits / maxVisits) * 100;
						const countryName =
							getCountryNameByCode(country.dimensions.metric) ||
							country.dimensions.metric;

						return (
							<div
								key={index}
								className='flex items-center justify-between gap-3'
							>
								<div className='flex-1 min-w-0'>
									<span className='font-medium text-sm text-gray-900 truncate block'>
										{countryName}
									</span>
								</div>
								<div className='flex items-center gap-2 sm:gap-3 flex-shrink-0'>
									<span className='text-sm text-gray-600 min-w-[2.5rem] sm:min-w-[3rem] text-right'>
										{formatNumber(country.sum.visits)}
									</span>
									<div className='w-16 sm:w-20 lg:w-24 h-2 bg-gray-200 rounded-full overflow-hidden'>
										<div
											className='h-full bg-blue-500 transition-all duration-300'
											style={{
												width: `${percentage}%`,
											}}
										/>
									</div>
								</div>
							</div>
						);
					})}
				</div>
			</CardContent>
		</Card>
	);
}
