import React, { useState, useEffect } from 'react';
import { Home, AlertCircle, Edit, Loader2 } from 'lucide-react';
import { cn } from '@/lib/utils';
import {
	Dialog,
	DialogContent,
	DialogHeader,
	DialogTitle,
	DialogFooter,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { createClient } from '@/utils/supabase/client';

interface CustomHomepageModalProps {
	isOpen: boolean;
	onClose: () => void;
	projectId: string;
	onSuccess: () => void;
	toast: (message: string, type: 'success' | 'error', title: string) => void;
}

export function CustomHomepageModal({
	isOpen,
	onClose,
	projectId,
	onSuccess,
	toast,
}: CustomHomepageModalProps) {
	const [isEditingExisting, setIsEditingExisting] = useState(false);
	const [isLoadingContent, setIsLoadingContent] = useState(false);
	const [htmlContent, setHtmlContent] = useState<string>(`<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Custom Homepage</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 40px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 40px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
        }
        p {
            color: #666;
            line-height: 1.6;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Welcome to Your Custom Homepage</h1>
        <p>This is your custom HTML homepage. You can edit this content to create your perfect landing page.</p>
        <p>Add your own styling, content, and functionality to make it uniquely yours.</p>
    </div>
</body>
</html>`);
	const [isLoading, setIsLoading] = useState(false);
	const [validationError, setValidationError] = useState<string | null>(null);

	// Load existing homepage content when modal opens
	useEffect(() => {
		const loadExistingHomepage = async () => {
			if (!isOpen) return;

			setIsLoadingContent(true);
			const supabase = createClient();

			try {
				// Check if custom homepage already exists (path="/")
				const { data: existingPage, error } = await supabase
					.from('project_pages')
					.select('content')
					.eq('project_id', parseInt(projectId, 10))
					.eq('path', '/')
					.single();

				if (error && error.code !== 'PGRST116') {
					// PGRST116 is "not found" error, which is expected if no homepage exists
					console.error('❌ Error loading existing homepage:', error);
				} else if (existingPage) {
					// Homepage exists, load its content
					setHtmlContent(existingPage.content);
					setIsEditingExisting(true);
					console.log('✅ Loaded existing homepage content');
				} else {
					// No existing homepage, keep default content
					setIsEditingExisting(false);
					console.log('ℹ️ No existing homepage found, using default content');
				}
			} catch (error) {
				console.error('❌ Unexpected error loading homepage:', error);
			} finally {
				setIsLoadingContent(false);
			}
		};

		loadExistingHomepage();
	}, [isOpen, projectId]);

	const validateHtml = (html: string): boolean => {
		// Basic HTML validation
		if (!html.trim()) {
			setValidationError('HTML content cannot be empty');
			return false;
		}

		// Check for basic HTML structure
		const hasHtmlTag = /<html[^>]*>/i.test(html);
		const hasHeadTag = /<head[^>]*>/i.test(html);
		const hasBodyTag = /<body[^>]*>/i.test(html);

		if (!hasHtmlTag || !hasHeadTag || !hasBodyTag) {
			setValidationError('HTML must include <html>, <head>, and <body> tags');
			return false;
		}

		// Check for potentially dangerous scripts (basic security)
		const hasScript = /<script[^>]*>/i.test(html);
		if (hasScript) {
			setValidationError('Script tags are not allowed for security reasons');
			return false;
		}

		setValidationError(null);
		return true;
	};

	const handleSave = async () => {
		if (!validateHtml(htmlContent)) {
			return;
		}

		setIsLoading(true);
		const supabase = createClient();

		try {
			// First, try to insert the HTML content with path "/" (homepage)
			const insertData = {
				project_id: parseInt(projectId, 10),
				path: '/',
				title: 'Custom Homepage',
				content: htmlContent,
				is_api_file: false,
			};

			const { data: insertResult, error: insertError } = await supabase
				.from('project_pages')
				.insert(insertData)
				.select()
				.single();

			if (insertError) {
				// Check if it's a duplicate key error (page already exists)
				if (
					insertError.code === '23505' ||
					insertError.message?.includes('duplicate') ||
					insertError.message?.includes('unique')
				) {
					console.log('🔄 Homepage already exists, updating existing page...');

					// Update the existing page instead
					const { data: updateResult, error: updateError } = await supabase
						.from('project_pages')
						.update({
							title: insertData.title,
							content: insertData.content,
							is_api_file: insertData.is_api_file,
						})
						.eq('project_id', insertData.project_id)
						.eq('path', insertData.path)
						.select()
						.single();

					if (updateError) {
						console.error('❌ Error updating homepage:', updateError);
						toast('Failed to update homepage file', 'error', 'Error');
						return;
					}

					console.log('✅ Homepage file updated:', updateResult);
				} else {
					console.error('❌ Error saving homepage:', insertError);
					toast('Failed to save homepage file', 'error', 'Error');
					return;
				}
			} else {
				console.log('✅ Homepage file created:', insertResult);
			}

			// Then, update the project configuration to set homepage to homepage.html
			const { data: projectData, error: projectError } = await supabase
				.from('projects')
				.select('configjson')
				.eq('id', parseInt(projectId, 10))
				.single();

			if (projectError) {
				console.error('❌ Error fetching project config:', projectError);
				toast('Failed to update project configuration', 'error', 'Error');
				return;
			}

			const updatedConfig = {
				...projectData.configjson,
				homepage: 'homepage.html',
			};

			const { error: updateError } = await supabase
				.from('projects')
				.update({ configjson: updatedConfig })
				.eq('id', parseInt(projectId, 10));

			if (updateError) {
				console.error('❌ Error updating project config:', updateError);
				toast('Failed to update project configuration', 'error', 'Error');
				return;
			}

			console.log('✅ Project configuration updated with custom homepage');
			toast(
				isEditingExisting
					? 'Custom homepage updated successfully!'
					: 'Custom homepage created successfully!',
				'success',
				'Success'
			);
			onSuccess();
			onClose();
		} catch (error) {
			console.error('❌ Unexpected error:', error);
			toast('An unexpected error occurred', 'error', 'Error');
		} finally {
			setIsLoading(false);
		}
	};

	const handleClose = () => {
		if (!isLoading) {
			setValidationError(null);
			onClose();
		}
	};

	const handleContentChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
		setHtmlContent(e.target.value);
		if (validationError) {
			setValidationError(null);
		}
	};

	return (
		<Dialog open={isOpen} onOpenChange={handleClose}>
			<DialogContent className='sm:max-w-[800px] max-h-[80vh] overflow-y-auto'>
				<DialogHeader>
					<DialogTitle className='flex items-center gap-2'>
						{isEditingExisting ? (
							<>
								<Edit className='h-5 w-5 text-orange-600' />
								Edit Custom Homepage
							</>
						) : (
							<>
								<Home className='h-5 w-5 text-blue-600' />
								Create Custom Homepage
							</>
						)}
					</DialogTitle>
				</DialogHeader>

				<div className='space-y-4'>
					{isLoadingContent ? (
						<div className='flex items-center justify-center py-8'>
							<div className='text-sm text-gray-500'>
								Loading existing content...
							</div>
						</div>
					) : (
						<>
							<div>
								<p className='text-sm text-gray-600 dark:text-gray-400 mb-4'>
									{isEditingExisting
										? 'Edit your existing custom HTML homepage. Changes will be saved to "homepage.html".'
										: 'Create a custom HTML homepage for your project. This will be saved as "homepage.html" and set as your project\'s main page.'}
								</p>
							</div>

							<div className='space-y-2'>
								<label
									htmlFor='html-content'
									className='text-sm font-medium text-gray-700 dark:text-gray-300'
								>
									HTML Content
								</label>
								<textarea
									id='html-content'
									value={htmlContent}
									onChange={handleContentChange}
									className='w-full h-96 p-3 border border-gray-300 dark:border-gray-600 rounded-md font-mono text-sm bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100 resize-none focus:ring-2 focus:ring-blue-500 focus:border-transparent'
									placeholder='Enter your HTML content here...'
									disabled={isLoading}
								/>
							</div>

							{validationError && (
								<div className='flex items-center gap-2 p-3 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-md'>
									<AlertCircle className='h-4 w-4 text-red-500' />
									<span className='text-sm text-red-700 dark:text-red-400'>
										{validationError}
									</span>
								</div>
							)}

							<div className='text-xs text-gray-500 dark:text-gray-400'>
								<p>
									• HTML must include &lt;html&gt;, &lt;head&gt;, and
									&lt;body&gt; tags
								</p>
								<p>• Script tags are not allowed for security reasons</p>
								<p>
									• This will {isEditingExisting ? 'update' : 'replace'} any
									existing custom homepage
								</p>
							</div>
						</>
					)}
				</div>

				<DialogFooter>
					<Button variant='outline' onClick={handleClose} disabled={isLoading}>
						Cancel
					</Button>
					<div className='relative group'>
						<Button
							onClick={handleSave}
							disabled={isLoading || isLoadingContent}
							className={cn(
								'relative overflow-hidden transition-all duration-300 select-none font-medium shadow-lg',
								'bg-gradient-to-r from-blue-600 to-indigo-600 border-blue-600',
								'hover:from-blue-700 hover:to-indigo-700 hover:shadow-xl hover:scale-102',
								'disabled:opacity-60 disabled:cursor-not-allowed disabled:hover:scale-100',
								'focus:ring-4 focus:ring-blue-300/50',
								'text-white hover:text-white focus:text-white active:text-white'
							)}
						>
							{/* Shimmer effect on hover */}
							<div className='absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent -translate-x-full group-hover:translate-x-full transition-transform duration-700 ease-in-out' />

							{/* Pulse effect during loading */}
							{isLoading && (
								<div className='absolute inset-0 bg-gradient-to-r from-blue-500/20 to-indigo-500/20 animate-pulse' />
							)}

							{/* Content */}
							<div className='relative z-10 flex items-center'>
								{isLoading ? (
									<Loader2 className='h-4 w-4 animate-spin' />
								) : isEditingExisting ? (
									<Edit className='h-4 w-4' />
								) : (
									<Home className='h-4 w-4' />
								)}
								<span className='ml-2 font-medium text-white'>
									{isLoading
										? isEditingExisting
											? 'Saving...'
											: 'Creating...'
										: isEditingExisting
											? 'Save Changes'
											: 'Create Homepage'}
								</span>
							</div>
						</Button>
					</div>
				</DialogFooter>
			</DialogContent>
		</Dialog>
	);
}
