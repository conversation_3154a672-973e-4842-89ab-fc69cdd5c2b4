'use client';

import { useState, useCallback, useRef } from 'react';

interface UploadedImage {
	id: string;
	image_name: string;
	image_url: string;
	alt_text?: string;
	tags?: string[];
	metadata?: Record<string, unknown>;
}

interface UploadResult {
	success: boolean;
	uploadedImages?: UploadedImage[];
	error?: string;
	errors?: string[];
}

interface QueueItem {
	id: string;
	files: File[];
	metadata: {
		projectId: number;
		organizationId: string;
	};
	status: 'pending' | 'uploading' | 'completed' | 'error';
	progress: number;
	error?: string;
	retryCount: number;
	priority: 'low' | 'normal' | 'high';
}

interface UploadQueueConfig {
	maxConcurrent: number;
	maxRetries: number;
	retryDelay: number;
	batchSize: number;
}

interface UseUploadQueueProps {
	config?: Partial<UploadQueueConfig>;
	onItemComplete?: (item: QueueItem, uploadedImages?: UploadedImage[]) => void;
	onItemError?: (item: QueueItem, error: Error) => void;
	onQueueComplete?: () => void;
}

const DEFAULT_CONFIG: UploadQueueConfig = {
	maxConcurrent: 3,
	maxRetries: 3,
	retryDelay: 1000,
	batchSize: 5,
};

export const useUploadQueue = ({
	config = {},
	onItemComplete,
	onItemError,
	onQueueComplete,
}: UseUploadQueueProps = {}) => {
	const [queue, setQueue] = useState<QueueItem[]>([]);
	const [activeUploads, setActiveUploads] = useState<Set<string>>(new Set());
	const [isProcessing, setIsProcessing] = useState(false);

	const finalConfig = { ...DEFAULT_CONFIG, ...config };
	const processingRef = useRef(false);

	// Add items to queue
	const addToQueue = useCallback(
		(
			files: File[],
			metadata: { projectId: number; organizationId: string },
			priority: 'low' | 'normal' | 'high' = 'normal'
		) => {
			// Split files into batches
			const batches: File[][] = [];
			for (let i = 0; i < files.length; i += finalConfig.batchSize) {
				batches.push(files.slice(i, i + finalConfig.batchSize));
			}

			const newItems: QueueItem[] = batches.map((batch, index) => ({
				id: `${Date.now()}_${index}_${Math.random().toString(36).substring(2)}`,
				files: batch,
				metadata,
				status: 'pending',
				progress: 0,
				retryCount: 0,
				priority,
			}));

			setQueue((prev) => {
				// Sort by priority: high -> normal -> low
				const priorityOrder = { high: 0, normal: 1, low: 2 };
				const updated = [...prev, ...newItems].sort(
					(a, b) => priorityOrder[a.priority] - priorityOrder[b.priority]
				);
				return updated;
			});

			// Start processing if not already running
			if (!processingRef.current) {
				processQueue();
			}

			return newItems.map((item) => item.id);
		},
		[finalConfig.batchSize]
	);

	// Remove item from queue
	const removeFromQueue = useCallback((itemId: string) => {
		setQueue((prev) => prev.filter((item) => item.id !== itemId));
		setActiveUploads((prev) => {
			const updated = new Set(prev);
			updated.delete(itemId);
			return updated;
		});
	}, []);

	// Retry failed item
	const retryItem = useCallback((itemId: string) => {
		setQueue((prev) =>
			prev.map((item) =>
				item.id === itemId
					? { ...item, status: 'pending', error: undefined, progress: 0 }
					: item
			)
		);

		if (!processingRef.current) {
			processQueue();
		}
	}, []);

	// Clear completed items
	const clearCompleted = useCallback(() => {
		setQueue((prev) =>
			prev.filter(
				(item) => item.status !== 'completed' && item.status !== 'error'
			)
		);
	}, []);

	// Upload function with retry logic and auth
	const uploadBatch = useCallback(
		async (item: QueueItem): Promise<UploadResult> => {
			const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
			if (!supabaseUrl) {
				throw new Error('Supabase URL not found');
			}

			// Get auth session
			const { createClient } = await import('@/utils/supabase/client');
			const supabase = createClient();
			const {
				data: { session },
			} = await supabase.auth.getSession();

			if (!session) {
				throw new Error('User not authenticated');
			}

			// Create FormData
			const formData = new FormData();

			const metadata = {
				projectId: item.metadata.projectId,
				organizationId: item.metadata.organizationId,
				images: item.files.map((file) => ({
					name: file.name.replace(/\.[^/.]+$/, ''),
					contentType: file.type,
					altText: file.name.replace(/\.[^/.]+$/, ''),
					tags: [],
					metadata: {
						originalName: file.name,
						uploadedAt: new Date().toISOString(),
						queueId: item.id,
					},
				})),
			};

			formData.append('metadata', JSON.stringify(metadata));
			item.files.forEach((file, index) => {
				formData.append(`file_${index}`, file);
			});

			console.log(
				`Uploading queue batch ${item.id} with ${item.files.length} files`
			);

			// Upload with auth
			const response = await fetch(
				`${supabaseUrl}/functions/v1/advanced-image-upload`,
				{
					method: 'POST',
					headers: {
						Authorization: `Bearer ${session.access_token}`,
					},
					body: formData,
				}
			);

			if (!response.ok) {
				const errorText = await response.text();
				console.error('Queue upload error response:', errorText);
				throw new Error(`Upload failed: ${response.statusText} - ${errorText}`);
			}

			const result = (await response.json()) as UploadResult;
			if (!result.success) {
				throw new Error(
					result.error || result.errors?.join(', ') || 'Upload failed'
				);
			}

			console.log(
				`Queue batch ${item.id} uploaded successfully: ${result.uploadedImages?.length || 0} images`
			);
			return result;
		},
		[]
	);

	// Process queue with concurrency control
	const processQueue = useCallback(async () => {
		if (processingRef.current) return;

		processingRef.current = true;
		setIsProcessing(true);

		try {
			while (true) {
				// Get pending items that can be processed
				const pendingItems = queue.filter(
					(item) => item.status === 'pending' && !activeUploads.has(item.id)
				);

				// Check if we can start more uploads
				const availableSlots = finalConfig.maxConcurrent - activeUploads.size;
				const itemsToProcess = pendingItems.slice(0, availableSlots);

				if (itemsToProcess.length === 0) {
					// No more items to process or all slots are full
					if (activeUploads.size === 0) {
						// Queue is empty and no active uploads
						break;
					}
					// Wait a bit and check again
					await new Promise((resolve) => setTimeout(resolve, 100));
					continue;
				}

				// Start processing items
				const uploadPromises = itemsToProcess.map(async (item) => {
					// Mark as active
					setActiveUploads((prev) => new Set([...prev, item.id]));
					setQueue((prev) =>
						prev.map((qItem) =>
							qItem.id === item.id
								? { ...qItem, status: 'uploading', progress: 0 }
								: qItem
						)
					);

					try {
						// Simulate progress updates
						const progressInterval = setInterval(() => {
							setQueue((prev) =>
								prev.map((qItem) =>
									qItem.id === item.id && qItem.status === 'uploading'
										? { ...qItem, progress: Math.min(qItem.progress + 10, 90) }
										: qItem
								)
							);
						}, 200);

						// Perform upload
						const uploadResult = await uploadBatch(item);

						clearInterval(progressInterval);

						// Mark as completed
						setQueue((prev) =>
							prev.map((qItem) =>
								qItem.id === item.id
									? { ...qItem, status: 'completed', progress: 100 }
									: qItem
							)
						);

						// Call completion callback with uploaded images
						onItemComplete?.(item, uploadResult?.uploadedImages);
					} catch (error) {
						const errorObj =
							error instanceof Error ? error : new Error('Unknown error');

						// Check if we should retry
						if (item.retryCount < finalConfig.maxRetries) {
							// Schedule retry
							setTimeout(
								() => {
									setQueue((prev) =>
										prev.map((qItem) =>
											qItem.id === item.id
												? {
														...qItem,
														status: 'pending',
														retryCount: qItem.retryCount + 1,
														error: undefined,
														progress: 0,
													}
												: qItem
										)
									);
								},
								finalConfig.retryDelay * Math.pow(2, item.retryCount)
							); // Exponential backoff
						} else {
							// Mark as error
							setQueue((prev) =>
								prev.map((qItem) =>
									qItem.id === item.id
										? {
												...qItem,
												status: 'error',
												error: errorObj.message,
												progress: 0,
											}
										: qItem
								)
							);

							onItemError?.(item, errorObj);
						}
					} finally {
						// Remove from active uploads
						setActiveUploads((prev) => {
							const updated = new Set(prev);
							updated.delete(item.id);
							return updated;
						});
					}
				});

				// Wait for at least one upload to complete before checking for more
				await Promise.race(uploadPromises);
			}

			// Queue processing complete
			onQueueComplete?.();
		} finally {
			processingRef.current = false;
			setIsProcessing(false);
		}
	}, [
		queue,
		activeUploads,
		finalConfig,
		uploadBatch,
		onItemComplete,
		onItemError,
		onQueueComplete,
	]);

	// Pause/resume queue
	const pauseQueue = useCallback(() => {
		processingRef.current = false;
		setIsProcessing(false);
	}, []);

	const resumeQueue = useCallback(() => {
		if (!processingRef.current) {
			processQueue();
		}
	}, [processQueue]);

	// Get queue statistics
	const getQueueStats = useCallback(() => {
		const stats = queue.reduce(
			(acc, item) => {
				acc[item.status] = (acc[item.status] || 0) + 1;
				return acc;
			},
			{} as Record<string, number>
		);

		return {
			total: queue.length,
			pending: stats.pending || 0,
			uploading: stats.uploading || 0,
			completed: stats.completed || 0,
			error: stats.error || 0,
			activeUploads: activeUploads.size,
		};
	}, [queue, activeUploads]);

	return {
		queue,
		isProcessing,
		activeUploads: activeUploads.size,
		addToQueue,
		removeFromQueue,
		retryItem,
		clearCompleted,
		pauseQueue,
		resumeQueue,
		getQueueStats,
		config: finalConfig,
	};
};
