interface IntervalToggleProps {
	isAnnual: boolean;
	setIsAnnual: (isAnnual: boolean) => void;
}

export function IntervalToggle({ isAnnual, setIsAnnual }: IntervalToggleProps) {
	return (
		<div className='flex justify-center mb-8'>
			<div className='inline-flex items-center rounded-full bg-gray-100 p-1'>
				<button
					onClick={() => setIsAnnual(false)}
					className={`px-4 sm:px-6 py-2 text-sm font-medium rounded-full transition-colors ${
						!isAnnual
							? "bg-wd-blue text-white shadow-sm"
							: "text-gray-700 hover:text-gray-900"
					}`}
				>
					Monthly
				</button>
				<button
					onClick={() => setIsAnnual(true)}
					className={`px-4 sm:px-6 py-2 text-sm font-medium rounded-full transition-colors ${
						isAnnual
							? "bg-wd-blue text-white shadow-sm"
							: "text-gray-700 hover:text-gray-900"
					}`}
				>
					Annual
				</button>
			</div>
		</div>
	);
}
