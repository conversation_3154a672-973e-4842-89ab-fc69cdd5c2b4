import { useState, useEffect, useCallback } from "react";
import { useProject } from "@/contexts/ProjectContext/ProjectContextProvider";
import { useUser } from "@/contexts/UserContext/UserContextProvider";
import { useToast } from "@/hooks/use-toast";
import type {
	ProjectMember,
	MemberRole,
	MembersResponse,
	PendingInvite,
} from "@/types/members";

interface OptimisticChanges {
	roleChanges: Record<
		string,
		{ newRole: MemberRole; oldRole: MemberRole; isProcessing: boolean }
	>;
	removedMembers: Set<string>;
	processingRemovals: Set<string>;
	removedInvites: Set<number>;
	processingInviteRemovals: Set<number>;
}

export const useMembers = () => {
	const { selectedProject } = useProject();
	const { user } = useUser();
	const { toast } = useToast();

	const [members, setMembers] = useState<ProjectMember[]>([]);
	const [pendingInvites, setPendingInvites] = useState<PendingInvite[]>([]);
	const [isLoading, setIsLoading] = useState(true);
	const [currentUserRole, setCurrentUserRole] = useState<MemberRole | null>(
		null
	);
	const [optimisticChanges, setOptimisticChanges] = useState<OptimisticChanges>(
		{
			roleChanges: {},
			removedMembers: new Set(),
			processingRemovals: new Set(),
			removedInvites: new Set(),
			processingInviteRemovals: new Set(),
		}
	);

	// Internal function to perform the actual fetch and set members
	const performFetchAndSetMembers = useCallback(
		async (projectId: string) => {
			setIsLoading(true);
			try {
				const response = await fetch(`/api/members?projectId=${projectId}`);

				if (!response.ok) {
					throw new Error("Failed to fetch members");
				}

				const data = (await response.json()) as MembersResponse;
				setMembers(data.members || []);
				setPendingInvites(data.pending_invites || []);
			} catch (error) {
				console.error("Error fetching members:", error);
				setMembers([]); // Clear members on error
				setPendingInvites([]); // Clear invites on error
				toast({
					title: "Error",
					description: "Failed to load project members",
					variant: "destructive",
				});
			} finally {
				setIsLoading(false);
			}
		},
		[toast] // Depends only on toast, which is stable
	);

	// Effect to fetch members when selectedProject.id changes
	useEffect(() => {
		if (selectedProject?.id) {
			performFetchAndSetMembers(String(selectedProject.id));
		} else {
			// Clear state if no project is selected
			setMembers([]);
			setPendingInvites([]);
			setCurrentUserRole(null);
			setIsLoading(false); // Set loading to false if no project
			setOptimisticChanges({
				roleChanges: {},
				removedMembers: new Set(),
				processingRemovals: new Set(),
				removedInvites: new Set(),
				processingInviteRemovals: new Set(),
			});
		}
	}, [selectedProject?.id, performFetchAndSetMembers]);

	// Effect to update currentUserRole when user or members state changes
	useEffect(() => {
		if (user && members.length > 0) {
			const currentMember = members.find(
				(member) => member.user_id === user.id
			);
			setCurrentUserRole(currentMember?.role || null);
		} else if (!user || members.length === 0) {
			// If no user or no members, there's no specific role in this project context
			setCurrentUserRole(null);
		}
	}, [user, members]);

	const getMemberCurrentState = useCallback(
		(member: ProjectMember) => {
			const userId = member.user_id;
			if (optimisticChanges.removedMembers.has(userId)) {
				return null;
			}
			const roleChange = optimisticChanges.roleChanges[userId];
			if (roleChange) {
				return { ...member, role: roleChange.newRole };
			}
			return member;
		},
		[optimisticChanges]
	);

	const getDisplayMembers = useCallback(() => {
		return members
			.map(getMemberCurrentState)
			.filter(Boolean) as ProjectMember[];
	}, [members, getMemberCurrentState]);

	const getDisplayInvites = useCallback(() => {
		return pendingInvites.filter(
			(invite) => !optimisticChanges.removedInvites.has(invite.id)
		);
	}, [pendingInvites, optimisticChanges.removedInvites]);

	const handleInviteMember = useCallback(
		async (email: string, role: MemberRole) => {
			if (!selectedProject?.id || !email.trim()) return false;

			try {
				const response = await fetch("/api/members/invite", {
					method: "POST",
					headers: { "Content-Type": "application/json" },
					body: JSON.stringify({
						email: email.trim(),
						projectId: selectedProject.id,
						role,
					}),
				});

				const data = (await response.json()) as {
					error?: string;
					message?: string;
				};

				if (!response.ok) {
					throw new Error(data.error || "Failed to invite member");
				}

				toast({
					title: "Success",
					description: "Member invitation sent successfully",
				});

				// Refetch members after successful invite
				await performFetchAndSetMembers(String(selectedProject.id));
				return true;
			} catch (error) {
				const errorMessage =
					error instanceof Error ? error.message : "Failed to invite member";
				toast({
					title: "Error",
					description: errorMessage,
					variant: "destructive",
				});
				return false;
			}
		},
		[selectedProject?.id, toast, performFetchAndSetMembers]
	);

	const handleRemoveMember = useCallback(
		async (userId: string) => {
			if (!selectedProject?.id) return;

			setOptimisticChanges((prev) => ({
				...prev,
				removedMembers: new Set([...prev.removedMembers, userId]),
				processingRemovals: new Set([...prev.processingRemovals, userId]),
			}));

			try {
				const response = await fetch(
					`/api/members/${userId}?projectId=${selectedProject.id}`,
					{ method: "DELETE" }
				);

				const data = (await response.json()) as {
					error?: string;
					message?: string;
				};

				if (!response.ok) {
					throw new Error(data.error || "Failed to remove member");
				}

				toast({
					title: "Success",
					description: "Member removed successfully",
				});

				// Update local state directly, no need to refetch the whole list
				setMembers((prevMembers) =>
					prevMembers.filter((member) => member.user_id !== userId)
				);
				setOptimisticChanges((prev) => {
					const newRemovedMembers = new Set(prev.removedMembers);
					const newProcessingRemovals = new Set(prev.processingRemovals);
					newRemovedMembers.delete(userId);
					newProcessingRemovals.delete(userId);
					return {
						...prev,
						removedMembers: newRemovedMembers,
						processingRemovals: newProcessingRemovals,
					};
				});
			} catch (error) {
				// Rollback optimistic update
				setOptimisticChanges((prev) => {
					const newRemovedMembers = new Set(prev.removedMembers);
					const newProcessingRemovals = new Set(prev.processingRemovals);
					newRemovedMembers.delete(userId);
					newProcessingRemovals.delete(userId);
					return {
						...prev,
						removedMembers: newRemovedMembers,
						processingRemovals: newProcessingRemovals,
					};
				});

				const errorMessage =
					error instanceof Error ? error.message : "Failed to remove member";
				toast({
					title: "Error",
					description: errorMessage,
					variant: "destructive",
				});
			}
		},
		[selectedProject?.id, toast] // Removed setMembers from deps as it's stable
	);

	const handleLeaveProject = useCallback(async () => {
		if (!selectedProject?.id || !user) return;

		try {
			const response = await fetch(
				`/api/members/${user.id}?projectId=${selectedProject.id}`,
				{ method: "DELETE" }
			);

			const data = (await response.json()) as {
				error?: string;
				message?: string;
			};

			if (!response.ok) {
				throw new Error(data.error || "Failed to leave project");
			}

			toast({
				title: "Success",
				description: "You have left the project",
			});

			window.location.href = "/"; // Or redirect to a more appropriate page
		} catch (error) {
			const errorMessage =
				error instanceof Error ? error.message : "Failed to leave project";
			toast({
				title: "Error",
				description: errorMessage,
				variant: "destructive",
			});
		}
	}, [selectedProject?.id, user, toast]);

	const handleUpdateRole = useCallback(
		async (userId: string, newRole: MemberRole) => {
			if (!selectedProject?.id) return;

			const currentMember = members.find((m) => m.user_id === userId);
			if (!currentMember) return;

			const oldRole = currentMember.role;

			setOptimisticChanges((prev) => ({
				...prev,
				roleChanges: {
					...prev.roleChanges,
					[userId]: { newRole, oldRole, isProcessing: true },
				},
			}));

			try {
				const response = await fetch(`/api/members/${userId}`, {
					method: "PATCH",
					headers: { "Content-Type": "application/json" },
					body: JSON.stringify({
						role: newRole,
						projectId: selectedProject.id,
					}),
				});

				const data = (await response.json()) as {
					error?: string;
					message?: string;
				};

				if (!response.ok) {
					throw new Error(data.error || "Failed to update member role");
				}

				toast({
					title: "Success",
					description: "Member role updated successfully",
				});

				// Update local state directly
				setMembers((prevMembers) =>
					prevMembers.map((member) =>
						member.user_id === userId ? { ...member, role: newRole } : member
					)
				);
				setOptimisticChanges((prev) => {
					const newRoleChanges = { ...prev.roleChanges };
					delete newRoleChanges[userId];
					return { ...prev, roleChanges: newRoleChanges };
				});
			} catch (error) {
				// Rollback optimistic update
				setOptimisticChanges((prev) => {
					const newRoleChanges = { ...prev.roleChanges };
					// Restore old role or simply remove the processing state
					// For simplicity, just removing the change, UI will revert on next render if members state didn't change
					// or rely on the fact that setMembers wasn't called with newRole on error.
					// A more robust rollback might involve setting role back to oldRole in optimisticChanges too.
					delete newRoleChanges[userId];
					return { ...prev, roleChanges: newRoleChanges };
				});

				const errorMessage =
					error instanceof Error
						? error.message
						: "Failed to update member role";
				toast({
					title: "Error",
					description: errorMessage,
					variant: "destructive",
				});
			}
		},
		[selectedProject?.id, members, toast] // members is a dep because we read it for oldRole
	);

	// Effect to clear optimistic changes if the project itself changes
	// This is now partially handled in the main fetch effect, but good for explicit reset
	useEffect(() => {
		setOptimisticChanges({
			roleChanges: {},
			removedMembers: new Set(),
			processingRemovals: new Set(),
			removedInvites: new Set(),
			processingInviteRemovals: new Set(),
		});
	}, [selectedProject?.id]);

	// Exposed fetch function for manual refresh if needed
	const fetchMembers = useCallback(() => {
		if (selectedProject?.id) {
			performFetchAndSetMembers(String(selectedProject.id));
		}
	}, [selectedProject?.id, performFetchAndSetMembers]);

	const handleCancelInvite = useCallback(
		async (inviteId: number) => {
			if (!selectedProject?.id) return;

			// Optimistic update: immediately hide the invite
			setOptimisticChanges((prev) => ({
				...prev,
				removedInvites: new Set([...prev.removedInvites, inviteId]),
				processingInviteRemovals: new Set([
					...prev.processingInviteRemovals,
					inviteId,
				]),
			}));

			try {
				const response = await fetch("/api/my-invites", {
					method: "DELETE",
					headers: { "Content-Type": "application/json" },
					body: JSON.stringify({ inviteId }),
				});

				if (!response.ok) {
					throw new Error("Failed to cancel invite");
				}

				toast({
					title: "Success",
					description: "Invite cancelled successfully",
				});

				// Update local state directly
				setPendingInvites((prev) =>
					prev.filter((invite) => invite.id !== inviteId)
				);
				setOptimisticChanges((prev) => {
					const newRemovedInvites = new Set(prev.removedInvites);
					const newProcessingInviteRemovals = new Set(
						prev.processingInviteRemovals
					);
					newRemovedInvites.delete(inviteId);
					newProcessingInviteRemovals.delete(inviteId);
					return {
						...prev,
						removedInvites: newRemovedInvites,
						processingInviteRemovals: newProcessingInviteRemovals,
					};
				});
			} catch (error) {
				// Rollback optimistic update
				setOptimisticChanges((prev) => {
					const newRemovedInvites = new Set(prev.removedInvites);
					const newProcessingInviteRemovals = new Set(
						prev.processingInviteRemovals
					);
					newRemovedInvites.delete(inviteId);
					newProcessingInviteRemovals.delete(inviteId);
					return {
						...prev,
						removedInvites: newRemovedInvites,
						processingInviteRemovals: newProcessingInviteRemovals,
					};
				});

				const errorMessage =
					error instanceof Error ? error.message : "Failed to cancel invite";
				toast({
					title: "Error",
					description: errorMessage,
					variant: "destructive",
				});
			}
		},
		[selectedProject?.id, toast]
	);

	return {
		members: getDisplayMembers(),
		pendingInvites: getDisplayInvites(),
		isLoading,
		currentUserRole,
		optimisticChanges,
		handleInviteMember,
		handleRemoveMember,
		handleLeaveProject,
		handleUpdateRole,
		fetchMembers, // Expose the manual refresh function
		handleCancelInvite,
	};
};
