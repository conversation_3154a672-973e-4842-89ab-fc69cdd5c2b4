import React, { useState } from "react";
import { RichTextInput } from "../RichTextInput";

export const RichTextInputExamples: React.FC = () => {
  const [defaultValue, setDefaultValue] = useState(
    "<p>Este é um exemplo de texto rico com <strong>formatação</strong> e <em>estilos</em>.</p>"
  );
  const [compactValue, setCompactValue] = useState(
    "<p>Versão compacta do editor.</p>"
  );
  const [minimalValue, setMinimalValue] = useState(
    "Texto simples para uma linha"
  );
  const [errorValue, setErrorValue] = useState("");
  const [formValue, setFormValue] = useState("");

  return (
    <div className="max-w-4xl mx-auto p-6 space-y-8">
      <div>
        <h1 className="text-3xl font-bold text-gray-900 dark:text-white mb-2">
          RichTextInput Component
        </h1>
        <p className="text-gray-600 dark:text-gray-300">
          Exemplos de uso do componente RichTextInput em diferentes
          configurações.
        </p>
      </div>

      {/* Default Variant */}
      <section className="space-y-4">
        <h2 className="text-xl font-semibold text-gray-800 dark:text-gray-200">
          Variante Padrão
        </h2>
        <p className="text-sm text-gray-600 dark:text-gray-400">
          Editor completo com todas as funcionalidades de formatação.
        </p>
        <div className="space-y-2">
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
            Descrição do Produto
          </label>
          <RichTextInput
            value={defaultValue}
            onChange={setDefaultValue}
            placeholder="Digite a descrição do seu produto..."
            maxLength={500}
            showCounter={true}
            showToolbar={true}
            helperText="Use formatação rica para destacar informações importantes"
          />
        </div>
      </section>

      {/* Compact Variant */}
      <section className="space-y-4">
        <h2 className="text-xl font-semibold text-gray-800 dark:text-gray-200">
          Variante Compacta
        </h2>
        <p className="text-sm text-gray-600 dark:text-gray-400">
          Versão menor com toolbar simplificada, ideal para cards e componentes
          menores.
        </p>
        <div className="space-y-2">
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
            Descrição Curta
          </label>
          <RichTextInput
            value={compactValue}
            onChange={setCompactValue}
            placeholder="Uma breve descrição..."
            maxLength={200}
            variant="compact"
            showCounter={true}
            showToolbar={true}
            helperText="Versão mais compacta para espaços menores"
          />
        </div>
      </section>

      {/* Minimal Variant */}
      <section className="space-y-4">
        <h2 className="text-xl font-semibold text-gray-800 dark:text-gray-200">
          Variante Mínima
        </h2>
        <p className="text-sm text-gray-600 dark:text-gray-400">
          Uma única linha com formatação básica, ideal para títulos e campos
          simples.
        </p>
        <div className="space-y-2">
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
            Título do Artigo
          </label>
          <RichTextInput
            value={minimalValue}
            onChange={setMinimalValue}
            placeholder="Digite o título..."
            maxLength={100}
            variant="minimal"
            showCounter={true}
            showToolbar={true}
            helperText="Mantém em uma linha com formatação básica"
          />
        </div>
      </section>

      {/* Error State */}
      <section className="space-y-4">
        <h2 className="text-xl font-semibold text-gray-800 dark:text-gray-200">
          Estado de Erro
        </h2>
        <p className="text-sm text-gray-600 dark:text-gray-400">
          Como o componente aparece quando há um erro de validação.
        </p>
        <div className="space-y-2">
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
            Campo Obrigatório
          </label>
          <RichTextInput
            value={errorValue}
            onChange={setErrorValue}
            placeholder="Este campo é obrigatório..."
            maxLength={150}
            variant="compact"
            error={true}
            required={true}
            showCounter={true}
            helperText="Este campo é obrigatório e não pode ficar vazio"
          />
        </div>
      </section>

      {/* Disabled State */}
      <section className="space-y-4">
        <h2 className="text-xl font-semibold text-gray-800 dark:text-gray-200">
          Estado Desabilitado
        </h2>
        <p className="text-sm text-gray-600 dark:text-gray-400">
          Campo somente leitura ou desabilitado.
        </p>
        <div className="space-y-2">
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
            Campo Somente Leitura
          </label>
          <RichTextInput
            value="<p>Este conteúdo não pode ser <strong>editado</strong> no momento.</p>"
            onChange={() => {}}
            placeholder="Conteúdo somente leitura..."
            disabled={true}
            showToolbar={false}
            helperText="Este campo está desabilitado"
          />
        </div>
      </section>

      {/* Form Example */}
      <section className="space-y-4">
        <h2 className="text-xl font-semibold text-gray-800 dark:text-gray-200">
          Exemplo em Formulário
        </h2>
        <p className="text-sm text-gray-600 dark:text-gray-400">
          Integração com formulários e validação.
        </p>
        <form
          onSubmit={(e) => {
            e.preventDefault();
            alert(`Conteúdo enviado: ${formValue}`);
          }}
          className="space-y-4"
        >
          <div className="space-y-2">
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
              Comentário
            </label>
            <RichTextInput
              value={formValue}
              onChange={setFormValue}
              placeholder="Escreva seu comentário..."
              maxLength={300}
              variant="default"
              showCounter={true}
              showToolbar={true}
              required={true}
              id="form-comment"
              name="comment"
              helperText="Compartilhe seus pensamentos com formatação rica"
            />
          </div>
          <button
            type="submit"
            className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500"
          >
            Enviar Comentário
          </button>
        </form>
      </section>

      {/* No Toolbar Example */}
      <section className="space-y-4">
        <h2 className="text-xl font-semibold text-gray-800 dark:text-gray-200">
          Sem Barra de Ferramentas
        </h2>
        <p className="text-sm text-gray-600 dark:text-gray-400">
          Editor sem toolbar, útil quando você quer apenas capturar texto
          simples mas manter a formatação existente.
        </p>
        <div className="space-y-2">
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
            Notas Simples
          </label>
          <RichTextInput
            value="<p>Editor sem barra de ferramentas. A formatação existente é preservada.</p>"
            onChange={() => {}}
            placeholder="Digite suas notas..."
            maxLength={200}
            variant="compact"
            showToolbar={false}
            showCounter={true}
            helperText="Use atalhos do teclado para formatação (Ctrl+B, Ctrl+I)"
          />
        </div>
      </section>

      {/* Enhanced Features Example */}
      <section className="space-y-4">
        <h2 className="text-xl font-semibold text-gray-800 dark:text-gray-200">
          Funcionalidades Avançadas
        </h2>
        <p className="text-sm text-gray-600 dark:text-gray-400">
          Editor com suporte a code blocks e links (habilitado por padrão no
          modo default).
        </p>
        <div className="space-y-2">
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
            Documentação Técnica
          </label>
          <RichTextInput
            value='<p>Este editor suporta <strong>formatação rica</strong>, <a href="https://exemplo.com">links</a> e blocos de código:</p><pre><code>function exemplo() {
  console.log("Hello World!");
}</code></pre><p>Use a toolbar para adicionar links e code blocks facilmente.</p>'
            onChange={setDefaultValue}
            placeholder="Digite documentação com código e links..."
            maxLength={1000}
            variant="default"
            showCounter={true}
            showToolbar={true}
            enableCodeBlock={true}
            enableLink={true}
            helperText="Suporte completo a code blocks e links - use os ícones na toolbar"
          />
        </div>
      </section>

      {/* Compact with Code Blocks */}
      <section className="space-y-4">
        <h2 className="text-xl font-semibold text-gray-800 dark:text-gray-200">
          Versão Compacta com Code Blocks
        </h2>
        <p className="text-sm text-gray-600 dark:text-gray-400">
          Versão compacta com suporte a blocos de código habilitado
          explicitamente.
        </p>
        <div className="space-y-2">
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
            Snippet de Código
          </label>
          <RichTextInput
            value="<p>Exemplo de código:</p><pre><code>const result = data.map(item => item.value);</code></pre>"
            onChange={setCompactValue}
            placeholder="Cole seu código aqui..."
            maxLength={500}
            variant="compact"
            showCounter={true}
            showToolbar={true}
            enableCodeBlock={true}
            enableLink={true}
            helperText="Code blocks em versão compacta"
          />
        </div>
      </section>

      {/* Auto Focus Example */}
      <section className="space-y-4">
        <h2 className="text-xl font-semibold text-gray-800 dark:text-gray-200">
          Com Foco Automático
        </h2>
        <p className="text-sm text-gray-600 dark:text-gray-400">
          Campo que recebe foco automaticamente ao carregar.
        </p>
        <div className="space-y-2">
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
            Campo Principal
          </label>
          <RichTextInput
            value=""
            onChange={() => {}}
            placeholder="Este campo recebe foco automaticamente..."
            autoFocus={true}
            variant="default"
            showToolbar={true}
            helperText="Foco automático ao carregar a página"
          />
        </div>
      </section>

      {/* API Reference */}
      <section className="space-y-4">
        <h2 className="text-xl font-semibold text-gray-800 dark:text-gray-200">
          Referência da API
        </h2>
        <div className="bg-gray-50 dark:bg-gray-800 p-4 rounded-lg">
          <h3 className="font-semibold mb-2">Props Principais:</h3>
          <ul className="text-sm space-y-1 text-gray-600 dark:text-gray-300">
            <li>
              <strong>value:</strong> string - Conteúdo HTML do editor
            </li>
            <li>
              <strong>onChange:</strong> (value: string) =&gt; void - Callback
              quando o conteúdo muda
            </li>
            <li>
              <strong>variant:</strong> &quot;default&quot; |
              &quot;compact&quot; | &quot;minimal&quot; - Variante visual
            </li>
            <li>
              <strong>placeholder:</strong> string - Texto placeholder
            </li>
            <li>
              <strong>maxLength:</strong> number - Limite de caracteres
            </li>
            <li>
              <strong>showToolbar:</strong> boolean - Mostra/esconde toolbar
            </li>
            <li>
              <strong>showCounter:</strong> boolean - Mostra/esconde contador
            </li>
            <li>
              <strong>disabled:</strong> boolean - Desabilita o editor
            </li>
            <li>
              <strong>error:</strong> boolean - Estado de erro
            </li>
            <li>
              <strong>helperText:</strong> string - Texto de ajuda
            </li>
            <li>
              <strong>autoFocus:</strong> boolean - Foco automático
            </li>
            <li>
              <strong>required:</strong> boolean - Campo obrigatório
            </li>
            <li>
              <strong>enableCodeBlock:</strong> boolean - Habilita blocos de
              código
            </li>
            <li>
              <strong>enableLink:</strong> boolean - Habilita links (padrão:
              true no modo default)
            </li>
          </ul>
        </div>
      </section>
    </div>
  );
};

export default RichTextInputExamples;
