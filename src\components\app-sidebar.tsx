"use client";

import { Home, Setting<PERSON>, FileEdit, Bo<PERSON> } from "lucide-react";

import { Sidebar<PERSON>ogo } from "./sidebar-logo";

import {
  Sidebar,
  SidebarContent,
  SidebarFooter,
  SidebarGroup,
  SidebarGroupContent,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
  SidebarRail,
} from "@/components/ui/sidebar";

// Novos imports para os componentes do footer
import { DocsDropdownMenuItem } from "./docs-dropdown-menu-item";
import { ProjectsDropdownMenuItem } from "./projects-dropdown-menu-item";
import { UserDropdownMenuItem } from "./user-dropdown-menu-item";

import { useParams } from "next/navigation";
import Link from "next/link";

export function AppSidebar() {
  const { projectId } = useParams();
  const menuItems = [
    { title: "Home", url: `/${projectId}/dashboard`, icon: Home },
    { title: "Editor", url: `/${projectId}/editor`, icon: FileEdit },
    // {
    // 	title: 'Translations',
    // 	url: `/${projectId}/translations`,
    // 	icon: Languages,
    // },
    { title: "Ask AI", url: `/${projectId}/ask-ai`, icon: Bot },
    {
      title: "Settings",
      url: `/${projectId}/settings`,
      icon: Settings,
    },
  ];

  // Definição dos elementos do footer movida para o escopo da função AppSidebar
  const footerElements = [
    { id: "docs", component: DocsDropdownMenuItem, props: {} },
    { id: "projects", component: ProjectsDropdownMenuItem, props: {} },
    {
      id: "user",
      component: UserDropdownMenuItem,
      props: { className: "mt-auto" },
    },
  ];

  return (
    <Sidebar collapsible="icon">
      <SidebarRail />
      <div className="flex flex-col h-full bg-gradient-to-b from-info-300 to-wd-blueDark/10">
        <SidebarContent>
          <SidebarLogo />
          <SidebarGroup>
            <SidebarGroupContent>
              <SidebarMenu className="flex flex-col items-center gap-y-2 py-2">
                {menuItems.map((item) => (
                  <SidebarMenuItem key={item.title} className="w-full">
                    <SidebarMenuButton asChild size="lg">
                      <Link
                        href={item.url}
                        className="flex flex-col items-center justify-center text-center w-full h-fit"
                      >
                        <item.icon className="group-data-[state=expanded]:h-5 group-data-[state=expanded]:w-5 h-4 w-4" />
                        <span className="group-data-[state=collapsed]:hidden text-xs">
                          {item.title}
                        </span>
                      </Link>
                    </SidebarMenuButton>
                  </SidebarMenuItem>
                ))}
              </SidebarMenu>
            </SidebarGroupContent>
          </SidebarGroup>
        </SidebarContent>
        <SidebarFooter>
          <SidebarMenu className="flex flex-col flex-grow gap-y-2 py-2">
            {footerElements.map((item) => {
              const ItemComponent = item.component;
              return <ItemComponent key={item.id} {...item.props} />;
            })}
          </SidebarMenu>
        </SidebarFooter>
      </div>
    </Sidebar>
  );
}
