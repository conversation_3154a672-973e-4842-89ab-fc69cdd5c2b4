export type MemberRole = "Owner" | "Admin" | "Editor";
export type InviteStatus = "invited" | "accepted" | "declined";

export interface ProjectMember {
	user_id: string;
	email: string;
	role: MemberRole;
	is_creator: boolean;
}

export interface ProjectInvite {
	id: number;
	project_id: number;
	email: string;
	role: MemberRole;
	status: InviteStatus;
	created_at: string;
}

export interface PendingInvite {
	id: number;
	email: string;
	status: string;
	created_at: string;
	project?: {
		id: number;
		project_name: string;
	};
	project_id?: number;
}

export interface MembersResponse {
	members: ProjectMember[];
	pending_invites: PendingInvite[];
}

export interface InviteMemberRequest {
	email: string;
	projectId: string;
	role?: MemberRole;
}

export interface UpdateMemberRoleRequest {
	role: MemberRole;
	projectId: string;
}
