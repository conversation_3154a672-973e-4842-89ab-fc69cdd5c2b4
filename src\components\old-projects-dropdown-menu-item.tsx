"use client";

import { FolderGit2, Check } from "lucide-react";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
  DropdownMenuLabel,
  DropdownMenuSeparator,
} from "@/components/ui/dropdown-menu";
import { SidebarMenuButton, SidebarMenuItem } from "@/components/ui/sidebar";
import { useProject } from "@/contexts/ProjectContext/ProjectContextProvider";
import { Project } from "@/contexts/ProjectContext/types";
import React from "react";

export function ProjectsDropdownMenuItem() {
  const { projects, selectedProject, setSelectedProject, isLoading } =
    useProject();

  const groupedProjects: Record<string, Project[]> = projects.reduce(
    (acc, project) => {
      const companyName = project.company_name || "Others";
      if (!acc[companyName]) {
        acc[companyName] = [];
      }
      acc[companyName].push(project);
      return acc;
    },
    {} as Record<string, Project[]>
  );

  const companyNames = Object.keys(groupedProjects);

  if (isLoading) {
    // Optional: Render a loading state for the dropdown trigger or content
    // For now, we'll let it render normally, but the content might be empty or update quickly.
  }

  return (
    <SidebarMenuItem>
      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <SidebarMenuButton
            className="cursor-pointer flex items-center w-full"
            size="lg"
            disabled={isLoading || projects.length === 0}
          >
            <div className="flex flex-col items-center justify-center text-center flex-grow h-fit">
              <FolderGit2 className="group-data-[state=expanded]:h-5 group-data-[state=expanded]:w-5 h-4 w-4" />
              <span className="group-data-[state=collapsed]:hidden">
                Projects
              </span>
            </div>
          </SidebarMenuButton>
        </DropdownMenuTrigger>
        <DropdownMenuContent>
          {projects.length === 0 && !isLoading && (
            <DropdownMenuItem disabled>No projects found</DropdownMenuItem>
          )}
          {isLoading && (
            <DropdownMenuItem disabled>Loading projects...</DropdownMenuItem>
          )}
          {!isLoading &&
            companyNames.map((companyName, index) => {
              const companyProjects = groupedProjects[companyName];
              return (
                <React.Fragment key={companyName}>
                  <DropdownMenuLabel>{companyName}</DropdownMenuLabel>
                  {companyProjects.map((project) => (
                    <DropdownMenuItem
                      key={project.id}
                      onClick={() => setSelectedProject(project)}
                      className="flex items-center justify-between"
                    >
                      {project.project_name}
                      {selectedProject?.id === project.id && (
                        <Check className="h-4 w-4 ml-2" />
                      )}
                    </DropdownMenuItem>
                  ))}
                  {index < companyNames.length - 1 && <DropdownMenuSeparator />}
                </React.Fragment>
              );
            })}
        </DropdownMenuContent>
      </DropdownMenu>
    </SidebarMenuItem>
  );
}
