import { NextRequest, NextResponse } from "next/server";

export const runtime = "edge";

// API Response Types
interface ListApiResponse {
	success: boolean;
	result: WebAnalyticsSite[];
	errors: unknown[];
	messages: unknown[];
	result_info: unknown;
}

interface GraphQLResponse<T> {
	data?: T;
	errors?: { message: string; [key: string]: unknown }[];
}

interface WebAnalyticsSite {
	site_tag: string;
	site_token: string;
	host: string;
	created: string;
	auto_install: boolean;
	snippet?: string;
	is_host_regex?: boolean;
}

interface WebAnalyticsTopNsResponse {
	success: boolean;
	siteTag: string;
	dateRange: { since: string; until: string };
	data: {
		countries: Array<{
			count: number;
			sum: { visits: number };
			dimensions: { metric: string };
		}>;
		topReferers: Array<{
			count: number;
			sum: { visits: number };
			dimensions: { metric: string };
		}>;
		topPaths: Array<{
			count: number;
			sum: { visits: number };
			dimensions: { metric: string };
		}>;
		topBrowsers: Array<{
			count: number;
			sum: { visits: number };
			dimensions: { metric: string };
		}>;
		topOSs: Array<{
			count: number;
			sum: { visits: number };
			dimensions: { metric: string };
		}>;
		topDeviceTypes: Array<{
			count: number;
			sum: { visits: number };
			dimensions: { metric: string };
		}>;
	};
}

interface TimeseriesResponse {
	success: boolean;
	siteTag: string;
	dateRange: { since: string; until: string };
	groupBy?: string;
	data: TimeseriesData;
}

/**
 * CLOUDFLARE WEB ANALYTICS LIMITS INTERFACE
 *
 * These are the critical limits that govern all Web Analytics queries:
 *
 * HISTORICAL DATA RETENTION (notOlderThan):
 * • Cloudflare typically retains Web Analytics data for 184 days
 * • This is configurable per account and fetched via GraphQL API
 * • Data older than this limit cannot be queried
 *
 * MAXIMUM QUERY RANGE (maxDuration):
 * • Cloudflare limits single queries to 93 days maximum
 * • This prevents overwhelming the GraphQL API with large time ranges
 * • Queries exceeding this limit will return errors
 *
 * SAFETY BUFFER IMPLEMENTATION:
 * • Backend applies 1-day safety buffer to maxDuration (92 days usable)
 * • This prevents edge cases where timezone differences cause API errors
 * • Better to be conservative than have queries fail
 */
interface WebAnalyticsLimitsResponse {
	success: boolean;
	limits: {
		enabled: boolean;
		notOlderThan: number; // seconds - Historical data retention limit
		maxDuration: number; // seconds - Maximum query range per request
		notOlderThanDays: number; // days - Historical data retention limit
		maxDurationDays: number; // days - Maximum query range per request
		isUsingFallback: boolean; // true if using fallback values, false if from GraphQL API
	};
}

interface TimeRangePreset {
	label: string;
	value: string;
	since: string;
	until: string;
	isCustom?: boolean;
}

interface PresetsResponse {
	success: boolean;
	presets: TimeRangePreset[];
}

interface ValidateRangeResponse {
	success: boolean;
	valid: boolean;
	error?: string;
	maxAllowedDays?: number;
}

interface DateLimitsResponse {
	success: boolean;
	minDate: string; // ISO string
	maxDate: string; // ISO string
	notOlderThanDays: number;
	maxDurationDays: number;
}

interface CreateTimeRangeResponse {
	success: boolean;
	timeRange: TimeRangePreset;
}

/**
 * PRESET RANGES FOR CLOUDFLARE WEB ANALYTICS
 *
 * These ranges are carefully selected to work within Cloudflare's limitations:
 * • All presets are well within the 93-day maximum query range
 * • Presets are dynamically filtered based on account's retention limits
 * • Removed problematic ranges (30min, 6h, 72h, 14d, 21d) that caused UI/API issues
 * • Added longer ranges (2 months, 3 months) for better analytics insights
 *
 * PRESET FILTERING:
 * • Backend filters presets based on account's notOlderThan limit
 * • If account only has 90 days retention, 3-month preset is automatically removed
 * • This prevents users from selecting ranges that would fail API validation
 */
const PRESET_RANGES: TimeRangePreset[] = [
	{
		label: "Last 24 hours",
		value: "24h",
		since: "-24h",
		until: "now",
	},
	{
		label: "Last 7 days",
		value: "7d",
		since: "-7d",
		until: "now",
	},
	{
		label: "Last 1 month",
		value: "30d",
		since: "-30d",
		until: "now",
	},
	{
		label: "Last 2 months",
		value: "60d",
		since: "-60d",
		until: "now",
	},
	{
		label: "Last 3 months",
		value: "90d",
		since: "-90d",
		until: "now",
	},
];

/**
 * GET CLOUDFLARE WEB ANALYTICS LIMITS
 *
 * This function fetches the actual account limits from Cloudflare's GraphQL API.
 *
 * CLOUDFLARE GRAPHQL API SPECIFICS:
 * • Uses the rumPageloadEventsAdaptiveGroups settings node
 * • This node contains the actual retention and query limits for the account
 * • Different accounts may have different limits based on their plan
 *
 * FALLBACK STRATEGY:
 * • If GraphQL API fails or returns no data, use known common limits
 * • 184 days (15,897,600 seconds) is the standard retention for most accounts
 * • 93 days (8,035,200 seconds) is the standard maximum query range
 * • These fallback values are based on Cloudflare's documented limits
 *
 * CONVERSION LOGIC:
 * • API returns limits in seconds, we convert to days for easier handling
 * • Math.floor ensures we don't exceed limits due to rounding
 * • This conservative approach prevents API errors
 *
 * ERROR HANDLING:
 * • Silent catch on API errors - fallback values are reliable
 * • Logs indicate whether values are from API or fallback
 * • Frontend can handle both scenarios transparently
 */
async function getWebAnalyticsLimits(accountId: string, apiToken: string) {
	try {
		const response = await fetch(
			`https://api.cloudflare.com/client/v4/graphql`,
			{
				method: "POST",
				headers: {
					Authorization: `Bearer ${apiToken}`,
					"Content-Type": "application/json",
				},
				body: JSON.stringify({
					query: RUM_ANALYTICS_LIMITS_QUERY,
					variables: {
						accountTag: accountId,
					},
				}),
			}
		);

		if (response.ok) {
			const data = (await response.json()) as GraphQLResponse<{
				viewer: {
					accounts: {
						settings: {
							rumPageloadEventsAdaptiveGroups: {
								enabled: boolean;
								notOlderThan: number;
								maxDuration: number;
							};
						};
					}[];
				};
			}>;

			if (!data.errors) {
				const account = data.data?.viewer?.accounts?.[0];
				const settings = account?.settings?.rumPageloadEventsAdaptiveGroups;

				if (settings) {
					const notOlderThanDays = Math.floor(
						settings.notOlderThan / (24 * 60 * 60)
					);
					const maxDurationDays = Math.floor(
						settings.maxDuration / (24 * 60 * 60)
					);

					return {
						enabled: settings.enabled,
						notOlderThan: settings.notOlderThan,
						maxDuration: settings.maxDuration,
						notOlderThanDays,
						maxDurationDays,
						isUsingFallback: false, // Successfully fetched from GraphQL API
					};
				}
			}
		}
	} catch {
		// Silent catch, will use fallback values
		// This ensures the application always works even if GraphQL API is unavailable
	}

	// FALLBACK TO KNOWN CLOUDFLARE LIMITS
	// These values are based on Cloudflare's standard Web Analytics limits
	// and are used when the GraphQL API is unavailable or returns no data
	return {
		enabled: true,
		notOlderThan: ********, // 184 days in seconds (standard retention)
		maxDuration: 8035200, // 93 days in seconds (standard max query range)
		notOlderThanDays: 184, // 184 days historical retention
		maxDurationDays: 93, // 93 days maximum query range
		isUsingFallback: true, // Using fallback values
	};
}

// Helper function to parse preset range to days
function parsePresetToDays(value: string): number {
	const match = value.match(/^(\d+)([dhm])$/);
	if (match) {
		const num = parseInt(match[1]);
		const unit = match[2];

		switch (unit) {
			case "d":
				return num;
			case "h":
				return num / 24;
			case "m":
				return num / 1440;
			default:
				return 0;
		}
	}
	return 0;
}

/**
 * CALCULATE DAYS DIFFERENCE BETWEEN DATES
 *
 * This function calculates the absolute difference in days between two dates.
 * Used for validating that custom date ranges don't exceed Cloudflare's 93-day limit.
 *
 * TECHNICAL DETAILS:
 * • Uses Math.abs() to handle both forward and backward date selections
 * • Math.floor() ensures we don't round up and exceed limits
 * • Converts milliseconds to days using standard conversion (1000 * 60 * 60 * 24)
 */
function calculateDaysDifference(fromDate: string, toDate: string): number {
	const from = new Date(fromDate);
	const to = new Date(toDate);
	return Math.abs(
		Math.floor((to.getTime() - from.getTime()) / (1000 * 60 * 60 * 24))
	);
}

// Sub-types for TimeseriesData
type PerformanceDataItem = {
	count: number;
	aggregation: { pageLoadTime: number };
	dimensions: { ts: string };
};
type TotalPerformanceDataItem = {
	aggregation: { pageLoadTime: number };
};
type WebVitalsDataItem = {
	count: number;
	sum: { [key: string]: number };
};
type VisitsDeltaDataItem = {
	sum: { visits: number };
};
type PageviewsDeltaDataItem = {
	count: number;
};
type PerformanceDeltaDataItem = {
	count: number;
	aggregation: { pageLoadTime: number };
};

type TimeseriesData = {
	visits?: { sum: { visits: number }; dimensions: { ts: string } }[];
	pageviews?: { count: number; dimensions: { ts: string } }[];
	series?: {
		count: number;
		sum: { visits: number };
		dimensions: { ts: string; metric: string };
	}[];
	performance?: PerformanceDataItem[];
	totalPerformance?: TotalPerformanceDataItem[];
	lcp?: WebVitalsDataItem[];
	inp?: WebVitalsDataItem[];
	cls?: WebVitalsDataItem[];
	visitsDelta?: VisitsDeltaDataItem[];
	pageviewsDelta?: PageviewsDeltaDataItem[];
	performanceDelta?: PerformanceDeltaDataItem[];
};

// GraphQL Data Types
type TimeseriesGroupedGraphQLData = {
	viewer: {
		accounts: {
			series: TimeseriesData["series"];
		}[];
	};
};

type SparklineGraphQLData = {
	viewer: {
		accounts: (Partial<TimeseriesData> & { __typename: string })[];
	};
};

type TopNsData = WebAnalyticsTopNsResponse["data"];

type TopNsGraphQLData = {
	viewer: {
		accounts: TopNsData[];
	};
};

// Query para períodos até 24 horas (intervalos de 15 minutos)
const RUM_SPARKLINE_FIFTEEN_MINUTES_QUERY = `
  query RumSparklineBydatetimeFifteenMinutes($accountTag: string, $visitsFilter: ZoneHttpRequestsAdaptiveGroupsFilter_InputObject, $pageviewsFilter: ZoneHttpRequestsAdaptiveGroupsFilter_InputObject) {
    viewer {
      accounts(filter: {accountTag: $accountTag}) {
        visits: rumPageloadEventsAdaptiveGroups(limit: 5000, filter: $visitsFilter) {
          sum {
            visits
          }
          dimensions {
            ts: datetimeFifteenMinutes
          }
        }
        pageviews: rumPageloadEventsAdaptiveGroups(limit: 5000, filter: $pageviewsFilter) {
          count
          dimensions {
            ts: datetimeFifteenMinutes
          }
        }
      }
    }
  }
`;

// Query para períodos maiores que 24 horas (intervalos de 1 hora)
const RUM_SPARKLINE_HOUR_QUERY = `
  query RumSparklineBydatetimeHour($accountTag: string, $visitsFilter: ZoneHttpRequestsAdaptiveGroupsFilter_InputObject, $pageviewsFilter: ZoneHttpRequestsAdaptiveGroupsFilter_InputObject) {
    viewer {
      accounts(filter: {accountTag: $accountTag}) {
        visits: rumPageloadEventsAdaptiveGroups(limit: 5000, filter: $visitsFilter) {
          sum {
            visits
          }
          dimensions {
            ts: datetimeHour
          }
        }
        pageviews: rumPageloadEventsAdaptiveGroups(limit: 5000, filter: $pageviewsFilter) {
          count
          dimensions {
            ts: datetimeHour
          }
        }
      }
    }
  }
`;

const RUM_TIMESERIES_GROUPED_QUERY = `
  query RumAnalyticsTimeseriesBydatetimeHourGroupedBy($accountTag: string, $filter: AccountRumPageloadEventsAdaptiveGroupsFilter_InputObject) {
    viewer {
      accounts(filter: {accountTag: $accountTag}) {
        series: rumPageloadEventsAdaptiveGroups(limit: 5000, filter: $filter) {
          count
          avg {
            sampleInterval
          }
          sum {
            visits
          }
          dimensions {
            metric: DIMENSION_PLACEHOLDER
            ts: datetimeHour
          }
        }
      }
    }
  }
`;

const RUM_ANALYTICS_TOPNS_QUERY = `
  query GetRumAnalyticsTopNs($accountTag: string, $filter: AccountRumPageloadEventsAdaptiveGroupsFilter_InputObject, $order: string) {
    viewer {
      accounts(filter: {accountTag: $accountTag}) {
        topReferers: rumPageloadEventsAdaptiveGroups(filter: $filter, limit: 200, orderBy: [$order]) {
          count
          avg {
            sampleInterval
          }
          sum {
            visits
          }
          dimensions {
            metric: refererHost
          }
        }
        topPaths: rumPageloadEventsAdaptiveGroups(filter: $filter, limit: 200, orderBy: [$order]) {
          count
          avg {
            sampleInterval
          }
          sum {
            visits
          }
          dimensions {
            metric: requestPath
          }
        }
        topBrowsers: rumPageloadEventsAdaptiveGroups(filter: $filter, limit: 200, orderBy: [$order]) {
          count
          avg {
            sampleInterval
          }
          sum {
            visits
          }
          dimensions {
            metric: userAgentBrowser
          }
        }
        topOSs: rumPageloadEventsAdaptiveGroups(filter: $filter, limit: 200, orderBy: [$order]) {
          count
          avg {
            sampleInterval
          }
          sum {
            visits
          }
          dimensions {
            metric: userAgentOS
          }
        }
        topDeviceTypes: rumPageloadEventsAdaptiveGroups(filter: $filter, limit: 200, orderBy: [$order]) {
          count
          avg {
            sampleInterval
          }
          sum {
            visits
          }
          dimensions {
            metric: deviceType
          }
        }
        countries: rumPageloadEventsAdaptiveGroups(filter: $filter, limit: 200, orderBy: [$order]) {
          count
          avg {
            sampleInterval
          }
          sum {
            visits
          }
          dimensions {
            metric: countryName
          }
        }
      }
    }
  }
`;

// Query to get Web Analytics limits for the account
const RUM_ANALYTICS_LIMITS_QUERY = `
  query GetWebAnalyticsLimits($accountTag: string) {
    viewer {
      accounts(filter: { accountTag: $accountTag }) {
        settings {
          rumPageloadEventsAdaptiveGroups {
            enabled
            notOlderThan
            maxDuration
          }
        }
      }
    }
  }
`;

// Função para converter intervalos relativos para datas absolutas
function parseTimeRange(since: string, until: string) {
	let sinceDate: Date;
	let untilDate: Date;

	if (until === "now") {
		untilDate = new Date();
	} else {
		untilDate = new Date(until);
	}

	if (since.startsWith("-")) {
		// Parse diferentes formatos: -7d, -24h, -6h, -30m
		const match = since.match(/^-(\d+)([dhm])$/);
		if (match) {
			const value = parseInt(match[1]);
			const unit = match[2];

			sinceDate = new Date(untilDate);

			switch (unit) {
				case "d": // dias
					sinceDate.setDate(sinceDate.getDate() - value);
					break;
				case "h": // horas
					sinceDate.setHours(sinceDate.getHours() - value);
					break;
				case "m": // minutos
					sinceDate.setMinutes(sinceDate.getMinutes() - value);
					break;
				default:
					sinceDate.setDate(sinceDate.getDate() - 7); // fallback
			}
		} else {
			sinceDate = new Date(untilDate);
			sinceDate.setDate(sinceDate.getDate() - 7); // fallback
		}
	} else {
		sinceDate = new Date(since);
	}

	return { sinceDate, untilDate };
}

/**
 * DETERMINE GRAPHQL QUERY GRANULARITY
 *
 * Cloudflare's Web Analytics GraphQL API supports different time granularities:
 * • 15-minute intervals (datetimeFifteenMinutes) for short periods
 * • 1-hour intervals (datetimeHour) for longer periods
 *
 * GRANULARITY SELECTION LOGIC:
 * • 15-minute intervals: Used for periods up to 24 hours
 * • 1-hour intervals: Used for periods longer than 24 hours
 * • This provides optimal data resolution without overwhelming the API
 *
 * CLOUDFLARE API BEHAVIOR:
 * • Higher granularity (15-min) gives more data points for short periods
 * • Lower granularity (1-hour) is more efficient for longer periods
 * • API performance is better when appropriate granularity is selected
 *
 * SUPPORTED PERIOD EXAMPLES:
 * • "-6h", "-12h", "-24h", "-1d" → 15-minute intervals
 * • "-7d", "-30d", "-90d" → 1-hour intervals
 */
function shouldUseFifteenMinutes(since: string): boolean {
	const match = since.match(/^-(\d+)([dhm])$/);
	if (match) {
		const value = parseInt(match[1]);
		const unit = match[2];

		// Use 15-minute intervals for periods up to 24 hours
		if (unit === "h" && value <= 24) return true;
		if (unit === "m") return true;
		if (unit === "d" && value <= 1) return true;
	}

	return false;
}

export async function GET(request: NextRequest) {
	try {
		const { searchParams } = new URL(request.url);
		const action = searchParams.get("action") || "list";
		const siteTag = searchParams.get("siteTag");
		const since = searchParams.get("since") || "-7d";
		const until = searchParams.get("until") || "now";
		const groupBy = searchParams.get("groupBy"); // refererHost, countryName, userAgentBrowser, etc.
		const projectName = searchParams.get("projectName");

		const accountId = process.env.CLOUDFLARE_ACCOUNT_ID;
		const apiToken = process.env.CLOUDFLARE_API_TOKEN;

		if (!accountId || !apiToken) {
			return NextResponse.json(
				{
					success: false,
					error: "Cloudflare credentials not configured",
				},
				{ status: 500 }
			);
		}

		if (action === "list") {
			if (!projectName) {
				return NextResponse.json(
					{
						success: false,
						error: "projectName query parameter is required for action=list",
					},
					{ status: 400 }
				);
			}
			// List Web Analytics sites and find a match
			const response = await fetch(
				`https://api.cloudflare.com/client/v4/accounts/${accountId}/rum/site_info/list`,
				{
					headers: {
						Authorization: `Bearer ${apiToken}`,
						"Content-Type": "application/json",
					},
				}
			);

			if (!response.ok) {
				const error = await response.text();
				console.error("Cloudflare API error:", error);
				return NextResponse.json(
					{
						success: false,
						error: `Cloudflare API error: ${response.status} ${response.statusText}`,
						debug: error,
					},
					{ status: response.status }
				);
			}

			const data = (await response.json()) as ListApiResponse;

			if (!data.success) {
				return NextResponse.json(
					{
						success: false,
						error: "Cloudflare API returned error",
						debug: data,
					},
					{ status: 400 }
				);
			}

			// Logic to find the best matching site
			const keywords = projectName
				.split(/[-_.]/)
				.filter((word) => word.length > 2);

			let bestSite: WebAnalyticsSite | undefined;
			let bestScore = 0;
			const MINIMUM_CONFIDENCE_SCORE = 1000;

			data.result?.forEach((site: WebAnalyticsSite) => {
				const hostString = site.host || "";
				let score = 0;

				if (hostString.includes(projectName)) {
					score += MINIMUM_CONFIDENCE_SCORE;
				}

				const matchingKeywords = keywords.filter((keyword) =>
					hostString.toLowerCase().includes(keyword.toLowerCase())
				);
				score += matchingKeywords.length * 10;

				if (score > bestScore) {
					bestSite = site;
					bestScore = score;
				}
			});

			let matchingSite = bestSite;
			if (bestScore < MINIMUM_CONFIDENCE_SCORE) {
				matchingSite = undefined; // Reject the match
			}

			if (matchingSite) {
				return NextResponse.json({
					success: true,
					site: {
						site_tag: matchingSite.site_tag,
						host: matchingSite.host,
					},
				});
			}
			return NextResponse.json(
				{
					success: false,
					error: `No matching Web Analytics site found for project "${projectName}"`,
					debug: {
						searchedKeywords: keywords,
						availableSites: data.result?.map((s) => s.host),
					},
				},
				{ status: 404 }
			);
		}

		if (action === "sparkline" || action === "timeseries") {
			// Get sparkline/timeseries data
			if (!siteTag) {
				return NextResponse.json(
					{
						success: false,
						error:
							"siteTag parameter is required for sparkline/timeseries data",
					},
					{ status: 400 }
				);
			}

			// Parse time range
			const { sinceDate, untilDate } = parseTimeRange(since, until);

			// Build filters following exact HAR structure
			const baseFilter = {
				AND: [
					{
						datetime_geq: sinceDate.toISOString(),
						datetime_leq: untilDate.toISOString(),
					},
					{
						OR: [{ siteTag: siteTag }],
					},
					{
						bot: 0,
					},
				],
			};

			// Determine which query to use based on time range
			const useFifteenMinutes = shouldUseFifteenMinutes(since);
			const query = useFifteenMinutes
				? RUM_SPARKLINE_FIFTEEN_MINUTES_QUERY
				: RUM_SPARKLINE_HOUR_QUERY;

			// Build variables following exact HAR structure
			const variables = {
				accountTag: accountId,
				visitsFilter: baseFilter,
				pageviewsFilter: baseFilter,
			};

			// Add groupBy filter if specified (for timeseries)
			if (groupBy && action === "timeseries") {
				// For timeseries grouped by dimension, we use a different query
				const timeseriesQuery = RUM_TIMESERIES_GROUPED_QUERY.replace(
					"DIMENSION_PLACEHOLDER",
					groupBy
				);

				const response = await fetch(
					`https://api.cloudflare.com/client/v4/graphql`,
					{
						method: "POST",
						headers: {
							Authorization: `Bearer ${apiToken}`,
							"Content-Type": "application/json",
						},
						body: JSON.stringify({
							query: timeseriesQuery,
							variables: {
								accountTag: accountId,
								filter: baseFilter,
							},
						}),
					}
				);

				if (!response.ok) {
					const error = await response.text();
					console.error("Cloudflare GraphQL API error:", error);
					return NextResponse.json(
						{
							success: false,
							error: `Cloudflare GraphQL API error: ${response.status} ${response.statusText}`,
							debug: error,
						},
						{ status: response.status }
					);
				}

				const data =
					(await response.json()) as GraphQLResponse<TimeseriesGroupedGraphQLData>;

				if (data.errors) {
					console.error("GraphQL errors:", data.errors);
					return NextResponse.json(
						{
							success: false,
							error: "GraphQL query failed",
							debug: data.errors,
						},
						{ status: 400 }
					);
				}

				const account = data.data?.viewer?.accounts?.[0];
				if (!account) {
					return NextResponse.json(
						{
							success: false,
							error: "No account data found",
						},
						{ status: 404 }
					);
				}

				return NextResponse.json({
					success: true,
					siteTag,
					dateRange: {
						since: sinceDate.toISOString(),
						until: untilDate.toISOString(),
					},
					groupBy,
					data: {
						series: account.series || [],
					},
				} as TimeseriesResponse);
			} else {
				// Regular sparkline query
				const response = await fetch(
					`https://api.cloudflare.com/client/v4/graphql`,
					{
						method: "POST",
						headers: {
							Authorization: `Bearer ${apiToken}`,
							"Content-Type": "application/json",
						},
						body: JSON.stringify({
							query,
							variables,
						}),
					}
				);

				if (!response.ok) {
					const error = await response.text();
					console.error("Cloudflare GraphQL API error:", error);
					return NextResponse.json(
						{
							success: false,
							error: `Cloudflare GraphQL API error: ${response.status} ${response.statusText}`,
							debug: error,
						},
						{ status: response.status }
					);
				}

				const data =
					(await response.json()) as GraphQLResponse<SparklineGraphQLData>;

				if (data.errors) {
					console.error("GraphQL errors:", data.errors);
					return NextResponse.json(
						{
							success: false,
							error: "GraphQL query failed",
							debug: data.errors,
						},
						{ status: 400 }
					);
				}

				const account = data.data?.viewer?.accounts?.[0];
				if (!account) {
					return NextResponse.json(
						{
							success: false,
							error: "No account data found",
						},
						{ status: 404 }
					);
				}

				return NextResponse.json({
					success: true,
					siteTag,
					dateRange: {
						since: sinceDate.toISOString(),
						until: untilDate.toISOString(),
					},
					data: {
						visits: account.visits || [],
						pageviews: account.pageviews || [],
					},
				} as TimeseriesResponse);
			}
		}

		if (action === "topns") {
			// Get TopNs data
			if (!siteTag) {
				return NextResponse.json(
					{
						success: false,
						error: "siteTag parameter is required for topns data",
					},
					{ status: 400 }
				);
			}

			const { sinceDate, untilDate } = parseTimeRange(since, until);

			const filter = {
				AND: [
					{
						datetime_geq: sinceDate.toISOString(),
						datetime_leq: untilDate.toISOString(),
					},
					{
						OR: [{ siteTag: siteTag }],
					},
					{
						bot: 0,
					},
				],
			};

			const response = await fetch(
				`https://api.cloudflare.com/client/v4/graphql`,
				{
					method: "POST",
					headers: {
						Authorization: `Bearer ${apiToken}`,
						"Content-Type": "application/json",
					},
					body: JSON.stringify({
						query: RUM_ANALYTICS_TOPNS_QUERY,
						variables: {
							accountTag: accountId,
							filter,
							order: "sum_visits_DESC",
						},
					}),
				}
			);

			if (!response.ok) {
				const error = await response.text();
				console.error("Cloudflare GraphQL API error:", error);
				return NextResponse.json(
					{
						success: false,
						error: `Cloudflare GraphQL API error: ${response.status} ${response.statusText}`,
						debug: error,
					},
					{ status: response.status }
				);
			}

			const data = (await response.json()) as GraphQLResponse<TopNsGraphQLData>;

			if (data.errors) {
				console.error("GraphQL errors:", data.errors);
				return NextResponse.json(
					{
						success: false,
						error: "GraphQL query failed",
						debug: data.errors,
					},
					{ status: 400 }
				);
			}

			const account = data.data?.viewer?.accounts?.[0];
			if (!account) {
				return NextResponse.json(
					{
						success: false,
						error: "No account data found",
					},
					{ status: 404 }
				);
			}

			return NextResponse.json({
				success: true,
				siteTag,
				dateRange: {
					since: sinceDate.toISOString(),
					until: untilDate.toISOString(),
				},
				data: {
					countries: account.countries || [],
					topReferers: account.topReferers || [],
					topPaths: account.topPaths || [],
					topBrowsers: account.topBrowsers || [],
					topOSs: account.topOSs || [],
					topDeviceTypes: account.topDeviceTypes || [],
				},
			} as WebAnalyticsTopNsResponse);
		}

		if (action === "limits") {
			/**
			 * FETCH CLOUDFLARE WEB ANALYTICS ACCOUNT LIMITS
			 *
			 * This endpoint returns the actual limits for the Cloudflare account.
			 * Critical for determining what date ranges users can select.
			 *
			 * LIMIT DETECTION:
			 * • Attempts to fetch real limits from Cloudflare GraphQL API
			 * • Falls back to standard limits (184 days retention, 93 days max range)
			 * • Fallback detection helps with debugging and monitoring
			 *
			 * USAGE:
			 * • Frontend uses these values to disable invalid dates in calendar
			 * • Backend uses these values to validate all date range requests
			 * • Both seconds and days provided for different calculation needs
			 */
			const limits = await getWebAnalyticsLimits(accountId, apiToken);

			if (limits.isUsingFallback) {
				console.warn(
					"[LIMITS] Using fallback values - Cloudflare GraphQL API unavailable"
				);
			}

			return NextResponse.json({
				success: true,
				limits,
			} as WebAnalyticsLimitsResponse);
		}

		if (action === "presets") {
			/**
			 * GET AVAILABLE PRESET RANGES
			 *
			 * This endpoint returns only the preset ranges that are valid for the account.
			 *
			 * DYNAMIC FILTERING:
			 * • Fetches account limits to determine available retention period
			 * • Filters out presets that exceed the account's notOlderThan limit
			 * • For example, if account only has 90 days retention, "Last 3 months" is removed
			 *
			 * BUSINESS LOGIC:
			 * • Prevents users from seeing unusable options in the UI
			 * • Reduces confusion and failed API calls
			 * • Adapts automatically to different Cloudflare account plans
			 *
			 * PRESET SAFETY:
			 * • All presets are well within the 93-day maximum query range
			 * • Focus is on filtering by historical retention (184-day limit)
			 * • No preset exceeds 90 days, so maxDuration is never an issue
			 */
			const limits = await getWebAnalyticsLimits(accountId, apiToken);

			if (limits.isUsingFallback) {
				console.warn(
					"[PRESETS] Using fallback values - Cloudflare GraphQL API unavailable"
				);
			}

			const availablePresets = PRESET_RANGES.filter((preset) => {
				const days = parsePresetToDays(preset.value);
				return days <= limits.notOlderThanDays;
			});

			return NextResponse.json({
				success: true,
				presets: availablePresets,
			} as PresetsResponse);
		}

		if (action === "validate-range") {
			/**
			 * VALIDATE CUSTOM DATE RANGE AGAINST CLOUDFLARE LIMITS
			 *
			 * This endpoint performs comprehensive validation of custom date ranges.
			 * Implements both the 93-day maximum query range and 184-day retention limits.
			 *
			 * VALIDATION PROCESS:
			 * 1. Check if range exceeds maximum query duration (93 days)
			 * 2. Check if start date is beyond historical retention (184 days)
			 * 3. Apply 1-day safety buffer to prevent edge case failures
			 *
			 * SAFETY BUFFER EXPLANATION:
			 * • Cloudflare's 93-day limit can be affected by timezone differences
			 * • We use 92 days (maxDurationDays - 1) as the practical limit
			 * • This prevents queries that might fail due to time zone edge cases
			 * • Better to be conservative than have API calls fail unexpectedly
			 *
			 * ERROR HANDLING:
			 * • Returns specific error messages for different validation failures
			 * • Includes maxAllowedDays for frontend to update UI dynamically
			 * • Distinguishes between range-too-long vs date-too-old errors
			 */
			const fromDate = searchParams.get("fromDate");
			const toDate = searchParams.get("toDate");

			if (!fromDate || !toDate) {
				return NextResponse.json(
					{
						success: false,
						valid: false,
						error: "fromDate and toDate parameters are required",
					},
					{ status: 400 }
				);
			}

			const limits = await getWebAnalyticsLimits(accountId, apiToken);

			if (limits.isUsingFallback) {
				console.warn(
					"[VALIDATE-RANGE] Using fallback values - Cloudflare GraphQL API unavailable"
				);
			}

			const daysDiff = calculateDaysDifference(fromDate, toDate);
			const maxAllowedDays = limits.maxDurationDays - 1; // Apply 1-day safety buffer

			// VALIDATION 1: Check if range exceeds maximum duration (93-day limit)
			if (daysDiff > maxAllowedDays) {
				return NextResponse.json({
					success: true,
					valid: false,
					error: `Date range cannot exceed ${maxAllowedDays} days`,
					maxAllowedDays,
				} as ValidateRangeResponse);
			}

			// VALIDATION 2: Check if dates are within historical retention (184-day limit)
			const today = new Date();
			const fromDateObj = new Date(fromDate);
			const daysSinceFromDate = Math.floor(
				(today.getTime() - fromDateObj.getTime()) / (1000 * 60 * 60 * 24)
			);

			if (daysSinceFromDate > limits.notOlderThanDays) {
				return NextResponse.json({
					success: true,
					valid: false,
					error: `Date cannot be older than ${limits.notOlderThanDays} days`,
					maxAllowedDays,
				} as ValidateRangeResponse);
			}

			return NextResponse.json({
				success: true,
				valid: true,
				maxAllowedDays,
			} as ValidateRangeResponse);
		}

		if (action === "date-limits") {
			/**
			 * CALCULATE SELECTABLE DATE BOUNDARIES
			 *
			 * This endpoint calculates the exact min/max dates that can be selected
			 * in the frontend calendar based on Cloudflare Web Analytics limits.
			 *
			 * DATE BOUNDARY CALCULATION:
			 * • maxDate: Today at 23:59:59 (latest possible time)
			 * • minDate: (Today - notOlderThanDays) at 00:00:00 (earliest possible time)
			 * • This creates the selectable window for the calendar component
			 *
			 * TIME PRECISION:
			 * • maxDate set to end-of-day to include all of today's data
			 * • minDate set to start-of-day to include all of the earliest day's data
			 * • This maximizes the available data range within Cloudflare's limits
			 *
			 * SAFETY BUFFER APPLICATION:
			 * • maxDurationDays reduced by 1 day to prevent edge case failures
			 * • This is the same safety buffer applied in validation endpoints
			 * • Ensures consistency across all date-related operations
			 *
			 * FRONTEND USAGE:
			 * • Calendar component uses these boundaries to disable invalid dates
			 * • Dynamic date disabling logic uses maxDurationDays for range validation
			 * • All date selection is constrained within these boundaries
			 */
			const limits = await getWebAnalyticsLimits(accountId, apiToken);

			if (limits.isUsingFallback) {
				console.warn(
					"[DATE-LIMITS] Using fallback values - Cloudflare GraphQL API unavailable"
				);
			}

			const today = new Date();
			today.setHours(23, 59, 59, 999); // End of today

			const minDate = new Date(today);
			minDate.setDate(minDate.getDate() - limits.notOlderThanDays);
			minDate.setHours(0, 0, 0, 0); // Start of earliest day

			return NextResponse.json({
				success: true,
				minDate: minDate.toISOString(),
				maxDate: today.toISOString(),
				notOlderThanDays: limits.notOlderThanDays,
				maxDurationDays: limits.maxDurationDays - 1, // Apply 1-day safety buffer
			} as DateLimitsResponse);
		}

		if (action === "create-timerange") {
			/**
			 * CREATE VALIDATED CUSTOM TIME RANGE
			 *
			 * This endpoint creates a properly formatted TimeRange object from user-selected dates.
			 * Includes validation against Cloudflare limits before creating the object.
			 *
			 * VALIDATION FLOW:
			 * 1. Validate required parameters (fromDate, toDate)
			 * 2. Fetch account limits from Cloudflare API
			 * 3. Apply 1-day safety buffer to maximum duration
			 * 4. Reject ranges that exceed 92 days (93 - 1 buffer)
			 * 5. Create formatted TimeRange object only if valid
			 *
			 * TIME RANGE FORMATTING:
			 * • startDate: Set to 00:00:00 to include entire first day
			 * • endDate: Set to 23:59:59 to include entire last day
			 * • This maximizes data coverage within the selected range
			 *
			 * UNIQUE VALUE GENERATION:
			 * • Uses timestamp-based value for uniqueness
			 * • Format: custom_{startTimestamp}_{endTimestamp}
			 * • Allows system to distinguish between different custom ranges
			 *
			 * LABEL FORMATTING:
			 * • Human-readable format using toLocaleDateString()
			 * • Example: "12/1/2023 - 12/31/2023"
			 * • Displayed in the time range selector UI
			 *
			 * INTEGRATION WITH FRONTEND:
			 * • This is called when user completes calendar date selection
			 * • Only successful responses trigger the analytics refresh
			 * • Errors are displayed to user with specific validation messages
			 */
			const fromDate = searchParams.get("fromDate");
			const toDate = searchParams.get("toDate");

			if (!fromDate || !toDate) {
				return NextResponse.json(
					{
						success: false,
						error: "fromDate and toDate parameters are required",
					},
					{ status: 400 }
				);
			}

			// VALIDATE AGAINST CLOUDFLARE LIMITS BEFORE CREATING
			const limits = await getWebAnalyticsLimits(accountId, apiToken);

			if (limits.isUsingFallback) {
				console.warn(
					"[CREATE-TIMERANGE] Using fallback values - Cloudflare GraphQL API unavailable"
				);
			}

			const daysDiff = calculateDaysDifference(fromDate, toDate);
			const maxAllowedDays = limits.maxDurationDays - 1; // Apply 1-day safety buffer

			// Reject ranges that exceed the 93-day limit (with safety buffer)
			if (daysDiff > maxAllowedDays) {
				return NextResponse.json(
					{
						success: false,
						error: `Date range cannot exceed ${maxAllowedDays} days`,
					},
					{ status: 400 }
				);
			}

			// CREATE PROPERLY FORMATTED TIME RANGE OBJECT
			const startDate = new Date(fromDate);
			startDate.setHours(0, 0, 0, 0); // Start of day

			const endDate = new Date(toDate);
			endDate.setHours(23, 59, 59, 999); // End of day

			const timeRange: TimeRangePreset = {
				label: `${startDate.toLocaleDateString()} - ${endDate.toLocaleDateString()}`,
				value: `custom_${startDate.getTime()}_${endDate.getTime()}`,
				since: startDate.toISOString(),
				until: endDate.toISOString(),
				isCustom: true,
			};

			return NextResponse.json({
				success: true,
				timeRange,
			} as CreateTimeRangeResponse);
		}

		return NextResponse.json(
			{
				success: false,
				error:
					"Invalid action. Supported actions: list, topns, sparkline, timeseries, limits, presets, validate-range, date-limits, create-timerange",
			},
			{ status: 400 }
		);
	} catch (error) {
		console.error("Web Analytics API error:", error);
		return NextResponse.json(
			{
				success: false,
				error: "Internal server error",
				debug: error instanceof Error ? error.message : "Unknown error",
			},
			{ status: 500 }
		);
	}
}
