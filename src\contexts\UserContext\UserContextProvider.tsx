"use client";

import React, { createContext, useContext, useState, useEffect } from "react";
import { SupabaseClient, User } from "@supabase/supabase-js";
import { createClient } from "@/utils/supabase/client";
import { FaUserCircle } from "react-icons/fa";
import type { UserContextType, UserProviderProps } from "./types";

const UserContext = createContext<UserContextType | undefined>(undefined);

export const UserProvider: React.FC<UserProviderProps> = ({ children }) => {
	const [supabaseClient, setSupabaseClient] = useState<SupabaseClient | null>(
		null
	);
	const [user, setUser] = useState<User | null>(null);
	const [userProfile, setUserProfile] =
		useState<UserContextType["userProfile"]>(null);
	const [isUserLoading, setIsUserLoading] = useState(true);

	useEffect(() => {
		const initializeClient = async () => {
			try {
				const client = await createClient();
				setSupabaseClient(client);
			} catch (error) {
				console.error("Erro ao inicializar o cliente Supabase:", error);
				setIsUserLoading(false);
			}
		};
		initializeClient();
	}, []);

	useEffect(() => {
		if (!supabaseClient) {
			return;
		}

		setIsUserLoading(true);
		let isMounted = true;

		const fetchUser = async () => {
			try {
				const {
					data: { user: fetchedUser }, // Renamed to avoid conflict
					error,
				} = await supabaseClient.auth.getUser();

				if (!isMounted) return;

				if (error) {
					console.error("Erro ao buscar usuário:", error);
					setUser(null);
					setUserProfile(null);
				} else if (fetchedUser) {
					setUser(fetchedUser);
					const avatarUrl = fetchedUser.user_metadata?.avatar_url;
					const email = fetchedUser.email;
					const name =
						fetchedUser.user_metadata?.user_name ||
						fetchedUser.user_metadata?.name;
					const fullName = fetchedUser.user_metadata?.full_name;
					setUserProfile({
						email: email,
						avatarUrl: avatarUrl || <FaUserCircle size={24} />,
						name: name,
						fullName: fullName,
					});
				} else {
					setUser(null);
					setUserProfile(null);
				}
			} catch (err) {
				if (isMounted) {
					console.error("Exceção ao buscar usuário:", err);
					setUser(null);
					setUserProfile(null);
				}
			} finally {
				if (isMounted) {
					setIsUserLoading(false);
				}
			}
		};

		fetchUser();

		const { data: authListener } = supabaseClient.auth.onAuthStateChange(
			(event, session) => {
				if (!isMounted) return;

				const currentUser = session?.user ?? null;
				setUser(currentUser);
				if (currentUser) {
					const avatarUrl = currentUser.user_metadata?.avatar_url;
					const email = currentUser.email;
					const name =
						currentUser.user_metadata?.user_name ||
						currentUser.user_metadata?.name;
					const fullName = currentUser.user_metadata?.full_name;
					setUserProfile({
						email: email,
						avatarUrl: avatarUrl || <FaUserCircle size={24} />,
						name: name,
						fullName: fullName,
					});
				} else {
					setUserProfile(null);
				}
			}
		);

		return () => {
			isMounted = false;
			authListener?.subscription.unsubscribe();
		};
	}, [supabaseClient]);

	const value = {
		user,
		userProfile,
		isUserLoading,
	};

	return <UserContext.Provider value={value}>{children}</UserContext.Provider>;
};

export const useUser = (): UserContextType => {
	const context = useContext(UserContext);
	if (context === undefined) {
		throw new Error("useUser must be used within a UserProvider");
	}
	return context;
};
