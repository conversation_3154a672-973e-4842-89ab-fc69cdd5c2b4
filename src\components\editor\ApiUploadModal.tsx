import React, { useState, useRef, useEffect } from "react";
import {
  Upload,
  <PERSON><PERSON><PERSON>,
  CheckCircle2,
  <PERSON>f<PERSON><PERSON><PERSON>,
  Trash2,
  <PERSON>ert<PERSON>ircle,
} from "lucide-react";
import {
  <PERSON><PERSON>,
  <PERSON>alogContent,
  <PERSON><PERSON><PERSON>eader,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>Footer,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import yaml from "js-yaml";
import {
  createApiFiles,
  getPagesByApiFile,
  deletePagesByApiFile,
} from "./utils/apiHelpers";
import { useProject } from "@/contexts";
import { createClient } from "@/utils/supabase/client";

interface ApiEndpoint {
  path: string;
  method: string;
  summary?: string;
  description?: string;
  operationId?: string;
}

interface ApiFile {
  name: string;
  url: string;
  uploadedAt: string;
  size: number;
  associatedPages?: string[]; // Pages generated by this API file
}

interface ApiUploadModalProps {
  isOpen: boolean;
  onClose: () => void;
  projectId: string;
  onSuccess?: () => void;
  toast: {
    success: (msg: string) => void;
    error: (msg: string) => void;
    warning?: (msg: string) => void;
  };
}

const MAX_API_FILES = 5;

export function ApiUploadModal({
  isOpen,
  onClose,
  projectId,
  onSuccess,
  toast,
}: ApiUploadModalProps) {
  const [isDragging, setIsDragging] = useState(false);
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [parsedEndpoints, setParsedEndpoints] = useState<ApiEndpoint[]>([]);
  const [destinationFolder, setDestinationFolder] = useState<string>("api");
  const [existingFiles, setExistingFiles] = useState<ApiFile[]>([]);
  const [loadingFiles, setLoadingFiles] = useState(false);
  const [reprocessingFile, setReprocessingFile] = useState<string | null>(null);
  const [reprocessEndpoints, setReprocessEndpoints] = useState<ApiEndpoint[]>(
    []
  );
  const [reprocessFolder, setReprocessFolder] = useState<string>("api");
  const fileInputRef = useRef<HTMLInputElement>(null);

  const { selectedProject } = useProject();

  // Load existing API files when modal opens
  useEffect(() => {
    if (isOpen) {
      loadExistingApiFiles();
    }
  }, [isOpen, projectId]);

  const loadExistingApiFiles = async () => {
    setLoadingFiles(true);
    try {
      const supabase = createClient();

      // List files in the project folder
      const { data: files, error } = await supabase.storage
        .from("api-files")
        .list(projectId, {
          limit: 100,
          sortBy: { column: "created_at", order: "desc" },
        });

      if (error) {
        console.error("Error loading existing API files:", error);
        return;
      }

      if (!files || files.length === 0) {
        setExistingFiles([]);
        return;
      }

      // Filter out placeholder files and get file info
      const apiFiles: ApiFile[] = [];
      for (const file of files) {
        if (
          file.name === ".emptyFolderPlaceholder" ||
          file.name.startsWith(".")
        ) {
          continue;
        }

        const filePath = `${projectId}/${file.name}`;
        const { data: urlData } = supabase.storage
          .from("api-files")
          .getPublicUrl(filePath);

        if (urlData?.publicUrl) {
          // Get associated pages for this API file
          const associatedPages = await getPagesByApiFile(
            projectId,
            urlData.publicUrl
          );

          apiFiles.push({
            name: file.name,
            url: urlData.publicUrl,
            uploadedAt: file.created_at || new Date().toISOString(),
            size: file.metadata?.size || 0,
            associatedPages,
          });
        }
      }

      setExistingFiles(apiFiles);
    } catch (error) {
      console.error("Error loading existing API files:", error);
    } finally {
      setLoadingFiles(false);
    }
  };

  const deleteApiFile = async (fileName: string) => {
    const file = existingFiles.find((f) => f.name === fileName);
    if (!file) return;

    const hasAssociatedPages =
      file.associatedPages && file.associatedPages.length > 0;

    let deleteOption: "file-only" | "file-and-pages" | "cancel" = "cancel";

    if (hasAssociatedPages) {
      // Show options when there are associated pages
      const choice = confirm(
        `"${fileName}" has ${
          file.associatedPages!.length
        } associated pages.\n\n` +
          `Click OK to delete the file AND all associated pages.\n` +
          `Click Cancel to abort the deletion.`
      );

      if (!choice) {
        return; // User cancelled
      }

      deleteOption = "file-and-pages";
    } else {
      // Simple confirmation when no associated pages
      const choice = confirm(
        `Are you sure you want to delete "${fileName}"?\n\n` +
          `This file has no associated pages. This action cannot be undone.`
      );

      if (!choice) {
        return; // User cancelled
      }

      deleteOption = "file-only";
    }

    try {
      const supabase = createClient();

      // Delete associated pages if requested
      if (deleteOption === "file-and-pages" && hasAssociatedPages) {
        console.log(
          `🗑️ Deleting ${
            file.associatedPages!.length
          } pages associated with ${fileName}`
        );

        const deleteResult = await deletePagesByApiFile(projectId, file.url);

        if (deleteResult.errors.length > 0) {
          console.error(
            "Errors deleting associated pages:",
            deleteResult.errors
          );
          toast.error(
            `Failed to delete some associated pages: ${deleteResult.errors.join(
              ", "
            )}`
          );
          return;
        }

        console.log(
          `✅ Successfully deleted ${deleteResult.deletedCount} associated pages`
        );
      }

      // Delete from storage
      const filePath = `${projectId}/${fileName}`;
      const { error: storageError } = await supabase.storage
        .from("api-files")
        .remove([filePath]);

      if (storageError) {
        console.error("Storage deletion error:", storageError);
        toast.error(
          "Failed to delete file from storage: " + storageError.message
        );
        return;
      }

      // Update existing files list
      setExistingFiles((prev) => prev.filter((file) => file.name !== fileName));

      // Force a complete update of config.json apiFiles by calling the API helper
      await updateConfigJsonWithAllApiFiles();

      const successMessage =
        deleteOption === "file-and-pages" && hasAssociatedPages
          ? `File "${fileName}" and ${
              file.associatedPages!.length
            } associated pages deleted successfully!`
          : `File "${fileName}" deleted successfully!`;

      toast.success(successMessage);

      // Refresh the page list if we deleted pages
      if (deleteOption === "file-and-pages" && onSuccess) {
        onSuccess();
      }
    } catch (error) {
      console.error("Error deleting API file:", error);
      toast.error(
        "Failed to delete file: " +
          (error instanceof Error ? error.message : "Unknown error")
      );
    }
  };

  const updateConfigJsonWithAllApiFiles = async () => {
    try {
      const supabase = createClient();

      // Get current config
      const { data: projectData, error: projectError } = await supabase
        .from("projects")
        .select("configjson")
        .eq("id", projectId)
        .single();

      if (projectError) {
        console.error("Error fetching project config:", projectError);
        return;
      }

      const configJson = projectData.configjson || {};

      // Get all API files from storage
      const { data: files, error } = await supabase.storage
        .from("api-files")
        .list(projectId, {
          limit: 100,
          sortBy: { column: "created_at", order: "desc" },
        });

      if (error) {
        console.error("Error listing API files from storage:", error);
        return;
      }

      // Generate URLs for all files (excluding placeholders)
      const apiFileUrls: string[] = [];
      if (files) {
        for (const file of files) {
          if (
            file.name === ".emptyFolderPlaceholder" ||
            file.name.startsWith(".")
          ) {
            continue;
          }

          const filePath = `${projectId}/${file.name}`;
          const { data: urlData } = supabase.storage
            .from("api-files")
            .getPublicUrl(filePath);

          if (urlData?.publicUrl) {
            apiFileUrls.push(urlData.publicUrl);
          }
        }
      }

      // Update config.json with the current list of API files
      configJson.apiFiles = apiFileUrls;

      // Save updated config
      const { error: updateError } = await supabase
        .from("projects")
        .update({ configjson: configJson })
        .eq("id", parseInt(projectId, 10));

      if (updateError) {
        console.error("Error updating config.json:", updateError);
      } else {
        console.log(
          "✅ Updated config.json with current API files:",
          apiFileUrls
        );
      }
    } catch (error) {
      console.error("Error updating config.json with all API files:", error);
    }
  };

  const handleReprocessClick = async (fileName: string, fileUrl: string) => {
    try {
      setIsLoading(true);
      setReprocessingFile(fileName);

      // Download the file content
      const response = await fetch(fileUrl);
      if (!response.ok) {
        throw new Error("Failed to download file");
      }

      const fileContent = await response.text();
      const fileExtension = fileName.split(".").pop()?.toLowerCase();

      // Parse the content
      const parsedContent =
        fileExtension === "json"
          ? JSON.parse(fileContent)
          : yaml.load(fileContent);

      if (!parsedContent || !parsedContent.paths) {
        toast.error("Invalid API specification file. No paths found.");
        return;
      }

      // Extract endpoints
      const endpoints: ApiEndpoint[] = [];
      for (const [path, methods] of Object.entries(parsedContent.paths)) {
        if (typeof methods !== "object") continue;

        for (const [method, details] of Object.entries(
          methods as Record<string, ApiEndpoint>
        )) {
          if (
            ![
              "get",
              "post",
              "put",
              "delete",
              "patch",
              "options",
              "head",
            ].includes(method.toLowerCase())
          )
            continue;

          endpoints.push({
            path,
            method: method.toUpperCase(),
            operationId: details.operationId,
            summary:
              details.summary ||
              details.description ||
              `${method.toUpperCase()} ${path}`,
            description: details.description,
          });
        }
      }

      // Set up for reprocessing (similar to new file upload)
      setReprocessEndpoints(endpoints);
      setReprocessFolder("api"); // Default folder

      // Clear any existing new file selection
      setSelectedFile(null);
      setParsedEndpoints([]);
      if (fileInputRef.current) {
        fileInputRef.current.value = "";
      }
    } catch (error) {
      console.error("Error parsing API file for reprocessing:", error);
      toast.error(
        "Error parsing API file: " +
          (error instanceof Error ? error.message : "Unknown error")
      );
    } finally {
      setIsLoading(false);
    }
  };

  const handleReprocessGenerate = async () => {
    if (reprocessEndpoints.length === 0) return;

    setIsLoading(true);

    try {
      const results = await createApiFiles(
        projectId,
        reprocessEndpoints,
        reprocessFolder,
        selectedProject?.owner_id,
        reprocessingFile
          ? existingFiles.find((f) => f.name === reprocessingFile)?.url
          : undefined
      );

      // Always reload existing files to ensure apiFiles in config.json is updated
      await loadExistingApiFiles();

      // Show detailed results
      let message = `Processing completed!`;
      if (results.successCount > 0) {
        message += `\n✅ ${results.successCount} endpoints processed successfully`;
      }
      if (results.skipCount > 0) {
        message += `\n⚠️ ${results.skipCount} endpoints skipped (already exist)`;
      }
      if (results.errorCount > 0) {
        message += `\n❌ ${results.errorCount} endpoints failed`;
      }

      if (results.errorCount > 0) {
        toast.error(message);
      } else if (results.skipCount > 0) {
        if (toast.warning) {
          toast.warning(message);
        } else {
          toast.success(message);
        }
      } else {
        toast.success(message);
      }

      if (onSuccess) {
        onSuccess();
      }

      // Reset reprocessing state
      setReprocessingFile(null);
      setReprocessEndpoints([]);
      setReprocessFolder("api");
    } catch (error) {
      console.error("Error reprocessing API file:", error);
      toast.error(
        "Error reprocessing API file: " +
          (error instanceof Error ? error.message : "Unknown error")
      );
    } finally {
      setIsLoading(false);
    }
  };

  const handleCancelReprocess = () => {
    setReprocessingFile(null);
    setReprocessEndpoints([]);
    setReprocessFolder("api");
  };

  const handleDragOver = (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    setIsDragging(true);
  };

  const handleDragLeave = (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    setIsDragging(false);
  };

  const handleFileDrop = (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    setIsDragging(false);

    if (existingFiles.length >= MAX_API_FILES) {
      toast.error(`Maximum of ${MAX_API_FILES} API files allowed per project.`);
      return;
    }

    if (e.dataTransfer.files && e.dataTransfer.files.length > 0) {
      const file = e.dataTransfer.files[0];
      validateAndSetFile(file);
    }
  };

  const handleFileSelect = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (existingFiles.length >= MAX_API_FILES) {
      toast.error(`Maximum of ${MAX_API_FILES} API files allowed per project.`);
      return;
    }

    if (e.target.files && e.target.files.length > 0) {
      const file = e.target.files[0];
      validateAndSetFile(file);
    }
  };

  const validateAndSetFile = async (file: File) => {
    const fileExtension = file.name.split(".").pop()?.toLowerCase();

    if (!fileExtension || !["json", "yaml", "yml"].includes(fileExtension)) {
      toast.error("Please select a JSON or YAML file.");
      return;
    }

    if (file.size > 5 * 1024 * 1024) {
      toast.error(
        "The file is too large. OpenAPI files should be less than 5MB."
      );
      return;
    }

    // Clear reprocessing state when selecting new file
    setReprocessingFile(null);
    setReprocessEndpoints([]);
    setReprocessFolder("api");

    setSelectedFile(file);
    await parseApiFile(file);
  };

  const parseApiFile = async (file: File) => {
    if (!file) return;

    try {
      const fileContent = await readFileContent(file);
      const fileExtension = file.name.split(".").pop()?.toLowerCase();
      const parsedContent =
        fileExtension === "json"
          ? JSON.parse(fileContent)
          : yaml.load(fileContent);

      if (!parsedContent || !parsedContent.paths) {
        toast.error("Invalid API specification file. No paths found.");
        return;
      }

      const endpoints: ApiEndpoint[] = [];

      for (const [path, methods] of Object.entries(parsedContent.paths)) {
        if (typeof methods !== "object") continue;

        for (const [method, details] of Object.entries(
          methods as Record<string, ApiEndpoint>
        )) {
          if (
            ![
              "get",
              "post",
              "put",
              "delete",
              "patch",
              "options",
              "head",
            ].includes(method.toLowerCase())
          )
            continue;

          endpoints.push({
            path,
            method: method.toUpperCase(),
            operationId: details.operationId,
            summary:
              details.summary ||
              details.description ||
              `${method.toUpperCase()} ${path}`,
            description: details.description,
          });
        }
      }

      setParsedEndpoints(endpoints);
    } catch (error) {
      console.error("Error parsing API file:", error);
      toast.error("Failed to parse API file. Check the file format.");
    }
  };

  const readFileContent = (file: File): Promise<string> => {
    return new Promise((resolve, reject) => {
      const reader = new FileReader();
      reader.onload = (event) => {
        if (event.target?.result) {
          resolve(event.target.result as string);
        } else {
          reject(new Error("Failed to read file content"));
        }
      };
      reader.onerror = (error) => reject(error);
      reader.readAsText(file);
    });
  };

  const uploadApiFileToStorage = async (file: File): Promise<string | null> => {
    if (!selectedProject?.owner_id) {
      console.error("No organization ID available");
      return null;
    }

    try {
      const supabase = createClient();
      const timestamp = Date.now();
      const fileExtension = file.name.split(".").pop()?.toLowerCase();

      // Sanitize filename
      const sanitizedName = file.name
        .replace(/[^a-zA-Z0-9\-_.]/g, "_")
        .replace(/\.(json|yaml|yml)$/i, "");

      const fileName = `${timestamp}_${sanitizedName}.${fileExtension}`;

      // Upload to storage
      const { error: uploadError } = await supabase.storage
        .from("api-files")
        .upload(`${projectId}/${fileName}`, file, {
          contentType: file.type,
        });

      if (uploadError) {
        console.error("Storage upload error:", uploadError);
        return null;
      }

      // Get public URL
      const { data: urlData } = supabase.storage
        .from("api-files")
        .getPublicUrl(`${projectId}/${fileName}`);

      console.log("✅ API file uploaded to storage:", urlData.publicUrl);
      return urlData.publicUrl;
    } catch (error) {
      console.error("Error uploading API file to storage:", error);
      return null;
    }
  };

  const handleGenerateApi = async () => {
    if (!selectedFile || parsedEndpoints.length === 0) return;

    setIsLoading(true);

    try {
      // First, upload the original file to storage
      const uploadedFileUrl = await uploadApiFileToStorage(selectedFile);

      if (!uploadedFileUrl) {
        console.warn(
          "Failed to upload API file to storage, continuing without it"
        );
      }

      const results = await createApiFiles(
        projectId,
        parsedEndpoints,
        destinationFolder,
        selectedProject?.owner_id,
        uploadedFileUrl || undefined // Pass the uploaded file URL to associate pages with the source file
      );

      // Reload existing files to show the new one
      await loadExistingApiFiles();

      // Show detailed results
      let message = `Processing completed!`;
      if (results.successCount > 0) {
        message += `\n✅ ${results.successCount} endpoints processed successfully`;
      }
      if (results.skipCount > 0) {
        message += `\n⚠️ ${results.skipCount} endpoints skipped (already exist)`;
      }
      if (results.errorCount > 0) {
        message += `\n❌ ${results.errorCount} endpoints failed`;
      }

      if (results.errorCount > 0) {
        toast.error(message);
      } else if (results.skipCount > 0) {
        if (toast.warning) {
          toast.warning(message);
        } else {
          toast.success(message);
        }
      } else {
        toast.success(message);
      }

      if (onSuccess) {
        onSuccess();
      }

      // Reset the form but keep the modal open to show the new file
      setSelectedFile(null);
      setParsedEndpoints([]);
      if (fileInputRef.current) {
        fileInputRef.current.value = "";
      }
    } catch (error) {
      console.error("Error generating API:", error);
      toast.error("Error generating API documentation.");
    } finally {
      setIsLoading(false);
    }
  };

  const handleClose = () => {
    setSelectedFile(null);
    setParsedEndpoints([]);
    setDestinationFolder("api");
    setIsDragging(false);
    setReprocessingFile(null);
    setReprocessEndpoints([]);
    setReprocessFolder("api");
    if (fileInputRef.current) {
      fileInputRef.current.value = "";
    }
    onClose();
  };

  const formatFileSize = (bytes: number): string => {
    if (bytes === 0) return "0 Bytes";
    const k = 1024;
    const sizes = ["Bytes", "KB", "MB", "GB"];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + " " + sizes[i];
  };

  const formatDate = (dateString: string): string => {
    return new Date(dateString).toLocaleDateString("en-US", {
      day: "2-digit",
      month: "2-digit",
      year: "numeric",
      hour: "2-digit",
      minute: "2-digit",
    });
  };

  // Determine which endpoints to show and which folder input to show
  const currentEndpoints =
    reprocessEndpoints.length > 0 ? reprocessEndpoints : parsedEndpoints;
  const currentFolder =
    reprocessEndpoints.length > 0 ? reprocessFolder : destinationFolder;
  const setCurrentFolder =
    reprocessEndpoints.length > 0 ? setReprocessFolder : setDestinationFolder;

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent className="sm:max-w-[700px] max-h-[80vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            Import API Documentation
            <span className="text-sm font-normal text-gray-500">
              ({existingFiles.length}/{MAX_API_FILES} files)
            </span>
          </DialogTitle>
        </DialogHeader>

        <div className="space-y-6">
          {/* Existing Files Section */}
          {loadingFiles ? (
            <div className="flex items-center justify-center py-4">
              <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600"></div>
              <span className="ml-2 text-sm text-gray-500">
                Loading existing files...
              </span>
            </div>
          ) : existingFiles.length > 0 ? (
            <div className="space-y-3">
              <h4 className="text-sm font-medium text-gray-700">
                Existing API Files
              </h4>
              <div className="space-y-2 max-h-[200px] overflow-y-auto border rounded-md p-2">
                {existingFiles.map((file, index) => (
                  <div
                    key={index}
                    className={`flex items-center justify-between p-3 rounded-md transition-colors ${
                      reprocessingFile === file.name
                        ? "bg-blue-50 border border-blue-200"
                        : "bg-gray-50 hover:bg-gray-100"
                    }`}
                  >
                    <div className="flex items-center gap-3 flex-1 min-w-0">
                      <FileJson className="h-5 w-5 text-blue-600 flex-shrink-0" />
                      <div className="min-w-0 flex-1">
                        <p className="text-sm font-medium text-gray-900 truncate">
                          {file.name}
                          {reprocessingFile === file.name && (
                            <span className="ml-2 text-xs text-blue-600 font-normal">
                              (Selected for reprocessing)
                            </span>
                          )}
                        </p>
                        <p className="text-xs text-gray-500">
                          {formatDate(file.uploadedAt)} •{" "}
                          {formatFileSize(file.size)}
                          {file.associatedPages &&
                            file.associatedPages.length > 0 && (
                              <span className="ml-2 text-blue-600">
                                • {file.associatedPages.length} página
                                {file.associatedPages.length !== 1
                                  ? "s"
                                  : ""}{" "}
                                associada
                                {file.associatedPages.length !== 1 ? "s" : ""}
                              </span>
                            )}
                        </p>
                      </div>
                    </div>
                    <div className="flex items-center gap-2">
                      <Button
                        size="sm"
                        variant="outline"
                        onClick={() =>
                          handleReprocessClick(file.name, file.url)
                        }
                        disabled={isLoading}
                        className="h-8 px-2"
                        title="Select this file for reprocessing"
                      >
                        <RefreshCw className="h-3 w-3" />
                      </Button>
                      <Button
                        size="sm"
                        variant="outline"
                        onClick={() => deleteApiFile(file.name)}
                        disabled={isLoading}
                        className={`h-8 px-2 ${
                          file.associatedPages &&
                          file.associatedPages.length > 0
                            ? "text-orange-600 hover:text-orange-700 hover:bg-orange-50"
                            : "text-red-600 hover:text-red-700 hover:bg-red-50"
                        }`}
                        title={
                          file.associatedPages &&
                          file.associatedPages.length > 0
                            ? `Delete file and ${file.associatedPages.length} associated pages`
                            : "Delete this file from storage"
                        }
                      >
                        <Trash2 className="h-3 w-3" />
                      </Button>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          ) : null}

          {/* Reprocessing notification */}
          {reprocessingFile && (
            <div className="flex items-center gap-3 p-4 bg-blue-50 border border-blue-200 rounded-lg">
              <RefreshCw className="h-5 w-5 text-blue-600 flex-shrink-0" />
              <div className="flex-1">
                <p className="text-sm font-medium text-blue-800">
                  Reprocessing: {reprocessingFile}
                </p>
                <p className="text-xs text-blue-700">
                  Choose the destination folder and generate the documentation
                  pages.
                </p>
              </div>
              <Button
                size="sm"
                variant="outline"
                onClick={handleCancelReprocess}
                className="text-gray-600 hover:text-gray-800"
              >
                Cancel
              </Button>
            </div>
          )}

          {/* Upload Section */}
          {existingFiles.length < MAX_API_FILES && !reprocessingFile ? (
            <>
              <div
                className={`border-2 border-dashed rounded-lg p-8 text-center transition-colors cursor-pointer ${
                  isDragging
                    ? "border-blue-400 bg-blue-50"
                    : selectedFile
                    ? "border-green-400 bg-green-50"
                    : "border-slate-200 hover:border-blue-300 hover:bg-slate-50"
                }`}
                onDragOver={handleDragOver}
                onDragLeave={handleDragLeave}
                onDrop={handleFileDrop}
                onClick={() => fileInputRef.current?.click()}
              >
                {selectedFile ? (
                  <div className="flex flex-col items-center">
                    <FileJson size={40} className="text-green-500 mb-3" />
                    <p className="text-sm font-medium text-slate-700">
                      {selectedFile.name}
                    </p>
                    <p className="text-xs text-slate-500 mt-1">
                      {(selectedFile.size / 1024).toFixed(1)} KB
                    </p>
                    <button
                      className="mt-3 text-xs text-red-500 hover:text-red-700"
                      onClick={(e) => {
                        e.stopPropagation();
                        setSelectedFile(null);
                        setParsedEndpoints([]);
                        if (fileInputRef.current) {
                          fileInputRef.current.value = "";
                        }
                      }}
                    >
                      Remove file
                    </button>
                  </div>
                ) : (
                  <div className="flex flex-col items-center">
                    <Upload size={40} className="text-slate-400 mb-3" />
                    <p className="text-sm font-medium text-slate-700">
                      Drag and drop your OpenAPI file here
                    </p>
                    <p className="text-xs text-slate-500 mt-1">
                      or click to browse files
                    </p>
                    <p className="text-xs text-slate-400 mt-3">
                      Supported formats: JSON, YAML
                    </p>
                  </div>
                )}
                <input
                  type="file"
                  className="hidden"
                  accept=".json,.yaml,.yml"
                  ref={fileInputRef}
                  onChange={handleFileSelect}
                />
              </div>
            </>
          ) : existingFiles.length >= MAX_API_FILES && !reprocessingFile ? (
            <div className="flex items-center gap-3 p-4 bg-amber-50 border border-amber-200 rounded-lg">
              <AlertCircle className="h-5 w-5 text-amber-600 flex-shrink-0" />
              <div>
                <p className="text-sm font-medium text-amber-800">
                  Maximum files reached
                </p>
                <p className="text-xs text-amber-700">
                  You have reached the maximum of {MAX_API_FILES} API files per
                  project. Delete an existing file to upload a new one.
                </p>
              </div>
            </div>
          ) : null}

          {/* Endpoints Preview Section */}
          {currentEndpoints.length > 0 && (
            <div className="space-y-3">
              <div className="flex items-center justify-between">
                <h4 className="text-sm font-medium text-slate-700">
                  Endpoints found ({currentEndpoints.length})
                </h4>
                <div className="flex items-center gap-2">
                  <span className="text-xs text-slate-500">
                    Destination folder:
                  </span>
                  <input
                    type="text"
                    value={currentFolder}
                    onChange={(e) => setCurrentFolder(e.target.value)}
                    className="px-2 py-1 text-sm border rounded"
                    placeholder="api"
                  />
                </div>
              </div>

              <div className="max-h-[200px] overflow-y-auto border rounded-md">
                {currentEndpoints.map((endpoint, index) => (
                  <div
                    key={index}
                    className="px-3 py-2 border-b last:border-b-0 hover:bg-slate-50"
                  >
                    <div className="flex items-center">
                      <span
                        className={`text-xs font-medium px-2 py-0.5 rounded mr-2 ${
                          endpoint.method === "GET"
                            ? "bg-blue-100 text-blue-800"
                            : endpoint.method === "POST"
                            ? "bg-green-100 text-green-800"
                            : endpoint.method === "PUT"
                            ? "bg-amber-100 text-amber-800"
                            : endpoint.method === "DELETE"
                            ? "bg-red-100 text-red-800"
                            : "bg-purple-100 text-purple-800"
                        }`}
                      >
                        {endpoint.method}
                      </span>
                      <span className="text-sm font-mono text-slate-700 truncate">
                        {endpoint.path}
                      </span>
                    </div>
                    {endpoint.summary && (
                      <p className="text-xs text-slate-500 mt-1">
                        {endpoint.summary}
                      </p>
                    )}
                  </div>
                ))}
              </div>

              <div className="flex items-center">
                <CheckCircle2 className="h-5 w-5 text-green-500 mr-2" />
                <span className="text-sm text-slate-700">
                  {currentEndpoints.length} endpoints will be generated
                </span>
              </div>
            </div>
          )}
        </div>

        <DialogFooter>
          <Button variant="outline" onClick={handleClose}>
            Close
          </Button>
          {selectedFile && parsedEndpoints.length > 0 && (
            <Button onClick={handleGenerateApi} disabled={isLoading}>
              {isLoading ? "Generating..." : "Generate Documentation"}
            </Button>
          )}
          {reprocessingFile && reprocessEndpoints.length > 0 && (
            <Button onClick={handleReprocessGenerate} disabled={isLoading}>
              {isLoading ? "Reprocessing..." : "Reprocess Documentation"}
            </Button>
          )}
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
