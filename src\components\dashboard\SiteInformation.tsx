import {
	Card,
	CardContent,
	CardDescription,
	Card<PERSON>eader,
	CardTitle,
} from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";

interface SiteInformationProps {
	siteName?: string;
	projectName?: string;
	siteTag?: string;
	apiVersion?: string;
}

export function SiteInformation({
	siteName,
	projectName,
	siteTag,
	apiVersion,
}: SiteInformationProps) {
	return (
		<div className='grid grid-cols-1 lg:grid-cols-2 gap-6'>
			<Card>
				<CardHeader>
					<CardTitle>Site Information</CardTitle>
					<CardDescription>
						Site details in Cloudflare Web Analytics
					</CardDescription>
				</CardHeader>
				<CardContent>
					<div className='space-y-2'>
						<div className='flex justify-between'>
							<span className='font-medium'>Site Name:</span>
							<Badge variant='secondary'>{siteName || projectName}</Badge>
						</div>
						<div className='flex justify-between'>
							<span className='font-medium'>Site Tag:</span>
							<span className='text-sm text-muted-foreground font-mono'>
								{siteTag}
							</span>
						</div>
					</div>
				</CardContent>
			</Card>

			<Card>
				<CardHeader>
					<CardTitle>Analysis Status</CardTitle>
					<CardDescription>Current state of collected data</CardDescription>
				</CardHeader>
				<CardContent>
					<div className='space-y-2'>
						<div className='flex justify-between'>
							<span className='font-medium'>Web Analytics:</span>
							<Badge variant={siteTag ? "default" : "secondary"}>
								{siteTag ? "Site Configured" : "Not Found"}
							</Badge>
						</div>
						<div className='flex justify-between'>
							<span className='font-medium'>Site Type:</span>
							<Badge variant='outline'>Cloudflare Pages</Badge>
						</div>
						<div className='flex justify-between'>
							<span className='font-medium'>API Version:</span>
							<Badge variant='outline'>{apiVersion || "Web Analytics"}</Badge>
						</div>
						<div className='flex justify-between'>
							<span className='font-medium'>Last Checked:</span>
							<span className='text-sm text-muted-foreground'>
								{new Date().toLocaleString("en-US")}
							</span>
						</div>
					</div>
				</CardContent>
			</Card>
		</div>
	);
}
