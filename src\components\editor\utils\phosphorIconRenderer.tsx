import React from 'react';
import * as PhosphorIcons from '@phosphor-icons/react';
import type { Icon } from '@phosphor-icons/react';

// Type for the PhosphorIcons object to enable safe dynamic access
type PhosphorIconsType = Record<string, Icon | unknown>;

export interface PhosphorIconProps {
	iconName: string;
	weight?: 'thin' | 'light' | 'regular' | 'bold' | 'fill' | 'duotone';
	size?: string | number;
	className?: string;
}

// Helper function to convert kebab-case to PascalCase
const formatIconName = (name: string): string => {
	return name
		.split('-')
		.map((word) => word.charAt(0).toUpperCase() + word.slice(1))
		.join('');
};

export const getPhosphorIcon = (
	iconName: string,
	weight:
		| 'thin'
		| 'light'
		| 'regular'
		| 'bold'
		| 'fill'
		| 'duotone' = 'regular',
	size: string | number = 48,
	className?: string
): React.ReactElement => {
	if (!iconName) {
		return (
			<div
				className={`bg-gray-100 dark:bg-gray-700 rounded-lg flex items-center justify-center ${className || ''}`}
				style={{
					width: typeof size === 'number' ? `${size}px` : size,
					height: typeof size === 'number' ? `${size}px` : size,
				}}
			>
				<PhosphorIcons.Image className='w-6 h-6 text-gray-400' />
			</div>
		);
	}

	// Normalize icon name to PascalCase
	const normalizedName = formatIconName(iconName);

	// Get the icon component from PhosphorIcons
	const IconComponent = (PhosphorIcons as PhosphorIconsType)[normalizedName] as
		| Icon
		| undefined;

	if (!IconComponent) {
		// Fallback for unknown icons - show the first letter
		return (
			<div
				className={`bg-blue-500 text-white rounded-lg flex items-center justify-center font-bold ${className || ''}`}
				style={{
					width: typeof size === 'number' ? `${size}px` : size,
					height: typeof size === 'number' ? `${size}px` : size,
					fontSize:
						typeof size === 'number' ? `${Math.max(16, size / 3)}px` : '16px',
				}}
			>
				{iconName.charAt(0).toUpperCase()}
			</div>
		);
	}

	const sizeNumber =
		typeof size === 'number'
			? size
			: parseInt(size.toString().replace('px', ''));

	return (
		<div
			className={`flex items-center justify-center ${className || ''}`}
			style={{
				width: typeof size === 'number' ? `${size}px` : size,
				height: typeof size === 'number' ? `${size}px` : size,
			}}
		>
			<IconComponent
				size={sizeNumber}
				weight={weight}
				className='text-blue-600 dark:text-blue-400'
			/>
		</div>
	);
};

// Helper function to check if an icon exists
export const hasIcon = (iconName: string): boolean => {
	if (!iconName) return false;
	const normalizedName = formatIconName(iconName);
	return (PhosphorIcons as PhosphorIconsType)[normalizedName] !== undefined;
};

// Get all available icon names (this will include all Phosphor Icons)
export const getAvailableIcons = (): string[] => {
	return Object.keys(PhosphorIcons)
		.filter(
			(key) => typeof (PhosphorIcons as PhosphorIconsType)[key] === 'function'
		)
		.map((key) =>
			key
				.replace(/([A-Z])/g, '-$1')
				.toLowerCase()
				.slice(1)
		);
};

export default getPhosphorIcon;
