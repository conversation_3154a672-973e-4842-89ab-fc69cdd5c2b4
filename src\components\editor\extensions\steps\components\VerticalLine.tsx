import React from "react";
import { VerticalLineProps } from "../types";
import { getLinePosition } from "../utils";

export const VerticalLine: React.FC<VerticalLineProps> = ({
	titleSize,
	hasSubSteps,
	isLast,
}) => {
	const linePosition = getLinePosition(titleSize);

	let height = "calc(100% - 32px)";

	if (hasSubSteps) {
		height = "calc(100% - 16px)";
	} else if (!isLast) {
		height = "calc(100% + 16px)";
	}

	const shouldApplyFade = isLast;

	return (
		<div
			className={`absolute w-0.5 z-0 ${
				shouldApplyFade ? "step-line-fade" : "bg-gray-300 dark:bg-gray-600"
			}`}
			style={{
				left: `${linePosition}px`,
				height,
				...(shouldApplyFade && {
					background: `linear-gradient(to bottom, 
						rgb(209 213 219) 0%, 
						rgb(209 213 219) 60%, 
						rgba(209, 213, 219, 0.3) 85%, 
						transparent 100%)`,
				}),
			}}
		/>
	);
};
