import { List, ExternalLink } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import {
	Popover,
	PopoverContent,
	PopoverTrigger,
} from "@/components/ui/popover";
import { DocumentSource } from "@/app/(dashboard)/[projectId]/ask-ai/types";
import { useState, useRef } from "react";

interface SourcesDisplayProps {
	sources: DocumentSource[];
}

export const SourcesDisplay = ({ sources }: SourcesDisplayProps) => {
	const [isOpen, setIsOpen] = useState(false);
	const triggerRef = useRef<HTMLButtonElement>(null);
	const timeoutRef = useRef<NodeJS.Timeout | null>(null);

	// Function to handle mouse enter
	const handleMouseEnter = () => {
		if (timeoutRef.current) {
			clearTimeout(timeoutRef.current);
		}
		setIsOpen(true);
	};

	// Function to handle mouse leave
	const handleMouseLeave = () => {
		// Add a small delay before closing to allow moving the mouse to the content
		timeoutRef.current = setTimeout(() => {
			setIsOpen(false);
		}, 300);
	};

	if (!sources || sources.length === 0) {
		return <span className='text-muted-foreground'>0</span>;
	}

	return (
		<Popover open={isOpen} onOpenChange={setIsOpen}>
			<PopoverTrigger asChild>
				<Button
					ref={triggerRef}
					variant='link'
					className='text-wd-blue hover:text-wd-blueDark flex items-center h-auto p-0'
					aria-label='View sources'
					onMouseEnter={handleMouseEnter}
					onMouseLeave={handleMouseLeave}
				>
					<span className='mr-1'>{sources.length}</span>
					<List size={16} />
				</Button>
			</PopoverTrigger>
			<PopoverContent
				className='w-80 p-0'
				onMouseEnter={handleMouseEnter}
				onMouseLeave={handleMouseLeave}
				sideOffset={5}
				align='start'
			>
				<div className='p-3 border-b border-gray-200'>
					<h4 className='font-medium'>Sources ({sources.length})</h4>
					<p className='text-xs text-gray-500'>
						Sources used to generate the answer
					</p>
				</div>
				<div className='max-h-[250px] overflow-y-auto p-2'>
					{sources.length > 0 ? (
						<ul className='space-y-2'>
							{sources.map((source, index) => (
								<li
									key={index}
									className='text-sm p-2 hover:bg-gray-50 rounded'
								>
									<div className='flex justify-between items-start'>
										<div className='flex-1'>
											<div className='font-medium'>
												{source.title || "Untitled"}
											</div>
											<div className='text-xs text-gray-500 truncate'>
												{source.type}
											</div>
										</div>
										{source.used && (
											<span className='bg-green-100 text-green-800 text-xs px-2 py-0.5 rounded'>
												Used
											</span>
										)}
									</div>
									{source.url && (
										<a
											href={source.url}
											target='_blank'
											rel='noopener noreferrer'
											className='mt-1 text-xs text-blue-600 hover:underline flex items-center'
										>
											<ExternalLink size={10} className='mr-1' />
											{source.url}
										</a>
									)}
								</li>
							))}
						</ul>
					) : (
						<div className='p-4 text-center text-gray-500'>
							No sources available
						</div>
					)}
				</div>
			</PopoverContent>
		</Popover>
	);
};
