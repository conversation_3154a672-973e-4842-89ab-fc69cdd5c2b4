'use client';
import { useRouter } from 'next/navigation';
import { useEffect, useState } from 'react';
import Head from 'next/head';

export default function NotFound() {
	const router = useRouter();
	const [countdown, setCountdown] = useState(15);
	const [isHovering, setIsHovering] = useState(false);
	const [pageLoaded, setPageLoaded] = useState(false);

	// Set page as loaded after mounting
	useEffect(() => {
		setPageLoaded(true);
	}, []);

	useEffect(() => {
		// Countdown logic
		const timer = setInterval(() => {
			setCountdown((prevCount) => {
				if (prevCount <= 1) {
					clearInterval(timer);
					return 0;
				}
				return prevCount - 1;
			});
		}, 1000);

		// Clean up intervals
		return () => {
			clearInterval(timer);
		};
	}, []);

	// Redirect after countdown
	useEffect(() => {
		if (countdown === 0) {
			router.push('/hub');
		}
	}, [countdown, router]);

	return (
		<>
			{/* This helps prevent flash of unstyled content */}
			<Head>
				<style>{`
          body { 
            background-color: #f0f0ff;
            color: #4a5178;
          }
        `}</style>
			</Head>

			<div
				className={`w-full min-h-screen bg-gradient-to-b from-[#f0f0ff] to-[#e5e7ff] text-[#4a5178] flex flex-col items-center justify-center p-6 relative overflow-hidden transition-opacity duration-300 ${
					pageLoaded ? 'opacity-100' : 'opacity-0'
				}`}
			>
				{/* Decorative elements */}
				<div className='absolute inset-0 overflow-hidden'>
					<div className='absolute top-0 left-0 w-full h-64 bg-gradient-to-b from-[#CAD3FF]/20 to-transparent'></div>
					<div className='absolute w-40 h-40 rounded-full bg-[#CAD3FF]/10 blur-3xl -top-10 left-1/4'></div>
					<div className='absolute w-60 h-60 rounded-full bg-[#CAD3FF]/10 blur-3xl top-1/3 -right-20'></div>
					<div className='absolute w-40 h-40 rounded-full bg-[#CAD3FF]/10 blur-3xl bottom-0 left-10'></div>

					{/* Animated grid lines */}
					<div
						className='absolute inset-0 opacity-[0.015]'
						style={{
							backgroundImage:
								'linear-gradient(#0029f5 1px, transparent 1px), linear-gradient(to right, #0029f5 1px, transparent 1px)',
							backgroundSize: '40px 40px',
						}}
					></div>
				</div>

				<div className='relative z-10 w-full max-w-3xl flex flex-col items-center space-y-12'>
					{/* Section with 404 text */}
					<div className='flex flex-col items-center'>
						<div className='relative mb-6'>
							{/* 404 with enhanced shadow effects */}
							<div className='relative'>
								{/* Shadow layers */}
								<span className='absolute -z-30 left-1 top-[20px] text-[11rem] font-black text-[#CAD3FF]/30 blur-[12px] leading-none'>
									404
								</span>
								<span className='absolute -z-20 left-0 top-[12px] text-[11rem] font-black text-[#CAD3FF]/50 blur-[6px] leading-none'>
									404
								</span>
								<span className='absolute -z-10 left-0 top-[4px] text-[11rem] font-black text-[#CAD3FF]/70 blur-[2px] leading-none'>
									404
								</span>
								{/* Main 404 text with gradient */}
								<span className='relative z-0 text-[11rem] font-black leading-none text-transparent bg-clip-text bg-gradient-to-b from-[#0029f5] to-[#5377F7]'>
									404
								</span>
							</div>
						</div>

						{/* Text below 404 */}
						<div className='text-center md:text-left mb-8 md:mb-0'>
							<h2 className='text-4xl font-bold tracking-tight text-[#0029f5] mb-4'>
								Page Not Found
							</h2>
							<p className='text-lg text-[#4a5178]/80 max-w-md'>
								We&apos;ve searched our documentation, but couldn&apos;t find
								what you&apos;re looking for.
							</p>
						</div>
					</div>

					{/* Section with redirect */}
					<div className='w-full max-w-md flex flex-col space-y-6'>
						{/* Enhanced Countdown and redirect notice with soft shadow */}
						<div className='bg-white p-6 rounded-xl border border-[#CAD3FF] shadow-lg shadow-[#CAD3FF]/10 relative overflow-hidden'>
							{/* Decorative corner accent */}
							<div className='absolute top-0 right-0 w-20 h-20 bg-[#f7f7ff] rounded-bl-full opacity-50'></div>

							<div className='flex flex-col'>
								<div className='flex items-center justify-center mb-5'>
									<div className='p-3 bg-[#f7f7ff] rounded-full mr-4'>
										<svg
											xmlns='http://www.w3.org/2000/svg'
											viewBox='0 0 24 24'
											fill='currentColor'
											className='w-6 h-6 text-[#0029f5]'
										>
											<path
												fillRule='evenodd'
												d='M12 2.25c-5.385 0-9.75 4.365-9.75 9.75s4.365 9.75 9.75 9.75 9.75-4.365 9.75-9.75S17.385 2.25 12 2.25zM12.75 6a.75.75 0 00-1.5 0v6c0 .414.336.75.75.75h4.5a.75.75 0 000-1.5h-3.75V6z'
												clipRule='evenodd'
											/>
										</svg>
									</div>
									<div>
										<h3 className='font-bold text-lg text-[#4a5178]'>
											Automatic Redirect
										</h3>
										<p className='text-[#4a5178]/70'>
											Redirecting to hub in{' '}
											<span className='inline-flex items-center justify-center bg-[#0029f5] text-white font-bold w-8 h-8 rounded-lg mx-1'>
												{countdown}
											</span>{' '}
											seconds
										</p>
									</div>
								</div>

								<button
									onMouseEnter={() => setIsHovering(true)}
									onMouseLeave={() => setIsHovering(false)}
									onClick={() => router.push('/hub')}
									className={`w-full py-3 rounded-lg font-medium transition-all duration-300 
                    ${
											isHovering
												? 'bg-[#0029f5] text-white shadow-lg shadow-[#CAD3FF] scale-[1.02]'
												: 'bg-[#0029f5] text-white'
										}`}
								>
									<span className='flex items-center justify-center'>
										Go to Hub Now
										<svg
											xmlns='http://www.w3.org/2000/svg'
											viewBox='0 0 20 20'
											fill='currentColor'
											className={`w-5 h-5 ml-2 transition-transform duration-300 ${
												isHovering ? 'translate-x-1' : ''
											}`}
										>
											<path
												fillRule='evenodd'
												d='M5 10a.75.75 0 01.75-.75h6.638L10.23 7.29a.75.75 0 111.04-1.08l3.5 3.25a.75.75 0 010 1.08l-3.5 3.25a.75.75 0 11-1.04-1.08l2.158-1.96H5.75A.75.75 0 015 10z'
												clipRule='evenodd'
											/>
										</svg>
									</span>
								</button>
							</div>
						</div>
					</div>
				</div>
			</div>
		</>
	);
}
