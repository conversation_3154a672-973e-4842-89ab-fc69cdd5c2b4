"use client";

import { getUserProjects } from "@/lib/supabase/userProjects";
import { createClient } from "@/utils/supabase/client";
import {
  createContext,
  useContext,
  useEffect,
  useState,
  useCallback,
  useRef,
} from "react";
import { useParams, useRouter } from "next/navigation";
import {
  Project,
  ProjectContextType,
  ProjectRecord,
  ProjectInvite,
  ProjectInviteRecord,
} from "./types";
import { RealtimeChannel } from "@supabase/supabase-js";
import { useToast } from "@/hooks/use-toast";

const ProjectContext = createContext<ProjectContextType | undefined>(undefined);

export const ProjectProvider = ({
  children,
}: {
  children: React.ReactNode;
}) => {
  const [projects, setProjects] = useState<Project[]>([]);
  const [selectedProject, setSelectedProjectState] = useState<Project | null>(
    null
  );
  const [isLoadingProjects, setIsLoadingProjects] = useState(true);

  // Invite-related state
  const [invites, setInvites] = useState<ProjectInvite[]>([]);
  const [isLoadingInvites, setIsLoadingInvites] = useState(true);

  // Track if we have Cloudflare data to avoid overwriting it
  const hasCloudflareDataRef = useRef(false);

  const supabase = createClient();
  const router = useRouter();
  const { toast } = useToast();
  const params = useParams();
  const urlProjectId = params?.projectId as string | undefined;

  const determineProjectToSelect = (
    currentProjects: Project[],
    targetProjectId?: string
  ): Project | null => {
    if (currentProjects.length === 0) {
      return null;
    }
    if (targetProjectId) {
      const projectFromUrl = currentProjects.find(
        (p) => p.id.toString() === targetProjectId
      );
      if (projectFromUrl) {
        return projectFromUrl;
      }
    }
    const creatorProject = currentProjects.find((p) => p.is_creator);
    if (creatorProject) {
      return creatorProject;
    }
    return currentProjects[0];
  };

  useEffect(() => {
    const fetchProjectsAndHandleSelection = async () => {
      console.log(
        "[fetchProjectsAndHandleSelection] Starting initial project fetch..."
      );
      setIsLoadingProjects(true);
      try {
        const fetchedProjects = await getUserProjects();
        const currentProjects = fetchedProjects || [];
        console.log(
          "[fetchProjectsAndHandleSelection] Fetched projects from Supabase:",
          currentProjects.length
        );

        // Only overwrite if we don't have Cloudflare data yet
        if (!hasCloudflareDataRef.current) {
          console.log(
            "[fetchProjectsAndHandleSelection] No Cloudflare data yet, setting fresh projects"
          );
          setProjects(currentProjects);
        } else {
          console.log(
            "[fetchProjectsAndHandleSelection] 🛡️ Cloudflare data exists, preserving it during project refresh"
          );
          // Preserve Cloudflare data when we already have it
          setProjects((prevProjects) => {
            return currentProjects.map((fetchedProject) => {
              const existingProject = prevProjects.find(
                (p) => p.id === fetchedProject.id
              );
              if (
                existingProject &&
                (existingProject.siteTag !== undefined ||
                  existingProject.siteName !== undefined ||
                  existingProject.hasWebAnalytics !== undefined)
              ) {
                console.log(
                  `[fetchProjectsAndHandleSelection] 🔄 Preserving Cloudflare data for project ${fetchedProject.id}`
                );
                return {
                  ...fetchedProject,
                  siteTag: existingProject.siteTag,
                  siteName: existingProject.siteName,
                  hasWebAnalytics: existingProject.hasWebAnalytics,
                };
              }
              return fetchedProject;
            });
          });
        }

        const projectToSelect = determineProjectToSelect(
          currentProjects,
          urlProjectId
        );
        setSelectedProjectState(projectToSelect);
      } catch (error) {
        console.error(
          "Error fetching projects or setting selected project:",
          error
        );
        setProjects([]);
        setSelectedProjectState(null);
      } finally {
        setIsLoadingProjects(false);
      }
    };
    fetchProjectsAndHandleSelection();
  }, [urlProjectId]); // Remove projects dependency to avoid infinite loop

  useEffect(() => {
    console.log("[realtime useEffect] Setting up realtime listeners...");

    const refreshProjects = async () => {
      console.log(
        "[refreshProjects] Refreshing projects due to realtime event or URL project ID change..."
      );
      try {
        const fetchedProjects = await getUserProjects();
        const currentProjects = fetchedProjects || [];

        // Preserve Cloudflare data (siteTag, siteName, hasWebAnalytics) when refreshing
        const projectsWithCloudflareData = currentProjects.map(
          (fetchedProject) => {
            const existingProject = projects.find(
              (p) => p.id === fetchedProject.id
            );
            if (
              existingProject &&
              (existingProject.siteTag !== undefined ||
                existingProject.siteName !== undefined ||
                existingProject.hasWebAnalytics !== undefined)
            ) {
              // Preserve Cloudflare data from context
              console.log(
                `[refreshProjects] Preserving Cloudflare data for project ${fetchedProject.id}:`,
                {
                  siteTag: existingProject.siteTag,
                  siteName: existingProject.siteName,
                  hasWebAnalytics: existingProject.hasWebAnalytics,
                }
              );
              return {
                ...fetchedProject,
                siteTag: existingProject.siteTag,
                siteName: existingProject.siteName,
                hasWebAnalytics: existingProject.hasWebAnalytics,
              };
            }
            console.log(
              `[refreshProjects] No Cloudflare data to preserve for project ${fetchedProject.id}`
            );
            return fetchedProject;
          }
        );

        setProjects(projectsWithCloudflareData);

        setSelectedProjectState((prevSelectedProject) => {
          if (urlProjectId) {
            const projectFromUrl = projectsWithCloudflareData.find(
              (p) => p.id.toString() === urlProjectId
            );
            if (projectFromUrl) {
              if (
                JSON.stringify(projectFromUrl) !==
                JSON.stringify(prevSelectedProject)
              ) {
                return projectFromUrl;
              }
              return prevSelectedProject;
            }
          }

          if (prevSelectedProject) {
            const stillExistsAndAccessible = projectsWithCloudflareData.find(
              (p) => p.id === prevSelectedProject.id
            );
            if (stillExistsAndAccessible) {
              if (
                JSON.stringify(stillExistsAndAccessible) !==
                JSON.stringify(prevSelectedProject)
              ) {
                return stillExistsAndAccessible;
              }
              return prevSelectedProject;
            }
          }
          return determineProjectToSelect(projectsWithCloudflareData);
        });
      } catch (error) {
        console.error("Error refreshing projects:", error);
      }
    };

    // Only setup realtime listeners, don't auto-refresh on isLoadingProjects change
    // This prevents overriding Cloudflare data that was set by server-side layout

    const projectsChannel: RealtimeChannel = supabase
      .channel("projects_changes")
      .on(
        "postgres_changes",
        { event: "INSERT", schema: "public", table: "projects" },
        (payload) => {
          console.log("Realtime: New project INSERT detected", payload);
          refreshProjects();
        }
      )
      .on(
        "postgres_changes",
        { event: "UPDATE", schema: "public", table: "projects" },
        (payload) => {
          console.log("Realtime: Project UPDATE detected", payload);
          const updatedRecord = payload.new as ProjectRecord;

          console.log(
            `[realtime UPDATE] Updating project ${updatedRecord.id} in state`
          );
          setProjects((currentProjects) => {
            const updatedProjects = currentProjects.map((p) => {
              if (p.id === updatedRecord.id) {
                // Preserve Cloudflare data during realtime updates
                const preservedData = {
                  siteTag: p.siteTag,
                  siteName: p.siteName,
                  hasWebAnalytics: p.hasWebAnalytics,
                };
                console.log(
                  `[realtime UPDATE] Preserving Cloudflare data for project ${p.id}:`,
                  preservedData
                );
                return {
                  ...p,
                  ...updatedRecord,
                  // Restore Cloudflare data
                  ...preservedData,
                };
              }
              return p;
            });
            return updatedProjects;
          });
          setSelectedProjectState((currentSelected) => {
            if (currentSelected && currentSelected.id === updatedRecord.id) {
              const newSelectedState: Project = {
                ...currentSelected,
                ...updatedRecord,
                // Preserve Cloudflare data in selected project too
                siteTag: currentSelected.siteTag,
                siteName: currentSelected.siteName,
                hasWebAnalytics: currentSelected.hasWebAnalytics,
              };
              if (
                JSON.stringify(newSelectedState) !==
                JSON.stringify(currentSelected)
              ) {
                return newSelectedState;
              }
            }
            if (urlProjectId && updatedRecord.id.toString() === urlProjectId) {
              return {
                ...(projects.find((p) => p.id === updatedRecord.id) ||
                  ({} as Project)),
                ...updatedRecord,
              };
            }
            return currentSelected;
          });
        }
      )
      .on(
        "postgres_changes",
        { event: "DELETE", schema: "public", table: "projects" },
        (payload) => {
          console.log("Realtime: Project DELETE detected", payload);
          const deletedRecord = payload.old as Partial<ProjectRecord>;
          const deletedProjectId = deletedRecord.id;
          if (deletedProjectId === undefined) return;

          let newSelectedProjectAfterDelete: Project | null = null;

          setProjects((prevProjects) => {
            const updatedProjects = prevProjects.filter(
              (p) => p.id !== deletedProjectId
            );
            newSelectedProjectAfterDelete = determineProjectToSelect(
              updatedProjects,
              urlProjectId
            );
            return updatedProjects;
          });
          setSelectedProjectState(newSelectedProjectAfterDelete);
        }
      )
      .subscribe((status, err) => {
        if (status === "SUBSCRIBED") {
          console.log("Realtime: Subscribed to 'projects' table changes!");
        } else if (err) {
          console.error(
            `Realtime: Error subscribing to 'projects' table: ${err.message}`
          );
        }
      });

    const projectUserChannel: RealtimeChannel = supabase
      .channel("project_user_changes")
      .on(
        "postgres_changes",
        { event: "*", schema: "public", table: "project_user" },
        (payload) => {
          console.log(
            "Realtime: Project_user change detected",
            payload.eventType,
            payload
          );
          if (payload.eventType === "DELETE") {
            console.log(
              "Realtime: project_user DELETE might affect current selection or project list. Refreshing."
            );
            refreshProjects();
          } else {
            refreshProjects();
          }
        }
      )
      .subscribe((status, err) => {
        if (status === "SUBSCRIBED") {
          console.log("Realtime: Subscribed to 'project_user' table changes!");
        } else if (err) {
          console.error(
            `Realtime: Error subscribing to 'project_user' table: ${err.message}`
          );
        }
      });

    const projectInvitesChannel: RealtimeChannel = supabase
      .channel("project_invites_changes")
      .on(
        "postgres_changes",
        {
          event: "INSERT",
          schema: "public",
          table: "project_invites",
          filter: "status=eq.invited",
        },
        (payload) => {
          console.log("Realtime: New invite INSERT detected", payload);
          fetchInvites();
        }
      )
      .on(
        "postgres_changes",
        { event: "UPDATE", schema: "public", table: "project_invites" },
        (payload) => {
          console.log("Realtime: Invite UPDATE detected", payload);
          fetchInvites();
          const updatedInvite = payload.new as ProjectInviteRecord;
          if (updatedInvite.status === "accepted") {
            refreshProjects();
          }
        }
      )
      .on(
        "postgres_changes",
        { event: "DELETE", schema: "public", table: "project_invites" },
        (payload) => {
          console.log("Realtime: Invite DELETE detected", payload);
          const deletedInvite = payload.old as ProjectInviteRecord;

          setInvites((prev) =>
            prev.filter((invite) => invite.id !== deletedInvite.id)
          );
        }
      )
      .subscribe((status, err) => {
        if (status === "SUBSCRIBED") {
          console.log(
            "Realtime: Subscribed to 'project_invites' table changes!"
          );
        } else if (err) {
          console.error(
            `Realtime: Error subscribing to 'project_invites' table: ${err.message}`
          );
        }
      });

    return () => {
      console.log("Realtime: Unsubscribing from channels.");
      supabase.removeChannel(projectsChannel);
      supabase.removeChannel(projectUserChannel);
      supabase.removeChannel(projectInvitesChannel);
    };
  }, [supabase, urlProjectId]);

  const setSelectedProject = (project: Project | null) => {
    setSelectedProjectState(project);
  };

  // Invite-related functions
  const fetchInvites = useCallback(async () => {
    try {
      setIsLoadingInvites(true);
      const response = await fetch("/api/my-invites");
      const data = (await response.json()) as {
        invites?: ProjectInvite[];
        error?: string;
      };

      if (response.ok) {
        setInvites(data.invites || []);
      } else {
        console.error("Error fetching invites:", data.error);
      }
    } catch (error) {
      console.error("Error fetching invites:", error);
    } finally {
      setIsLoadingInvites(false);
    }
  }, []);

  const acceptInvite = useCallback(
    async (inviteId: number, projectId: number) => {
      try {
        const response = await fetch("/api/my-invites", {
          method: "PATCH",
          headers: { "Content-Type": "application/json" },
          body: JSON.stringify({ inviteId, projectId }),
        });

        const data = (await response.json()) as {
          error?: string;
        };

        if (!response.ok) {
          throw new Error(data.error || "Error accepting invite");
        }

        setInvites((prev) => prev.filter((inv) => inv.id !== inviteId));

        toast({
          title: "Success",
          description: "Invite accepted successfully!",
        });

        const fetchedProjects = await getUserProjects();
        const currentProjects = fetchedProjects || [];

        // Preserve Cloudflare data when accepting invite
        const projectsWithCloudflareData = currentProjects.map(
          (fetchedProject) => {
            const existingProject = projects.find(
              (p) => p.id === fetchedProject.id
            );
            if (
              existingProject &&
              (existingProject.siteTag !== undefined ||
                existingProject.siteName !== undefined ||
                existingProject.hasWebAnalytics !== undefined)
            ) {
              // Preserve Cloudflare data from context
              return {
                ...fetchedProject,
                siteTag: existingProject.siteTag,
                siteName: existingProject.siteName,
                hasWebAnalytics: existingProject.hasWebAnalytics,
              };
            }
            return fetchedProject;
          }
        );

        setProjects(projectsWithCloudflareData);

        router.refresh();
        return true;
      } catch (error) {
        console.error("Error accepting invite:", error);
        toast({
          title: "Error",
          description:
            error instanceof Error ? error.message : "Error accepting invite",
          variant: "destructive",
        });
        return false;
      }
    },
    [router, toast, projects]
  );

  const declineInvite = useCallback(
    async (inviteId: number) => {
      try {
        const response = await fetch("/api/my-invites", {
          method: "DELETE",
          headers: { "Content-Type": "application/json" },
          body: JSON.stringify({ inviteId }),
        });

        if (!response.ok) {
          throw new Error("Failed to decline invite");
        }

        setInvites((prev) => prev.filter((inv) => inv.id !== inviteId));

        toast({
          title: "Success",
          description: "Invite declined successfully!",
        });
        return true;
      } catch (error) {
        console.error("Error declining invite:", error);
        toast({
          title: "Error",
          description: "Error declining invite",
          variant: "destructive",
        });
        return false;
      }
    },
    [toast]
  );

  // Computed values
  const hasInvites = invites.length > 0;
  const inviteCount = invites.length;
  const isLoading = isLoadingProjects || isLoadingInvites;

  // New function to update project info from server-side data
  const updateProjectInfo = useCallback(
    (
      projectId: number,
      projectInfo: {
        siteName: string | undefined;
        siteTag: string | undefined;
        hasWebAnalytics: boolean | undefined;
      }
    ) => {
      console.log(
        `[updateProjectInfo] 🟢 Setting Cloudflare data for project ${projectId}:`,
        projectInfo
      );

      // Mark that we now have Cloudflare data
      hasCloudflareDataRef.current = true;

      setProjects((prevProjects) => {
        const updatedProjects = prevProjects.map((project) =>
          project.id === projectId
            ? {
                ...project,
                ...projectInfo,
              }
            : project
        );
        console.log(
          `[updateProjectInfo] ✅ Updated projects state with Cloudflare data for project ${projectId}`
        );
        return updatedProjects;
      });

      // Also update selected project if it's the same
      setSelectedProjectState((prevSelected) =>
        prevSelected && prevSelected.id === projectId
          ? {
              ...prevSelected,
              ...projectInfo,
            }
          : prevSelected
      );
    },
    []
  );

  // Fetch invites on mount
  useEffect(() => {
    fetchInvites();
  }, [fetchInvites]);

  return (
    <ProjectContext.Provider
      value={{
        projects,
        setProjects,
        selectedProject,
        setSelectedProject,
        isLoadingProjects,
        invites,
        hasInvites,
        inviteCount,
        isLoading,
        isLoadingInvites,
        acceptInvite,
        declineInvite,
        refreshInvites: fetchInvites,
        updateProjectInfo,
      }}
    >
      {children}
    </ProjectContext.Provider>
  );
};

export const useProject = () => {
  const context = useContext(ProjectContext);
  if (!context) {
    throw new Error("useProject must be used within a ProjectProvider");
  }
  return context;
};
