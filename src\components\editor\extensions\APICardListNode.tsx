import { Node } from '@tiptap/core';
import { NodeViewWrapper, ReactNodeViewRenderer } from '@tiptap/react';
import React, { useState, useCallback } from 'react';
import { Edit3, Save, X, Plus, Trash2 } from 'lucide-react';
import { Button } from '@/components/ui/button';
import type { NodeViewProps } from '@tiptap/react';
import { RichTextInput } from '@/components/ui/RichTextInput';
import { useToast } from '@/components/ToastProvider';

// Interface for individual API card data
interface APICardData {
	title: string;
	description: string;
	link: string;
	type: 'GET' | 'POST' | 'PUT' | 'DELETE' | 'PATCH';
}

// Get method color classes
const getMethodColors = (method: string) => {
	switch (method) {
		case 'GET':
			return {
				badge: 'bg-green-100 text-green-800 border-green-200',
				border: 'border-green-200 hover:border-green-300',
			};
		case 'POST':
			return {
				badge: 'bg-blue-100 text-blue-800 border-blue-200',
				border: 'border-blue-200 hover:border-blue-300',
			};
		case 'PUT':
			return {
				badge: 'bg-purple-100 text-purple-800 border-purple-200',
				border: 'border-purple-200 hover:border-purple-300',
			};
		case 'PATCH':
			return {
				badge: 'bg-orange-100 text-orange-800 border-orange-200',
				border: 'border-orange-200 hover:border-orange-300',
			};
		case 'DELETE':
			return {
				badge: 'bg-red-100 text-red-800 border-red-200',
				border: 'border-red-200 hover:border-red-300',
			};
		default:
			return {
				badge: 'bg-gray-100 text-gray-800 border-gray-200',
				border: 'border-gray-200 hover:border-gray-300',
			};
	}
};

// React component for the API card list
const APICardListComponent: React.FC<NodeViewProps> = ({
	node,
	updateAttributes,
	selected,
}) => {
	const [isEditing, setIsEditing] = useState(false);
	const [editingCardIndex, setEditingCardIndex] = useState<number | null>(null);
	const { addToast } = useToast();
	const [cards, setCards] = useState<APICardData[]>(
		node.attrs.cards || [
			{
				title: 'Get Items',
				description:
					'This example demonstrates how to use a API card for a GET endpoint.',
				link: '',
				type: 'GET',
			},
			{
				title: 'Create Item',
				description:
					'This example demonstrates how to use a API card for a POST endpoint.',
				link: '',
				type: 'POST',
			},
		]
	);

	const handleSave = useCallback(() => {
		// Validate that all cards have required fields
		const invalidCards = cards.filter(
			(card) =>
				!card.title.trim() || !card.description.replace(/<[^>]*>/g, '').trim()
		);

		if (invalidCards.length > 0) {
			addToast('All cards must have a title and description', 'warning');
			return;
		}

		updateAttributes({ cards });
		setIsEditing(false);
		setEditingCardIndex(null);
		addToast('API Cards updated successfully', 'success', 'Success');
	}, [cards, updateAttributes, addToast]);

	const handleCancel = useCallback(() => {
		setCards(node.attrs.cards || []);
		setIsEditing(false);
		setEditingCardIndex(null);
	}, [node.attrs.cards]);

	const handleCardChange = useCallback(
		(index: number, field: keyof APICardData, value: string) => {
			setCards((prev) =>
				prev.map((card, i) =>
					i === index ? { ...card, [field]: value } : card
				)
			);
		},
		[]
	);

	const handleAddCard = useCallback(() => {
		const newCard: APICardData = {
			title: 'New Endpoint',
			description: 'Description for the new endpoint.',
			link: '',
			type: 'GET',
		};
		setCards((prev) => [...prev, newCard]);
		setEditingCardIndex(cards.length);
	}, [cards.length]);

	const handleRemoveCard = useCallback(
		(index: number) => {
			setCards((prev) => prev.filter((_, i) => i !== index));
			if (editingCardIndex === index) {
				setEditingCardIndex(null);
			} else if (editingCardIndex !== null && editingCardIndex > index) {
				setEditingCardIndex(editingCardIndex - 1);
			}
		},
		[editingCardIndex]
	);

	const handleCardClick = useCallback(() => {
		// Removed redirection - cards are now non-clickable for links
		// This prevents unwanted navigation when clicking on cards in the editor
	}, []);

	if (!isEditing) {
		return (
			<NodeViewWrapper
				className='api-card-list-node'
				as='div'
				data-drag-handle=''
				contentEditable={false}
			>
				<div
					className={`
            my-4 relative group
            ${selected ? 'ring-2 ring-blue-400/50 rounded-lg' : ''}
          `}
				>
					{/* Edit button - only visible on hover */}
					<Button
						variant='ghost'
						size='sm'
						onClick={(e) => {
							e.stopPropagation();
							setIsEditing(true);
						}}
						className='absolute top-2 right-2 h-6 px-2 text-xs opacity-0 group-hover:opacity-100 transition-opacity duration-200 bg-white/80 dark:bg-slate-800/80 hover:bg-gray-100 dark:hover:bg-slate-700 z-10'
					>
						<Edit3 className='w-3 h-3 mr-1' />
						Edit
					</Button>

					{/* Cards Grid - Always 2 columns */}
					<div className='grid grid-cols-1 md:grid-cols-2 gap-4'>
						{cards.map((card, index) => {
							const colors = getMethodColors(card.type);
							return (
								<div
									key={index}
									className={`
                    bg-white dark:bg-slate-800 border border-gray-200 dark:border-gray-700
                    rounded-lg shadow-sm transition-all duration-200
                    ${card.link ? colors.border : ''}
                    overflow-hidden
                  `}
									onClick={handleCardClick}
								>
									{/* Content Container */}
									<div className='p-4'>
										<div className='flex flex-col items-start'>
											{/* Title and Method Badge */}
											<div className='flex items-center justify-between w-full mb-2'>
												<h3 className='text-md font-semibold text-gray-900 dark:text-gray-100'>
													{card.title}
												</h3>
												<span
													className={`text-xs font-medium px-2 py-1 rounded border ${colors.badge}`}
												>
													{card.type}
												</span>
											</div>

											{/* Description */}
											<div
												className='text-sm text-gray-600 dark:text-gray-400 leading-snug prose-sm w-full'
												dangerouslySetInnerHTML={{
													__html: card.description,
												}}
											/>
										</div>
									</div>
								</div>
							);
						})}
					</div>
				</div>
			</NodeViewWrapper>
		);
	}

	return (
		<NodeViewWrapper
			className='api-card-list-node'
			as='div'
			data-drag-handle=''
			contentEditable={false}
		>
			<div
				className={`
          my-4 p-4 bg-white dark:bg-slate-800 border-2 border-blue-400/50 dark:border-blue-600/50 
          rounded-lg shadow-lg transition-all duration-300 ease-in-out
        `}
			>
				{/* Header */}
				<div className='flex items-center justify-between mb-4'>
					<h4 className='text-sm font-semibold text-gray-900 dark:text-gray-100'>
						Edit API Cards
					</h4>
					<div className='flex items-center space-x-2'>
						<Button
							variant='ghost'
							size='sm'
							onClick={handleCancel}
							className='h-6 px-2 text-xs hover:bg-red-100/50 dark:hover:bg-red-900/30 text-red-600 dark:text-red-400'
						>
							<X className='w-3 h-3 mr-1' />
							Cancel
						</Button>
						<Button
							variant='default'
							size='sm'
							onClick={handleSave}
							className='h-6 px-2 text-xs bg-blue-600 hover:bg-blue-700 text-white'
						>
							<Save className='w-3 h-3 mr-1' />
							Save
						</Button>
					</div>
				</div>

				{/* Cards Editor */}
				<div className='space-y-4'>
					{cards.map((card, index) => (
						<div
							key={index}
							className='p-4 border border-gray-200 dark:border-gray-600 rounded-lg bg-gray-50 dark:bg-slate-700'
						>
							<div className='flex items-center justify-between mb-3'>
								<div className='flex items-center space-x-2'>
									<h5 className='text-sm font-medium text-gray-700 dark:text-gray-300'>
										{card.title || `API Card ${index + 1}`}
									</h5>
									<span
										className={`text-xs font-medium px-2 py-1 rounded border ${getMethodColors(card.type).badge}`}
									>
										{card.type}
									</span>
								</div>
								<div className='flex items-center space-x-2'>
									<Button
										variant='ghost'
										size='sm'
										onClick={() =>
											setEditingCardIndex(
												editingCardIndex === index ? null : index
											)
										}
										className='h-6 px-2 text-xs'
									>
										<Edit3 className='w-3 h-3 mr-1' />
										{editingCardIndex === index ? 'Collapse' : 'Edit'}
									</Button>
									{cards.length > 1 && (
										<Button
											variant='ghost'
											size='sm'
											onClick={() => handleRemoveCard(index)}
											className='h-6 px-2 text-xs text-red-600 hover:bg-red-100/50 dark:hover:bg-red-900/30'
										>
											<Trash2 className='w-3 h-3' />
										</Button>
									)}
								</div>
							</div>

							{editingCardIndex === index && (
								<div className='space-y-3'>
									{/* Title and Type */}
									<div className='grid grid-cols-1 md:grid-cols-2 gap-3'>
										<div>
											<label className='block text-xs font-medium text-gray-700 dark:text-gray-300 mb-1'>
												Title *
											</label>
											<input
												type='text'
												value={card.title}
												onChange={(e) =>
													handleCardChange(index, 'title', e.target.value)
												}
												placeholder='Enter endpoint title'
												className='w-full px-3 py-2 text-sm bg-white dark:bg-slate-600 border border-gray-300 dark:border-gray-500 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:text-white'
											/>
										</div>
										<div>
											<label className='block text-xs font-medium text-gray-700 dark:text-gray-300 mb-1'>
												Method *
											</label>
											<select
												value={card.type}
												onChange={(e) =>
													handleCardChange(
														index,
														'type',
														e.target.value as APICardData['type']
													)
												}
												className='w-full px-3 py-2 text-sm bg-white dark:bg-slate-600 border border-gray-300 dark:border-gray-500 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:text-white'
											>
												<option value='GET'>GET</option>
												<option value='POST'>POST</option>
												<option value='PUT'>PUT</option>
												<option value='PATCH'>PATCH</option>
												<option value='DELETE'>DELETE</option>
											</select>
										</div>
									</div>

									{/* Description */}
									<div>
										<label className='block text-xs font-medium text-gray-700 dark:text-gray-300 mb-1'>
											Description *
										</label>
										<RichTextInput
											value={card.description}
											onChange={(html) =>
												handleCardChange(index, 'description', html)
											}
											placeholder="Brief explanation of the endpoint's purpose..."
											variant='default'
											className='w-full'
										/>
									</div>

									{/* Link */}
									<div>
										<label className='block text-xs font-medium text-gray-700 dark:text-gray-300 mb-1'>
											Link
										</label>
										<input
											type='url'
											value={card.link}
											onChange={(e) =>
												handleCardChange(index, 'link', e.target.value)
											}
											placeholder='https://example.com/api/docs or /api/endpoint'
											className='w-full px-3 py-2 text-sm bg-white dark:bg-slate-600 border border-gray-300 dark:border-gray-500 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:text-white'
										/>
									</div>
								</div>
							)}
						</div>
					))}

					{/* Add Card Button */}
					<Button
						variant='outline'
						size='sm'
						onClick={handleAddCard}
						className='w-full h-10 border-2 border-dashed border-blue-300 dark:border-blue-600 text-blue-600 dark:text-blue-400 hover:border-blue-400 dark:hover:border-blue-500 hover:bg-blue-50 dark:hover:bg-blue-900/20'
					>
						<Plus className='w-4 h-4 mr-2' />
						Add API Card
					</Button>
				</div>
			</div>
		</NodeViewWrapper>
	);
};

// Tiptap extension definition
export const APICardListNode = Node.create({
	name: 'apiCardListNode',

	group: 'block',

	atom: true,

	draggable: false,

	selectable: true,

	addAttributes() {
		return {
			cards: {
				default: [],
				parseHTML: (element) => {
					try {
						const cardsData =
							element.getAttribute('data-cards') ||
							element.getAttribute('cards');
						if (cardsData) {
							return JSON.parse(cardsData);
						}
						return [];
					} catch {
						return [];
					}
				},
				renderHTML: (attributes) => {
					if (!attributes.cards || !Array.isArray(attributes.cards)) return {};
					return { 'data-cards': JSON.stringify(attributes.cards) };
				},
			},
		};
	},

	parseHTML() {
		return [
			{
				tag: 'div[data-type="api-card-list"]',
			},
		];
	},

	renderHTML({ HTMLAttributes }) {
		return [
			'div',
			{
				...HTMLAttributes,
				'data-type': 'api-card-list',
				class: 'api-card-list-node',
			},
		];
	},

	addNodeView() {
		return ReactNodeViewRenderer(APICardListComponent);
	},
});

export default APICardListNode;
