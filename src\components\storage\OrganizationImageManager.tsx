"use client";

import React, { useState } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger } from "../ui/tabs";
import ImageDashboard from "./ImageDashboard";
import BulkImageUpload from "./BulkImageUpload";
import ImageGallery from "./ImageGallery";

interface UploadResult {
  success: boolean;
  uploadedImages: Array<{
    name: string;
    path: string;
    url: string;
    size: number;
  }>;
  errors: string[];
  totalUploaded: number;
}

interface OrganizationImageManagerProps {
  projectId: number;
  organizationId: string;
}

export default function OrganizationImageManager({
  projectId,
  organizationId,
}: OrganizationImageManagerProps) {
  const [activeTab, setActiveTab] = useState("dashboard");
  const [refreshKey, setRefreshKey] = useState(0);

  const handleUploadComplete = (results: UploadResult) => {
    console.log("Upload completed:", results);
    // Trigger refresh of gallery and dashboard
    setRefreshKey((prev) => prev + 1);
    // Show success message
    if (results.success) {
      alert(
        `Upload concluído! ${results.totalUploaded} imagens enviadas com sucesso.`
      );
      // Switch to gallery tab to show uploaded images
      setActiveTab("gallery");
    }
  };

  const handleManageImages = () => {
    setActiveTab("gallery");
  };

  const handleUploadImages = () => {
    setActiveTab("upload");
  };

  return (
    <div className="space-y-6">
      <div className="border-b border-gray-200 dark:border-gray-700">
        <div className="flex items-center justify-between pb-4">
          <div>
            <h1 className="text-2xl font-bold text-gray-900 dark:text-gray-100">
              Gerenciamento de Imagens
            </h1>
            <p className="text-gray-600 dark:text-gray-400 mt-1">
              Gerencie e organize imagens para seus projetos
            </p>
          </div>
        </div>
      </div>

      <Tabs
        value={activeTab}
        onValueChange={setActiveTab}
        className="space-y-6"
      >
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="dashboard">Dashboard</TabsTrigger>
          <TabsTrigger value="upload">Upload</TabsTrigger>
          <TabsTrigger value="gallery">Galeria</TabsTrigger>
        </TabsList>

        <TabsContent value="dashboard" className="space-y-6">
          <ImageDashboard
            key={`dashboard-${refreshKey}`}
            organizationId={organizationId}
            onManageImages={handleManageImages}
            onUploadImages={handleUploadImages}
          />
        </TabsContent>

        <TabsContent value="upload" className="space-y-6">
          <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
            <div className="mb-6">
              <h2 className="text-xl font-semibold text-gray-900 dark:text-gray-100 mb-2">
                Upload de Imagens em Lote
              </h2>
              <p className="text-gray-600 dark:text-gray-400">
                Selecione múltiplas imagens para fazer upload de uma vez. Você
                pode adicionar texto alternativo e tags para melhor organização.
              </p>
            </div>
            <BulkImageUpload
              projectId={projectId}
              organizationId={organizationId}
              onUploadComplete={handleUploadComplete}
              maxFiles={20}
              maxSizePerFile={10 * 1024 * 1024} // 10MB
            />
          </div>
        </TabsContent>

        <TabsContent value="gallery" className="space-y-6">
          <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
            <ImageGallery
              key={`gallery-${refreshKey}`}
              projectId={projectId}
              organizationId={organizationId}
              onImageSelect={(image) => {
                console.log("Image selected:", image);
                // Handle image selection if needed
              }}
              selectionMode={false}
            />
          </div>
        </TabsContent>
      </Tabs>
    </div>
  );
}
