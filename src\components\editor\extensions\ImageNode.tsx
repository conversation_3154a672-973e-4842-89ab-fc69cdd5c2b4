import { Node } from '@tiptap/core';
import { <PERSON><PERSON><PERSON><PERSON>w<PERSON><PERSON>per, ReactNodeViewRenderer } from '@tiptap/react';

declare module '@tiptap/core' {
	interface Commands<ReturnType> {
		image: {
			/**
			 * Insert an image with the specified attributes.
			 */
			setImage: (attributes?: Partial<ImageAttrs>) => ReturnType;
		};
	}
}
import React, { useState, useCallback } from 'react';
import {
	Edit3,
	Save,
	X,
	ImageIcon,
	Search,
	Sun,
	Moon,
	HelpCircle,
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import type { NodeViewProps } from '@tiptap/react';
import { ImageSelector } from '../ImageSelector';
import { useProject } from '@/contexts';
import { useToast } from '@/components/ToastProvider';

// Interface for image attributes
interface ImageAttrs {
	src: string;
	srcDark?: string;
	size?: string;
	alt?: string;
}

// Tooltip component for help text
const Tooltip: React.FC<{ children: React.ReactNode; content: string }> = ({
	children,
	content,
}) => {
	const [isVisible, setIsVisible] = useState(false);

	return (
		<div className='relative inline-block'>
			<div
				onMouseEnter={() => setIsVisible(true)}
				onMouseLeave={() => setIsVisible(false)}
			>
				{children}
			</div>
			{isVisible && (
				<div className='absolute z-50 px-2.5 py-1.5 text-xs text-white bg-gray-900 dark:bg-gray-700 rounded-md shadow-lg -top-1 transform -translate-y-full w-40 whitespace-normal leading-snug'>
					{content}
					<div className='absolute top-full left-2 w-0 h-0 border-l-3 border-r-3 border-t-3 border-transparent border-t-gray-900 dark:border-t-gray-700'></div>
				</div>
			)}
		</div>
	);
};

// Size preset options
const SIZE_PRESETS = [
	{ label: 'Small', value: '25%', displayLabel: 'Small (25%)' },
	{ label: 'Medium', value: '50%', displayLabel: 'Medium (50%)' },
	{ label: 'Large', value: '75%', displayLabel: 'Large (75%)' },
	{ label: 'Full Width', value: '100%', displayLabel: 'Full (100%)' },
	{ label: 'Custom', value: 'custom', displayLabel: 'Custom' },
];

// React component for the image
const ImageComponent: React.FC<NodeViewProps> = ({
	node,
	updateAttributes,
	selected,
}) => {
	const [isEditing, setIsEditing] = useState(false);
	const [showImageSelector, setShowImageSelector] = useState(false);
	const [selectingForDarkMode, setSelectingForDarkMode] = useState(false);
	const [isDarkMode, setIsDarkMode] = useState(false);
	const { selectedProject } = useProject();
	const { addToast } = useToast();

	// Form data state
	const [formData, setFormData] = useState<ImageAttrs>({
		src: node.attrs.src || '',
		srcDark: node.attrs.srcDark || '',
		size: node.attrs.size || '50%',
		alt: node.attrs.alt || '',
	});

	// Size selection state
	const [selectedSizePreset, setSelectedSizePreset] = useState(() => {
		const currentSize = node.attrs.size || '50%';
		const preset = SIZE_PRESETS.find((p) => p.value === currentSize);
		return preset ? preset.value : 'custom';
	});
	const [customSize, setCustomSize] = useState(() => {
		const currentSize = node.attrs.size || '50%';
		const preset = SIZE_PRESETS.find((p) => p.value === currentSize);
		return preset ? '' : currentSize;
	});

	// Update formData when node attrs change
	React.useEffect(() => {
		const newSize = node.attrs.size || '50%';
		setFormData({
			src: node.attrs.src || '',
			srcDark: node.attrs.srcDark || '',
			size: newSize,
			alt: node.attrs.alt || '',
		});

		// Update size preset selection
		const preset = SIZE_PRESETS.find((p) => p.value === newSize);
		if (preset) {
			setSelectedSizePreset(preset.value);
			setCustomSize('');
		} else {
			setSelectedSizePreset('custom');
			setCustomSize(newSize);
		}
	}, [node.attrs.src, node.attrs.srcDark, node.attrs.size, node.attrs.alt]);

	// Check if dark mode is active
	React.useEffect(() => {
		const checkDarkMode = () => {
			setIsDarkMode(document.documentElement.classList.contains('dark'));
		};

		checkDarkMode();
		// Watch for dark mode changes
		const observer = new MutationObserver(checkDarkMode);
		observer.observe(document.documentElement, {
			attributes: true,
			attributeFilter: ['class'],
		});

		return () => observer.disconnect();
	}, []);

	const handleSave = useCallback(() => {
		// Validate required fields
		if (!formData.src.trim()) {
			addToast('Image source is required', 'warning');
			return;
		}

		updateAttributes(formData);
		setIsEditing(false);
		addToast('Image updated successfully', 'success', 'Success');
	}, [formData, updateAttributes, addToast]);

	const handleCancel = useCallback(() => {
		const originalSize = node.attrs.size || '50%';
		setFormData({
			src: node.attrs.src || '',
			srcDark: node.attrs.srcDark || '',
			size: originalSize,
			alt: node.attrs.alt || '',
		});

		// Reset size selection
		const preset = SIZE_PRESETS.find((p) => p.value === originalSize);
		if (preset) {
			setSelectedSizePreset(preset.value);
			setCustomSize('');
		} else {
			setSelectedSizePreset('custom');
			setCustomSize(originalSize);
		}

		setIsEditing(false);
	}, [node.attrs]);

	const handleInputChange = useCallback(
		(field: keyof ImageAttrs, value: string) => {
			setFormData((prev) => ({ ...prev, [field]: value }));
		},
		[]
	);

	const handleSizePresetChange = useCallback(
		(presetValue: string) => {
			setSelectedSizePreset(presetValue);
			if (presetValue === 'custom') {
				// Keep current custom size or default to current size
				const currentCustom = customSize || formData.size || '50%';
				setCustomSize(currentCustom);
				handleInputChange('size', currentCustom);
			} else {
				// Use preset value
				setCustomSize('');
				handleInputChange('size', presetValue);
			}
		},
		[customSize, formData.size, handleInputChange]
	);

	const handleCustomSizeChange = useCallback(
		(value: string) => {
			setCustomSize(value);
			handleInputChange('size', value);
		},
		[handleInputChange]
	);

	const handleImageSelect = useCallback(
		(imageUrl: string) => {
			if (selectingForDarkMode) {
				handleInputChange('srcDark', imageUrl);
				setSelectingForDarkMode(false);
			} else {
				handleInputChange('src', imageUrl);
			}
			setShowImageSelector(false);
		},
		[handleInputChange, selectingForDarkMode]
	);

	const handleOpenImageSelector = useCallback(
		(forDarkMode: boolean = false) => {
			setSelectingForDarkMode(forDarkMode);
			setShowImageSelector(true);
		},
		[]
	);
	// Determine which image to show based on dark mode
	const currentImageSrc =
		isDarkMode && (isEditing ? formData.srcDark : node.attrs.srcDark)
			? isEditing
				? formData.srcDark
				: node.attrs.srcDark
			: isEditing
				? formData.src
				: node.attrs.src;

	if (!isEditing) {
		return (
			<NodeViewWrapper className='image-node' as='div' contentEditable={false}>
				<div
					className={`
          my-4 relative group block max-w-full mx-auto text-center
          ${selected ? 'ring-2 ring-blue-400/50 rounded-lg' : ''}
        `}
				>
					{/* Edit button - only visible on hover */}
					{selected && (
						<Button
							variant='ghost'
							size='sm'
							onClick={() => setIsEditing(true)}
							className='absolute top-2 right-2 h-6 px-2 text-xs opacity-0 group-hover:opacity-100 transition-opacity duration-200 bg-white/80 dark:bg-slate-800/80 hover:bg-gray-100 dark:hover:bg-slate-700 z-10 '
						>
							<Edit3 className='w-3 h-3 mr-1' />
							Edit
						</Button>
					)}

					{/* Dark mode indicator */}
					{node.attrs.srcDark && (
						<div className='absolute top-2 left-2 flex items-center space-x-1 bg-black/50 text-white px-2 py-1 rounded text-xs'>
							{isDarkMode ? (
								<>
									<Moon className='w-3 h-3' />
									<span>Dark</span>
								</>
							) : (
								<>
									<Sun className='w-3 h-3' />
									<span>Light</span>
								</>
							)}
						</div>
					)}

					{/* Image Display */}
					{currentImageSrc ? (
						<img
							src={currentImageSrc}
							alt={node.attrs.alt || ''}
							width={parseInt(node.attrs.size?.replace('px', '') || '200')}
							height={200}
							style={{
								width: node.attrs.size || '20px',
								maxWidth: node.attrs.size,
								height: 'auto',
							}}
							className='rounded-lg shadow-md hover:shadow-lg transition-shadow duration-200'
							onError={(e) => {
								e.currentTarget.src =
									"data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='400' height='300' viewBox='0 0 400 300'%3E%3Crect width='400' height='300' fill='%23f3f4f6'/%3E%3Ctext x='200' y='150' text-anchor='middle' dy='.3em' fill='%236b7280' font-size='20'%3EImage not found%3C/text%3E%3C/svg%3E";
							}}
						/>
					) : (
						<div
							style={{
								width: node.attrs.size || '20%',
								maxWidth: '100%',
								minHeight: '200px',
							}}
							className='flex items-center justify-center bg-gray-100 dark:bg-gray-700 rounded-lg p-8 mx-auto'
						>
							<div className='text-center'>
								<ImageIcon className='w-12 h-12 text-gray-400 mx-auto mb-2' />
								<p className='text-gray-500 dark:text-gray-400'>
									No image selected
								</p>
							</div>
						</div>
					)}
				</div>
			</NodeViewWrapper>
		);
	}

	return (
		<NodeViewWrapper
			className='image-node'
			as='div'
			data-drag-handle=''
			contentEditable={false}
		>
			<div
				className={`my-4 p-4 bg-white dark:bg-slate-800 border-2 border-blue-400/50 dark:border-blue-600/50 rounded-lg shadow-lg `}
			>
				{/* Header */}
				<div className='flex items-center justify-between mb-4 '>
					<h4 className='text-sm font-semibold text-gray-900 dark:text-gray-100'>
						Edit Image
					</h4>
					<div className='flex items-center space-x-2'>
						<Button
							variant='ghost'
							size='sm'
							onClick={handleCancel}
							className='h-6 px-2 text-xs hover:bg-red-100/50 dark:hover:bg-red-900/30 text-red-600 dark:text-red-400'
						>
							<X className='w-3 h-3 mr-1' />
							Cancel
						</Button>
						<Button
							variant='default'
							size='sm'
							onClick={handleSave}
							className='h-6 px-2 text-xs bg-blue-600 hover:bg-blue-700 text-white'
						>
							<Save className='w-3 h-3 mr-1' />
							Save
						</Button>
					</div>
				</div>

				{/* Edit Form */}
				<div className='space-y-5'>
					{/* Image Sources Section */}
					<div className='space-y-3'>
						<div className='flex items-center space-x-2 pb-2 border-b border-gray-200 dark:border-gray-600'>
							<ImageIcon className='w-4 h-4 text-gray-600 dark:text-gray-400' />
							<h5 className='text-sm font-medium text-gray-900 dark:text-gray-100'>
								Image Sources
							</h5>
						</div>

						{/* Main Image Source */}
						<div>
							<div className='flex items-center space-x-1 mb-1'>
								<label className='text-xs font-medium text-gray-700 dark:text-gray-300'>
									Primary Image *
								</label>
								<Tooltip content='The main image that will be displayed. This field is required.'>
									<HelpCircle className='w-3 h-3 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 cursor-help' />
								</Tooltip>
							</div>
							<div className='flex space-x-2'>
								<input
									type='url'
									value={formData.src}
									onChange={(e) => handleInputChange('src', e.target.value)}
									placeholder='https://example.com/image.jpg'
									className='flex-1 px-3 py-2 text-sm bg-white dark:bg-slate-700 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:text-white'
								/>
								<Button
									type='button'
									variant='outline'
									size='sm'
									onClick={() => handleOpenImageSelector(false)}
									className='flex items-center space-x-1 px-3'
								>
									<Search className='w-3 h-3' />
									<span>Browse</span>
								</Button>
							</div>
						</div>

						{/* Dark Mode Image Source */}
						<div className='bg-gray-50 dark:bg-slate-900/50 p-3 rounded-lg border border-gray-200 dark:border-gray-700'>
							<div className='flex items-center space-x-1 mb-2'>
								<Moon className='w-3 h-3 text-gray-600 dark:text-gray-400' />
								<label className='text-xs font-medium text-gray-700 dark:text-gray-300'>
									Dark Mode Variant
								</label>
								<Tooltip content='Optional alternative image that displays when dark mode is active. Useful for logos or images that need different contrast.'>
									<HelpCircle className='w-3 h-3 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 cursor-help' />
								</Tooltip>
							</div>
							<div className='flex space-x-2'>
								<input
									type='url'
									value={formData.srcDark || ''}
									onChange={(e) => handleInputChange('srcDark', e.target.value)}
									placeholder='https://example.com/image-dark.jpg'
									className='flex-1 px-3 py-2 text-sm bg-white dark:bg-slate-700 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:text-white'
								/>
								<Button
									type='button'
									variant='outline'
									size='sm'
									onClick={() => handleOpenImageSelector(true)}
									className='flex items-center space-x-1 px-3'
								>
									<Search className='w-3 h-3' />
									<span>Browse</span>
								</Button>
							</div>
							<p className='text-xs text-gray-500 dark:text-gray-400 mt-2 flex items-center space-x-1'>
								<span>
									Automatically switches based on user&apos;s theme preference
								</span>
							</p>
						</div>
					</div>

					{/* Display Settings Section */}
					<div className='space-y-3'>
						<div className='flex items-center space-x-2 pb-2 border-b border-gray-200 dark:border-gray-600'>
							<div className='w-4 h-4 border border-gray-400 rounded flex items-center justify-center'>
								<div className='w-2 h-2 bg-gray-400 rounded-sm'></div>
							</div>
							<h5 className='text-sm font-medium text-gray-900 dark:text-gray-100'>
								Display Settings
							</h5>
						</div>

						{/* Size Selector */}
						<div>
							<div className='flex items-center space-x-1 mb-2'>
								<label className='text-xs font-medium text-gray-700 dark:text-gray-300'>
									Image Size
								</label>
								<Tooltip content='Choose a preset size or enter a custom value. Supports percentages (50%) or pixels (300px).'>
									<HelpCircle className='w-3 h-3 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 cursor-help' />
								</Tooltip>
							</div>

							{/* Size Preset Buttons */}
							<div className='grid grid-cols-3 gap-2 mb-2'>
								{SIZE_PRESETS.slice(0, 3).map((preset) => (
									<button
										key={preset.value}
										type='button'
										onClick={() => handleSizePresetChange(preset.value)}
										className={`px-3 py-2 text-xs rounded border transition-colors text-center ${
											selectedSizePreset === preset.value
												? 'bg-blue-100 dark:bg-blue-900/30 border-blue-300 dark:border-blue-600 text-blue-700 dark:text-blue-300'
												: 'bg-white dark:bg-slate-700 border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-slate-600'
										}`}
									>
										<div className='font-medium'>{preset.label}</div>
										<div className='text-xs opacity-75'>{preset.value}</div>
									</button>
								))}
							</div>
							<div className='grid grid-cols-2 gap-2 mb-2'>
								{SIZE_PRESETS.slice(3).map((preset) => (
									<button
										key={preset.value}
										type='button'
										onClick={() => handleSizePresetChange(preset.value)}
										className={`px-3 py-2 text-xs rounded border transition-colors text-center ${
											selectedSizePreset === preset.value
												? 'bg-blue-100 dark:bg-blue-900/30 border-blue-300 dark:border-blue-600 text-blue-700 dark:text-blue-300'
												: 'bg-white dark:bg-slate-700 border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-slate-600'
										}`}
									>
										<div className='font-medium'>{preset.label}</div>
										{preset.value !== 'custom' && (
											<div className='text-xs opacity-75'>{preset.value}</div>
										)}
									</button>
								))}
							</div>

							{/* Custom Size Input */}
							{selectedSizePreset === 'custom' && (
								<input
									type='text'
									value={customSize}
									onChange={(e) => handleCustomSizeChange(e.target.value)}
									placeholder='e.g., 300px, 75%, 20rem'
									className='w-full px-3 py-2 text-sm bg-white dark:bg-slate-700 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:text-white'
								/>
							)}
						</div>

						{/* Alt Text */}
						<div>
							<div className='flex items-center space-x-1 mb-1'>
								<label className='text-xs font-medium text-gray-700 dark:text-gray-300'>
									Alt Text
								</label>
								<Tooltip content='Describes the image for screen readers and SEO. Important for accessibility and appears when the image fails to load.'>
									<HelpCircle className='w-3 h-3 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 cursor-help' />
								</Tooltip>
							</div>
							<input
								type='text'
								value={formData.alt || ''}
								onChange={(e) => handleInputChange('alt', e.target.value)}
								placeholder='Describe the image content...'
								className='w-full px-3 py-2 text-sm bg-white dark:bg-slate-700 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:text-white'
							/>
						</div>
					</div>

					{/* Compact Preview */}
					{formData.src && (
						<div className='border-t border-gray-200 dark:border-gray-600 pt-3'>
							<div className='flex items-center space-x-1 mb-2'>
								<label className='text-xs font-medium text-gray-700 dark:text-gray-300'>
									Preview
								</label>
								{isDarkMode && formData.srcDark && (
									<span className='text-xs text-blue-600 dark:text-blue-400 flex items-center space-x-1'>
										<Moon className='w-3 h-3' />
										<span>Dark mode active</span>
									</span>
								)}
							</div>
							<div className='border border-gray-200 dark:border-gray-600 rounded-lg p-3 bg-gray-50 dark:bg-slate-900 max-h-32 overflow-hidden flex items-center justify-center'>
								<img
									src={
										isDarkMode && formData.srcDark
											? formData.srcDark
											: formData.src
									}
									alt={formData.alt || 'Preview'}
									style={{
										width: formData.size || '50%',
										maxWidth: '100%',
										maxHeight: '100px',
										height: 'auto',
									}}
									className='rounded shadow-sm'
									onError={(e) => {
										e.currentTarget.src =
											"data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='200' height='150' viewBox='0 0 200 150'%3E%3Crect width='200' height='150' fill='%23f3f4f6'/%3E%3Ctext x='100' y='75' text-anchor='middle' dy='.3em' fill='%236b7280' font-size='12'%3EImage not found%3C/text%3E%3C/svg%3E";
									}}
								/>
							</div>
						</div>
					)}
				</div>
			</div>

			{/* Image Selector Modal */}
			<ImageSelector
				isOpen={showImageSelector}
				onClose={() => setShowImageSelector(false)}
				onSelectImage={handleImageSelect}
				projectId={selectedProject?.id}
				organizationId={selectedProject?.owner_id}
			/>
		</NodeViewWrapper>
	);
};

// Tiptap extension definition
export const ImageNode = Node.create({
	name: 'imageNode',
	group: 'block',
	atom: true,
	draggable: false,
	selectable: true,

	addAttributes() {
		return {
			src: {
				default: '',
				parseHTML: (element) => element.getAttribute('src'),
				renderHTML: (attributes) => {
					if (!attributes.src) return {};
					return { src: attributes.src };
				},
			},
			srcDark: {
				default: '',
				parseHTML: (element) =>
					element.getAttribute('srcDark') || element.getAttribute('src-dark'),
				renderHTML: (attributes) => {
					if (!attributes.srcDark) return {};
					return { srcDark: attributes.srcDark };
				},
			},
			size: {
				default: '50%',
				parseHTML: (element) => {
					const size =
						element.getAttribute('size') ||
						element.getAttribute('data-size') ||
						'50%';

					return size;
				},
				renderHTML: (attributes) => {
					if (!attributes.size) return {};
					return { size: attributes.size };
				},
			},
			alt: {
				default: '',
				parseHTML: (element) => element.getAttribute('alt'),
				renderHTML: (attributes) => {
					if (!attributes.alt) return {};
					return { alt: attributes.alt };
				},
			},
		};
	},

	parseHTML() {
		return [
			{
				tag: 'Image',
				getAttrs: (element) => {
					if (!(element instanceof HTMLElement)) return false;
					const attrs = {
						src: element.getAttribute('src') || '',
						srcDark: element.getAttribute('srcDark') || '',
						size: element.getAttribute('size') || '50%',
						alt: element.getAttribute('alt') || '',
					};
					return attrs;
				},
			},
			{
				tag: 'img[data-type="media"]',
				getAttrs: (element) => {
					if (!(element instanceof HTMLElement)) return false;
					const attrs = {
						src: element.getAttribute('src') || '',
						srcDark: element.getAttribute('data-src-dark') || '',
						size: '50%',
						alt: element.getAttribute('alt') || '',
					};

					return attrs;
				},
			},
			{
				tag: 'div[data-type="image"]',
				getAttrs: (element) => {
					if (!(element instanceof HTMLElement)) return false;

					// Find the img child element
					const img = element.querySelector('img');
					if (!img) return false;

					const attrs = {
						src: img.getAttribute('src') || '',
						srcDark: img.getAttribute('data-src-dark') || '',
						size: element.getAttribute('data-size') || '50%',
						alt: img.getAttribute('alt') || '',
					};

					return attrs;
				},
			},
		];
	},

	renderHTML({ HTMLAttributes }) {
		// Ensure size is preserved
		const size = HTMLAttributes.size || '100%';

		return [
			'div',
			{
				'data-type': 'image',
				'data-size': size,
				class: 'image-node',
			},
			[
				'img',
				{
					src: HTMLAttributes.src,
					'data-src-dark': HTMLAttributes.srcDark,
					alt: HTMLAttributes.alt,
					style: `width: ${size}; max-width: 100%;`,
				},
			],
		];
	},

	addNodeView() {
		return ReactNodeViewRenderer(ImageComponent);
	},

	addCommands() {
		return {
			setImage:
				(attributes?: Partial<ImageAttrs>) =>
				({ commands }) => {
					return commands.insertContent({
						type: this.name,
						attrs: attributes,
					});
				},
		};
	},
});

export default ImageNode;
