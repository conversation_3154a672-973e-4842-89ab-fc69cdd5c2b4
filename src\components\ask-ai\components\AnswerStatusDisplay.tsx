import { CheckCircle2, MinusCircle } from "lucide-react";

interface AnswerStatusDisplayProps {
  couldAnswer: boolean;
  size?: number;
}

export const AnswerStatusDisplay = ({ couldAnswer, size = 22 }: AnswerStatusDisplayProps) => {
  if (couldAnswer) {
    return <CheckCircle2 className="text-green-500" size={size} />;
  } else {
    return <MinusCircle className="text-destructive" size={size} />;
  }
}; 