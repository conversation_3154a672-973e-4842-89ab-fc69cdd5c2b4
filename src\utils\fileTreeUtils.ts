import type {
	Config<PERSON><PERSON>,
	DocusaurusSidebar,
	DocusaurusCategory,
	DocusaurusPageGroup,
} from '@/types/sidebarConfig';
import type { TreeNode } from '@/components/editor/sidebar/types';

export const convertConfigToTree = (
	config: ConfigJson,
	expandedNodeIds?: Set<string>
): TreeNode[] => {
	const tree: TreeNode[] = [];

	// Criar estrutura hierárquica do navbar primeiro
	const navbarStructure: {
		[sidebarRef: string]: { dropdownNode?: TreeNode; directParent?: TreeNode };
	} = {};

	if (config.navbar) {
		config.navbar.forEach((navItem, navIndex) => {
			if (navItem.dropdown) {
				// ID Estável: baseado no índice do nav item
				const dropdownId = `nav-dropdown-${navIndex}`;
				const dropdownNode: TreeNode = {
					id: dropdownId,
					name: navItem.label,
					type: 'navbar',
					isExpanded: expandedNodeIds?.has(dropdownId) ?? true,
					children: [],
					data: {
						type: 'navbar-dropdown',
						label: navItem.label,
						index: navIndex,
					},
				};

				// Criar nós para cada item dentro do dropdown
				navItem.dropdown.forEach((dropdownItem, dropdownIndex) => {
					if (dropdownItem.sidebarRef) {
						// ID Estável: baseado nos índices do dropdown e do item
						const dropdownItemId = `nav-item-${navIndex}-${dropdownIndex}`;
						const dropdownItemNode: TreeNode = {
							id: dropdownItemId,
							name: dropdownItem.label,
							type: 'navbar',
							isExpanded: expandedNodeIds?.has(dropdownItemId) ?? true,
							parentId: dropdownNode.id,
							children: [],
							data: {
								type: 'navbar-dropdown-item',
								label: dropdownItem.label,
								sidebarRef: dropdownItem.sidebarRef,
								dropdownIndex: navIndex,
								index: dropdownIndex,
							},
						};

						dropdownNode.children!.push(dropdownItemNode);
						navbarStructure[dropdownItem.sidebarRef] = {
							dropdownNode,
							directParent: dropdownItemNode,
						};
					}
				});

				tree.push(dropdownNode);
			} else if (navItem.sidebarRef) {
				// Item direto do navbar (sem dropdown)
				// ID Estável: baseado no índice do nav item
				const navbarItemId = `nav-item-${navIndex}`;
				const navbarItemNode: TreeNode = {
					id: navbarItemId,
					name: navItem.label,
					type: 'navbar',
					isExpanded: expandedNodeIds?.has(navbarItemId) ?? true,
					children: [],
					data: {
						type: 'navbar-item',
						label: navItem.label,
						sidebarRef: navItem.sidebarRef,
						index: navIndex,
					},
				};

				navbarStructure[navItem.sidebarRef] = { directParent: navbarItemNode };
				tree.push(navbarItemNode);
			}
		});
	}

	// Agora processar os sidebars e anexá-los à estrutura do navbar
	config.sidebars.forEach((sidebar, sidebarIndex) => {
		const navStructure = navbarStructure[sidebar.sidebarRef];

		if (navStructure && navStructure.directParent) {
			// Adicionar categorias como filhos do nó navbar apropriado
			sidebar.categories.forEach((category, categoryIndex) => {
				// ID Estável: baseado no sidebarRef e no índice da categoria
				const categoryId = `category-${sidebar.sidebarRef}-${categoryIndex}`;
				const categoryNode: TreeNode = {
					id: categoryId,
					name: category.categoryName,
					type: 'category',
					isExpanded:
						expandedNodeIds?.has(categoryId) ??
						(expandedNodeIds ? false : true), // Se há expansão salva, usar false como padrão, senão true
					parentId: navStructure.directParent!.id,
					children: [],
					data: {
						type: 'category',
						categoryName: category.categoryName,
						sidebarIndex,
						index: categoryIndex,
						// Preserva dados do navbar para reconstrução
						navbar: config.navbar,
					},
				};

				category.pages.forEach((page, pageIndex) => {
					if (typeof page === 'string') {
						// Página simples
						// ID Estável: baseado no path da página (sanitizado)
						const pageId = `page-${page.replace(/[^\w\s]/gi, '_')}`;
						categoryNode.children!.push({
							id: pageId,
							name: page.split('/').pop() || page,
							type: 'page',
							parentId: categoryNode.id,
							path: page,
							data: {
								type: 'page',
								page,
								sidebarIndex,
								categoryIndex,
								index: pageIndex,
							},
						});
					} else {
						// Group de páginas
						const groupNode = createGroupNode(
							page,
							categoryNode.id,
							sidebarIndex,
							categoryIndex,
							pageIndex,
							expandedNodeIds
						);
						categoryNode.children!.push(groupNode);
					}
				});

				navStructure.directParent!.children!.push(categoryNode);
			});
		} else {
			// Fallback: criar sidebar como nó independente se não encontrar no navbar
			// ID Estável: baseado no sidebarRef
			const sidebarId = `sidebar-${sidebar.sidebarRef}`;
			const sidebarNode: TreeNode = {
				id: sidebarId,
				name: `Sidebar (${sidebar.sidebarRef})`,
				type: 'sidebar',
				isExpanded: expandedNodeIds?.has(sidebarId) ?? true,
				children: [],
				data: {
					type: 'sidebar',
					sidebarRef: sidebar.sidebarRef,
					index: sidebarIndex,
					navbar: config.navbar,
				},
			};

			sidebar.categories.forEach((category, categoryIndex) => {
				// ID Estável: baseado no sidebarRef e no índice da categoria
				const categoryId = `category-${sidebar.sidebarRef}-${categoryIndex}`;
				const categoryNode: TreeNode = {
					id: categoryId,
					name: category.categoryName,
					type: 'category',
					isExpanded:
						expandedNodeIds?.has(categoryId) ??
						(expandedNodeIds ? false : true),
					parentId: sidebarNode.id,
					children: [],
					data: {
						type: 'category',
						categoryName: category.categoryName,
						sidebarIndex,
						index: categoryIndex,
					},
				};

				category.pages.forEach((page, pageIndex) => {
					if (typeof page === 'string') {
						// Página simples
						// ID Estável: baseado no path da página (sanitizado)
						const pageId = `page-${page.replace(/[^\w\s]/gi, '_')}`;
						categoryNode.children!.push({
							id: pageId,
							name: page.split('/').pop() || page,
							type: 'page',
							parentId: categoryNode.id,
							path: page,
							data: {
								type: 'page',
								page,
								sidebarIndex,
								categoryIndex,
								index: pageIndex,
							},
						});
					} else {
						// Group de páginas
						const groupNode = createGroupNode(
							page,
							categoryNode.id,
							sidebarIndex,
							categoryIndex,
							pageIndex,
							expandedNodeIds
						);
						categoryNode.children!.push(groupNode);
					}
				});

				sidebarNode.children!.push(categoryNode);
			});

			tree.push(sidebarNode);
		}
	});

	return tree;
};

// Função auxiliar para criar groups (incluindo aninhados)
function createGroupNode(
	group: DocusaurusPageGroup,
	parentId: string,
	sidebarIndex: number,
	categoryIndex: number,
	pageIndex: number,
	expandedNodeIds?: Set<string>
): TreeNode {
	// ID Estável: composto pelo ID do pai e o índice do grupo
	const groupId = `${parentId}-group-${pageIndex}`;
	const groupNode: TreeNode = {
		id: groupId,
		name: group.groupName,
		type: 'group',
		isExpanded:
			expandedNodeIds?.has(groupId) ?? (expandedNodeIds ? false : true),
		parentId: parentId,
		children: [],
		data: {
			type: 'group',
			groupName: group.groupName,
			page: group.page,
			sidebarIndex,
			categoryIndex,
			index: pageIndex,
		},
	};

	// Adiciona a página principal do grupo se existir
	if (group.page) {
		// ID Estável: baseado no path da página (sanitizado)
		const pageId = `page-${group.page.replace(/[^\w\s]/gi, '_')}`;
		groupNode.children!.push({
			id: pageId,
			name: group.page.split('/').pop() || group.page,
			type: 'page',
			parentId: groupNode.id,
			path: group.page,
			data: {
				type: 'group-page',
				page: group.page,
				sidebarIndex,
				categoryIndex,
				groupIndex: pageIndex,
			},
		});
	}

	// Adiciona as subpages (podem ser strings ou groups aninhados)
	group.subpages.forEach((subpage, subpageIndex) => {
		if (typeof subpage === 'string') {
			// ID Estável: baseado no path da subpágina (sanitizado)
			const subpageId = `subpage-${subpage.replace(/[^\w\s]/gi, '_')}`;
			groupNode.children!.push({
				id: subpageId,
				name: subpage.split('/').pop() || subpage,
				type: 'subpage',
				parentId: groupNode.id,
				path: subpage,
				data: {
					type: 'subpage',
					page: subpage,
					sidebarIndex,
					categoryIndex,
					groupIndex: pageIndex,
					index: subpageIndex,
				},
			});
		} else {
			// Group aninhado dentro de subpages
			const nestedGroup = createGroupNode(
				subpage,
				groupNode.id, // Passa o ID estável do pai
				sidebarIndex,
				categoryIndex,
				subpageIndex,
				expandedNodeIds
			);
			groupNode.children!.push(nestedGroup);
		}
	});

	return groupNode;
}

export const convertTreeToConfig = (
	tree: TreeNode[],
	originalConfig?: ConfigJson
): ConfigJson => {
	console.log('🔄 Convertendo árvore para config...');

	// Define valores padrão para propriedades obrigatórias
	const defaultRequiredProperties = {
		websiteName: 'WriteDocs Documentation',
		description: 'Documentation site generated by WriteDocs',
		homepage: '/',
		images: {
			logo: 'media/logo.png',
			favicon: 'media/favicon.ico',
		},
		styles: {
			mainColor: '#0029F5',
		},
	};

	// Se houver um config original, preserva todas as propriedades
	// e atualiza apenas navbar e sidebars
	// Garante que propriedades obrigatórias existam
	const config: ConfigJson = originalConfig
		? {
				...defaultRequiredProperties,
				...originalConfig,
				// Garante que images e styles tenham as propriedades obrigatórias
				images: {
					...defaultRequiredProperties.images,
					...(originalConfig.images || {}),
				},
				styles: {
					...defaultRequiredProperties.styles,
					...(originalConfig.styles || {}),
				},
				navbar: [],
				sidebars: [],
			}
		: {
				...defaultRequiredProperties,
				navbar: [],
				sidebars: [],
			};

	// Primeiro, reconstrói o navbar a partir da estrutura da árvore
	tree.forEach((topLevelNode) => {
		if (topLevelNode.type === 'navbar') {
			if (topLevelNode.data?.type === 'navbar-dropdown') {
				// Este é um dropdown
				const dropdownNavItem: {
					label: string;
					dropdown: Array<{ label: string; sidebarRef: string }>;
				} = {
					label: topLevelNode.data.label || topLevelNode.name,
					dropdown: [],
				};

				// Processa os itens dentro do dropdown
				topLevelNode.children?.forEach((dropdownItemNode) => {
					if (
						dropdownItemNode.type === 'navbar' &&
						dropdownItemNode.data?.type === 'navbar-dropdown-item'
					) {
						dropdownNavItem.dropdown.push({
							label: dropdownItemNode.data.label || dropdownItemNode.name,
							sidebarRef: dropdownItemNode.data.sidebarRef || '',
						});
					}
				});

				config.navbar.push(dropdownNavItem);
			} else if (topLevelNode.data?.type === 'navbar-item') {
				// Este é um item direto do navbar
				config.navbar.push({
					label: topLevelNode.data.label || topLevelNode.name,
					sidebarRef: topLevelNode.data.sidebarRef || '',
				});
			}
		}
	});

	// Agora processa os sidebars da nova estrutura
	const processNavbarNode = (node: TreeNode) => {
		if (node.data?.sidebarRef) {
			const sidebar: DocusaurusSidebar = {
				sidebarRef: node.data.sidebarRef,
				categories: [],
			};

			// Processa as categories que são filhas deste nó navbar
			node.children?.forEach((categoryNode) => {
				if (categoryNode.type === 'category' && categoryNode.children) {
					const category: DocusaurusCategory = {
						categoryName: categoryNode.data?.categoryName || categoryNode.name,
						pages: [],
					};

					categoryNode.children.forEach((pageNode) => {
						console.log(
							`  📄 Processando ${pageNode.type}: ${pageNode.name} (path: ${pageNode.path})`
						);

						if (pageNode.type === 'page' || pageNode.type === 'subpage') {
							// Usa o path atualizado se disponível, senão usa o data.page
							const finalPath = pageNode.path || pageNode.data?.page;
							if (finalPath) {
								category.pages.push(finalPath);
								console.log(`    ✅ Adicionado como página: ${finalPath}`);
							}
						} else if (pageNode.type === 'group' && pageNode.children) {
							const group = convertNodeToGroup(pageNode);
							category.pages.push(group);
							console.log(`    ✅ Adicionado como grupo: ${group.groupName}`);
						} else {
							console.log(`    ⚠️ Tipo não processado: ${pageNode.type}`);
						}
					});

					sidebar.categories.push(category);
				} else if (categoryNode.type === 'group') {
					// TRANSFORMAÇÃO INTELIGENTE: Um grupo arrastado para um item de navbar
					// é transformado em uma nova categoria, em vez de criar uma "General".
					console.log(
						`🔄 Transformando grupo "${categoryNode.name}" em uma nova categoria.`
					);

					const newCategory: DocusaurusCategory = {
						categoryName: categoryNode.name, // O nome do grupo vira o nome da categoria
						pages: [],
					};

					// Processar os filhos do nó do grupo (que são páginas ou subgrupos)
					if (categoryNode.children) {
						categoryNode.children.forEach((pageNode) => {
							if (pageNode.type === 'page' || pageNode.type === 'subpage') {
								const finalPath = pageNode.path || pageNode.data?.page;
								if (finalPath) newCategory.pages.push(finalPath);
							} else if (pageNode.type === 'group' && pageNode.children) {
								// Se houver um grupo aninhado, ele permanece como um grupo dentro da nova categoria
								const group = convertNodeToGroup(pageNode);
								newCategory.pages.push(group);
							}
						});
					}

					sidebar.categories.push(newCategory);
					console.log(
						`    ✅ Grupo transformado e adicionado como categoria: ${newCategory.categoryName}`
					);
				}
			});

			// Sempre adiciona o sidebar, mesmo que vazio (para novos navbar items)
			config.sidebars.push(sidebar);
			console.log(
				`📁 Sidebar criado/atualizado: ${sidebar.sidebarRef} com ${sidebar.categories.length} categorias`
			);
		}

		// Processa recursivamente os filhos
		node.children?.forEach((child) => {
			processNavbarNode(child);
		});
	};

	// Processa todos os nós da árvore
	tree.forEach((topLevelNode) => {
		if (topLevelNode.type === 'navbar') {
			processNavbarNode(topLevelNode);
		} else if (topLevelNode.type === 'sidebar') {
			// Fallback para sidebars independentes (estrutura antiga)
			const sidebar: DocusaurusSidebar = {
				sidebarRef: topLevelNode.data?.sidebarRef || '',
				categories: [],
			};

			topLevelNode.children?.forEach((categoryNode) => {
				if (categoryNode.type === 'category' && categoryNode.children) {
					const category: DocusaurusCategory = {
						categoryName: categoryNode.data?.categoryName || categoryNode.name,
						pages: [],
					};

					categoryNode.children.forEach((pageNode) => {
						console.log(
							`  📄 Processando ${pageNode.type}: ${pageNode.name} (path: ${pageNode.path})`
						);

						if (pageNode.type === 'page' || pageNode.type === 'subpage') {
							// Usa o path atualizado se disponível, senão usa o data.page
							const finalPath = pageNode.path || pageNode.data?.page;
							if (finalPath) {
								category.pages.push(finalPath);
								console.log(`    ✅ Adicionado como página: ${finalPath}`);
							}
						} else if (pageNode.type === 'group' && pageNode.children) {
							const group = convertNodeToGroup(pageNode);
							category.pages.push(group);
							console.log(`    ✅ Adicionado como grupo: ${group.groupName}`);
						} else {
							console.log(`    ⚠️ Tipo não processado: ${pageNode.type}`);
						}
					});

					sidebar.categories.push(category);
				}
			});

			config.sidebars.push(sidebar);
		}
	});

	console.log('✅ Config final gerada:', JSON.stringify(config, null, 2));
	return config;
};

// Função auxiliar para converter node de grupo em DocusaurusPageGroup (incluindo aninhados)
function convertNodeToGroup(groupNode: TreeNode): DocusaurusPageGroup {
	const group: DocusaurusPageGroup = {
		groupName:
			groupNode.data?.groupName ||
			groupNode.data?.categoryName ||
			groupNode.name,
		subpages: [],
	};

	// Coletar todas as páginas e analisar qual deve ser a principal
	const allPages: Array<{ path: string; fileName: string; isIndex: boolean }> =
		[];

	groupNode.children?.forEach((child) => {
		if (child.type === 'page' || child.type === 'subpage') {
			const childPath = child.path || child.data?.page;
			if (childPath) {
				const fileName = childPath.split('/').pop() || '';
				const sanitizedGroupName = sanitizeFileName(group.groupName);

				// Considera como index se:
				// 1. O nome do arquivo é "index" (comportamento atual)
				// 2. O nome do arquivo é igual ao nome sanitizado do grupo (novo comportamento)
				const isIndex =
					fileName.toLowerCase() === 'index' ||
					fileName.toLowerCase() === sanitizedGroupName.toLowerCase();

				allPages.push({ path: childPath, fileName, isIndex });

				if (fileName.toLowerCase() === sanitizedGroupName.toLowerCase()) {
					console.log(
						`  🎯 Arquivo com nome sanitizado do grupo encontrado: ${fileName} = ${sanitizedGroupName} (original: ${group.groupName})`
					);
				}
			}
		} else if (child.type === 'group') {
			// Group aninhado dentro das subpages
			const nestedGroup = convertNodeToGroup(child);
			group.subpages.push(nestedGroup);
		} else if (child.type === 'category') {
			// Category que foi transformada em group (para cases de drag and drop)
			const categoryAsGroup: DocusaurusPageGroup = {
				groupName: child.data?.categoryName || child.name,
				subpages: [],
			};

			// Processa as páginas da categoria como subpages
			child.children?.forEach((categoryChild) => {
				if (categoryChild.type === 'page') {
					const pagePath = categoryChild.path || categoryChild.data?.page;
					if (pagePath) {
						categoryAsGroup.subpages.push(pagePath);
					}
				} else if (categoryChild.type === 'subpage') {
					const subpagePath = categoryChild.path || categoryChild.data?.page;
					if (subpagePath) {
						categoryAsGroup.subpages.push(subpagePath);
					}
				} else if (categoryChild.type === 'group') {
					const nestedGroup = convertNodeToGroup(categoryChild);
					categoryAsGroup.subpages.push(nestedGroup);
				}
			});

			group.subpages.push(categoryAsGroup);
		}
	});

	// LÓGICA PRINCIPAL: Determinar página principal e subpages
	const indexPages = allPages.filter((p) => p.isIndex);
	const nonIndexPages = allPages.filter((p) => !p.isIndex);

	if (indexPages.length > 0) {
		// Priorizar arquivos "index" verdadeiros sobre arquivos com mesmo nome do grupo
		const trueIndexPages = indexPages.filter(
			(p) => p.fileName.toLowerCase() === 'index'
		);
		const sanitizedGroupName = sanitizeFileName(group.groupName);
		const groupNamePages = indexPages.filter(
			(p) => p.fileName.toLowerCase() === sanitizedGroupName.toLowerCase()
		);

		let selectedIndexPage: (typeof indexPages)[0];

		if (trueIndexPages.length > 0) {
			// Se há arquivo "index", usar ele
			selectedIndexPage = trueIndexPages[0];
			console.log(
				`  🏠 Definindo arquivo index como página principal: ${selectedIndexPage.path}`
			);
		} else {
			// Senão, usar arquivo com mesmo nome do grupo
			selectedIndexPage = groupNamePages[0];
			console.log(
				`  🎯 Definindo arquivo com nome do grupo como página principal: ${selectedIndexPage.path}`
			);
		}

		group.page = selectedIndexPage.path;

		// Todos os outros indexes (excluindo o selecionado como página principal) vão para subpages
		const otherIndexes = indexPages.filter(
			(p) => p.path !== selectedIndexPage.path
		);
		const allSubpages = [...nonIndexPages, ...otherIndexes];

		group.subpages.push(...allSubpages.map((p) => p.path));

		if (otherIndexes.length > 0) {
			console.log(
				`  ⚠️ Múltiplos indexes encontrados, extras vão para subpages: ${otherIndexes
					.map((p) => p.path)
					.join(', ')}`
			);
		}
	} else {
		// Se não há index, não há página principal - todas vão para subpages
		console.log(
			`  📝 Nenhum index encontrado, todas as páginas vão para subpages`
		);
		group.subpages.push(...nonIndexPages.map((p) => p.path));
		// Não definir group.page (deixar undefined)
	}

	// Remove a propriedade page se estiver vazia
	if (group.page && group.page.trim() === '') {
		delete group.page;
		console.log(
			`  🗑️ Removendo propriedade 'page' vazia do grupo ${group.groupName}`
		);
	}

	console.log(
		`  ✅ Group convertido - ${group.groupName}: page=${
			group.page || 'undefined'
		}, subpages=${group.subpages.length}`
	);

	return group;
}

// Utility functions for tree manipulation
export const findNodeById = (
	nodes: TreeNode[],
	id: string
): TreeNode | null => {
	for (const node of nodes) {
		if (node.id === id) return node;
		if (node.children) {
			const found = findNodeById(node.children, id);
			if (found) return found;
		}
	}
	return null;
};

export const removeNodeById = (nodes: TreeNode[], id: string): TreeNode[] => {
	return nodes.filter((node) => {
		if (node.id === id) return false;
		if (node.children) {
			node.children = removeNodeById(node.children, id);
		}
		return true;
	});
};

export const insertNode = (
	nodes: TreeNode[],
	targetId: string,
	newNode: TreeNode,
	position: 'inside' | 'before' | 'after'
): TreeNode[] => {
	return nodes.map((node) => {
		if (node.id === targetId) {
			if (position === 'inside' && node.children !== undefined) {
				const updatedNode = {
					...node,
					children: [
						...(node.children || []),
						{ ...newNode, parentId: node.id },
					],
					isExpanded: true,
				};
				return updatedNode;
			}
			return node;
		}

		if (node.children) {
			const newChildren = insertNode(
				node.children,
				targetId,
				newNode,
				position
			);

			if (position === 'before' || position === 'after') {
				const targetIndex = newChildren.findIndex(
					(child) => child.id === targetId
				);
				if (targetIndex !== -1) {
					const insertIndex =
						position === 'before' ? targetIndex : targetIndex + 1;
					newChildren.splice(insertIndex, 0, {
						...newNode,
						parentId: node.id,
					});
				}
			}

			return { ...node, children: newChildren };
		}

		return node;
	});
};

// Helper function to check if targetId is a descendant of nodeId
export const isDescendant = (
	nodeId: string,
	targetId: string,
	nodes: TreeNode[]
): boolean => {
	const node = findNodeById(nodes, nodeId);
	if (!node || !node.children) return false;

	for (const child of node.children) {
		if (child.id === targetId) return true;
		if (isDescendant(child.id, targetId, nodes)) return true;
	}
	return false;
};

// Função para construir o path baseado na hierarquia da árvore
export const buildPathFromHierarchy = (
	nodeId: string,
	tree: TreeNode[]
): string => {
	const pathParts: string[] = [];

	const buildPath = (currentNodeId: string, nodes: TreeNode[]): boolean => {
		for (const node of nodes) {
			if (node.id === currentNodeId) {
				// Para páginas, usa apenas o nome do arquivo
				if (node.type === 'page' || node.type === 'subpage') {
					pathParts.unshift(node.name);
				} else {
					// Para containers (categories, groups), adiciona o nome
					pathParts.unshift(node.name);
				}
				return true;
			}

			if (node.children && buildPath(currentNodeId, node.children)) {
				// Adiciona o nome do nó pai na hierarquia
				if (node.type === 'navbar') {
					// Para navbar, sempre inclui o nome na hierarquia
					pathParts.unshift(node.name);
				} else if (node.type === 'sidebar') {
					// Para sidebar, usa o nome limpo (sem ">")
					const sidebarName = node.name.split(' > ').join('/');
					pathParts.unshift(sidebarName);
				} else if (node.type === 'category' || node.type === 'group') {
					pathParts.unshift(node.name);
				}
				return true;
			}
		}
		return false;
	};

	buildPath(nodeId, tree);
	const fullPath = pathParts.join('/');

	console.log(`🛤️ Path construído para ${nodeId}: ${fullPath}`);
	console.log(`📋 Parts: [${pathParts.join(', ')}]`);

	return fullPath;
};

// Função para atualizar recursivamente todos os paths de um nó e seus filhos
export const updateNodePaths = (node: TreeNode, tree: TreeNode[]): TreeNode => {
	const updatedNode = { ...node };

	// Atualiza o path se for uma página
	if (node.type === 'page' || node.type === 'subpage') {
		// Verifica se ainda é realmente uma group-page (dentro de um grupo)
		const parentNode = findNodeById(tree, node.parentId || '');
		const isStillGroupPage =
			node.data?.type === 'group-page' && parentNode?.type === 'group';

		if (isStillGroupPage) {
			console.log(
				`  🏷️  Mantendo path original da group-page: ${node.name} -> ${node.path}`
			);
			// Mantém o path original, apenas atualiza o data se necessário
			if (updatedNode.data) {
				updatedNode.data = {
					...updatedNode.data,
					page: node.path,
				};
			}
		} else {
			// Para todas as outras páginas, SEMPRE reconstrói o path baseado na hierarquia atual
			const newPath = buildPathFromHierarchy(node.id, tree);
			console.log(`  🔄 Reconstruindo path: ${node.name} -> ${newPath}`);
			updatedNode.path = newPath;

			// Normaliza o tipo baseado no contexto atual
			if (parentNode) {
				if (parentNode.type === 'category') {
					updatedNode.type = 'page';
					updatedNode.data = {
						...updatedNode.data,
						type: 'page',
						page: newPath,
					};
					// Se era uma group-page que foi movida para categoria, corrige o tipo
					if (node.data?.type === 'group-page') {
						console.log(`  🔄 Convertendo group-page para page: ${node.name}`);
					}
				} else if (parentNode.type === 'group') {
					updatedNode.type = 'subpage';
					updatedNode.data = {
						...updatedNode.data,
						type: 'subpage',
						page: newPath,
					};
				}
			}

			// Atualiza também no data
			if (updatedNode.data) {
				updatedNode.data = {
					...updatedNode.data,
					page: newPath,
				};
			}
		}
	}

	// Atualiza recursivamente os filhos
	if (updatedNode.children) {
		updatedNode.children = updatedNode.children.map((child) =>
			updateNodePaths(child, tree)
		);
	}

	return updatedNode;
};

// Função otimizada para atualizar apenas nós afetados
export const updateAffectedPaths = (
	tree: TreeNode[],
	affectedNodeIds: Set<string>
): TreeNode[] => {
	console.log(
		'🔄 Atualizando paths dos nós afetados:',
		Array.from(affectedNodeIds)
	);

	const updateNodeIfAffected = (node: TreeNode): TreeNode => {
		// Se este nó ou algum ancestral está na lista de afetados, atualiza
		if (affectedNodeIds.has(node.id)) {
			return updateNodePaths(node, tree);
		}

		// Verifica se algum filho está afetado
		if (node.children && node.children.length > 0) {
			const hasAffectedChild = node.children.some(
				(child) =>
					affectedNodeIds.has(child.id) ||
					hasAffectedDescendant(child, affectedNodeIds)
			);

			if (hasAffectedChild) {
				return {
					...node,
					children: node.children.map((child) => updateNodeIfAffected(child)),
				};
			}
		}

		// Nó não afetado, retorna como está
		return node;
	};

	const hasAffectedDescendant = (node: TreeNode, ids: Set<string>): boolean => {
		if (ids.has(node.id)) return true;
		if (node.children) {
			return node.children.some((child) => hasAffectedDescendant(child, ids));
		}
		return false;
	};

	return tree.map((node) => updateNodeIfAffected(node));
};

// Função para coletar todos os descendentes de um nó
export const collectDescendantIds = (node: TreeNode): Set<string> => {
	const ids = new Set<string>();
	ids.add(node.id);

	if (node.children) {
		node.children.forEach((child) => {
			const childIds = collectDescendantIds(child);
			childIds.forEach((id) => ids.add(id));
		});
	}

	return ids;
};

// Função para atualizar todos os paths na árvore após mudanças
// Agora com suporte opcional para atualizar apenas nós específicos
export const updateAllPaths = (
	tree: TreeNode[],
	affectedNodeIds?: Set<string>
): TreeNode[] => {
	// Se IDs específicos foram fornecidos, usa a versão otimizada
	if (affectedNodeIds && affectedNodeIds.size > 0) {
		console.log(
			`🚀 Otimização: Atualizando apenas ${affectedNodeIds.size} nós afetados`
		);
		return updateAffectedPaths(tree, affectedNodeIds);
	}

	// Caso contrário, mantém o comportamento original (atualiza tudo)
	console.log('🔄 Atualizando paths da árvore inteira...');
	const updatedTree = tree.map((node) => updateNodePaths(node, tree));

	// Log dos paths atualizados
	const logPaths = (nodes: TreeNode[], prefix = '') => {
		nodes.forEach((node) => {
			if ((node.type === 'page' || node.type === 'subpage') && node.path) {
				console.log(`${prefix}📄 [${node.type}] ${node.name}: ${node.path}`);
			} else if (node.type === 'category' || node.type === 'group') {
				console.log(`${prefix}📁 [${node.type}] ${node.name}`);
			}
			if (node.children) {
				logPaths(node.children, prefix + '  ');
			}
		});
	};

	console.log('📁 Estrutura da árvore atualizada:');
	logPaths(updatedTree);

	return updatedTree;
};

// ===== CONFLICT DETECTION AND PREVENTION UTILITIES =====

export interface ConflictResult {
	hasConflicts: boolean;
	conflicts: Array<{
		itemName: string;
		itemType: 'file' | 'folder';
		conflictPath: string;
		reason: string;
	}>;
	canMove: Array<{
		node: TreeNode;
		newPath: string;
	}>;
	cannotMove: Array<{
		node: TreeNode;
		reason: string;
		conflictPath: string;
	}>;
}

/**
 * Checks for naming conflicts when moving a node to a target location
 */
export const checkMoveConflicts = (
	draggedNode: TreeNode,
	targetNode: TreeNode,
	position: 'inside' | 'before' | 'after',
	tree: TreeNode[]
): ConflictResult => {
	console.log(
		`🔍 Checking conflicts for moving ${draggedNode.name} to ${targetNode.name} (${position})`
	);

	const result: ConflictResult = {
		hasConflicts: false,
		conflicts: [],
		canMove: [],
		cannotMove: [],
	};

	// Determine the target parent based on position
	let targetParent: TreeNode | null = null;

	if (position === 'inside') {
		targetParent = targetNode;
	} else {
		// For 'before' or 'after', the parent is the target's parent
		targetParent = findNodeById(tree, targetNode.parentId || '') || null;
	}

	if (!targetParent) {
		// Moving to root level - check against root level nodes
		return checkConflictsAtRootLevel(draggedNode, tree);
	}

	// Check conflicts for the dragged node and all its descendants
	return checkConflictsRecursive(draggedNode, targetParent, tree);
};

/**
 * Checks conflicts when moving to root level
 */
const checkConflictsAtRootLevel = (
	draggedNode: TreeNode,
	tree: TreeNode[]
): ConflictResult => {
	const result: ConflictResult = {
		hasConflicts: false,
		conflicts: [],
		canMove: [],
		cannotMove: [],
	};

	const rootLevelNodes = tree.filter((node) => !node.parentId);
	const existingNames = new Set(
		rootLevelNodes.map((node) => node.name.toLowerCase())
	);

	if (existingNames.has(draggedNode.name.toLowerCase())) {
		result.hasConflicts = true;
		result.conflicts.push({
			itemName: draggedNode.name,
			itemType:
				draggedNode.type === 'page' || draggedNode.type === 'subpage'
					? 'file'
					: 'folder',
			conflictPath: draggedNode.name,
			reason: `A ${draggedNode.type === 'page' || draggedNode.type === 'subpage' ? 'file' : 'folder'} with this name already exists at the root level`,
		});
		result.cannotMove.push({
			node: draggedNode,
			reason: `Name conflict at root level`,
			conflictPath: draggedNode.name,
		});
	} else {
		result.canMove.push({
			node: draggedNode,
			newPath: draggedNode.name,
		});
	}

	return result;
};

/**
 * Recursively checks conflicts for a node and all its descendants
 */
const checkConflictsRecursive = (
	nodeToMove: TreeNode,
	targetParent: TreeNode,
	tree: TreeNode[]
): ConflictResult => {
	const result: ConflictResult = {
		hasConflicts: false,
		conflicts: [],
		canMove: [],
		cannotMove: [],
	};

	// Get existing children names in target location
	const existingChildren = targetParent.children || [];
	const existingNames = new Set(
		existingChildren.map((child) => child.name.toLowerCase())
	);

	// Check the main node first
	const nodeConflict = checkSingleNodeConflict(
		nodeToMove,
		existingNames,
		targetParent,
		tree
	);

	if (nodeConflict.hasConflict) {
		result.hasConflicts = true;
		result.conflicts.push(nodeConflict.conflict!);
		result.cannotMove.push({
			node: nodeToMove,
			reason: nodeConflict.conflict!.reason,
			conflictPath: nodeConflict.conflict!.conflictPath,
		});
	} else {
		// If no conflict for the main node, check its children recursively
		const childResults = checkChildrenConflicts(nodeToMove, targetParent, tree);

		result.canMove.push({
			node: nodeToMove,
			newPath: buildPathFromHierarchy(nodeToMove.id, tree), // This will be recalculated after move
		});

		// Merge child results
		result.hasConflicts = result.hasConflicts || childResults.hasConflicts;
		result.conflicts.push(...childResults.conflicts);
		result.canMove.push(...childResults.canMove);
		result.cannotMove.push(...childResults.cannotMove);
	}

	return result;
};

/**
 * Checks conflict for a single node against existing names
 */
const checkSingleNodeConflict = (
	node: TreeNode,
	existingNames: Set<string>,
	targetParent: TreeNode,
	tree: TreeNode[]
): { hasConflict: boolean; conflict?: ConflictResult['conflicts'][0] } => {
	if (existingNames.has(node.name.toLowerCase())) {
		const targetPath = buildPathFromHierarchy(targetParent.id, tree);
		const conflictPath = targetPath ? `${targetPath}/${node.name}` : node.name;

		return {
			hasConflict: true,
			conflict: {
				itemName: node.name,
				itemType:
					node.type === 'page' || node.type === 'subpage' ? 'file' : 'folder',
				conflictPath,
				reason: `A ${node.type === 'page' || node.type === 'subpage' ? 'file' : 'folder'} with this name already exists at the destination`,
			},
		};
	}

	return { hasConflict: false };
};

/**
 * Recursively checks conflicts for all children of a node
 */
const checkChildrenConflicts = (
	parentNode: TreeNode,
	targetParent: TreeNode,
	tree: TreeNode[]
): ConflictResult => {
	const result: ConflictResult = {
		hasConflicts: false,
		conflicts: [],
		canMove: [],
		cannotMove: [],
	};

	if (!parentNode.children || parentNode.children.length === 0) {
		return result;
	}

	// For each child, we need to check if it would conflict in the new location
	for (const child of parentNode.children) {
		// Simulate the child's new location after the parent moves
		const simulatedParentInNewLocation = {
			...parentNode,
			parentId: targetParent.id,
			children: parentNode.children,
		};

		// Check if this child would conflict with existing items at the same level
		const childTargetParent = simulatedParentInNewLocation;
		const childResult = checkConflictsRecursive(child, childTargetParent, tree);

		result.hasConflicts = result.hasConflicts || childResult.hasConflicts;
		result.conflicts.push(...childResult.conflicts);
		result.canMove.push(...childResult.canMove);
		result.cannotMove.push(...childResult.cannotMove);
	}

	return result;
};

/**
 * Executes a selective move operation, moving only items that don't have conflicts
 */
export const executeSelectiveMove = (
	draggedNode: TreeNode,
	targetNode: TreeNode,
	position: 'inside' | 'before' | 'after',
	tree: TreeNode[],
	conflictResult: ConflictResult
): {
	updatedTree: TreeNode[];
	movedItems: TreeNode[];
	skippedItems: TreeNode[];
} => {
	console.log('🔄 Executing selective move operation');

	let updatedTree = [...tree];
	const movedItems: TreeNode[] = [];
	const skippedItems: TreeNode[] = [];

	// If the main node can be moved, proceed with the move
	const mainNodeCanMove = conflictResult.canMove.some(
		(item) => item.node.id === draggedNode.id
	);

	if (mainNodeCanMove) {
		// Remove the original node from the tree
		updatedTree = removeNodeById(updatedTree, draggedNode.id);

		// Create a filtered version of the node with only moveable children
		const filteredNode = createFilteredNodeForMove(draggedNode, conflictResult);

		// Insert the filtered node at the target location
		updatedTree = insertNode(
			updatedTree,
			targetNode.id,
			filteredNode,
			position
		);

		movedItems.push(filteredNode);

		// Collect all moved descendants
		collectMovedDescendants(filteredNode, movedItems);
	} else {
		// Main node cannot be moved due to conflict
		skippedItems.push(draggedNode);
	}

	// Collect skipped items
	conflictResult.cannotMove.forEach((item) => {
		if (!skippedItems.some((skipped) => skipped.id === item.node.id)) {
			skippedItems.push(item.node);
		}
	});

	return { updatedTree, movedItems, skippedItems };
};

/**
 * Creates a filtered version of a node that only includes children that can be moved
 */
const createFilteredNodeForMove = (
	node: TreeNode,
	conflictResult: ConflictResult
): TreeNode => {
	const canMoveIds = new Set(
		conflictResult.canMove.map((item) => item.node.id)
	);

	const filteredNode: TreeNode = {
		...node,
		children: node.children
			? filterMovableChildren(node.children, canMoveIds)
			: undefined,
	};

	return filteredNode;
};

/**
 * Recursively filters children to only include those that can be moved
 */
const filterMovableChildren = (
	children: TreeNode[],
	canMoveIds: Set<string>
): TreeNode[] => {
	return children
		.filter((child) => canMoveIds.has(child.id))
		.map((child) => ({
			...child,
			children: child.children
				? filterMovableChildren(child.children, canMoveIds)
				: undefined,
		}));
};

/**
 * Recursively collects all descendants of a moved node
 */
const collectMovedDescendants = (
	node: TreeNode,
	movedItems: TreeNode[]
): void => {
	if (node.children) {
		node.children.forEach((child) => {
			movedItems.push(child);
			collectMovedDescendants(child, movedItems);
		});
	}
};

/**
 * Generates user-friendly conflict messages
 */
export const generateConflictMessage = (
	conflictResult: ConflictResult
): string => {
	if (!conflictResult.hasConflicts) {
		return '';
	}

	const fileConflicts = conflictResult.conflicts.filter(
		(c) => c.itemType === 'file'
	);
	const folderConflicts = conflictResult.conflicts.filter(
		(c) => c.itemType === 'folder'
	);

	let message =
		'The following items could not be moved due to naming conflicts:\n\n';

	if (fileConflicts.length > 0) {
		message += '📄 Files:\n';
		fileConflicts.forEach((conflict) => {
			message += `• ${conflict.itemName} - ${conflict.reason}\n`;
		});
		message += '\n';
	}

	if (folderConflicts.length > 0) {
		message += '📁 Folders:\n';
		folderConflicts.forEach((conflict) => {
			message += `• ${conflict.itemName} - ${conflict.reason}\n`;
		});
		message += '\n';
	}

	if (conflictResult.canMove.length > 0) {
		message += `✅ ${conflictResult.canMove.length} item(s) were moved successfully.`;
	}

	return message.trim();
};

/**
 * Generates a summary message for successful partial moves
 */
export const generateMoveSuccessMessage = (
	movedItems: TreeNode[],
	skippedItems: TreeNode[]
): string => {
	if (skippedItems.length === 0) {
		return `✅ Successfully moved ${movedItems.length} item(s).`;
	}

	let message = `✅ Partially completed move operation:\n\n`;
	message += `• ${movedItems.length} item(s) moved successfully\n`;
	message += `• ${skippedItems.length} item(s) skipped due to conflicts\n\n`;

	if (skippedItems.length > 0) {
		message += 'Skipped items:\n';
		skippedItems.forEach((item) => {
			const itemType =
				item.type === 'page' || item.type === 'subpage' ? 'file' : 'folder';
			message += `• ${item.name} (${itemType})\n`;
		});
	}

	return message.trim();
};

// Function to validate folder names (categories, groups and navbar) - allows letters, numbers, spaces and underscores
export const validateFolderName = (
	name: string
): { isValid: boolean; error?: string } => {
	const trimmedName = name.trim();

	if (!trimmedName) {
		return { isValid: false, error: 'Name cannot be empty' };
	}

	if (trimmedName.includes('/')) {
		return { isValid: false, error: 'Name cannot contain /' };
	}

	return { isValid: true };
};

// Função para sanitizar nomes de arquivo
export const sanitizeFileName = (name: string): string => {
	return name
		.toLowerCase()
		.normalize('NFD') // Normalize accents
		.replace(/[\u0300-\u036f]/g, '') // Remove accent marks
		.replace(/[^a-z0-9\s_-]/g, '') // Keep only letters, numbers, spaces, underscores, and hyphens
		.replace(/\s+/g, '-') // Replace spaces with hyphens
		.replace(/-+/g, '-') // Replace multiple hyphens with single hyphen
		.replace(/^-|-$/g, ''); // Remove leading/trailing hyphens
};
