import {
	Thum<PERSON>Up,
	ThumbsDown,
	Minus,
	LifeBuoy,
	CheckCircle2,
	MinusCircle,
} from "lucide-react";
import { Button } from "@/components/ui/button";
import { DateRangePicker } from "@/components/ui/date-range-picker";
import { isAfter, isBefore, subDays } from "date-fns";

interface FiltersComponentProps {
	filters: {
		rating: string;
		escalation: string;
		couldAnswer: string;
	};
	dateRange: {
		startDate: Date | undefined;
		endDate: Date | undefined;
	};
	onRatingFilterChange: (value: string) => void;
	onEscalationFilterChange: (value: string) => void;
	onCouldAnswerFilterChange: (value: string) => void;
	onDateRangeChange: (
		startDate: Date | undefined,
		endDate: Date | undefined
	) => void;
	isLoading: boolean;
}

export const FiltersComponent = ({
	filters,
	dateRange,
	onRatingFilterChange,
	onEscalationFilterChange,
	onCouldAnswerFilterChange,
	onDateRangeChange,
	isLoading,
}: FiltersComponentProps) => {
	// Constants for date filtering
	const today = new Date();
	const maxPastDate = subDays(today, 90); // Maximum date in the past (90 days ago)

	// Function to disable invalid dates (future dates or more than 90 days in the past)
	const isDateDisabled = (date: Date) => {
		return isAfter(date, today) || isBefore(date, maxPastDate);
	};

	return (
		<div className='flex flex-wrap gap-x-4 gap-y-2'>
			{/* Rating Filter */}
			<div className='flex flex-col'>
				<label className='text-sm font-medium text-wd-font-color mb-1'>
					Rating
				</label>
				<div className='flex items-center space-x-1'>
					<Button
						onClick={() => onRatingFilterChange("all")}
						variant={
							filters.rating === "all" || !filters.rating ? "blue" : "outline"
						}
						size='sm'
						disabled={isLoading}
						className='px-2 text-xs'
					>
						All
					</Button>
					<Button
						onClick={() => onRatingFilterChange("positive")}
						variant={filters.rating === "positive" ? "blue" : "outline"}
						size='sm'
						disabled={isLoading}
						className='px-2 text-xs flex items-center'
					>
						<ThumbsUp
							className={
								filters.rating === "positive"
									? "text-white mr-1"
									: "text-green-500 mr-1"
							}
							size={12}
						/>
						Up
					</Button>
					<Button
						onClick={() => onRatingFilterChange("neutral")}
						variant={filters.rating === "neutral" ? "blue" : "outline"}
						size='sm'
						disabled={isLoading}
						className='px-2 text-xs flex items-center'
					>
						<Minus
							className={
								filters.rating === "neutral"
									? "text-white mr-1"
									: "text-wd-font-color mr-1"
							}
							size={12}
						/>
						Neutral
					</Button>
					<Button
						onClick={() => onRatingFilterChange("negative")}
						variant={filters.rating === "negative" ? "blue" : "outline"}
						size='sm'
						disabled={isLoading}
						className='px-2 text-xs flex items-center'
					>
						<ThumbsDown
							className={
								filters.rating === "negative"
									? "text-white mr-1"
									: "text-destructive mr-1"
							}
							size={12}
						/>
						Down
					</Button>
				</div>
			</div>

			{/* Escalated Filter */}
			<div className='flex flex-col'>
				<label className='text-sm font-medium text-wd-font-color mb-1'>
					Escalated
				</label>
				<div className='flex items-center space-x-1'>
					<Button
						onClick={() => onEscalationFilterChange("all")}
						variant={
							filters.escalation === "all" || !filters.escalation
								? "blue"
								: "outline"
						}
						size='sm'
						disabled={isLoading}
						className='px-2 text-xs'
					>
						All
					</Button>
					<Button
						onClick={() => onEscalationFilterChange("escalated")}
						variant={filters.escalation === "escalated" ? "blue" : "outline"}
						size='sm'
						disabled={isLoading}
						className='px-2 text-xs flex items-center'
					>
						<LifeBuoy
							className={
								filters.escalation === "escalated"
									? "text-white mr-1"
									: "text-wd-blue mr-1"
							}
							size={12}
						/>
						Yes
					</Button>
					<Button
						onClick={() => onEscalationFilterChange("notEscalated")}
						variant={filters.escalation === "notEscalated" ? "blue" : "outline"}
						size='sm'
						disabled={isLoading}
						className='px-2 text-xs flex items-center'
					>
						<LifeBuoy
							className={
								filters.escalation === "notEscalated"
									? "text-white mr-1"
									: "text-muted-foreground mr-1"
							}
							size={12}
						/>
						No
					</Button>
				</div>
			</div>

			{/* Could Answer Filter */}
			<div className='flex flex-col'>
				<label className='text-sm font-medium text-wd-font-color mb-1'>
					Could Answer
				</label>
				<div className='flex items-center space-x-1'>
					<Button
						onClick={() => onCouldAnswerFilterChange("all")}
						variant={
							filters.couldAnswer === "all" || !filters.couldAnswer
								? "blue"
								: "outline"
						}
						size='sm'
						disabled={isLoading}
						className='px-2 text-xs'
					>
						All
					</Button>
					<Button
						onClick={() => onCouldAnswerFilterChange("yes")}
						variant={filters.couldAnswer === "yes" ? "blue" : "outline"}
						size='sm'
						disabled={isLoading}
						className='px-2 text-xs flex items-center'
					>
						<CheckCircle2
							className={
								filters.couldAnswer === "yes"
									? "text-white mr-1"
									: "text-green-500 mr-1"
							}
							size={12}
						/>
						Yes
					</Button>
					<Button
						onClick={() => onCouldAnswerFilterChange("no")}
						variant={filters.couldAnswer === "no" ? "blue" : "outline"}
						size='sm'
						disabled={isLoading}
						className='px-2 text-xs flex items-center'
					>
						<MinusCircle
							className={
								filters.couldAnswer === "no"
									? "text-white mr-1"
									: "text-destructive mr-1"
							}
							size={12}
						/>
						No
					</Button>
				</div>
			</div>

			{/* Date Range Filter */}
			<div className='flex flex-col justify-end items-end'>
				<DateRangePicker
					dateRange={{
						from: dateRange.startDate,
						to: dateRange.endDate,
					}}
					onDateRangeChange={(from, to) => onDateRangeChange(from, to)}
					disabled={isLoading}
					isDisabledDate={(date) => isDateDisabled(date) || isLoading}
					placeholder='Select date range'
					className='w-48'
				/>
			</div>
		</div>
	);
};
