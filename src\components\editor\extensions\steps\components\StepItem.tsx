import React, { useState } from "react";
import { StepItemProps, StepsListProps } from "../types";
import { getStepNumberClass, getStepTitleClass } from "../utils";
import { ActionButtons } from "./ActionButtons";
import { VerticalLine } from "./VerticalLine";
import { StepEditForm } from "./StepEditForm";

export const StepItem: React.FC<StepItemProps> = ({
  step,
  index,
  level,
  isLast,
  onEdit,
  onDelete,
  onAddSubStep,
  isEditing,
  editingStepId,
  onSaveEdit,
  onCancelEdit,
  isFirstStepInLevel = false,
}) => {
  const [isHovered, setIsHovered] = useState(false);

  if (isEditing) {
    return (
      <div className="step-item relative w-full">
        <StepEditForm
          step={step}
          onSave={onSaveEdit}
          onCancel={onCancelEdit}
          isFirstStepInLevel={isFirstStepInLevel}
        />
      </div>
    );
  }

  const hasSubSteps = step.subSteps && step.subSteps.length > 0;

  return (
    <div className="step-item relative w-full">
      <VerticalLine
        titleSize={step.titleSize}
        hasSubSteps={!!hasSubSteps}
        isLast={isLast}
      />

      <div className="flex gap-3 pb-4 relative w-full">
        <div className={getStepNumberClass(step.titleSize)}>{index + 1}</div>

        <div className="step-body flex-1 min-w-0 w-full relative">
          {/* Container do conteúdo do step com hover isolado */}
          <div
            className={`step-hover-zone relative rounded-lg p-2 -m-2 transition-all duration-200 ${
              isHovered
                ? "bg-blue-50/50 dark:bg-blue-900/10 border border-blue-200/50 dark:border-blue-800/50"
                : "border border-transparent"
            }`}
            onMouseEnter={(e) => {
              e.stopPropagation();
              setIsHovered(true);
            }}
            onMouseLeave={(e) => {
              e.stopPropagation();
              setIsHovered(false);
            }}
          >
            {isHovered && (
              <ActionButtons
                onEdit={() => onEdit(step)}
                onAddSubStep={() => onAddSubStep(step.id)}
                onDelete={() => onDelete(step.id)}
              />
            )}

            <div className="w-full pr-2">
              <div className={getStepTitleClass(step.titleSize)}>
                {step.title || `Step ${index + 1}`}
              </div>
              {step.content && (
                <div
                  className="step-content text-gray-700 dark:text-gray-300 leading-relaxed prose prose-sm max-w-none mb-2 w-full"
                  dangerouslySetInnerHTML={{ __html: step.content }}
                />
              )}
            </div>
          </div>

          {/* Sub-steps fora do container de hover */}
          {hasSubSteps && (
            <div className="w-full mt-2">
              <StepsList
                steps={step.subSteps!}
                level={level + 1}
                onEdit={onEdit}
                onDelete={onDelete}
                onAddSubStep={onAddSubStep}
                editingStepId={editingStepId}
                onSaveEdit={onSaveEdit}
                onCancelEdit={onCancelEdit}
              />
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export const StepsList: React.FC<StepsListProps> = ({
  steps,
  level,
  onEdit,
  onDelete,
  onAddSubStep,
  editingStepId,
  onSaveEdit,
  onCancelEdit,
}) => {
  if (!steps || steps.length === 0) return null;

  const getMainLinePosition = () => {
    if (steps.length === 0) return 15;
    const firstStep = steps[0];
    const sizeMap = {
      h2: 19,
      h3: 15,
      h4: 13,
      h5: 11,
      h6: 9,
    };
    return sizeMap[firstStep.titleSize as keyof typeof sizeMap] || 15;
  };

  const shouldMainLineFade = true;

  return (
    <div className="steps-container relative w-full">
      <div
        className={`line absolute w-0.5 z-0 pointer-events-none ${
          shouldMainLineFade ? "step-line-fade" : "bg-gray-300 dark:bg-gray-600"
        }`}
        style={{
          left: `${getMainLinePosition()}px`,
          top: "32px",
          height: "calc(100% - 16px)",
        }}
      />

      <div className="relative w-full">
        {steps.map((step, index) => (
          <StepItem
            key={step.id}
            step={step}
            index={index}
            level={level}
            isLast={index === steps.length - 1}
            onEdit={onEdit}
            onDelete={onDelete}
            onAddSubStep={onAddSubStep}
            isEditing={editingStepId === step.id}
            editingStepId={editingStepId}
            onSaveEdit={onSaveEdit}
            onCancelEdit={onCancelEdit}
            isFirstStepInLevel={index === 0}
          />
        ))}
      </div>
    </div>
  );
};
