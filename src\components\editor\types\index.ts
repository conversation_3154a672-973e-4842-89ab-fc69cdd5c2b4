import type { Editor } from "@tiptap/react";

// Interface for Card component
export interface CardData {
  title: string;
  description: string;
  link: string;
  icon: string;
  iconType: string;
  iconSize: string;
  image: string;
}

export interface TiptapEditorProps {
  content?: string;
  editable?: boolean;
  onUpdate?: (editor: Editor) => void;
}

export interface CommandProps {
  editor: Editor;
  range: { from: number; to: number };
}
