import { findCloudflareProjectByRepo } from "@/utils/create-repo/cloudflare";
import { NextRequest, NextResponse } from "next/server";

export const runtime = 'edge';

export async function GET(request: NextRequest) {
	const { searchParams } = new URL(request.url);
	const repoName = searchParams.get("repoName");

	if (!repoName) {
		return NextResponse.json(
			{ error: "repoName query parameter is required" },
			{ status: 400 }
		);
	}

	try {
		const project = await findCloudflareProjectByRepo(repoName);
		if (!project) {
			return NextResponse.json(
				{ error: `Project with repo name "${repoName}" not found` },
				{ status: 404 }
			);
		}
		return NextResponse.json({ project });
	} catch (error) {
		console.error(`Error finding project by repo name ${repoName}:`, error);
		return NextResponse.json(
			{ error: "Failed to fetch project from Cloudflare" },
			{ status: 500 }
		);
	}
}
