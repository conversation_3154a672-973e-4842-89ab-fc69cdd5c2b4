"use client";

import React, {
  createContext,
  useContext,
  useState,
  useCallback,
  useEffect,
} from "react";
import { AnimatePresence, motion } from "framer-motion";
import {
  AlertCircle,
  CheckCircle,
  XCircle,
  Info,
  X,
  Loader2,
} from "lucide-react";

type ToastType = "success" | "error" | "info" | "warning" | "loading";

interface Toast {
  id: string;
  message: string;
  type: ToastType;
  title?: string;
  duration?: number;
  spinnerOnly?: boolean;
}

interface ToastContextType {
  addToast: (
    message: string,
    type: ToastType,
    title?: string,
    id?: string,
    duration?: number,
    spinnerOnly?: boolean
  ) => void;
  removeToast: (id: string) => void;
}

const ToastContext = createContext<ToastContextType | null>(null);

const toastIcons = {
  success: <CheckCircle className="w-5 h-5 text-green-500" />,
  error: <XCircle className="w-5 h-5 text-red-500" />,
  warning: <AlertCircle className="w-5 h-5 text-yellow-500" />,
  info: <Info className="w-5 h-5 text-blue-500" />,
  loading: <Loader2 className="w-5 h-5 text-blue-500 animate-spin" />,
};

export function useToast() {
  const context = useContext(ToastContext);
  if (!context) {
    throw new Error("useToast must be used within a ToastProvider");
  }
  return context;
}

export function ToastProvider({ children }: { children: React.ReactNode }) {
  const [toasts, setToasts] = useState<Toast[]>([]);
  const timersRef = React.useRef<Map<string, NodeJS.Timeout>>(new Map());
  const updateRef = React.useRef<{ id: string; timestamp: number } | null>(
    null
  );

  const resetTimer = useCallback((id: string, duration?: number) => {
    if (timersRef.current.has(id)) {
      clearTimeout(timersRef.current.get(id));
    }
    const newTimer = setTimeout(() => {
      setToasts((current) => current.filter((t) => t.id !== id));
      timersRef.current.delete(id);
      if (updateRef.current?.id === id) {
        updateRef.current = null;
      }
    }, duration || 4000);
    timersRef.current.set(id, newTimer);
  }, []);

  const addToast = useCallback(
    (
      message: string,
      type: ToastType,
      title?: string,
      customId?: string,
      duration?: number,
      spinnerOnly?: boolean
    ) => {
      setToasts((prev) => {
        const existingToast = customId
          ? prev.find((t) => t.id === customId)
          : prev.find(
              (t) =>
                t.message === message && t.type === type && t.title === title
            );

        if (existingToast) {
          updateRef.current = { id: existingToast.id, timestamp: Date.now() };
          resetTimer(existingToast.id, duration);
          return prev.map((t) =>
            t.id === existingToast.id
              ? { ...t, message, type, title, duration, spinnerOnly }
              : t
          );
        }

        const id = customId || crypto.randomUUID();
        resetTimer(id, duration);
        return [...prev, { id, message, type, title, duration, spinnerOnly }];
      });
    },
    [resetTimer]
  );

  useEffect(() => {
    return () => {
      timersRef.current.forEach((timer) => clearTimeout(timer));
    };
  }, []);

  const removeToast = useCallback((id: string) => {
    setToasts((prev) => prev.filter((toast) => toast.id !== id));
  }, []);

  // Separate toasts into publish-related and regular toasts
  const publishToasts = toasts.filter(
    (t) => !t.spinnerOnly && t.title?.toLowerCase().includes("publish")
  );
  const regularToasts = toasts.filter(
    (t) => !t.spinnerOnly && !t.title?.toLowerCase().includes("publish")
  );
  const spinnerToasts = toasts.filter((t) => t.spinnerOnly);

  return (
    <ToastContext.Provider value={{ addToast, removeToast }}>
      {children}
      <div
        id="toast-container"
        style={{
          position: "fixed",
          pointerEvents: "none",
          zIndex: 50,
          top: 0,
          right: 0,
          width: "100%",
          height: "100%",
        }}
      >
        {/* Publish-related toasts container (top-right) */}
        <div
          style={{
            display: "flex",
            flexDirection: "column",
            alignItems: "flex-end",
            paddingTop: "1rem",
            width: "100%",
            maxWidth: "420px",
            marginLeft: "auto",
          }}
        >
          <AnimatePresence initial={false}>
            {publishToasts.map((toast) => (
              <motion.div
                layout
                key={toast.id}
                initial={{ opacity: 0, x: 100, y: 0 }}
                animate={{ opacity: 1, x: 0, y: 0 }}
                exit={{
                  opacity: 0,
                  x: 100,
                  transition: {
                    duration: 0.3,
                    ease: "easeInOut",
                  },
                }}
                transition={{
                  type: "spring",
                  stiffness: 400,
                  damping: 30,
                }}
                style={{
                  pointerEvents: "auto",
                  marginRight: "1rem",
                  marginBottom: "0.5rem",
                  backgroundColor: "white",
                  boxShadow:
                    "0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05)",
                  borderRadius: "0.5rem",
                  padding: "1rem",
                  display: "flex",
                  alignItems: "flex-start",
                  gap: "0.75rem",
                  minWidth: "320px",
                  maxWidth: "420px",
                  borderWidth: "1px",
                  borderColor:
                    toast.type === "error"
                      ? "#f56565"
                      : toast.type === "success"
                      ? "#48bb78"
                      : toast.type === "warning"
                      ? "#ed8936"
                      : toast.type === "loading"
                      ? "#4299e1"
                      : "#4299e1",
                  borderLeftWidth: toast.type === "loading" ? "4px" : "1px",
                }}
              >
                <div className="flex-shrink-0 mt-0.5">
                  {toastIcons[toast.type]}
                </div>
                <div className="flex-1 pt-[2px]">
                  {toast.title && (
                    <h5 className="font-semibold text-gray-900 mb-1">
                      {toast.title}{" "}
                      {toast.type === "loading" && (
                        <span className="text-blue-500">...</span>
                      )}
                    </h5>
                  )}
                  <p
                    className="text-sm text-gray-600 whitespace-pre-line"
                    dangerouslySetInnerHTML={{ __html: toast.message }}
                  />
                </div>
                <button
                  onClick={() => removeToast(toast.id)}
                  className="flex-shrink-0 flex items-center justify-center w-6 h-6 rounded-full hover:bg-gray-100 transition-colors"
                >
                  <X className="w-4 h-4 text-gray-500" />
                </button>
              </motion.div>
            ))}
          </AnimatePresence>
        </div>

        {/* Regular toasts container (bottom-right) */}
        <div
          style={{
            display: "flex",
            flexDirection: "column-reverse",
            alignItems: "flex-end",
            position: "absolute",
            bottom: "7rem",
            right: 0,
            width: "100%",
            maxWidth: "420px",
            maxHeight: "calc(100vh - 6rem)",
            overflow: "hidden",
            paddingBottom: "1rem",
            zIndex: 100,
          }}
        >
          <AnimatePresence initial={false}>
            {regularToasts.map((toast) => (
              <motion.div
                layout
                key={toast.id}
                initial={{ opacity: 0, x: 100, y: 0 }}
                animate={{ opacity: 1, x: 0, y: 0 }}
                exit={{
                  opacity: 0,
                  x: 100,
                  transition: {
                    duration: 0.3,
                    ease: "easeInOut",
                  },
                }}
                transition={{
                  type: "spring",
                  stiffness: 400,
                  damping: 30,
                }}
                style={{
                  pointerEvents: "auto",
                  marginRight: "1rem",
                  marginBottom: "0.75rem",
                  backgroundColor: "white",
                  boxShadow:
                    "0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05)",
                  borderRadius: "0.5rem",
                  padding: "1rem",
                  display: "flex",
                  alignItems: "flex-start",
                  gap: "0.75rem",
                  minWidth: "320px",
                  maxWidth: "420px",
                  minHeight: "auto",
                  borderWidth: "1px",
                  borderColor:
                    toast.type === "error"
                      ? "#f56565"
                      : toast.type === "success"
                      ? "#48bb78"
                      : toast.type === "warning"
                      ? "#ed8936"
                      : toast.type === "loading"
                      ? "#4299e1"
                      : "#4299e1",
                  borderLeftWidth: toast.type === "loading" ? "4px" : "1px",
                }}
              >
                <div className="flex-shrink-0 mt-0.5">
                  {toastIcons[toast.type]}
                </div>
                <div className="flex-1 pt-[2px]">
                  {toast.title && (
                    <h5 className="font-semibold text-gray-900 mb-1">
                      {toast.title}{" "}
                      {toast.type === "loading" && (
                        <span className="text-blue-500">...</span>
                      )}
                    </h5>
                  )}
                  <p
                    className="text-sm text-gray-600 whitespace-pre-line"
                    dangerouslySetInnerHTML={{ __html: toast.message }}
                  />
                </div>
                <button
                  onClick={() => removeToast(toast.id)}
                  className="flex-shrink-0 flex items-center justify-center w-6 h-6 rounded-full hover:bg-gray-100 transition-colors"
                >
                  <X className="w-4 h-4 text-gray-500" />
                </button>
              </motion.div>
            ))}
          </AnimatePresence>
        </div>

        {/* Spinner-only toasts - fixed at the exact right edge */}
        <div style={{ position: "absolute", top: "4rem", right: 0 }}>
          <AnimatePresence initial={false}>
            {spinnerToasts.map((toast) => (
              <motion.div
                layout
                key={toast.id}
                initial={{ opacity: 0, x: 20 }}
                animate={{ opacity: 1, x: 0 }}
                exit={{ opacity: 0, x: 20 }}
                transition={{ duration: 0.2 }}
                style={{
                  pointerEvents: "auto",
                  backgroundColor: "white",
                  boxShadow:
                    "0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)",
                  borderTopLeftRadius: "9999px",
                  borderBottomLeftRadius: "9999px",
                  padding: "0.35rem 0.6rem",
                  display: "flex",
                  flexDirection: "row",
                  alignItems: "center",
                  justifyContent: "flex-end",
                  width: "fit-content",
                  height: "2rem",
                  gap: "0.5rem",
                }}
              >
                {toast.title && (
                  <span className="text-xs font-medium text-blue-600">
                    {toast.title.replace("...", "")}
                  </span>
                )}
                <Loader2 className="w-4 h-4 text-blue-500 animate-spin flex-shrink-0" />
              </motion.div>
            ))}
          </AnimatePresence>
        </div>
      </div>
    </ToastContext.Provider>
  );
}
