import "../globals.css";
import { CombinedContextProvider } from "@/contexts";
import { ToastProvider } from "@/components/ToastProvider";
import { TooltipProvider } from "@/components/ui/tooltip";
import "chart.js/auto";

interface DocsBotAIType {
  init: (config: { id: string }) => Promise<void>;
  mount: (config: { id: string }) => Promise<void>;
}

declare global {
  interface Window {
    DocsBotAI: DocsBotAIType;
  }
}

export const runtime = "edge";

export default async function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <CombinedContextProvider>
      <TooltipProvider delayDuration={100} disableHoverableContent={false}>
        <ToastProvider>{children}</ToastProvider>
      </TooltipProvider>
    </CombinedContextProvider>
  );
}
