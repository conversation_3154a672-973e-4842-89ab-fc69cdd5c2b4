import { setupDevPlatform } from '@cloudflare/next-on-pages/next-dev';

// Here we use the @cloudflare/next-on-pages next-dev module to allow us to
// use bindings during local development (when running the application with
// next dev). This function is only necessary during development and
// has no impact outside of that. For more information see:
// https://github.com/cloudflare/next-on-pages/blob/main/internal-packages/next-dev/README.md
setupDevPlatform().catch(console.error);

import { NextConfig } from 'next';

const nextConfig: NextConfig = {
	/* config options here */
	async redirects() {
		return [
			{
				source: '/',
				destination: '/hub',
				permanent: false,
			},
		];
	},
	// Otimizações para reduzir uso de memória
	experimental: {
		// Otimiza imports de bibliotecas grandes
		optimizePackageImports: ['mermaid', 'chart.js', '@tiptap/react'],
	},
	// Configurações de webpack para otimizar o build
	webpack: (config, { isServer }) => {
		// Otimizações para reduzir uso de memória
		config.optimization = {
			...config.optimization,
			splitChunks: {
				...config.optimization.splitChunks,
				cacheGroups: {
					...config.optimization.splitChunks?.cacheGroups,
					// Separa bibliotecas pesadas em chunks menores
					mermaid: {
						name: 'mermaid',
						test: /[\\/]node_modules[\\/]mermaid[\\/]/,
						chunks: 'all',
						priority: 30,
					},
					charts: {
						name: 'charts',
						test: /[\\/]node_modules[\\/](chart\.js|react-chartjs-2)[\\/]/,
						chunks: 'all',
						priority: 25,
					},
					tiptap: {
						name: 'tiptap',
						test: /[\\/]node_modules[\\/]@tiptap[\\/]/,
						chunks: 'all',
						priority: 20,
					},
				},
			},
		};

		return config;
	},
};

export default nextConfig;
