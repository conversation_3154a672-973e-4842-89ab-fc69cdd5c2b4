import { StripeCustomer, formatPrice } from "./types";
import PaymentLogo from "@/components/PaymentLogo";
import { ExternalLink } from "lucide-react";
import { useState } from "react";
import clsx from "clsx";
import { redirectToCustomerPortal } from "./hooks";
import { useRouter, useParams } from "next/navigation";

type ManagePlanProps = {
	customer: StripeCustomer | null;
	hasAdminPermission: boolean;
};

export function ManagePlan({ customer, hasAdminPermission }: ManagePlanProps) {
	const [isLoading, setIsLoading] = useState(false);
	const router = useRouter();
	const params = useParams();
	const projectId = params.projectId as string;

	// If there's no customer or subscription, don't show the component
	if (!customer || !customer.subscription_id) {
		return null;
	}

	// Handler for opening the Stripe Customer Portal or navigating to Members tab
	const handleManagePlan = async () => {
		if (!hasAdminPermission) {
			const targetUrl = projectId
				? `/${projectId}/settings?tab=members`
				: "/settings?tab=members";
			router.push(targetUrl);
			return;
		}

		if (!customer?.id) return;

		try {
			setIsLoading(true);
			console.log("🎯 [MANAGE_PLAN] Redirecionando para portal do Stripe:", {
				customerId: customer.id,
				projectId: projectId,
			});
			await redirectToCustomerPortal(customer.id, projectId);
		} catch (error) {
			console.error("❌ [MANAGE_PLAN] Erro ao abrir portal do cliente:", error);
			setIsLoading(false);
		}
	};

	// Handler for navigating to Members tab
	const handleNavigateToMembers = () => {
		const targetUrl = projectId
			? `/${projectId}/settings?tab=members`
			: "/settings?tab=members";
		router.push(targetUrl);
	};

	// Format next payment date
	const formatDate = (dateString?: string) => {
		if (!dateString) return "N/A";
		return new Date(dateString).toLocaleDateString("en-US", {
			day: "numeric",
			month: "long",
			year: "numeric",
		});
	};

	// Format payment method
	const formatPaymentMethod = () => {
		const brand = customer.lastPayment?.payment_method_brand || "";
		const last4 = customer.lastPayment?.payment_method_last4 || "";

		return {
			raw: { brand, last4 },
			formatted: last4 ? `•••• ${last4}` : "Not available",
			icon: <PaymentLogo brand={brand} size={24} />,
		};
	};

	const nextPayment = formatDate(customer.current_period_end);
	const paymentMethod = formatPaymentMethod();

	const statusMap = {
		active: "Paid",
		past_due: "Past Due",
		canceled: "Canceled",
		incomplete: "Incomplete",
		incomplete_expired: "Expired",
		trialing: "Trial",
		unpaid: "Unpaid",
		inactive: "Inactive",
	};
	const status = statusMap[customer.status] || customer.status;

	// Determine status color based on value
	const getStatusInfo = () => {
		switch (customer.status) {
			case "active":
				return {
					bg: "bg-green-100",
					text: "text-green-700",
					border: "border-green-200",
				};
			case "past_due":
			case "unpaid":
				return {
					bg: "bg-amber-100",
					text: "text-amber-700",
					border: "border-amber-200",
				};
			case "canceled":
			case "incomplete_expired":
				return {
					bg: "bg-red-100",
					text: "text-red-700",
					border: "border-red-200",
				};
			case "trialing":
				return {
					bg: "bg-blue-100",
					text: "text-blue-700",
					border: "border-blue-200",
				};
			case "incomplete":
			default:
				return {
					bg: "bg-gray-100",
					text: "text-gray-700",
					border: "border-gray-200",
				};
		}
	};

	const statusInfo = getStatusInfo();

	// Format payment amount if available
	const amount =
		customer.amount && customer.lastPayment?.currency
			? formatPrice(customer.amount, customer.lastPayment.currency)
			: "N/A";

	// Billing interval (monthly/yearly)
	const billingInterval = customer.billingInterval || "Monthly";

	return (
		<div className='bg-white rounded-lg border border-gray-200 mb-8 mx-auto'>
			<div className='p-6'>
				<div className='flex justify-between items-center mb-6'>
					<div className='flex items-center gap-2'>
						<button
							onClick={handleManagePlan}
							disabled={isLoading || !hasAdminPermission}
							className={clsx(
								"text-lg font-semibold text-gray-900 flex items-center gap-1",
								hasAdminPermission &&
									"transition-all disabled:cursor-not-allowed",
								hasAdminPermission && !isLoading && "group"
							)}
							title={
								hasAdminPermission
									? "Manage plan in Stripe portal"
									: "Admin permission required to manage plan"
							}
						>
							<span className={clsx(hasAdminPermission && "relative")}>
								Manage Plan
								{hasAdminPermission && (
									<span className='absolute bottom-0 left-0 w-0 h-0.5 bg-gray-900 transition-all duration-300 group-hover:w-full opacity-80'></span>
								)}
							</span>
							{isLoading ? (
								<div className='animate-spin h-3.5 w-3.5 border-2 border-b-transparent rounded-full border-gray-400 ml-1' />
							) : hasAdminPermission ? (
								<ExternalLink className='h-3.5 w-3.5 text-gray-500' />
							) : null}
						</button>
					</div>
				</div>

				<div className='grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-5 gap-6 items-center'>
					<div>
						<p className='text-sm text-gray-500 mb-1'>AMOUNT</p>
						<div className='text-base font-medium text-gray-900'>{amount}</div>
					</div>
					<div>
						<p className='text-sm text-gray-500 mb-1'>BILLING</p>
						<div className='text-base font-medium text-gray-900'>
							{billingInterval}
						</div>
					</div>
					<div>
						<div className='flex flex-col justify-center items-center w-fit'>
							<p className='text-sm text-gray-500 mb-1'>STATUS</p>
							<div className='flex items-start'>
								<span
									className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium border ${statusInfo.bg} ${statusInfo.text} ${statusInfo.border}`}
								>
									{status}
								</span>
							</div>
						</div>
					</div>
					<div>
						<p className='text-sm text-gray-500 mb-1'>NEXT PAYMENT</p>
						<div className='text-base font-medium text-gray-900'>
							{nextPayment}
						</div>
					</div>
					<div>
						<p className='text-sm text-gray-500 mb-1'>PAYMENT METHOD</p>
						<div className='flex items-center space-x-3'>
							{paymentMethod.icon}
							<span className='text-sm text-gray-600'>
								{paymentMethod.formatted}
							</span>
						</div>
					</div>
				</div>

				{!hasAdminPermission && (
					<>
						{/* line separator */}
						<div className='mt-6 border-t border-gray-200'></div>
						<div className='mt-4 text-sm text-gray-500'>
							<span
								onClick={handleNavigateToMembers}
								className='text-blue-600 hover:text-blue-800 cursor-pointer'
							>
								Contact project owner
							</span>{" "}
							if you need to make changes to your plan or payment information.
						</div>
					</>
				)}
			</div>
		</div>
	);
}
