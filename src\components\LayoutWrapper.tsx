"use client";

import { useProject } from "@/contexts";
import { useEffect, useRef } from "react";

interface ProjectInfo {
	siteName: string | undefined;
	siteTag: string | undefined;
	hasWebAnalytics: boolean | undefined;
}

interface LayoutWrapperProps {
	children: React.ReactNode;
	projectId: string;
	projectInfo: ProjectInfo;
}

/**
 * LayoutWrapper Component
 *
 * This client component wraps the layout content and sets
 * the project information fetched server-side into the context.
 * Waits for the context to finish loading projects before updating.
 */
export function LayoutWrapper({
	children,
	projectId,
	projectInfo,
}: LayoutWrapperProps) {
	const { updateProjectInfo, isLoadingProjects, projects } = useProject();

	// Track if we've already updated this project to prevent loops
	const updatedProjectsRef = useRef<Set<string>>(new Set());

	useEffect(() => {
		// Only update project info after context has finished loading
		if (!isLoadingProjects && projects.length > 0) {
			const projectIdNum = parseInt(projectId);
			const projectExists = projects.find((p) => p.id === projectIdNum);

			// Create a unique key for this update
			const updateKey = `${projectId}-${projectInfo.siteTag}-${projectInfo.siteName}-${projectInfo.hasWebAnalytics}`;

			if (projectIdNum && projectInfo && projectExists) {
				// Check if the data is already the same (prevent unnecessary updates)
				const currentData = {
					siteTag: projectExists.siteTag,
					siteName: projectExists.siteName,
					hasWebAnalytics: projectExists.hasWebAnalytics,
				};

				const newData = {
					siteTag: projectInfo.siteTag,
					siteName: projectInfo.siteName,
					hasWebAnalytics: projectInfo.hasWebAnalytics,
				};

				const dataChanged =
					JSON.stringify(currentData) !== JSON.stringify(newData);
				const notYetUpdated = !updatedProjectsRef.current.has(updateKey);

				if (dataChanged && notYetUpdated) {
					console.log(
						`[LayoutWrapper] 🔄 Updating project ${projectIdNum} with server-side Cloudflare data:`,
						projectInfo
					);
					updateProjectInfo(projectIdNum, projectInfo);
					updatedProjectsRef.current.add(updateKey);
				} else if (!dataChanged) {
					console.log(
						`[LayoutWrapper] ✅ Project ${projectIdNum} already has correct Cloudflare data, skipping update`
					);
					updatedProjectsRef.current.add(updateKey);
				} else {
					console.log(
						`[LayoutWrapper] 🔒 Project ${projectIdNum} already updated with this data, skipping to prevent loop`
					);
				}
			} else if (projectIdNum && projectInfo && !projectExists) {
				console.warn(
					`[LayoutWrapper] ⚠️ Project ${projectIdNum} not found in context, cannot update Cloudflare data`
				);
			}
		} else if (!isLoadingProjects && projects.length === 0) {
			console.warn(
				`[LayoutWrapper] ⚠️ No projects found in context after loading completed`
			);
		} else {
			console.log(
				`[LayoutWrapper] ⏳ Waiting for projects to load... (isLoadingProjects: ${isLoadingProjects}, projects: ${projects.length})`
			);
		}
	}, [projectId, projectInfo, updateProjectInfo, isLoadingProjects]); // Remove 'projects' dependency to prevent loop

	return <>{children}</>;
}
