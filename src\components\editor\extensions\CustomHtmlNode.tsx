import { Node, mergeAttributes } from "@tiptap/core";
import { <PERSON>de<PERSON>iew<PERSON>rapper, ReactNodeViewRenderer } from "@tiptap/react";
import React, { useState, useCallback, useEffect, useRef } from "react";
import { Eye, EyeOff, Copy, Check, Code } from "lucide-react";
import { Button } from "@/components/ui/button";
import type { NodeViewProps } from "@tiptap/react";
import hljs from "highlight.js/lib/core";
import xml from "highlight.js/lib/languages/xml"; // for HTML

// Register HTML highlighting
hljs.registerLanguage("html", xml);

// Enhanced HTML sanitization function - allows CSS but blocks dangerous content
const sanitizeHtml = (html: string): string => {
	if (!html) return "";

	let sanitized = html;

	// 1. Remove script tags and their content (multiple patterns for evasion attempts)
	sanitized = sanitized.replace(
		/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi,
		""
	);
	sanitized = sanitized.replace(/<script[^>]*>/gi, "");
	sanitized = sanitized.replace(/<\/script>/gi, "");

	// 2. Remove dangerous tags that can execute code
	const dangerousTags = [
		"iframe",
		"object",
		"embed",
		"applet",
		"base",
		"meta",
		"link",
	];
	dangerousTags.forEach((tag) => {
		sanitized = sanitized.replace(
			new RegExp(`<${tag}\\b[^<]*(?:(?!<\\/${tag}>)<[^<]*)*<\\/${tag}>`, "gi"),
			""
		);
		sanitized = sanitized.replace(new RegExp(`<${tag}\\b[^>]*\/?>`, "gi"), "");
	});

	// 3. Remove all event handlers (comprehensive list)
	const eventHandlers = [
		"onload",
		"onclick",
		"onmouseover",
		"onmouseout",
		"onmousedown",
		"onmouseup",
		"onkeydown",
		"onkeyup",
		"onkeypress",
		"onfocus",
		"onblur",
		"onchange",
		"onsubmit",
		"onreset",
		"onselect",
		"onresize",
		"onscroll",
		"onerror",
		"onabort",
		"ondblclick",
		"ondrag",
		"ondrop",
		"oncontextmenu",
	];
	eventHandlers.forEach((handler) => {
		sanitized = sanitized.replace(
			new RegExp(`\\s+${handler}\\s*=\\s*["'][^"']*["']`, "gi"),
			""
		);
	});

	// 4. Remove dangerous URL schemes
	const dangerousSchemes = [
		"javascript:",
		"vbscript:",
		"data:text/html",
		"data:application/",
		"file:",
		"ftp:",
		"jar:",
		"mailto:",
	];
	dangerousSchemes.forEach((scheme) => {
		sanitized = sanitized.replace(
			new RegExp(
				`\\s+(href|src|action|formaction)\\s*=\\s*["']?\\s*${scheme.replace(":", "\\:")}[^"'>]*["']?`,
				"gi"
			),
			""
		);
	});

	// 5. Remove CSS expressions and dangerous CSS functions
	sanitized = sanitized.replace(/expression\s*\(/gi, "");
	sanitized = sanitized.replace(/javascript\s*:/gi, "");
	sanitized = sanitized.replace(/@import/gi, "");
	sanitized = sanitized.replace(/behavior\s*:/gi, "");

	// 6. Remove HTML comments that might contain dangerous content
	sanitized = sanitized.replace(/<!--[\s\S]*?-->/g, "");

	// 7. Remove form elements that could be used for phishing
	sanitized = sanitized.replace(/<form\b[^>]*>/gi, "");
	sanitized = sanitized.replace(/<\/form>/gi, "");

	return sanitized;
};

// Normalize HTML whitespace for proper preview rendering
const normalizeHtmlWhitespace = (html: string): string => {
	if (!html) return "";

	// First sanitize the HTML
	let normalized = sanitizeHtml(html);

	// Normalize whitespace while preserving HTML structure
	// This mimics how browsers handle whitespace in HTML
	normalized = normalized
		// Remove leading/trailing whitespace first
		.trim()
		// Normalize whitespace between tags - remove excessive spaces/newlines
		.replace(/>\s+</g, "><")
		// Normalize multiple whitespace characters within text content to single space
		// This includes spaces, tabs, newlines, and carriage returns
		.replace(/\s+/g, " ")
		// Final trim to ensure no leading/trailing whitespace
		.trim();

	return normalized;
};

// Generate line numbers for the editor
const generateLineNumbers = (content: string): string[] => {
	if (!content) return ["1"];
	const lines = content.split("\n");
	return Array.from({ length: lines.length }, (_, i) => String(i + 1));
};

// Custom HTML Component
const CustomHtmlComponent: React.FC<NodeViewProps> = ({
	node,
	updateAttributes,
	selected,
}) => {
	const [html, setHtml] = useState(node.attrs.html || "");
	const [isPreview, setIsPreview] = useState(false);
	const [copied, setCopied] = useState(false);
	const [highlightedCode, setHighlightedCode] = useState("");
	const [lineNumbers, setLineNumbers] = useState<string[]>(["1"]);
	const [isFocused, setIsFocused] = useState(false);

	const textareaRef = useRef<HTMLTextAreaElement>(null);
	const preRef = useRef<HTMLPreElement>(null);
	const lineNumbersRef = useRef<HTMLDivElement>(null);

	// Auto-resize textarea based on content
	const resizeTextarea = useCallback(() => {
		if (textareaRef.current) {
			const textarea = textareaRef.current;
			textarea.style.height = "auto";
			const contentHeight = textarea.scrollHeight;
			const minHeight = 128;
			const newHeight = Math.max(minHeight, contentHeight);
			textarea.style.height = `${newHeight}px`;

			if (preRef.current) {
				preRef.current.style.height = `${newHeight}px`;
			}

			if (lineNumbersRef.current) {
				lineNumbersRef.current.style.height = `${newHeight}px`;
			}
		}
	}, []);

	// Sync local state with node attributes when they change
	useEffect(() => {
		const nodeHtml = node.attrs.html || "";
		if (nodeHtml !== html) {
			setHtml(nodeHtml);
			// Resize after state update
			setTimeout(resizeTextarea, 10);
		}
	}, [node.attrs.html, html, resizeTextarea]);

	// Update line numbers when content changes
	useEffect(() => {
		setLineNumbers(generateLineNumbers(html));
	}, [html]);

	// Apply syntax highlighting when HTML changes
	useEffect(() => {
		if (html) {
			try {
				const highlighted = hljs.highlight(html, { language: "html" }).value;
				setHighlightedCode(highlighted);
			} catch {
				// If highlighting fails, fallback to escaped HTML
				setHighlightedCode(
					html
						.replace(/&/g, "&amp;")
						.replace(/</g, "&lt;")
						.replace(/>/g, "&gt;")
				);
			}
		} else {
			setHighlightedCode("");
		}
	}, [html]);

	// Initial resize on mount and when HTML is first loaded
	useEffect(() => {
		if (html) {
			setTimeout(resizeTextarea, 50);
		}
	}, [html, resizeTextarea]);

	// Resize on content change
	useEffect(() => {
		resizeTextarea();
	}, [html, resizeTextarea]);

	// Initialize component and ensure proper sizing on mount
	useEffect(() => {
		console.log("CustomHTML: Component initialized");
		// Force resize on component mount to ensure proper sizing
		setTimeout(() => {
			resizeTextarea();
		}, 100);
	}, [resizeTextarea]);

	// Handle clicks outside the component to ensure proper focus management
	useEffect(() => {
		const handleClickOutside = (event: MouseEvent) => {
			if (
				textareaRef.current &&
				event.target &&
				!textareaRef.current.contains(event.target as HTMLElement)
			) {
				// User clicked outside the textarea, ensure it's not focused
				if (isFocused) {
					console.log("CustomHTML: Click outside detected, removing focus");
					setIsFocused(false);
				}
			}
		};

		// Only add listener if component is focused
		if (isFocused) {
			document.addEventListener("mousedown", handleClickOutside);
			return () => {
				document.removeEventListener("mousedown", handleClickOutside);
			};
		}
	}, [isFocused]);

	// Track when switching from preview to code mode
	useEffect(() => {
		if (!isPreview) {
			console.log(
				"CustomHTML: Switched to code mode, ready for user interaction"
			);
		}
	}, [isPreview]);

	// Sync scroll between textarea and line numbers
	const handleScroll = useCallback(() => {
		if (textareaRef.current && lineNumbersRef.current) {
			lineNumbersRef.current.scrollTop = textareaRef.current.scrollTop;
		}
	}, []);

	const handleHtmlChange = useCallback(
		(newHtml: string) => {
			setHtml(newHtml);
			updateAttributes({ html: newHtml });
			// User is typing, indicating interaction
		},
		[updateAttributes]
	);

	const handleCopy = useCallback(async () => {
		try {
			await navigator.clipboard.writeText(html);
			setCopied(true);
			setTimeout(() => setCopied(false), 2000);
		} catch (err) {
			console.error("Failed to copy HTML:", err);
		}
	}, [html]);

	const togglePreview = useCallback(() => {
		setIsPreview((prev) => {
			const newPreview = !prev;
			// If switching from preview to editor, resize after a short delay
			if (prev && !newPreview) {
				setTimeout(resizeTextarea, 50);
			}
			return newPreview;
		});
	}, [resizeTextarea]);

	// Focus management handlers
	const handleTextareaFocus = useCallback(() => {
		console.log("CustomHTML: Textarea received focus");
		setIsFocused(true);
	}, []);

	const handleTextareaBlur = useCallback((e: React.FocusEvent) => {
		console.log(
			"CustomHTML: Textarea lost focus, relatedTarget:",
			e.relatedTarget
		);
		setIsFocused(false);
	}, []);

	// Handle clicks on the code editor area to focus the textarea
	const handleCodeAreaClick = useCallback(
		(e: React.MouseEvent) => {
			// Only handle direct clicks on the code area, not on child elements
			if (!isPreview && textareaRef.current) {
				// Check if the click is actually within the textarea bounds
				const textarea = textareaRef.current;
				const rect = textarea.getBoundingClientRect();
				const clickX = e.clientX;
				const clickY = e.clientY;

				// Only proceed if click is within textarea bounds
				if (
					clickX >= rect.left &&
					clickX <= rect.right &&
					clickY >= rect.top &&
					clickY <= rect.bottom
				) {
					e.preventDefault();
					e.stopPropagation();

					console.log("CustomHTML: User clicked in textarea area, focusing");

					// Calculate click position for proper cursor placement
					const x = e.clientX - rect.left;
					const y = e.clientY - rect.top;

					// Focus first, then set cursor position
					textarea.focus();
					setIsFocused(true);

					// Use a small delay to ensure focus is complete before setting cursor position
					setTimeout(() => {
						if (textarea) {
							// Get computed styles for accurate measurements
							const computedStyle = window.getComputedStyle(textarea);
							const lineHeight = parseFloat(computedStyle.lineHeight) || 22.4;
							const fontSize = parseFloat(computedStyle.fontSize) || 14;
							const charWidth = fontSize * 0.6; // More accurate for monospace fonts
							const paddingTop = parseFloat(computedStyle.paddingTop) || 16;
							const paddingLeft = parseFloat(computedStyle.paddingLeft) || 16;

							const clickedLine = Math.max(
								0,
								Math.floor((y - paddingTop) / lineHeight)
							);
							const clickedChar = Math.max(
								0,
								Math.floor((x - paddingLeft) / charWidth)
							);

							const lines = textarea.value.split("\n");
							let position = 0;

							// Calculate character position more accurately
							for (
								let i = 0;
								i < Math.min(clickedLine, lines.length - 1);
								i++
							) {
								position += lines[i].length + 1; // +1 for newline
							}

							if (clickedLine < lines.length) {
								position += Math.min(clickedChar, lines[clickedLine].length);
							} else {
								// If clicked beyond last line, position at end
								position = textarea.value.length;
							}

							// Set cursor position
							textarea.setSelectionRange(position, position);
							console.log(
								`CustomHTML: Cursor positioned at character ${position} (line ${clickedLine + 1}, char ${clickedChar + 1})`
							);
						}
					}, 10);
				}
			}
		},
		[isPreview]
	);

	// Handle keydown events on the textarea specifically
	const handleTextareaKeyDown = useCallback(
		(e: React.KeyboardEvent<HTMLTextAreaElement>) => {
			// Stop propagation for all key events when textarea is focused
			// This prevents the editor from handling these events
			e.stopPropagation();
		},
		[]
	);

	// Handle keydown events on the component wrapper
	const handleWrapperKeyDown = useCallback(
		(e: React.KeyboardEvent) => {
			// Only stop propagation if the textarea is focused
			if (isFocused) {
				e.stopPropagation();
			}
		},
		[isFocused]
	);

	const sanitizedHtml = sanitizeHtml(html);
	const normalizedHtml = normalizeHtmlWhitespace(html);

	// Debug log
	useEffect(() => {
		if (isPreview && html) {
			const wasModified = html !== sanitizedHtml;
			console.log("CustomHtml Preview Debug:", {
				originalHtml: html,
				sanitizedHtml: sanitizedHtml,
				normalizedHtml: normalizedHtml,
				htmlLength: html.length,
				sanitizedLength: sanitizedHtml.length,
				normalizedLength: normalizedHtml.length,
				wasModifiedBySanitization: wasModified,
				containsScript: /<script/i.test(html),
				containsEventHandlers: /on\w+\s*=/i.test(html),
				startsWithWhitespace: /^\s/.test(normalizedHtml),
				endsWithWhitespace: /\s$/.test(normalizedHtml),
				isPreview: isPreview,
			});
		}
	}, [isPreview, html, sanitizedHtml, normalizedHtml]);

	return (
		<NodeViewWrapper
			className='custom-html-node'
			as='div'
			data-drag-handle=''
			contentEditable={false}
			onKeyDown={handleWrapperKeyDown}
		>
			<div
				className={`
        my-4 bg-gray-900 dark:bg-gray-950 rounded-lg overflow-hidden
        border border-gray-700 dark:border-gray-800
        ${selected ? "ring-2 ring-blue-400/50" : ""}
        ${isFocused ? "ring-2 ring-blue-500/70" : ""}
        transition-all duration-300 group relative
      `}
			>
				{/* Header */}
				<div className='flex items-center justify-between px-4 py-3 bg-gray-800 dark:bg-gray-900 border-b border-gray-700 dark:border-gray-800'>
					<div className='flex items-center space-x-2'>
						<Code className='w-4 h-4 text-orange-400' />
						<span className='text-sm font-medium text-gray-300'>
							Custom Html{" "}
							<span className='text-xs text-gray-400'>
								(&lt;scripts&gt; removed)
							</span>
						</span>
					</div>

					<div className='flex items-center space-x-2'>
						{/* Preview Toggle */}
						<Button
							variant='ghost'
							size='sm'
							onClick={togglePreview}
							className='h-7 px-3 text-xs text-gray-400 hover:text-white hover:bg-gray-700'
						>
							{isPreview ? (
								<>
									<EyeOff className='w-3 h-3 mr-1.5' />
									Code
								</>
							) : (
								<>
									<Eye className='w-3 h-3 mr-1.5' />
									Preview
								</>
							)}
						</Button>

						{/* Copy Button */}
						<Button
							variant='ghost'
							size='sm'
							onClick={handleCopy}
							className='h-7 px-3 text-xs text-gray-400 hover:text-white hover:bg-gray-700'
						>
							{copied ? (
								<>
									<Check className='w-3 h-3 mr-1.5' />
									Copied!
								</>
							) : (
								<>
									<Copy className='w-3 h-3 mr-1.5' />
									Copy
								</>
							)}
						</Button>
					</div>
				</div>

				{/* Content Area */}
				{isPreview ? (
					/* Preview Mode */
					<div className='bg-white dark:bg-gray-100 p-4 min-h-32'>
						{normalizedHtml.trim() ? (
							<div
								className='border border-gray-200 dark:border-gray-300 rounded p-2 bg-white dark:bg-white custom-html-preview'
								style={{
									whiteSpace: "normal",
									wordWrap: "break-word",
									overflowWrap: "break-word",
									marginTop: "0",
								}}
								dangerouslySetInnerHTML={{ __html: normalizedHtml }}
							/>
						) : (
							<div className='text-gray-500 dark:text-gray-600 italic text-sm'>
								No HTML content to preview. Add some HTML code to see the
								preview.
							</div>
						)}
					</div>
				) : (
					/* Code Editor Mode */
					<div
						className='relative bg-gray-900 dark:bg-gray-950 flex'
						onClick={handleCodeAreaClick}
					>
						{/* Line Numbers */}
						<div
							ref={lineNumbersRef}
							className='flex-shrink-0 bg-gray-800 dark:bg-gray-900 text-gray-500 text-sm font-mono select-none overflow-hidden'
							style={{
								fontFamily:
									"'Fira Code', 'JetBrains Mono', Consolas, monospace",
								lineHeight: "1.6",
								fontSize: "14px",
								padding: "16px 8px 16px 16px",
								borderRight: "1px solid #374151",
								minWidth: "50px",
								textAlign: "right",
							}}
						>
							{lineNumbers.map((num, index) => (
								<div key={index} style={{ height: "22.4px" }}>
									{num}
								</div>
							))}
						</div>

						{/* Code Area */}
						<div className='flex-1 relative'>
							{/* Highlighted code overlay */}
							<pre
								ref={preRef}
								className='absolute inset-0 bg-transparent text-sm font-mono pointer-events-none whitespace-pre-wrap break-words z-10 overflow-hidden'
								style={{
									fontFamily:
										"'Fira Code', 'JetBrains Mono', Consolas, monospace",
									lineHeight: "1.6",
									fontSize: "14px",
									color: "#f8f8f2",
									background: "transparent",
									margin: 0,
									border: "none",
									outline: "none",
									padding: "16px",
									boxSizing: "border-box",
								}}
								dangerouslySetInnerHTML={{ __html: highlightedCode }}
							/>

							{/* Transparent textarea for editing */}
							<textarea
								ref={textareaRef}
								value={html}
								onChange={(e) => handleHtmlChange(e.target.value)}
								onScroll={handleScroll}
								onFocus={handleTextareaFocus}
								onBlur={handleTextareaBlur}
								onKeyDown={handleTextareaKeyDown}
								placeholder='Enter your HTML and CSS code here...'
								className='relative w-full min-h-32 bg-transparent text-transparent font-mono text-sm resize-none outline-none border-none placeholder-gray-500 caret-white z-20 overflow-hidden'
								style={{
									fontFamily:
										"'Fira Code', 'JetBrains Mono', Consolas, monospace",
									lineHeight: "1.6",
									fontSize: "14px",
									tabSize: 2,
									background: "transparent",
									margin: 0,
									padding: "16px",
									boxSizing: "border-box",
									border: "none",
									outline: "none",
								}}
							/>
						</div>
					</div>
				)}
			</div>
		</NodeViewWrapper>
	);
};

// Tiptap Node Definition
export const CustomHtmlNode = Node.create({
	name: "customHtml",

	group: "block",

	content: "",

	marks: "",

	code: true,

	defining: true,

	atom: true,

	addAttributes() {
		return {
			html: {
				default: "",
				parseHTML: (element) =>
					element.getAttribute("data-html") || element.textContent || "",
				renderHTML: (attributes) => {
					if (!attributes.html) return {};
					return { "data-html": attributes.html };
				},
			},
		};
	},

	parseHTML() {
		return [
			{
				tag: "div[data-type='custom-html']",
				preserveWhitespace: "full",
				getAttrs: (element) => {
					return {
						html:
							element.getAttribute("data-html") || element.textContent || "",
					};
				},
			},
		];
	},

	renderHTML({ HTMLAttributes, node }) {
		const html = node?.attrs?.html || "";
		return [
			"div",
			mergeAttributes(HTMLAttributes, {
				"data-type": "custom-html",
				"data-html": html,
			}),
			html,
		];
	},

	// Commands temporarily removed due to type issues
	// addCommands() {
	// 	return {
	// 		setCustomHtml: (attributes: any) => ({ commands }: any) => {
	// 			return commands.setNode(this.name, attributes);
	// 		},
	// 		toggleCustomHtml: (attributes: any) => ({ commands }: any) => {
	// 			return commands.toggleNode(this.name, 'paragraph', attributes);
	// 		},
	// 	};
	// },

	// Keyboard shortcuts temporarily removed due to command dependency
	// addKeyboardShortcuts() {
	// 	return {
	// 		'Mod-Alt-h': () => this.editor.commands.toggleCustomHtml(),
	// 	};
	// },

	addNodeView() {
		return ReactNodeViewRenderer(CustomHtmlComponent);
	},
});

export default CustomHtmlNode;
