"use client";

import { useState, useEffect } from "react";
import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON><PERSON>le } from "@/components/ui/card";
import { Skeleton } from "@/components/ui/skeleton";
import { Button } from "@/components/ui/button";
import { getCountryNameByCode } from "@/utils/countries";
import {
	Chart as ChartJS,
	CategoryScale,
	LinearScale,
	PointElement,
	LineElement,
	Title,
	Tooltip as ChartTooltip,
	Legend,
	ChartOptions,
} from "chart.js";
import { Line } from "react-chartjs-2";

// Register Chart.js components
ChartJS.register(
	CategoryScale,
	LinearScale,
	PointElement,
	LineElement,
	Title,
	ChartTooltip,
	Legend
);

interface TimeseriesData {
	success: boolean;
	siteTag: string;
	dateRange: { since: string; until: string };
	groupBy?: string;
	data: {
		visits?: Array<{
			sum: { visits: number };
			dimensions: { ts: string; metric?: string };
		}>;
		pageviews?: Array<{
			count: number;
			dimensions: { ts: string; metric?: string };
		}>;
		series?: Array<{
			count: number;
			sum: { visits: number };
			dimensions: { ts: string; metric: string };
		}>;
	};
}

interface WebAnalyticsSparklineResponse {
	success: boolean;
	siteTag: string;
	dateRange: { since: string; until: string };
	data: {
		visits?: Array<{
			sum: { visits: number };
			dimensions: { ts: string };
		}>;
		pageviews?: Array<{
			count: number;
			dimensions: { ts: string };
		}>;
	};
}

interface WebAnalyticsTimeseriesResponse {
	success: boolean;
	siteTag: string;
	dateRange: { since: string; until: string };
	groupBy?: string;
	data: {
		series?: Array<{
			count: number;
			sum: { visits: number };
			dimensions: { ts: string; metric: string };
		}>;
	};
}

// Chart visualization options
const CHART_VIEWS = [
	{ key: "all", label: "All", dimension: null },
	{ key: "countries", label: "Countries", dimension: "countryName" },
	{ key: "sources", label: "Sources", dimension: "refererHost" },
	{ key: "paths", label: "Paths", dimension: "requestPath" },
	{ key: "browsers", label: "Browsers", dimension: "userAgentBrowser" },
	{ key: "os", label: "OS", dimension: "userAgentOS" },
	{ key: "devices", label: "Devices", dimension: "deviceType" },
];

interface VisitsSummaryChartProps {
	sparklineData: WebAnalyticsSparklineResponse;
	timeseriesData: { [key: string]: WebAnalyticsTimeseriesResponse };
}

export function VisitsSummaryChart({
	sparklineData,
	timeseriesData,
}: VisitsSummaryChartProps) {
	const [timeseriesResult, setTimeseriesResult] =
		useState<TimeseriesData | null>(null);
	const [selectedChartView, setSelectedChartView] = useState<string>("all");
	const [loading, setLoading] = useState(false);
	const [error, setError] = useState<string | null>(null);

	// Transform data instead of fetching
	const transformData = (dimension: string | null) => {
		try {
			setLoading(true);
			setError(null);

			if (dimension === null || dimension === "all") {
				// Use sparkline data for "All" view
				const transformedData: TimeseriesData = {
					success: true,
					siteTag: sparklineData.siteTag,
					dateRange: sparklineData.dateRange,
					data: {
						visits: sparklineData.data.visits,
						pageviews: sparklineData.data.pageviews,
					},
				};
				setTimeseriesResult(transformedData);
			} else {
				// Use timeseries data for specific dimensions
				const dimensionData = timeseriesData[dimension];
				if (dimensionData) {
					const transformedData: TimeseriesData = {
						success: true,
						siteTag: dimensionData.siteTag,
						dateRange: dimensionData.dateRange,
						groupBy: dimensionData.groupBy,
						data: {
							series: dimensionData.data.series,
						},
					};
					setTimeseriesResult(transformedData);
				} else {
					setError(`Data not available for dimension: ${dimension}`);
				}
			}
		} catch (err) {
			console.error("Error transforming data:", err);
			setError(err instanceof Error ? err.message : "Failed to transform data");
		} finally {
			setLoading(false);
		}
	};

	useEffect(() => {
		transformData(null);
	}, [sparklineData, timeseriesData]);

	const handleChartViewChange = (viewKey: string) => {
		setSelectedChartView(viewKey);

		const view = CHART_VIEWS.find((v) => v.key === viewKey);
		if (view) {
			transformData(view.dimension);
		}
	};

	// Prepare chart data
	const prepareChartData = () => {
		if (!timeseriesResult) return null;

		const { data } = timeseriesResult;

		if (selectedChartView === "all") {
			// Show aggregated visits and pageviews over time
			if (!data.visits || !data.pageviews) return null;

			const timestamps = Array.from(
				new Set([
					...data.visits.map((v) => v.dimensions.ts),
					...data.pageviews.map((p) => p.dimensions.ts),
				])
			).sort();

			const visitsMap = new Map(
				data.visits.map((v) => [v.dimensions.ts, v.sum.visits])
			);
			const pageviewsMap = new Map(
				data.pageviews.map((p) => [p.dimensions.ts, p.count])
			);

			return {
				labels: timestamps.map((ts, index) => {
					const date = new Date(ts);
					const dayName = date.toLocaleDateString("en-US", {
						weekday: "short",
					});
					const day = date.getDate();
					const hour = date.getHours();
					const minute = date.getMinutes().toString().padStart(2, "0");

					// Show day name + date for first occurrence of each day or every 6th item
					const isFirstOfDay =
						index === 0 || new Date(timestamps[index - 1]).getDate() !== day;
					const showDayLabel = isFirstOfDay || index % 6 === 0;

					if (showDayLabel) {
						return `${dayName} ${day}`;
					} else {
						return `${hour}:${minute}`;
					}
				}),
				datasets: [
					{
						label: "Visits",
						data: timestamps.map((ts) => visitsMap.get(ts) || 0),
						borderColor: "rgb(59, 130, 246)",
						backgroundColor: "rgba(59, 130, 246, 0.1)",
						tension: 0.4,
					},
					{
						label: "Page Views",
						data: timestamps.map((ts) => pageviewsMap.get(ts) || 0),
						borderColor: "rgb(16, 185, 129)",
						backgroundColor: "rgba(16, 185, 129, 0.1)",
						tension: 0.4,
					},
				],
			};
		} else {
			// Show grouped data by dimension
			if (!data.series) return null;

			// Group by metric and time
			const metricMap = new Map<string, Map<string, number>>();
			const allTimestamps = new Set<string>();

			data.series.forEach((item) => {
				const metric = item.dimensions.metric;
				const ts = item.dimensions.ts;
				const visits = item.sum.visits;

				allTimestamps.add(ts);

				if (!metricMap.has(metric)) {
					metricMap.set(metric, new Map());
				}
				metricMap.get(metric)!.set(ts, visits);
			});

			const timestamps = Array.from(allTimestamps).sort();
			const metrics = Array.from(metricMap.keys()).slice(0, 10); // Top 10 metrics

			const colors = [
				"rgb(59, 130, 246)",
				"rgb(16, 185, 129)",
				"rgb(245, 158, 11)",
				"rgb(239, 68, 68)",
				"rgb(139, 92, 246)",
				"rgb(236, 72, 153)",
				"rgb(6, 182, 212)",
				"rgb(34, 197, 94)",
				"rgb(251, 146, 60)",
				"rgb(168, 85, 247)",
			];

			return {
				labels: timestamps.map((ts, index) => {
					const date = new Date(ts);
					const dayName = date.toLocaleDateString("en-US", {
						weekday: "short",
					});
					const day = date.getDate();
					const hour = date.getHours();
					const minute = date.getMinutes().toString().padStart(2, "0");

					// Show day name + date for first occurrence of each day or every 6th item
					const isFirstOfDay =
						index === 0 || new Date(timestamps[index - 1]).getDate() !== day;
					const showDayLabel = isFirstOfDay || index % 6 === 0;

					if (showDayLabel) {
						return `${dayName} ${day}`;
					} else {
						return `${hour}:${minute}`;
					}
				}),
				datasets: metrics.map((metric, index) => {
					// Use country code for countries view, otherwise use metric as is
					const displayLabel = metric === "" ? "Direct" : metric;

					return {
						label: displayLabel,
						data: timestamps.map((ts) => metricMap.get(metric)?.get(ts) || 0),
						borderColor: colors[index % colors.length],
						backgroundColor: colors[index % colors.length]
							.replace("rgb", "rgba")
							.replace(")", ", 0.1)"),
						tension: 0.4,
					};
				}),
			};
		}
	};

	const chartData = prepareChartData();

	const options: ChartOptions<"line"> = {
		responsive: true,
		maintainAspectRatio: false,
		plugins: {
			legend: {
				position: "top" as const,
				labels: {
					usePointStyle: true,
					padding: 20,
					font: {
						size: 12,
					},
				},
			},
			tooltip: {
				callbacks: {
					// Customize tooltip to show country names
					label: (context) => {
						let label = context.dataset.label || "";

						if (
							selectedChartView === "countries" &&
							label &&
							label !== "Direct"
						) {
							const countryName = getCountryNameByCode(label);
							if (countryName) {
								label = `${countryName} (${label})`;
							}
						}

						if (label) {
							label += ": ";
						}
						if (context.parsed.y !== null) {
							label += context.parsed.y + " visits";
						}
						return label;
					},
				},
			},
			title: {
				display: false,
			},
		},
		scales: {
			x: {
				grid: {
					color: "rgba(0, 0, 0, 0.1)",
				},
				ticks: {
					font: {
						size: 11,
					},
					maxRotation: 45,
					minRotation: 0,
				},
			},
			y: {
				beginAtZero: true,
				grid: {
					color: "rgba(0, 0, 0, 0.1)",
				},
				ticks: {
					font: {
						size: 11,
					},
				},
			},
		},
		elements: {
			point: {
				radius: 3,
				hoverRadius: 6,
			},
			line: {
				borderWidth: 2,
			},
		},
		layout: {
			padding: {
				top: 5,
				right: 5,
				bottom: 5,
				left: 5,
			},
		},
	};

	return (
		<Card className='w-full'>
			<CardHeader className='px-4 sm:px-6 pt-4 sm:pt-6 pb-4'>
				<CardTitle className='text-lg sm:text-xl font-semibold mb-4'>
					Visits Summary -{" "}
					{CHART_VIEWS.find((v) => v.key === selectedChartView)?.label || "All"}
				</CardTitle>
				<div className='flex flex-wrap gap-2'>
					{CHART_VIEWS.map((view) => (
						<Button
							key={view.key}
							variant={selectedChartView === view.key ? "blue" : "outline"}
							size='sm'
							onClick={() => handleChartViewChange(view.key)}
							disabled={loading}
							className='text-xs'
						>
							{view.label}
						</Button>
					))}
				</div>
			</CardHeader>
			<CardContent className='px-2 sm:px-4 pb-2 sm:pb-4'>
				{loading && (
					<div className='w-full'>
						{/* Chart Title Skeleton */}
						<div className='mb-4'>
							<Skeleton className='h-4 w-32 mb-2' />
						</div>

						{/* Chart Area Skeleton */}
						<div className='h-64 sm:h-80 lg:h-96 flex flex-col justify-end space-y-2'>
							{/* Y-axis labels */}
							<div className='flex justify-between items-end h-full'>
								{Array.from({ length: 12 }).map((_, i) => (
									<div key={i} className='flex flex-col items-center space-y-1'>
										<Skeleton
											className='w-1 bg-gray-300'
											style={{ height: `${Math.random() * 60 + 20}%` }}
										/>
										<Skeleton className='h-2 w-8' />
									</div>
								))}
							</div>

							{/* X-axis */}
							<div className='flex justify-between'>
								{Array.from({ length: 7 }).map((_, i) => (
									<Skeleton key={i} className='h-3 w-12' />
								))}
							</div>
						</div>
					</div>
				)}

				{error && (
					<div className='flex items-center justify-center h-64 sm:h-80 lg:h-96'>
						<div className='text-red-500'>Error: {error}</div>
					</div>
				)}

				{!loading && !error && chartData && (
					<div className='h-64 sm:h-80 lg:h-96 w-full'>
						<Line data={chartData} options={options} />
					</div>
				)}

				{!loading && !error && !chartData && (
					<div className='flex items-center justify-center h-64 sm:h-80 lg:h-96'>
						<div className='text-gray-500'>No data available to display</div>
					</div>
				)}
			</CardContent>
		</Card>
	);
}

export function VisitsSummaryChartSkeleton() {
	return (
		<Card className='w-full'>
			<CardHeader className='px-4 sm:px-6 pt-4 sm:pt-6 pb-4'>
				<div className='flex items-center justify-between mb-4'>
					<Skeleton className='h-6 w-48' />
				</div>
				<div className='flex flex-wrap gap-2'>
					{Array.from({ length: 7 }).map((_, i) => (
						<Skeleton key={i} className='h-8 w-20 rounded-md' />
					))}
				</div>
			</CardHeader>
			<CardContent className='px-2 sm:px-4 pb-2 sm:pb-4'>
				<div className='w-full'>
					{/* Chart Area Skeleton */}
					<div className='h-64 sm:h-80 lg:h-96 flex flex-col justify-end space-y-2'>
						{/* Chart bars */}
						<div className='flex justify-between items-end h-full'>
							{Array.from({ length: 24 }).map((_, i) => (
								<div key={i} className='flex flex-col items-center space-y-1'>
									<Skeleton
										className='w-1 bg-gray-300'
										style={{ height: `${Math.random() * 60 + 20}%` }}
									/>
									<Skeleton className='h-2 w-6' />
								</div>
							))}
						</div>

						{/* X-axis labels */}
						<div className='flex justify-between pt-2'>
							{Array.from({ length: 8 }).map((_, i) => (
								<Skeleton key={i} className='h-3 w-12' />
							))}
						</div>
					</div>
				</div>
			</CardContent>
		</Card>
	);
}
