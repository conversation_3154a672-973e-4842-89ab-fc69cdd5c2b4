/* Rich Text Input Component Styles */

.rich-text-input-container {
  position: relative;
  width: 100%;
}

.rich-text-input-container--compact {
  --toolbar-offset: -50px;
}

.rich-text-input-container--minimal {
  --toolbar-offset: -47px;
}

.rich-text-input-content {
  position: relative;
  width: 100%;
}

/* Base editor styles - replicating TiptapEditor.css */
.rich-text-input-content .ProseMirror {
  padding: 12px 16px;
  border: 2px solid #e5e7eb;
  border-radius: 8px;
  background: white;
  color: #1f2937;
  font-size: 16px;
  line-height: 1.7;
  min-height: 44px;
  max-height: 200px;
  overflow-y: auto;
  outline: none;
  transition: all 0.2s ease;
  resize: none;
  position: relative;
}

.rich-text-input-content .ProseMirror:focus {
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
  outline: none;
}

/* Compact variant */
.rich-text-input-container--compact .rich-text-input-content .ProseMirror {
  padding: 8px 12px;
  min-height: 36px;
  max-height: 120px;
  font-size: 14px;
  line-height: 1.6;
}

/* Minimal variant */
.rich-text-input-container--minimal .rich-text-input-content .ProseMirror {
  padding: 6px 10px;
  min-height: 32px;
  max-height: 32px;
  font-size: 13px;
  line-height: 1.4;
  overflow: hidden;
}

/* Error state */
.rich-text-editor--error {
  border-color: #ef4444 !important;
  box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.1) !important;
}

/* Dark mode */
.dark .rich-text-input-content .ProseMirror {
  background: #374151;
  border-color: #4b5563;
  color: #f9fafb;
}

.dark .rich-text-input-content .ProseMirror:focus {
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.2);
}

.dark .rich-text-editor--error {
  border-color: #ef4444 !important;
  box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.2) !important;
}

/* Paragraph styling - matching TiptapEditor */
.rich-text-input-content .ProseMirror p {
  margin-top: 0;
  margin-bottom: 1em;
  line-height: 1.6;
  min-height: 1.2rem;
}

.rich-text-input-content .ProseMirror p:last-child {
  margin-bottom: 0;
}

.rich-text-input-container--minimal .rich-text-input-content .ProseMirror p {
  margin-bottom: 0;
  min-height: 1rem;
}

/* Placeholder styling - matching TiptapEditor */
.rich-text-input-content .ProseMirror p.is-editor-empty:first-child::before {
  content: attr(data-placeholder);
  float: left;
  color: #adb5bd;
  pointer-events: none;
  height: 0;
  font-style: italic;
}

.dark
  .rich-text-input-content
  .ProseMirror
  p.is-editor-empty:first-child::before {
  color: #6b7280;
}

/* Ensure empty paragraphs have a minimum height to be selectable */
.rich-text-input-content .ProseMirror p:empty,
.rich-text-input-content .ProseMirror p.is-empty,
.rich-text-input-content .ProseMirror p:has(> br),
.rich-text-input-content .ProseMirror p[data-empty="true"] {
  min-height: 1rem;
}

/* Headings - matching TiptapEditor */
.rich-text-input-content .ProseMirror h1,
.rich-text-input-content .ProseMirror h2,
.rich-text-input-content .ProseMirror h3,
.rich-text-input-content .ProseMirror h4,
.rich-text-input-content .ProseMirror h5,
.rich-text-input-content .ProseMirror h6 {
  line-height: 1;
  margin-top: 1em;
  margin-bottom: 1em;
  font-weight: 800;
}

.rich-text-input-content .ProseMirror h1 {
  font-size: 2em;
}

.rich-text-input-content .ProseMirror h2 {
  font-size: 1.5em;
}

.rich-text-input-content .ProseMirror h3 {
  font-size: 1.25em;
}

.rich-text-input-content .ProseMirror h4 {
  font-size: 1.1em;
}

.rich-text-input-content .ProseMirror h5 {
  font-size: 1em;
}

.rich-text-input-content .ProseMirror h6 {
  font-size: 0.9em;
}

/* Compact variant headings */
.rich-text-input-container--compact .rich-text-input-content .ProseMirror h1 {
  font-size: 1.5em;
}

.rich-text-input-container--compact .rich-text-input-content .ProseMirror h2 {
  font-size: 1.25em;
}

.rich-text-input-container--compact .rich-text-input-content .ProseMirror h3 {
  font-size: 1.125em;
}

/* Lists - matching TiptapEditor */
.rich-text-input-content .ProseMirror ul,
.rich-text-input-content .ProseMirror ol {
  margin-left: 1em;
  padding-left: 1em;
  margin-bottom: 1em;
}

.rich-text-input-content .ProseMirror ul {
  list-style-type: disc;
}

.rich-text-input-content .ProseMirror ol {
  list-style-type: decimal;
}

.rich-text-input-content .ProseMirror li {
  margin-bottom: 1em;
}

.rich-text-input-content .ProseMirror li > p:last-child {
  margin-bottom: 0;
}

/* Blockquotes - matching TiptapEditor */
.rich-text-input-content .ProseMirror blockquote {
  padding-left: 1em;
  border-left: 2px solid #ccc;
  color: #666;
  margin-bottom: 1em;
}

.dark .rich-text-input-content .ProseMirror blockquote {
  border-left-color: #4b5563;
  color: #9ca3af;
}

/* Horizontal rules - matching TiptapEditor */
.rich-text-input-content .ProseMirror hr {
  border: none;
  border-top: 1px solid #ccc;
  margin: 1.5em 0;
}

.dark .rich-text-input-content .ProseMirror hr {
  border-top-color: #4b5563;
}

/* Code blocks - matching TiptapEditor exactly */
.rich-text-input-content .ProseMirror pre {
  background: #f4f4f4;
  color: #333;
  font-family: "Fira Code", "JetBrains Mono", Consolas, "Courier New", Courier,
    monospace;
  padding: 1em;
  border-radius: 0.5em;
  margin-bottom: 1em;
  white-space: pre-wrap;
  word-wrap: break-word;
  overflow-x: auto;
  line-height: 1.6;
  font-size: 0.9em;
}

.rich-text-input-content .ProseMirror pre code {
  font-family: inherit;
  background: none;
  color: inherit;
  padding: 0;
  font-size: 0.9em;
  border-radius: 0;
  border: none;
}

.dark .rich-text-input-content .ProseMirror pre {
  background: #1e293b;
  color: #f8fafc;
  border: 1px solid #334155;
}

/* Inline code - matching TiptapEditor */
.rich-text-input-content .ProseMirror code {
  background-color: #f6f7f8;
  padding: 0.2em 0.2em;
  border-radius: 7px;
  border: 1px solid #ccc;
  font-size: 0.9em;
  font-family: "Courier New", Courier, monospace;
}

.dark .rich-text-input-content .ProseMirror code {
  background-color: #374151;
  border-color: #4b5563;
  color: #f472b6;
}

/* Text formatting - matching TiptapEditor */
.rich-text-input-content .ProseMirror strong,
.rich-text-input-content .ProseMirror b {
  font-weight: bold;
}

.rich-text-input-content .ProseMirror em,
.rich-text-input-content .ProseMirror i {
  font-style: italic;
}

.rich-text-input-content .ProseMirror s {
  text-decoration: line-through;
}

.rich-text-input-content .ProseMirror u {
  text-decoration: underline;
}

/* Links - matching TiptapEditor exactly */
.rich-text-input-content .ProseMirror a,
.rich-text-input-content .ProseMirror .rich-text-link {
  color: #0b32f5;
  cursor: pointer;
  transition: color 0.2s ease;
  text-decoration: underline;
}

.rich-text-input-content .ProseMirror a:hover,
.rich-text-input-content .ProseMirror .rich-text-link:hover {
  color: #2563eb;
  text-decoration: underline;
}

.dark .rich-text-input-content .ProseMirror a,
.dark .rich-text-input-content .ProseMirror .rich-text-link {
  color: #60a5fa;
}

.dark .rich-text-input-content .ProseMirror a:hover,
.dark .rich-text-input-content .ProseMirror .rich-text-link:hover {
  color: #93c5fd;
}

/* Character counter */
.rich-text-input-counter {
  position: absolute;
  bottom: -20px;
  right: 0;
  font-size: 11px;
  line-height: 1;
}

.rich-text-input-container--compact .rich-text-input-counter {
  bottom: -18px;
}

.rich-text-input-container--minimal .rich-text-input-counter {
  bottom: -16px;
}

/* Toolbar styling - enhanced to match TiptapEditor */
.rich-text-input-toolbar {
  position: absolute;
  top: var(--toolbar-offset, -55px);
  left: 0;
  display: flex;
  gap: 2px;
  background: rgba(255, 255, 255, 0.98);
  border: 1px solid rgba(226, 232, 240, 0.8);
  border-radius: 12px;
  padding: 6px;
  box-shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.1),
    0 10px 10px -5px rgba(0, 0, 0, 0.04);
  backdrop-filter: blur(16px) saturate(180%);
  opacity: 0;
  visibility: hidden;
  transition: all 0.2s ease;
  z-index: 50;
}

.rich-text-input-toolbar--minimal {
  top: var(--toolbar-offset, -47px);
  padding: 4px;
  border-radius: 8px;
}

.dark .rich-text-input-toolbar {
  background: rgba(30, 41, 59, 0.98);
  border-color: rgba(71, 85, 105, 0.8);
  box-shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.3),
    0 10px 10px -5px rgba(0, 0, 0, 0.2);
}

.rich-text-input-container:focus-within .rich-text-input-toolbar {
  opacity: 1;
  visibility: visible;
}

/* Toolbar buttons - matching bubble menu style */
.toolbar-button {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  border: none;
  background: transparent;
  border-radius: 8px;
  cursor: pointer;
  color: #6b7280;
  transition: all 0.15s ease;
  font-size: 14px;
  font-weight: 500;
}

.toolbar-button--small {
  width: 28px;
  height: 28px;
  font-size: 12px;
}

.toolbar-button:hover:not(:disabled) {
  background: rgba(243, 244, 246, 0.8);
  color: #374151;
  transform: translateY(-1px);
}

.toolbar-button--active {
  background: #3b82f6 !important;
  color: white !important;
  box-shadow: 0 2px 4px rgba(59, 130, 246, 0.3);
}

.toolbar-button--active:hover {
  background: #2563eb !important;
  transform: translateY(-1px);
}

.toolbar-button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.dark .toolbar-button {
  color: #9ca3af;
}

.dark .toolbar-button:hover:not(:disabled) {
  background: rgba(71, 85, 105, 0.8);
  color: #e5e7eb;
}

.dark .toolbar-button--active {
  background: #3b82f6 !important;
  color: white !important;
}

/* Toolbar button icons */
.toolbar-button svg {
  transition: transform 0.15s ease;
}

.toolbar-button:hover:not(:disabled) svg {
  transform: scale(1.05);
}

.toolbar-button--active svg {
  transform: scale(1.1);
}

/* Toolbar divider */
.toolbar-divider {
  width: 1px;
  height: 20px;
  background: #e5e7eb;
  margin: 0 4px;
  align-self: center;
}

.dark .toolbar-divider {
  background: #4b5563;
}

/* Helper text */
.rich-text-input-helper {
  margin-top: 6px;
  font-size: 12px;
  color: #6b7280;
  line-height: 1.4;
}

.rich-text-input-helper--error {
  color: #ef4444;
}

.dark .rich-text-input-helper {
  color: #9ca3af;
}

.dark .rich-text-input-helper--error {
  color: #f87171;
}

/* Loading placeholder */
.rich-text-input-placeholder {
  padding: 12px 16px;
  border: 2px solid #e5e7eb;
  border-radius: 8px;
  background: white;
  min-height: 44px;
  display: flex;
  align-items: center;
}

.rich-text-input-placeholder--compact {
  padding: 8px 12px;
  min-height: 36px;
}

.rich-text-input-placeholder--minimal {
  padding: 6px 10px;
  min-height: 32px;
}

.dark .rich-text-input-placeholder {
  background: #374151;
  border-color: #4b5563;
}

/* Scrollbar styling - matching TiptapEditor */
.rich-text-input-content .ProseMirror::-webkit-scrollbar {
  width: 6px;
}

.rich-text-input-content .ProseMirror::-webkit-scrollbar-track {
  background: transparent;
}

.rich-text-input-content .ProseMirror::-webkit-scrollbar-thumb {
  background: rgba(148, 163, 184, 0.3);
  border-radius: 3px;
}

.rich-text-input-content .ProseMirror::-webkit-scrollbar-thumb:hover {
  background: rgba(148, 163, 184, 0.5);
}

.dark .rich-text-input-content .ProseMirror::-webkit-scrollbar-thumb {
  background: rgba(148, 163, 184, 0.3);
}

.dark .rich-text-input-content .ProseMirror::-webkit-scrollbar-thumb:hover {
  background: rgba(148, 163, 184, 0.5);
}

/* Code block scrollbar */
.rich-text-input-content .ProseMirror pre::-webkit-scrollbar {
  height: 6px;
}

.rich-text-input-content .ProseMirror pre::-webkit-scrollbar-track {
  background: transparent;
}

.rich-text-input-content .ProseMirror pre::-webkit-scrollbar-thumb {
  background: rgba(148, 163, 184, 0.3);
  border-radius: 3px;
}

.rich-text-input-content .ProseMirror pre::-webkit-scrollbar-thumb:hover {
  background: rgba(148, 163, 184, 0.5);
}

/* CodeBlockNode styles - matching TiptapEditor exactly */
.rich-text-input-content .code-block-node {
  animation: codeBlockSlideIn 0.3s ease-out;
}

.rich-text-input-content .code-block-node .ProseMirror-selectednode {
  outline: 2px solid #3b82f6;
  outline-offset: 2px;
  border-radius: 8px;
}

.rich-text-input-content .code-block-node .hljs {
  background: transparent !important;
  color: #f8fafc;
}

.rich-text-input-content .code-block-node pre,
.rich-text-input-content .code-block-node textarea {
  font-family: "Fira Code", "JetBrains Mono", Consolas, "Courier New", Courier,
    monospace !important;
}

.rich-text-input-content .code-block-node .relative.bg-gray-900 {
  background: #111827 !important;
}

.dark .rich-text-input-content .code-block-node .relative.bg-gray-950 {
  background: #030712 !important;
}

.rich-text-input-content .code-block-node {
  margin: 1.5rem 0;
}

.rich-text-input-content .code-block-node pre {
  margin: 0;
  padding: 0;
}

.rich-text-input-content .code-block-node .hljs-keyword,
.rich-text-input-content .code-block-node .hljs-selector-tag,
.rich-text-input-content .code-block-node .hljs-literal,
.rich-text-input-content .code-block-node .hljs-section,
.rich-text-input-content .code-block-node .hljs-link {
  color: #8b5cf6;
}

.rich-text-input-content .code-block-node .hljs-function .hljs-keyword {
  color: #ec4899;
}

.rich-text-input-content .code-block-node .hljs-subst {
  color: #f8fafc;
}

.rich-text-input-content .code-block-node .hljs-string,
.rich-text-input-content .code-block-node .hljs-title,
.rich-text-input-content .code-block-node .hljs-name,
.rich-text-input-content .code-block-node .hljs-type,
.rich-text-input-content .code-block-node .hljs-attribute,
.rich-text-input-content .code-block-node .hljs-symbol,
.rich-text-input-content .code-block-node .hljs-bullet,
.rich-text-input-content .code-block-node .hljs-addition,
.rich-text-input-content .code-block-node .hljs-variable,
.rich-text-input-content .code-block-node .hljs-template-tag,
.rich-text-input-content .code-block-node .hljs-template-variable {
  color: #10b981;
}

.rich-text-input-content .code-block-node .hljs-comment,
.rich-text-input-content .code-block-node .hljs-quote,
.rich-text-input-content .code-block-node .hljs-deletion,
.rich-text-input-content .code-block-node .hljs-meta {
  color: #6b7280;
}

.rich-text-input-content .code-block-node .hljs-keyword,
.rich-text-input-content .code-block-node .hljs-selector-tag,
.rich-text-input-content .code-block-node .hljs-literal,
.rich-text-input-content .code-block-node .hljs-title,
.rich-text-input-content .code-block-node .hljs-section,
.rich-text-input-content .code-block-node .hljs-doctag,
.rich-text-input-content .code-block-node .hljs-type,
.rich-text-input-content .code-block-node .hljs-name,
.rich-text-input-content .code-block-node .hljs-strong {
  font-weight: bold;
}

.rich-text-input-content .code-block-node .hljs-number {
  color: #f59e0b;
}

.rich-text-input-content .code-block-node .hljs-built_in,
.rich-text-input-content .code-block-node .hljs-builtin-name,
.rich-text-input-content .code-block-node .hljs-class .hljs-title {
  color: #06b6d4;
}

.rich-text-input-content .code-block-node .hljs-attr {
  color: #f59e0b;
}

.rich-text-input-content .code-block-node .hljs-emphasis {
  font-style: italic;
}

.rich-text-input-content .code-block-node .hljs-tag {
  color: #8b5cf6;
}

.rich-text-input-content .code-block-node .hljs-tag .hljs-name {
  color: #ec4899;
}

.rich-text-input-content .code-block-node .hljs-tag .hljs-attr {
  color: #10b981;
}

.rich-text-input-content .code-block-node textarea {
  font-family: "Fira Code", "JetBrains Mono", Consolas, "Courier New", Courier,
    monospace;
  font-size: 14px;
  line-height: 1.6;
  tab-size: 2;
  resize: none;
  border: none;
  outline: none;
  background: transparent;
  color: transparent;
  caret-color: white;
  white-space: pre;
  overflow-wrap: normal;
  overflow-x: auto;
}

.rich-text-input-content .code-block-node pre {
  font-family: "Fira Code", "JetBrains Mono", Consolas, "Courier New", Courier,
    monospace;
  font-size: 14px;
  line-height: 1.6;
  white-space: pre;
  overflow-wrap: normal;
  overflow-x: auto;
}

.rich-text-input-content .code-block-node textarea::-webkit-scrollbar {
  height: 8px;
}

.rich-text-input-content .code-block-node textarea::-webkit-scrollbar-track {
  background: transparent;
}

.rich-text-input-content .code-block-node textarea::-webkit-scrollbar-thumb {
  background: rgba(156, 163, 175, 0.3);
  border-radius: 4px;
}

.rich-text-input-content
  .code-block-node
  textarea::-webkit-scrollbar-thumb:hover {
  background: rgba(156, 163, 175, 0.5);
}

.rich-text-input-content .code-block-node .absolute.top-full {
  background: #1f2937;
  border: 1px solid #374151;
  border-radius: 6px;
}

.dark .rich-text-input-content .code-block-node {
  color: #f8fafc;
}

.rich-text-input-content .code-block-node .group:hover .opacity-0 {
  opacity: 100 !important;
}

@keyframes codeBlockSlideIn {
  from {
    opacity: 0;
    transform: translateY(-10px) scale(0.98);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

.rich-text-input-content .code-block-node > div {
  animation: codeBlockSlideIn 0.3s ease-out;
}

.rich-text-input-content .code-block-node textarea:focus {
  outline: none;
  border: none;
  box-shadow: none;
  background: transparent;
}

.rich-text-input-content .code-block-node .caret-white {
  caret-color: white;
}

@media (max-width: 768px) {
  .rich-text-input-content .code-block-node textarea {
    font-size: 13px;
  }

  .rich-text-input-content .code-block-node .px-4 {
    padding-left: 12px;
    padding-right: 12px;
  }
}

/* Selection styles */
.rich-text-input-content .ProseMirror ::selection {
  background: rgba(59, 130, 246, 0.2);
}

.dark .rich-text-input-content .ProseMirror ::selection {
  background: rgba(59, 130, 246, 0.3);
}

/* Enhanced focus states */
.rich-text-input-content .ProseMirror:focus-within {
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.dark .rich-text-input-content .ProseMirror:focus-within {
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.2);
}

/* Focus states for accessibility */
.toolbar-button:focus {
  outline: 2px solid #3b82f6;
  outline-offset: 1px;
}

.rich-text-input-content .ProseMirror:focus {
  outline: none;
}

/* Animation for toolbar appearance */
@keyframes toolbarSlideIn {
  from {
    opacity: 0;
    transform: translateY(-8px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

.rich-text-input-toolbar {
  animation: toolbarSlideIn 0.2s ease-out;
}

/* Responsive adjustments */
@media (max-width: 640px) {
  .rich-text-input-toolbar {
    position: fixed;
    top: auto;
    bottom: 20px;
    left: 50%;
    transform: translateX(-50%);
    box-shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.1);
    z-index: 100;
  }

  .rich-text-input-counter {
    bottom: -24px;
    right: 0;
  }

  .rich-text-input-content .ProseMirror {
    font-size: 14px;
  }

  .rich-text-input-container--compact .rich-text-input-content .ProseMirror {
    font-size: 13px;
  }

  .rich-text-input-container--minimal .rich-text-input-content .ProseMirror {
    font-size: 12px;
  }
}
