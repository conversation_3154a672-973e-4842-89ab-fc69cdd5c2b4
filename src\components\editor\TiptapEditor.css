/* src/components/editor/TiptapEditor.css */

/* Main editor container */
.ProseMirror {
  min-height: 100%;
  outline: none;
  padding: 2rem;
  line-height: 1.7;
  font-size: 16px;
  color: #1f2937;
  background: transparent;
  border: none;
  transition: all 0.2s ease;
  position: relative;
  scroll-behavior: smooth;
  /* Ensure tab focus stays within editor */
  tab-size: 2;
  -moz-tab-size: 2;
}

/* Melhorar a experiência de scroll */
.prose {
  scroll-behavior: smooth;
  scroll-padding-top: 2rem;
  scroll-padding-bottom: 2rem;
}

/* Container do editor com scroll suave */
.prose.dark\:prose-invert {
  scroll-behavior: smooth;
}

/* Garantir que toda a área do editor seja clic<PERSON> */
.ProseMirror {
  min-height: calc(100vh - 110px);
  cursor: text;
}

/* <PERSON><PERSON> clicável expandida para o container do editor */
.ProseMirror-focused {
  outline: none;
}

/* Garantir que cliques em áreas vazias focalizem o editor */
.prose {
  cursor: text;
}

.prose>div {
  cursor: text;
}

.ProseMirror:focus {
  outline: none;
  border: none;
  box-shadow: none;
}

.dark .ProseMirror {
  color: #f9fafb;
}

/* Estilos específicos para parágrafos dentro do editor */
.ProseMirror p {
  margin-top: 0;
  margin-bottom: 1em;
  line-height: 1.6;
}

/* Exemplo: Estilo para placeholders (requer a extensão Placeholder) */
.ProseMirror p.is-editor-empty:first-child::before {
  content: attr(data-placeholder);
  float: left;
  color: #adb5bd;
  /* Cor do placeholder */
  pointer-events: none;
  height: 0;
}

/* Ensure empty paragraphs have a minimum height to be selectable */
.ProseMirror p:empty,
.ProseMirror p.is-empty,
.ProseMirror p:has(> br),
.ProseMirror p[data-empty="true"] {
  min-height: 1rem;
  /* Adjust as needed */
}

/* Handle paragraphs that only contain non-breaking space */
.ProseMirror p {
  min-height: 1.2rem;
}

/* Ensure the editor has focus styles */
.ProseMirror:focus {
  outline: none;
}

/* Slash command menu */
[cmdk-root] {
  max-width: 280px;
  width: 320px;
  background: rgba(255, 255, 255, 0.98);
  border-radius: 20px;
  overflow: hidden;
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1),
    0 10px 10px -5px rgba(0, 0, 0, 0.04), 0 0 0 1px rgba(255, 255, 255, 0.2);
  border: 1px solid rgba(226, 232, 240, 0.8);
  z-index: 100;
  position: fixed !important;
  /* Use fixed instead of absolute for better control */
  backdrop-filter: blur(16px) saturate(180%);
  /* Subtle inner glow */
  background-image: linear-gradient(135deg,
      rgba(255, 255, 255, 0.1) 0%,
      transparent 50%),
    linear-gradient(225deg, rgba(59, 130, 246, 0.05) 0%, transparent 50%);

  /* Smart positioning to stay within viewport */
  max-height: min(400px, calc(100vh - 120px)) !important;
  transform-origin: top left;

  /* Ensure it doesn't go off-screen */
  --offset-x: 0px;
  --offset-y: 8px;

  /* Dynamic positioning that respects viewport bounds */
  transform: translateX(var(--offset-x)) translateY(var(--offset-y));

  /* Smooth animations */
  transition: transform 0.2s ease-out, opacity 0.2s ease-out;
  opacity: 1;

  /* Initial animation */
  animation: slideInFromTop 0.2s ease-out;
}

/* Ensure the menu appears smoothly */
[cmdk-root]:not([data-position]) {
  animation: slideInFromTop 0.2s ease-out;
}

/* When positioned at the bottom of viewport, flip upward */
[cmdk-root][data-position*="top"] {
  transform: translateX(var(--offset-x)) translateY(calc(-100% - var(--offset-y)));
  transform-origin: bottom left;
  animation: slideInFromBottom 0.2s ease-out;
}

/* Handle right edge positioning */
[cmdk-root][data-position*="left"] {
  transform: translateX(calc(-100% - 16px)) translateY(var(--offset-y));
  transform-origin: top right;
}

/* Combined positioning (top-left) */
[cmdk-root][data-position*="top"][data-position*="left"] {
  transform: translateX(calc(-100% - 16px)) translateY(calc(-100% - var(--offset-y)));
  transform-origin: bottom right;
  animation: slideInFromBottomRight 0.2s ease-out;
}

/* Position adjustments for viewport edges */
[cmdk-root][data-align="start"] {
  transform-origin: top left;
}

[cmdk-root][data-align="end"] {
  transform-origin: top right;
}

/* Animations for smooth appearance */
@keyframes slideInFromTop {
  from {
    opacity: 0;
    transform: translateY(-10px) scale(0.95);
  }

  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

@keyframes slideInFromBottom {
  from {
    opacity: 0;
    transform: translateY(10px) scale(0.95);
  }

  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

@keyframes slideInFromBottomRight {
  from {
    opacity: 0;
    transform: translateX(calc(-100% - 16px)) translateY(calc(-100% - var(--offset-y) + 10px)) scale(0.95);
  }

  to {
    opacity: 1;
    transform: translateX(calc(-100% - 16px)) translateY(calc(-100% - var(--offset-y))) scale(1);
  }
}

[cmdk-list] {
  max-height: 400px;
  overflow-y: auto;
  padding: 12px;
  margin: 0;
  list-style: none;
  /* Custom scrollbar */
  scrollbar-width: thin;
  scrollbar-color: rgba(148, 163, 184, 0.3) transparent;
}

[cmdk-list]::-webkit-scrollbar {
  width: 6px;
}

[cmdk-list]::-webkit-scrollbar-track {
  background: transparent;
}

[cmdk-list]::-webkit-scrollbar-thumb {
  background: rgba(148, 163, 184, 0.3);
  border-radius: 3px;
}

[cmdk-list]::-webkit-scrollbar-thumb:hover {
  background: rgba(148, 163, 184, 0.5);
}

[cmdk-item] {
  cursor: pointer;
  height: 40px;
  display: flex;
  align-items: center;
  padding: 0 14px;
  font-size: 14px;
  border-radius: 12px;
  margin: 3px 0;
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  font-weight: 500;
  background: transparent;
  border: 1px solid transparent;
}

[cmdk-item][data-selected="true"] {
  background: rgba(248, 250, 252, 0.8);
  color: #374151;
  transform: translateX(2px);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06),
    inset 0 1px 0 rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(226, 232, 240, 0.8);
}

[cmdk-item]:hover:not([data-selected="true"]) {
  background: rgba(248, 250, 252, 0.5);
  transform: translateX(1px);
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.04);
  border: 1px solid rgba(226, 232, 240, 0.4);
}

[cmdk-item] svg {
  width: 16px;
  height: 16px;
  flex-shrink: 0;
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
}

[cmdk-item][data-selected="true"] svg {
  transform: scale(1.1);
}

[cmdk-item]:hover svg {
  transform: scale(1.05);
}

[cmdk-item] .command-title {
  font-weight: 500;
  color: #4b5563;
  transition: all 0.2s ease;
  letter-spacing: -0.01em;
}

[cmdk-item][data-selected="true"] .command-title {
  color: #374151;
  font-weight: 600;
}

[cmdk-item]:hover .command-title {
  color: #374151;
}

[cmdk-input] {
  font-family: inherit;
  width: 100%;
  font-size: 14px;
  font-weight: 500;
  padding: 16px 20px;
  outline: none;
  border: none;
  border-bottom: 1px solid rgba(226, 232, 240, 0.8);
  margin: 0;
  background: linear-gradient(135deg,
      rgba(255, 255, 255, 0.1) 0%,
      transparent 100%);
  color: #1f2937;
  letter-spacing: -0.01em;
}

[cmdk-input]::placeholder {
  color: #9ca3af;
  font-weight: 400;
}

[cmdk-empty] {
  font-size: 14px;
  font-weight: 500;
  padding: 24px;
  text-align: center;
  color: #94a3b8;
  letter-spacing: -0.01em;
}

/* For dark mode */
.dark [cmdk-root] {
  background: rgba(15, 23, 42, 0.98);
  border-color: rgba(71, 85, 105, 0.8);
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.6),
    0 10px 10px -5px rgba(0, 0, 0, 0.3), 0 0 0 1px rgba(71, 85, 105, 0.3);
  background-image: linear-gradient(135deg,
      rgba(71, 85, 105, 0.1) 0%,
      transparent 50%),
    linear-gradient(225deg, rgba(59, 130, 246, 0.08) 0%, transparent 50%);
}

.dark [cmdk-item] {
  color: #9ca3af;
  background: transparent;
}

.dark [cmdk-item][data-selected="true"] {
  background: rgba(51, 65, 85, 0.6);
  color: #e2e8f0;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15),
    inset 0 1px 0 rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(71, 85, 105, 0.8);
}

.dark [cmdk-item]:hover:not([data-selected="true"]) {
  background: rgba(51, 65, 85, 0.3);
  border: 1px solid rgba(71, 85, 105, 0.4);
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.1);
}

.dark [cmdk-item] svg {
  color: #9ca3af;
}

.dark [cmdk-item][data-selected="true"] svg {
  color: #e2e8f0;
}

.dark [cmdk-item]:hover svg {
  color: #cbd5e1;
}

.dark [cmdk-item] .command-title {
  color: #9ca3af;
}

.dark [cmdk-item][data-selected="true"] .command-title {
  color: #e2e8f0;
}

.dark [cmdk-item]:hover .command-title {
  color: #cbd5e1;
}

.dark [cmdk-input] {
  color: #f1f5f9;
  border-color: rgba(71, 85, 105, 0.8);
  background: linear-gradient(135deg,
      rgba(71, 85, 105, 0.1) 0%,
      transparent 100%);
}

.dark [cmdk-input]::placeholder {
  color: #64748b;
}

.dark [cmdk-empty] {
  color: #64748b;
}

/* Additional fixes for proper rendering */
.slash-dropdown {
  position: relative;
}

/* Adicione mais regras CSS conforme necessário */

.ProseMirror h1,
.ProseMirror h2,
.ProseMirror h3,
.ProseMirror h4,
.ProseMirror h5,
.ProseMirror h6 {
  line-height: 1;
  margin-top: 1em;
  margin-bottom: 1em;
  font-weight: 800;
}

.ProseMirror h1 {
  font-size: 2em;
}

.ProseMirror h2 {
  font-size: 1.5em;
}

.ProseMirror h3 {
  font-size: 1.25em;
}

.ProseMirror h4 {
  font-size: 1.1em;
}

.ProseMirror h5 {
  font-size: 1em;
}

.ProseMirror h6 {
  font-size: 0.9em;
}

.ProseMirror ul,
.ProseMirror ol {
  margin-left: 1em;
  padding-left: 1em;
  /* Espaçamento antes do marcador */
  margin-bottom: 1em;
}

.ProseMirror ul {
  list-style-type: disc;
}

.ProseMirror ol {
  list-style-type: decimal;
}

.ProseMirror li {
  margin-bottom: 1em;
  position: relative;
}

/* Nested lists */
.ProseMirror li ul,
.ProseMirror li ol {
  margin-top: 0.5em;
  margin-bottom: 0.5em;
  margin-left: 1.5em;
}

/* Nested list items */
.ProseMirror li li {
  margin-bottom: 0.5em;
}

/* Focus styles for better Tab navigation */
.ProseMirror li:focus-within {
  outline: none;
}

.ProseMirror li>p {
  margin: 0;
}

/* Remove margem do último parágrafo dentro de um item de lista */
.ProseMirror li>p:last-child {
  margin-bottom: 0;
}

/* Citação / Blockquote */
.ProseMirror blockquote {
  padding-left: 1em;
  border-left: 2px solid #ccc;
  color: #666;
  margin-bottom: 1em;
}

/* Linha Horizontal */
.ProseMirror hr {
  border: none;
  border-top: 1px solid #ccc;
  margin: 1.5em 0;
}

/* Bloco de Código */
.ProseMirror pre {
  background: #f4f4f4;
  /* Cor de fundo */
  color: #333;
  /* Cor do texto */
  font-family: "Courier New", Courier, monospace;
  padding: 1em;
  border-radius: 0.5em;
  margin-bottom: 1em;
  white-space: pre-wrap;
  /* Quebra de linha */
  word-wrap: break-word;
}

.ProseMirror pre code {
  font-family: inherit;
  background: none;
  color: inherit;
  padding: 0;
  font-size: 0.9em;
}

/* Código Inline */
.ProseMirror code {
  background-color: #f6f7f8;
  padding: 0.2em 0.2em;
  border-radius: 7px;
  border: 1px solid #ccc;
  font-size: 0.9em;
  font-family: "Courier New", Courier, monospace;
}

/* Negrito */
.ProseMirror strong,
.ProseMirror b {
  font-weight: bold;
}

/* Itálico */
.ProseMirror em,
.ProseMirror i {
  font-style: italic;
}

/* Riscado (Strikethrough) */
.ProseMirror s {
  text-decoration: line-through;
}

/* Sublinhado */
.ProseMirror u {
  text-decoration: underline;
}

/* Links */

/*!
  Theme: atom-one-dark
  Author: (https://github.com/תומר)
  Description: Dark theme with Atom's vibrant colors
  Origin: https://github.com/highlightjs/highlight.js/blob/main/src/styles/atom-one-dark.css
*/

.hljs {
  color: #abb2bf;
  background: #282c34;
}

.hljs-comment,
.hljs-quote {
  color: #5c6370;
  font-style: italic;
}

.hljs-doctag,
.hljs-formula,
.hljs-keyword {
  color: #c678dd;
}

.hljs-deletion,
.hljs-name,
.hljs-section,
.hljs-selector-tag,
.hljs-subst {
  color: #e06c75;
}

.hljs-literal {
  color: #56b6c2;
}

.hljs-addition,
.hljs-attribute,
.hljs-meta .hljs-string,
.hljs-regexp,
.hljs-string {
  color: #98c379;
}

.hljs-attr,
.hljs-number,
.hljs-selector-attr,
.hljs-selector-class,
.hljs-selector-pseudo,
.hljs-template-variable,
.hljs-type,
.hljs-variable {
  color: #d19a66;
}

.hljs-bullet,
.hljs-link,
.hljs-meta,
.hljs-selector-id,
.h .ProseMirror a,
.ProseMirror .editor-link {
  color: #0b32f5;
  cursor: text !important;
  transition: color 0.2s ease;
  pointer-events: none !important;
  user-select: text;
  text-decoration: underline;
}

.ProseMirror a:hover,
.ProseMirror .editor-link:hover {
  color: #2563eb;
  text-decoration: underline;
  pointer-events: none !important;
}

.dark .ProseMirror a,
.dark .ProseMirror .editor-link {
  color: #60a5fa;
  pointer-events: none !important;
  cursor: text !important;
}

.dark .ProseMirror a:hover,
.dark .ProseMirror .editor-link:hover {
  color: #93c5fd;
  pointer-events: none !important;
}

/* Override any default link behavior globally */
.ProseMirror a {
  pointer-events: none !important;
  cursor: text !important;
  user-select: text;
}

.ProseMirror a:hover {
  pointer-events: none !important;
  cursor: text !important;
}

.ProseMirror a:focus {
  pointer-events: none !important;
  outline: none !important;
}

.ProseMirror a:active {
  pointer-events: none !important;
}

/* Floating menu */
.floating-menu {
  display: flex;
  background-color: var(--gray-3);
  padding: 0.1rem;
  border-radius: 0.5rem;

  button {
    background-color: unset;
    padding: 0.275rem 0.425rem;
    border-radius: 0.3rem;

    &:hover {
      background-color: var(--gray-3);
    }

    &.is-active {
      background-color: var(--white);
      color: var(--purple);

      &:hover {
        color: var(--purple-contrast);
      }
    }
  }
}

/* Bubble menu styles */
.bubble-menu {
  display: flex;
  align-items: center;
  background: rgba(255, 255, 255, 0.98);
  border: 1px solid rgba(226, 232, 240, 0.8);
  border-radius: 12px;
  padding: 6px;
  box-shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.1),
    0 10px 10px -5px rgba(0, 0, 0, 0.04);
  backdrop-filter: blur(16px) saturate(180%);
  z-index: 9999;
  gap: 2px;
}

.dark .bubble-menu {
  background: rgba(30, 41, 59, 0.98);
  border-color: rgba(71, 85, 105, 0.8);
  box-shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.3),
    0 10px 10px -5px rgba(0, 0, 0, 0.2);
}

.bubble-menu-button {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  border: none;
  background: transparent;
  border-radius: 8px;
  cursor: pointer;
  color: #6b7280;
  transition: all 0.15s ease;
  font-size: 14px;
  font-weight: 500;
}

.bubble-menu-button:hover {
  background: rgba(243, 244, 246, 0.8);
  color: #374151;
  transform: translateY(-1px);
}

.bubble-menu-button.active {
  background: #3b82f6;
  color: white;
  box-shadow: 0 2px 4px rgba(59, 130, 246, 0.3);
}

.bubble-menu-button.active:hover {
  background: #2563eb;
  transform: translateY(-1px);
}

.dark .bubble-menu-button {
  color: #9ca3af;
}

.dark .bubble-menu-button:hover {
  background: rgba(71, 85, 105, 0.8);
  color: #e5e7eb;
}

.dark .bubble-menu-button.active {
  background: #3b82f6;
  color: white;
}

.bubble-menu-divider {
  width: 1px;
  height: 20px;
  background: #e5e7eb;
  margin: 0 4px;
}

.dark .bubble-menu-divider {
  background: #4b5563;
}

/* Animation for bubble menu appearance */
@keyframes bubbleMenuSlideIn {
  from {
    opacity: 0;
    transform: translateY(-8px) scale(0.95);
  }

  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

.bubble-menu {
  animation: bubbleMenuSlideIn 0.2s ease-out;
}

/* Card node link styles - ensure links maintain color outside edit mode */
.card-node a,
.card-node .editor-link,
.card-node .rich-text-link {
  color: #0b32f5 !important;
  text-decoration: underline !important;
  transition: color 0.2s ease;
}

.card-node a:hover,
.card-node .editor-link:hover,
.card-node .rich-text-link:hover {
  color: #2563eb !important;
}

.dark .card-node a,
.dark .card-node .editor-link,
.dark .card-node .rich-text-link {
  color: #60a5fa !important;
}

.dark .card-node a:hover,
.dark .card-node .editor-link:hover,
.dark .card-node .rich-text-link:hover {
  color: #93c5fd !important;
}

/* Ensure prose-sm doesn't override link colors in cards */
.card-node .prose-sm a,
.card-node .prose-sm .editor-link,
.card-node .prose-sm .rich-text-link {
  color: #0b32f5 !important;
  text-decoration: underline !important;
}

.dark .card-node .prose-sm a,
.dark .card-node .prose-sm .editor-link,
.dark .card-node .prose-sm .rich-text-link {
  color: #60a5fa !important;
}

/* Card list node specific styles */
.card-list-node .prose-sm a,
.card-list-node .prose-sm .editor-link,
.card-list-node .prose-sm .rich-text-link {
  color: #0b32f5 !important;
  text-decoration: underline !important;
}

.dark .card-list-node .prose-sm a,
.dark .card-list-node .prose-sm .editor-link,
.dark .card-list-node .prose-sm .rich-text-link {
  color: #60a5fa !important;
}

/* Accordion node specific styles */
.accordion-node .prose-sm a,
.accordion-node .prose-sm .editor-link,
.accordion-node .prose-sm .rich-text-link {
  color: #0b32f5 !important;
  text-decoration: underline !important;
}

.dark .accordion-node .prose-sm a,
.dark .accordion-node .prose-sm .editor-link,
.dark .accordion-node .prose-sm .rich-text-link {
  color: #60a5fa !important;
}

/* Accordion group node specific styles */
.accordion-group-node .prose-sm a,
.accordion-group-node .prose-sm .editor-link,
.accordion-group-node .prose-sm .rich-text-link,
.accordion-content-wrapper a,
.accordion-content-wrapper .editor-link,
.accordion-content-wrapper .rich-text-link {
  color: #0b32f5 !important;
  text-decoration: underline !important;
}

.dark .accordion-group-node .prose-sm a,
.dark .accordion-group-node .prose-sm .editor-link,
.dark .accordion-group-node .prose-sm .rich-text-link,
.dark .accordion-content-wrapper a,
.dark .accordion-content-wrapper .editor-link,
.dark .accordion-content-wrapper .rich-text-link {
  color: #60a5fa !important;
}

/* Tabs Node link styles */
.tabs-node .prose-sm a,
.tabs-node .prose-sm .editor-link,
.tabs-node .prose-sm .rich-text-link,
.tabs-content-wrapper a,
.tabs-content-wrapper .editor-link,
.tabs-content-wrapper .rich-text-link {
  color: #0b32f5 !important;
  text-decoration: underline !important;
}

.dark .tabs-node .prose-sm a,
.dark .tabs-node .prose-sm .editor-link,
.dark .tabs-node .prose-sm .rich-text-link,
.dark .tabs-content-wrapper a,
.dark .tabs-content-wrapper .editor-link,
.dark .tabs-content-wrapper .rich-text-link {
  color: #60a5fa !important;
}

/* Steps Node link styles */
.steps-node .prose-sm a,
.steps-node .prose-sm .editor-link,
.steps-node .prose-sm .rich-text-link,
.steps-node-container a,
.steps-node-container .editor-link,
.steps-node-container .rich-text-link {
  color: #0b32f5 !important;
  text-decoration: underline !important;
}

.dark .steps-node .prose-sm a,
.dark .steps-node .prose-sm .editor-link,
.dark .steps-node .prose-sm .rich-text-link,
.dark .steps-node-container a,
.dark .steps-node-container .editor-link,
.dark .steps-node-container .rich-text-link {
  color: #60a5fa !important;
}

/* CalloutNode Link Styles */
.callout-node .prose-sm a,
.callout-node div a,
.callout-node p a,
.callout-node span a {
  color: #3b82f6 !important; /* Blue-500 */
  cursor: pointer !important;
  text-decoration: underline !important;
  pointer-events: auto !important;
}

.dark .callout-node .prose-sm a,
.dark .callout-node div a,
.dark .callout-node p a,
.dark .callout-node span a {
  color: #60a5fa !important; /* Blue-400 */
}

.callout-node .prose-sm a:hover,
.callout-node div a:hover,
.callout-node p a:hover,
.callout-node span a:hover {
  color: #2563eb !important; /* Blue-600 */
}

.dark .callout-node .prose-sm a:hover,
.dark .callout-node div a:hover,
.dark .callout-node p a:hover,
.dark .callout-node span a:hover {
  color: #3b82f6 !important; /* Blue-500 */
}

/* CalloutNode Code Block Link Styles */
.callout-node pre a,
.callout-node pre code a,
.callout-node code a {
  color: #3b82f6 !important; /* Blue-500 */
  cursor: pointer !important;
  text-decoration: underline !important;
  pointer-events: auto !important;
}

.dark .callout-node pre a,
.dark .callout-node pre code a,
.dark .callout-node code a {
  color: #60a5fa !important; /* Blue-400 */
}

.callout-node pre a:hover,
.callout-node pre code a:hover,
.callout-node code a:hover {
  color: #2563eb !important; /* Blue-600 */
}

.dark .callout-node pre a:hover,
.dark .callout-node pre code a:hover,
.dark .callout-node code a:hover {
  color: #3b82f6 !important; /* Blue-500 */
}

/* Metadata node styles */
.metadata-node {
  user-select: none;
  position: relative;
}

.metadata-node .line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

/* Garantir que o nó de metadados não pode ser deletado facilmente */
.metadata-node .ProseMirror-selectednode {
  outline: 2px solid #3b82f6 !important;
  outline-offset: 2px;
  border-radius: 12px;
}

/* Estilo para quando o nó de metadados está sendo arrastado */
.metadata-node.ProseMirror-selectednode {
  outline: 2px dashed #3b82f6 !important;
  outline-offset: 2px;
  border-radius: 12px;
  opacity: 0.8;
}

/* Impedir interações de arrasto no nó de metadados */
.metadata-node[data-drag-handle] {
  cursor: default !important;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
}

/* Adicionar um efeito visual quando o usuário tenta selecionar para deletar */
.metadata-node:hover::before {
  content: "";
  position: absolute;
  top: -2px;
  left: -2px;
  right: -2px;
  bottom: -2px;
  background: linear-gradient(45deg,
      transparent 30%,
      rgba(59, 130, 246, 0.1) 50%,
      transparent 70%);
  border-radius: 14px;
  pointer-events: none;
  z-index: -1;
}

/* Estilo para o badge de proteção */
.metadata-node .protection-badge {
  animation: pulse-protection 2s infinite;
}

@keyframes pulse-protection {

  0%,
  100% {
    opacity: 1;
    transform: scale(1);
  }

  50% {
    opacity: 0.8;
    transform: scale(1.05);
  }
}

/* Garantir que nenhum texto dentro do nó de metadados seja selecionável */
.metadata-node * {
  user-select: none !important;
  -webkit-user-select: none !important;
  -moz-user-select: none !important;
  -ms-user-select: none !important;
}

/* Permitir seleção apenas nos inputs quando em modo de edição */
.metadata-node input,
.metadata-node textarea {
  user-select: text !important;
  -webkit-user-select: text !important;
  -moz-user-select: text !important;
  -ms-user-select: text !important;
}

/* Remove margin and padding from paragraphs inside cards */
.ProseMirror .card-node p,
.ProseMirror .card-list-node p {
  margin: 0 !important;
  padding: 0 !important;
}

/* Accordion styles */
.ProseMirror .accordion-node {
  margin: 1rem 0;
}

.ProseMirror .accordion-node p {
  margin: 0;
  padding: 0;
}

/* Accordion Group styles */
.ProseMirror .accordion-group-node {
  margin: 1rem 0;
}

.ProseMirror .accordion-group-node p {
  margin: 0;
  padding: 0;
}

/* Accordion animation */
.accordion-content {
  transition: all 0.3s ease;
  overflow: hidden;
}

.accordion-content.expanded {
  max-height: 1000px;
}

.accordion-content.collapsed {
  max-height: 0;
}

/* Callout Node Styles */
.callout-node {
  user-select: none;
  position: relative;
}

.callout-node .ProseMirror-selectednode {
  outline: 2px solid #3b82f6 !important;
  outline-offset: 2px;
  border-radius: 12px;
}

/* Callout type-specific styling */
.callout-node .bg-blue-50 {
  background-color: #eff6ff;
}

.dark .callout-node .dark\:bg-blue-950\/30 {
  background-color: rgba(23, 37, 84, 0.3);
}

.callout-node .bg-green-50 {
  background-color: #f0fdf4;
}

.dark .callout-node .dark\:bg-green-950\/30 {
  background-color: rgba(20, 83, 45, 0.3);
}

.callout-node .bg-yellow-50 {
  background-color: #fefce8;
}

.dark .callout-node .dark\:bg-yellow-950\/30 {
  background-color: rgba(66, 32, 6, 0.3);
}

.callout-node .bg-red-50 {
  background-color: #fef2f2;
}

.dark .callout-node .dark\:bg-red-950\/30 {
  background-color: rgba(69, 10, 10, 0.3);
}

.callout-node .bg-gray-50 {
  background-color: #f9fafb;
}

.dark .callout-node .dark\:bg-gray-950\/30 {
  background-color: rgba(9, 9, 11, 0.3);
}

.callout-node .bg-amber-50 {
  background-color: #fffbeb;
}

.dark .callout-node .dark\:bg-amber-950\/30 {
  background-color: rgba(69, 26, 3, 0.3);
}

.callout-node .bg-purple-50 {
  background-color: #faf5ff;
}

.dark .callout-node .dark\:bg-purple-950\/30 {
  background-color: rgba(59, 7, 100, 0.3);
}

/* Callout animation */
@keyframes calloutSlideIn {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }

  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.callout-node>div {
  animation: calloutSlideIn 0.3s ease-out;
}

/* Table Wrapper Styles */
.table-wrapper {
  position: relative;
  margin: 1.5em 0;
}

/* Table Styles */
.ProseMirror table {
  border-collapse: collapse;
  margin: 0;
  overflow: hidden;
  table-layout: auto;
  width: 100%;
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
  border: 1px solid #e5e7eb;
  background: #ffffff;
  resize: none !important;
}

.ProseMirror table td,
.ProseMirror table th {
  border: 1px solid #e5e7eb;
  box-sizing: border-box;
  min-width: 120px;
  padding: 12px 16px;
  position: relative;
  vertical-align: top;
  text-align: left;
  line-height: 1.5;
  font-size: 14px;
  transition: all 0.2s ease;
  resize: none !important;
  width: auto;
}

/* Force table to use available space and distribute columns equally */
.ProseMirror table {
  table-layout: fixed !important;
  width: 100% !important;
}

.ProseMirror table td,
.ProseMirror table th {
  max-width: none;
  word-wrap: break-word;
  overflow-wrap: break-word;
  width: 1% !important; /* This forces equal distribution */
}

.ProseMirror table th {
  background-color: #ffffff;
  font-weight: 600;
  color: #020303;
  border-bottom: 2px solid #d1d5db;
  font-size: 12px;
  letter-spacing: 0.05em;
  font-weight: bold;
  text-align: center;
}

.ProseMirror table td {
  background-color: #f7f7f7;
  color: #000000;
}

/* Zebra striping - even rows (linhas pares) */
.ProseMirror table tr:nth-child(odd) td {
  background-color: #ffffff;
}

.ProseMirror table td:hover,
.ProseMirror table th:hover {
  background-color: #f3f4f6;
}

/* Maintain zebra striping on hover for even rows */
.ProseMirror table tr:nth-child(even) td:hover {
  background-color: #eeeeee;
}

/* Focused cell */
.ProseMirror table .selectedCell {
  background-color: #dbeafe !important;
  border-color: #3b82f6 !important;
  outline: 2px solid #3b82f6;
  outline-offset: -1px;
}

/* Table with header row */
.ProseMirror table tr:first-child th {
  border-top: none;
}

.ProseMirror table tr:first-child td,
.ProseMirror table tr:first-child th {
  border-top: none;
}

.ProseMirror table tr td:first-child,
.ProseMirror table tr th:first-child {
  border-left: none;
}

.ProseMirror table tr td:last-child,
.ProseMirror table tr th:last-child {
  border-right: none;
}

.ProseMirror table tr:last-child td {
  border-bottom: none;
}

/* Column resize handle - DISABLED */
.ProseMirror .column-resize-handle {
  display: none !important;
  /* Completely hide resize handles */
}

.ProseMirror.resize-cursor {
  cursor: default !important;
  /* Remove resize cursor */
}

/* Disable any resize functionality */
.ProseMirror table {
  resize: none !important;
}

.ProseMirror table td,
.ProseMirror table th {
  resize: none !important;
}

/* Table selection */
.ProseMirror .tableWrapper {
  overflow-x: auto;
  margin: 1em 0;
}

.ProseMirror .tableWrapper table {
  margin: 0;
}

/* Empty table cell placeholder */
.ProseMirror table p {
  margin: 0;
  line-height: 1.5;
}

.ProseMirror table .is-empty::before {
  content: "";
  float: left;
  color: #9ca3af;
  pointer-events: none;
  height: 0;
}

/* Dark mode styles for tables */
.dark .ProseMirror table {
  background: #1f2937;
  border-color: #374151;
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.3), 0 1px 2px 0 rgba(0, 0, 0, 0.2);
}

.dark .ProseMirror table td,
.dark .ProseMirror table th {
  border-color: #374151;
  color: #f9fafb;
}

.dark .ProseMirror table th {
  background-color: #374151;
  color: #f9fafb;
  border-bottom-color: #4b5563;
  text-align: center;
}

.dark .ProseMirror table td {
  background-color: #1f2937;
}

/* Dark mode zebra striping - even rows */
.dark .ProseMirror table tr:nth-child(even) td {
  background-color: #2d3748;
}

.dark .ProseMirror table td:hover,
.dark .ProseMirror table th:hover {
  background-color: #374151;
}

/* Dark mode zebra striping on hover for even rows */
.dark .ProseMirror table tr:nth-child(even) td:hover {
  background-color: #3a4553;
}

.dark .ProseMirror table .selectedCell {
  background-color: #1e3a8a !important;
  border-color: #3b82f6 !important;
}

/* Table controls and buttons */
.table-controls {
  display: flex;
  gap: 4px;
  margin: 8px 0;
  flex-wrap: wrap;
}

.table-button {
  background: #f3f4f6;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  padding: 6px 12px;
  font-size: 12px;
  font-weight: 500;
  color: #374151;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  gap: 4px;
}

.table-button:hover {
  background: #e5e7eb;
  border-color: #9ca3af;
  transform: translateY(-1px);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.table-button:active {
  transform: translateY(0);
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.table-button.danger {
  background: #fef2f2;
  border-color: #fecaca;
  color: #dc2626;
}

.table-button.danger:hover {
  background: #fee2e2;
  border-color: #fca5a5;
}

/* Dark mode table controls */
.dark .table-button {
  background: #374151;
  border-color: #4b5563;
  color: #f9fafb;
}

.dark .table-button:hover {
  background: #4b5563;
  border-color: #6b7280;
}

.dark .table-button.danger {
  background: #7f1d1d;
  border-color: #991b1b;
  color: #fca5a5;
}

.dark .table-button.danger:hover {
  background: #991b1b;
  border-color: #b91c1c;
}

/* Table responsive behavior */
@media (max-width: 768px) {
  .ProseMirror table {
    font-size: 12px;
  }

  .ProseMirror table td,
  .ProseMirror table th {
    padding: 8px 12px;
    min-width: 60px;
  }

  .table-controls {
    gap: 2px;
  }

  .table-button {
    padding: 4px 8px;
    font-size: 11px;
  }
}

/* Table Search Input Styles */
.table-wrapper input[type="text"] {
  font-family: inherit;
  font-size: 14px;
  transition: all 0.2s ease;
  font-weight: 400;
}

.table-wrapper input[type="text"]:focus {
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
  transform: translateY(-1px);
}

/* Table with search enabled styling */
.table-wrapper .border-2.border-blue-300 {
  border-color: #93c5fd !important;
  box-shadow: 0 0 0 1px rgba(59, 130, 246, 0.2);
}

.dark .table-wrapper .border-2.border-blue-600 {
  border-color: #3b82f6 !important;
  box-shadow: 0 0 0 1px rgba(59, 130, 246, 0.3);
}

/* Search input dark mode */
.dark .table-wrapper input[type="text"] {
  background-color: #1f2937;
  border-color: #3b82f6;
  color: #f9fafb;
}

.dark .table-wrapper input[type="text"]:focus {
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.2);
}

.dark .table-wrapper input[type="text"]::placeholder {
  color: #9ca3af;
}

/* Table cell content alignment utilities */
.ProseMirror table .text-left {
  text-align: left;
}

.ProseMirror table .text-center {
  text-align: center;
}

.ProseMirror table .text-right {
  text-align: right;
}

/* Animation for table creation */
@keyframes tableSlideIn {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }

  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.ProseMirror table {
  animation: tableSlideIn 0.3s ease-out;
}

/* Custom Code Block Styles */
.code-block-node {
  user-select: none;
  position: relative;
}

.code-block-node .ProseMirror-selectednode {
  outline: 2px solid #3b82f6 !important;
  outline-offset: 2px;
  border-radius: 12px;
}

/* Syntax highlighting styles for code block */
.code-block-node .hljs {
  display: block;
  overflow-x: auto;
  padding: 0;
  color: #f8f8f2;
  background: transparent !important;
}

/* Force dark background for code area */
.code-block-node pre,
.code-block-node textarea {
  background: transparent !important;
}

.code-block-node .relative.bg-gray-900 {
  background-color: #111827 !important;
}

.dark .code-block-node .relative.bg-gray-950 {
  background-color: #030712 !important;
}

.code-block-node {
  color: #f8f8f2;
}

/* Default text color for code block content */
.code-block-node pre {
  color: #f8f8f2 !important;
}

.code-block-node .hljs-keyword,
.code-block-node .hljs-selector-tag,
.code-block-node .hljs-literal,
.code-block-node .hljs-section,
.code-block-node .hljs-link {
  color: #f92672;
}

.code-block-node .hljs-function .hljs-keyword {
  color: #66d9ef;
}

.code-block-node .hljs-subst {
  color: #f8f8f2;
}

.code-block-node .hljs-string,
.code-block-node .hljs-title,
.code-block-node .hljs-name,
.code-block-node .hljs-type,
.code-block-node .hljs-attribute,
.code-block-node .hljs-symbol,
.code-block-node .hljs-bullet,
.code-block-node .hljs-addition,
.code-block-node .hljs-variable,
.code-block-node .hljs-template-tag,
.code-block-node .hljs-template-variable {
  color: #e6db74;
}

.code-block-node .hljs-comment,
.code-block-node .hljs-quote,
.code-block-node .hljs-deletion,
.code-block-node .hljs-meta {
  color: #75715e;
}

.code-block-node .hljs-keyword,
.code-block-node .hljs-selector-tag,
.code-block-node .hljs-literal,
.code-block-node .hljs-title,
.code-block-node .hljs-section,
.code-block-node .hljs-doctag,
.code-block-node .hljs-type,
.code-block-node .hljs-name,
.code-block-node .hljs-strong {
  font-weight: bold;
}

.code-block-node .hljs-number {
  color: #ae81ff;
}

.code-block-node .hljs-built_in,
.code-block-node .hljs-builtin-name,
.code-block-node .hljs-class .hljs-title {
  color: #a6e22e;
}

.code-block-node .hljs-attr {
  color: #a6e22e;
}

.code-block-node .hljs-emphasis {
  font-style: italic;
}

.code-block-node .hljs-tag {
  color: #f8f8f2;
}

.code-block-node .hljs-tag .hljs-name {
  color: #f92672;
}

.code-block-node .hljs-tag .hljs-attr {
  color: #a6e22e;
}

/* Code block textarea styling */
.code-block-node textarea {
  font-variant-ligatures: normal;
  scrollbar-width: thin;
  scrollbar-color: rgba(148, 163, 184, 0.3);
  line-height: 1.6 !important;
  font-size: 14px !important;
  font-family: "Fira Code", "JetBrains Mono", Consolas, monospace !important;
  letter-spacing: normal !important;
  word-spacing: normal !important;
  text-indent: 0 !important;
  vertical-align: baseline !important;
}

.code-block-node pre {
  line-height: 1.6 !important;
  font-size: 14px !important;
  font-family: "Fira Code", "JetBrains Mono", Consolas, monospace !important;
  letter-spacing: normal !important;
  word-spacing: normal !important;
  text-indent: 0 !important;
  vertical-align: baseline !important;
}

.code-block-node textarea::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

.code-block-node textarea::-webkit-scrollbar-track {
  background: transparent;
}

.code-block-node textarea::-webkit-scrollbar-thumb {
  background: rgba(148, 163, 184, 0.3);
  border-radius: 3px;
}

.code-block-node textarea::-webkit-scrollbar-thumb:hover {
  background: rgba(148, 163, 184, 0.5);
}

/* Language dropdown styling */
.code-block-node .absolute.top-full {
  pointer-events: auto;
  user-select: auto;
}

/* Dark mode adjustments for code block */
.dark .code-block-node {
  color: #f1f5f9;
}

/* Smooth hover transition for copy button */
.code-block-node .group:hover .opacity-0 {
  opacity: 1;
}

/* Animation for code block creation */
@keyframes codeBlockSlideIn {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }

  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.code-block-node>div {
  animation: codeBlockSlideIn 0.3s ease-out;
}

/* Accordion and Steps content wrapper styles for code blocks */
.accordion-content-wrapper .code-block-node,
.steps-node .code-block-node {
  margin: 1rem 0;
}

.accordion-content-wrapper pre,
.steps-node pre {
  background: #111827 !important;
  font-family: "Fira Code", "JetBrains Mono", Consolas, monospace !important;
  padding: 1em;
  border-radius: 0.5em;
  margin-bottom: 1em;
  white-space: pre-wrap;
  word-wrap: break-word;
  overflow-x: auto;
  line-height: 1.6;
  font-size: 0.9em;
}

.accordion-content-wrapper pre code,
.steps-node pre code {
  font-family: inherit;
  background: none;
  color: #f8f8f2;
  /* Base color for code, allows hljs to override */
  padding: 0;
  font-size: 0.9em;
  border-radius: 0;
  border: none;
}

/* Language-specific overrides were removed to allow for proper token-based syntax highlighting */

.dark .accordion-content-wrapper pre,
.dark .steps-node pre {
  background: #030712 !important;
  color: #f8fafc !important;
  border: 1px solid #334155;
}

/* Ensure custom code block component works in accordion and steps */
.accordion-content-wrapper [data-type="codeBlock"],
.steps-node [data-type="codeBlock"] {
  position: relative;
  margin: 1rem 0;
}

.accordion-content-wrapper .bg-gray-900,
.steps-node .bg-gray-900 {
  background-color: #111827 !important;
}

.dark .accordion-content-wrapper .bg-gray-950,
.dark .steps-node .bg-gray-950 {
  background-color: #030712 !important;
}

/* Preserve code block header styles in accordion and steps */
.accordion-content-wrapper .bg-gray-800,
.steps-node .bg-gray-800 {
  background-color: #1f2937 !important;
}

.dark .accordion-content-wrapper .bg-gray-900,
.dark .steps-node .bg-gray-900 {
  background-color: #111827 !important;
}

/* Highlight.js syntax highlighting colors for accordion and steps content */
.accordion-content-wrapper .hljs,
.steps-node .hljs {
  display: block;
  overflow-x: auto;
  padding: 0;
  color: #f8f8f2;
  background: transparent !important;
}

.accordion-content-wrapper .hljs-keyword,
.accordion-content-wrapper .hljs-selector-tag,
.accordion-content-wrapper .hljs-literal,
.accordion-content-wrapper .hljs-section,
.accordion-content-wrapper .hljs-link,
.steps-node .hljs-keyword,
.steps-node .hljs-selector-tag,
.steps-node .hljs-literal,
.steps-node .hljs-section,
.steps-node .hljs-link {
  color: #f92672;
}

.accordion-content-wrapper .hljs-function .hljs-keyword,
.steps-node .hljs-function .hljs-keyword {
  color: #66d9ef;
}

.accordion-content-wrapper .hljs-subst,
.steps-node .hljs-subst {
  color: #f8f8f2;
}

.accordion-content-wrapper .hljs-string,
.accordion-content-wrapper .hljs-title,
.accordion-content-wrapper .hljs-name,
.accordion-content-wrapper .hljs-type,
.accordion-content-wrapper .hljs-attribute,
.accordion-content-wrapper .hljs-symbol,
.accordion-content-wrapper .hljs-bullet,
.accordion-content-wrapper .hljs-addition,
.accordion-content-wrapper .hljs-variable,
.accordion-content-wrapper .hljs-template-tag,
.accordion-content-wrapper .hljs-template-variable,
.steps-node .hljs-string,
.steps-node .hljs-title,
.steps-node .hljs-name,
.steps-node .hljs-type,
.steps-node .hljs-attribute,
.steps-node .hljs-symbol,
.steps-node .hljs-bullet,
.steps-node .hljs-addition,
.steps-node .hljs-variable,
.steps-node .hljs-template-tag,
.steps-node .hljs-template-variable {
  color: #e6db74;
}

.accordion-content-wrapper .hljs-comment,
.accordion-content-wrapper .hljs-quote,
.accordion-content-wrapper .hljs-deletion,
.accordion-content-wrapper .hljs-meta,
.steps-node .hljs-comment,
.steps-node .hljs-quote,
.steps-node .hljs-deletion,
.steps-node .hljs-meta {
  color: #75715e;
}

.accordion-content-wrapper .hljs-keyword,
.accordion-content-wrapper .hljs-selector-tag,
.accordion-content-wrapper .hljs-literal,
.accordion-content-wrapper .hljs-title,
.accordion-content-wrapper .hljs-section,
.accordion-content-wrapper .hljs-doctag,
.accordion-content-wrapper .hljs-type,
.accordion-content-wrapper .hljs-name,
.accordion-content-wrapper .hljs-strong,
.steps-node .hljs-keyword,
.steps-node .hljs-selector-tag,
.steps-node .hljs-literal,
.steps-node .hljs-title,
.steps-node .hljs-section,
.steps-node .hljs-doctag,
.steps-node .hljs-type,
.steps-node .hljs-name,
.steps-node .hljs-strong {
  font-weight: bold;
}

.accordion-content-wrapper .hljs-number,
.steps-node .hljs-number {
  color: #ae81ff;
}

.accordion-content-wrapper .hljs-built_in,
.accordion-content-wrapper .hljs-builtin-name,
.accordion-content-wrapper .hljs-class .hljs-title,
.steps-node .hljs-built_in,
.steps-node .hljs-builtin-name,
.steps-node .hljs-class .hljs-title {
  color: #a6e22e;
}

.accordion-content-wrapper .hljs-attr,
.steps-node .hljs-attr {
  color: #a6e22e;
}

.accordion-content-wrapper .hljs-emphasis,
.steps-node .hljs-emphasis {
  font-style: italic;
}

.accordion-content-wrapper .hljs-tag,
.steps-node .hljs-tag {
  color: #f8f8f2;
}

.accordion-content-wrapper .hljs-tag .hljs-name,
.steps-node .hljs-tag .hljs-name {
  color: #f92672;
}

.accordion-content-wrapper .hljs-tag .hljs-attr,
.steps-node .hljs-tag .hljs-attr {
  color: #a6e22e;
}

/* Focus state for code block */
.code-block-node textarea:focus {
  outline: none;
  border: none;
  box-shadow: none;
}

/* Caret visibility */
.code-block-node .caret-white {
  caret-color: white;
}

/* Responsive adjustments for code block */
@media (max-width: 768px) {
  .code-block-node textarea {
    font-size: 12px;
    padding: 12px;
  }

  .code-block-node .px-4 {
    padding-left: 12px;
    padding-right: 12px;
  }
}

/* Card Node Styles */
.card-node {
  user-select: none;
  position: relative;
}

.card-node .ProseMirror-selectednode {
  outline: 2px solid #3b82f6 !important;
  outline-offset: 2px;
  border-radius: 12px;
}

/* Card hover effects */
.card-node .group:hover .opacity-0 {
  opacity: 1;
}

/* Card link styling */
.card-node a {
  text-decoration: none;
  color: inherit;
}

.card-node a:hover {
  text-decoration: none;
}

/* Card image error handling */
.card-node img {
  transition: opacity 0.2s ease;
}

.card-node img[style*="display: none"] {
  display: none !important;
}

/* Card responsive design */
@media (max-width: 768px) {
  .card-node .flex {
    flex-direction: column;
    align-items: center;
    text-align: center;
  }

  .card-node .flex-shrink-0 {
    margin-bottom: 12px;
  }

  .card-node .space-x-4> :not([hidden])~ :not([hidden]) {
    --tw-space-x-reverse: 0;
    margin-right: calc(0px * var(--tw-space-x-reverse));
    margin-left: calc(0px * calc(1 - var(--tw-space-x-reverse)));
  }
}

/* Card animation */
@keyframes cardSlideIn {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }

  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.card-node>div {
  animation: cardSlideIn 0.3s ease-out;
}

/* Card edit mode styling */
.card-node .border-2 {
  border-style: solid;
}

/* Dark mode adjustments for cards */
.dark .card-node {
  color: #f1f5f9;
}

.dark .card-node .bg-white {
  background-color: #1e293b;
}

.dark .card-node .border-gray-200 {
  border-color: #374151;
}

.dark .card-node .text-gray-900 {
  color: #f1f5f9;
}

.dark .card-node .text-gray-600 {
  color: #9ca3af;
}

/* Accordion Group Node Styles */
.accordion-group-node .accordion-group>div:not(:first-child):not(:last-child) {
  border-radius: 0;
  border-top-left-radius: 0 !important;
  border-top-right-radius: 0 !important;
  border-bottom-left-radius: 0 !important;
  border-bottom-right-radius: 0 !important;
}

.accordion-group-node .accordion-group>div:last-child {
  border-top-left-radius: 0 !important;
  border-top-right-radius: 0 !important;
}

/* Tabs Node Styles */
.ProseMirror .tabs-node {
  margin: 1rem 0;
}

.ProseMirror .tabs-node p {
  margin: 0;
  padding: 0;
}

/* Tabs animation */
.tabs-content {
  transition: all 0.3s ease;
  overflow: hidden;
}

.tabs-content.active {
  opacity: 1;
  transform: translateY(0);
}

.tabs-content.inactive {
  opacity: 0;
  transform: translateY(-10px);
}

/* Tabs Node Styles */
.tabs-node {
  user-select: none;
  position: relative;
}

.tabs-node .ProseMirror-selectednode {
  outline: 2px solid #3b82f6 !important;
  outline-offset: 2px;
  border-radius: 12px;
}

/* Animation for tabs node creation */
@keyframes tabsSlideIn {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }

  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.tabs-node>div {
  animation: tabsSlideIn 0.3s ease-out;
}

/* Tabs content wrapper styles for code blocks */
.tabs-content-wrapper .code-block-node {
  margin: 1rem 0;
}

.tabs-content-wrapper pre {
  background: #111827 !important;
  color: #f8f8f2 !important;
  font-family: "Fira Code", "JetBrains Mono", Consolas, monospace !important;
  padding: 1em;
  border-radius: 0.5em;
  margin-bottom: 1em;
  white-space: pre-wrap;
  word-wrap: break-word;
  overflow-x: auto;
  line-height: 1.6;
  font-size: 0.9em;
}

.tabs-content-wrapper pre code {
  font-family: inherit;
  background: none;
  color: inherit;
  padding: 0;
  font-size: 0.9em;
  border-radius: 0;
  border: none;
}

.dark .tabs-content-wrapper pre {
  background: #030712 !important;
  color: #f8fafc !important;
  border: 1px solid #334155;
}

/* Ensure custom code block component works in tabs */
.tabs-content-wrapper [data-type="codeBlock"] {
  position: relative;
  margin: 1rem 0;
}

.tabs-content-wrapper .bg-gray-900 {
  background-color: #111827 !important;
}

.dark .tabs-content-wrapper .bg-gray-950 {
  background-color: #030712 !important;
}

/* Preserve code block header styles in tabs */
.tabs-content-wrapper .bg-gray-800 {
  background-color: #1f2937 !important;
}

.dark .tabs-content-wrapper .bg-gray-900 {
  background-color: #111827 !important;
}

/* Highlight.js syntax highlighting colors for tabs content */
.tabs-content-wrapper .hljs {
  display: block;
  overflow-x: auto;
  padding: 0;
  color: #f8f8f2;
  background: transparent !important;
}

.tabs-content-wrapper .hljs-keyword,
.tabs-content-wrapper .hljs-selector-tag,
.tabs-content-wrapper .hljs-literal,
.tabs-content-wrapper .hljs-section,
.tabs-content-wrapper .hljs-link {
  color: #f92672;
}

.tabs-content-wrapper .hljs-function .hljs-keyword {
  color: #66d9ef;
}

.tabs-content-wrapper .hljs-subst {
  color: #f8f8f2;
}

.tabs-content-wrapper .hljs-string,
.tabs-content-wrapper .hljs-title,
.tabs-content-wrapper .hljs-name,
.tabs-content-wrapper .hljs-type,
.tabs-content-wrapper .hljs-attribute,
.tabs-content-wrapper .hljs-symbol,
.tabs-content-wrapper .hljs-bullet,
.tabs-content-wrapper .hljs-addition,
.tabs-content-wrapper .hljs-variable,
.tabs-content-wrapper .hljs-template-tag,
.tabs-content-wrapper .hljs-template-variable {
  color: #e6db74;
}

.tabs-content-wrapper .hljs-comment,
.tabs-content-wrapper .hljs-quote,
.tabs-content-wrapper .hljs-deletion,
.tabs-content-wrapper .hljs-meta {
  color: #75715e;
}

.tabs-content-wrapper .hljs-keyword,
.tabs-content-wrapper .hljs-selector-tag,
.tabs-content-wrapper .hljs-literal,
.tabs-content-wrapper .hljs-title,
.tabs-content-wrapper .hljs-section,
.tabs-content-wrapper .hljs-doctag,
.tabs-content-wrapper .hljs-type,
.tabs-content-wrapper .hljs-name,
.tabs-content-wrapper .hljs-strong {
  font-weight: bold;
}

.tabs-content-wrapper .hljs-number {
  color: #ae81ff;
}

.tabs-content-wrapper .hljs-built_in,
.tabs-content-wrapper .hljs-builtin-name,
.tabs-content-wrapper .hljs-class .hljs-title {
  color: #a6e22e;
}

.tabs-content-wrapper .hljs-attr {
  color: #a6e22e;
}

.tabs-content-wrapper .hljs-emphasis {
  font-style: italic;
}

.tabs-content-wrapper .hljs-tag {
  color: #f8f8f2;
}

.tabs-content-wrapper .hljs-tag .hljs-name {
  color: #f92672;
}

.tabs-content-wrapper .hljs-tag .hljs-attr {
  color: #a6e22e;
}

/* Tab header styling */
.tabs-node .flex.border-b {
  background: #f8fafc;
  border-bottom: 2px solid #e2e8f0;
}

.dark .tabs-node .flex.border-b {
  background: #1e293b;
  border-bottom: 2px solid #475569;
}

/* Tab header buttons styling */
.tabs-node button {
  white-space: nowrap;
  flex-shrink: 0;
  font-weight: 500;
  text-transform: none;
  letter-spacing: -0.01em;
}

.tabs-node button:focus {
  outline: none;
  box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.5);
  border-radius: 6px 6px 0 0;
}

/* Responsive tab headers */
.tabs-node .overflow-x-auto {
  scrollbar-width: thin;
  scrollbar-color: rgba(148, 163, 184, 0.3) transparent;
}

.tabs-node .overflow-x-auto::-webkit-scrollbar {
  height: 4px;
}

.tabs-node .overflow-x-auto::-webkit-scrollbar-track {
  background: transparent;
}

.tabs-node .overflow-x-auto::-webkit-scrollbar-thumb {
  background: rgba(148, 163, 184, 0.3);
  border-radius: 2px;
}

.tabs-node .overflow-x-auto::-webkit-scrollbar-thumb:hover {
  background: rgba(148, 163, 184, 0.5);
}

/* Ensure consistent spacing */
.tabs-content-wrapper>*:first-child {
  margin-top: 0;
}

.tabs-content-wrapper>*:last-child {
  margin-bottom: 0;
}

/* Dark mode adjustments for tabs */
.dark .tabs-node {
  color: #f1f5f9;
}

.dark .tabs-node .bg-white {
  background-color: #1e293b;
}

.dark .tabs-node .border-gray-200 {
  border-color: #475569;
}

.dark .tabs-node .text-gray-900 {
  color: #f1f5f9;
}

.dark .tabs-node .text-gray-600 {
  color: #9ca3af;
}

/* Responsive adjustments for tabs */
@media (max-width: 768px) {
  .tabs-node button {
    font-size: 12px;
    padding: 8px 12px;
  }

  .tabs-content-wrapper {
    font-size: 14px;
  }

  .tabs-content-wrapper pre {
    font-size: 12px;
    padding: 12px;
  }
}

/* Tab header styling */
.tabs-node .flex.border-b {
  background: #f8fafc;
  border-bottom: 2px solid #e2e8f0;
}

.dark .tabs-node .flex.border-b {
  background: #1e293b;
  border-bottom: 2px solid #475569;
}

/* Tab header buttons styling */
.tabs-node button {
  white-space: nowrap;
  flex-shrink: 0;
  font-weight: 500;
  text-transform: none;
  letter-spacing: -0.01em;
}

.tabs-node button:focus {
  outline: none;
  box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.5);
  border-radius: 6px 6px 0 0;
}

/* Responsive tab headers */
.tabs-node .overflow-x-auto {
  scrollbar-width: thin;
  scrollbar-color: rgba(148, 163, 184, 0.3) transparent;
}

.tabs-node .overflow-x-auto::-webkit-scrollbar {
  height: 4px;
}

.tabs-node .overflow-x-auto::-webkit-scrollbar-track {
  background: transparent;
}

.tabs-node .overflow-x-auto::-webkit-scrollbar-thumb {
  background: rgba(148, 163, 184, 0.3);
  border-radius: 2px;
}

.tabs-node .overflow-x-auto::-webkit-scrollbar-thumb:hover {
  background: rgba(148, 163, 184, 0.5);
}

/* Ensure consistent spacing */
.tabs-content-wrapper>*:first-child {
  margin-top: 0;
}

.tabs-content-wrapper>*:last-child {
  margin-bottom: 0;
}

/* Dark mode adjustments for tabs */
.dark .tabs-node {
  color: #f1f5f9;
}

.dark .tabs-node .bg-white {
  background-color: #1e293b;
}

.dark .tabs-node .border-gray-200 {
  border-color: #475569;
}

.dark .tabs-node .text-gray-900 {
  color: #f1f5f9;
}

.dark .tabs-node .text-gray-600 {
  color: #9ca3af;
}

/* Responsive adjustments for tabs */
@media (max-width: 768px) {
  .tabs-node button {
    font-size: 12px;
    padding: 8px 12px;
  }

  .tabs-content-wrapper {
    font-size: 14px;
  }

  .tabs-content-wrapper pre {
    font-size: 12px;
    padding: 12px;
  }
}

/* Tab header styling */
.tabs-node .flex.border-b {
  background: #f8fafc;
  border-bottom: 2px solid #e2e8f0;
}

.dark .tabs-node .flex.border-b {
  background: #1e293b;
  border-bottom: 2px solid #475569;
}

/* Tab header buttons styling */
.tabs-node button {
  white-space: nowrap;
  flex-shrink: 0;
  font-weight: 500;
  text-transform: none;
  letter-spacing: -0.01em;
}

.tabs-node button:focus {
  outline: none;
  box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.5);
  border-radius: 6px 6px 0 0;
}

/* Responsive tab headers */
.tabs-node .overflow-x-auto {
  scrollbar-width: thin;
  scrollbar-color: rgba(148, 163, 184, 0.3) transparent;
}

.tabs-node .overflow-x-auto::-webkit-scrollbar {
  height: 4px;
}

.tabs-node .overflow-x-auto::-webkit-scrollbar-track {
  background: transparent;
}

.tabs-node .overflow-x-auto::-webkit-scrollbar-thumb {
  background: rgba(148, 163, 184, 0.3);
  border-radius: 2px;
}

.tabs-node .overflow-x-auto::-webkit-scrollbar-thumb:hover {
  background: rgba(148, 163, 184, 0.5);
}

/* Ensure consistent spacing */
.tabs-content-wrapper>*:first-child {
  margin-top: 0;
}

.tabs-content-wrapper>*:last-child {
  margin-bottom: 0;
}

/* Dark mode adjustments for tabs */
.dark .tabs-node {
  color: #f1f5f9;
}

.dark .tabs-node .bg-white {
  background-color: #1e293b;
}

.dark .tabs-node .border-gray-200 {
  border-color: #475569;
}

.dark .tabs-node .text-gray-900 {
  color: #f1f5f9;
}

.dark .tabs-node .text-gray-600 {
  color: #9ca3af;
}

/* Responsive adjustments for tabs */
@media (max-width: 768px) {
  .tabs-node button {
    font-size: 12px;
    padding: 8px 12px;
  }

  .tabs-content-wrapper {
    font-size: 14px;
  }

  .tabs-content-wrapper pre {
    font-size: 12px;
    padding: 12px;
  }
}

/* Image Node Styles */
.image-node {
  user-select: none;
  position: relative;
  display: block;
  text-align: center;
  width: 100%;
}

.image-node img {
  display: block;
  margin: 0 auto;
}

.image-node .ProseMirror-selectednode {
  outline: 2px solid #3b82f6 !important;
  outline-offset: 2px;
  border-radius: 12px;
}

/* Animation for image node creation */
@keyframes imageSlideIn {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }

  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.image-node>div {
  animation: imageSlideIn 0.3s ease-out;
}

/* ===== TAB INDENTATION AND LIST STYLES ===== */

/* Ensure proper list indentation */
.ProseMirror .editor-bullet-list,
.ProseMirror .editor-ordered-list {
  margin-left: 0;
  padding-left: 1.5rem;
}

/* Nested list indentation */
.ProseMirror .editor-bullet-list .editor-bullet-list,
.ProseMirror .editor-ordered-list .editor-ordered-list,
.ProseMirror .editor-bullet-list .editor-ordered-list,
.ProseMirror .editor-ordered-list .editor-bullet-list {
  margin-left: 1.5rem;
  margin-top: 0.25rem;
  margin-bottom: 0.25rem;
}

/* List item styling */
.ProseMirror .editor-list-item {
  margin: 0.25rem 0;
  position: relative;
}

/* Improve list item spacing */
.ProseMirror .editor-list-item p {
  margin: 0;
}

/* Visual feedback for indented content */
.ProseMirror .editor-list-item::marker {
  color: #6b7280;
}

/* Dark mode list markers */
.dark .ProseMirror .editor-list-item::marker {
  color: #9ca3af;
}

/* Ensure tab indentation is visible in regular paragraphs */
.ProseMirror p {
  white-space: pre-wrap;
  word-wrap: break-word;
}

/* Custom HTML Preview - Override whitespace behavior for proper HTML rendering */
.custom-html-preview,
.custom-html-preview * {
  white-space: normal !important;
  word-wrap: break-word;
  overflow-wrap: break-word;
}

/* Ensure specific HTML elements in preview behave normally */
.custom-html-preview p,
.custom-html-preview div,
.custom-html-preview span,
.custom-html-preview h1,
.custom-html-preview h2,
.custom-html-preview h3,
.custom-html-preview h4,
.custom-html-preview h5,
.custom-html-preview h6 {
  white-space: normal !important;
}

/* Remove excessive margins from first and last elements to prevent spacing issues */
.custom-html-preview>*:first-child {
  margin-top: 0 !important;
}

.custom-html-preview>*:last-child {
  margin-bottom: 0 !important;
}

/* Normalize margins for common HTML elements in preview */
.custom-html-preview h1,
.custom-html-preview h2,
.custom-html-preview h3,
.custom-html-preview h4,
.custom-html-preview h5,
.custom-html-preview h6 {
  margin-top: 0.5em;
  margin-bottom: 0.5em;
}

.custom-html-preview p {
  margin-top: 0.5em;
  margin-bottom: 0.5em;
}

.custom-html-preview div {
  margin: 0;
}

/* Preserve intentional whitespace only in pre and code elements */
.custom-html-preview pre,
.custom-html-preview code {
  white-space: pre-wrap !important;
}

/* Ensure CSS styles within Custom HTML are applied correctly */
.custom-html-preview style {
  display: none;
  /* Hide the style tag itself but allow its CSS to be applied */
}

/* Allow custom CSS to override default styles within the preview */
.custom-html-preview {
  position: relative;
  isolation: isolate;
  /* Create a new stacking context for custom styles */
}

/* Focus management - prevent tab from leaving editor */
.ProseMirror:focus-within {
  outline: none;
}

/* Ensure editor maintains focus during tab operations */
.ProseMirror * {
  tab-size: 2;
  -moz-tab-size: 2;
}

/* ===== HINT COMPONENT STYLES ===== */
.hint-node {
  display: inline;
  position: relative;
}

.hint-text {
  cursor: help;
  text-decoration: underline;
  text-decoration-style: dotted;
  text-decoration-color: #3b82f6;
  text-underline-offset: 2px;
  padding: 1px 2px;
  border-radius: 2px;
  transition: all 0.2s ease;
  background-color: transparent;
}

.hint-text:hover {
  background-color: rgba(59, 130, 246, 0.1);
  text-decoration-color: #1d4ed8;
}

/* Dark mode styles for hint */
.dark .hint-text {
  text-decoration-color: #60a5fa;
}

.dark .hint-text:hover {
  background-color: rgba(59, 130, 246, 0.2);
  text-decoration-color: #93c5fd;
}

/* Ensure hint text is selectable */
.hint-text {
  user-select: text;
  -webkit-user-select: text;
  -moz-user-select: text;
  -ms-user-select: text;
}

/* Custom tooltip styles */
.hint-tooltip {
  position: absolute;
  bottom: 100%;
  left: 50%;
  transform: translateX(-50%);
  margin-bottom: 10px;
  padding: 10px 14px;
  background-color: #1f2937;
  color: white;
  font-size: 14px;
  font-weight: 500;
  border-radius: 6px;
  z-index: 1000;
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  animation: fadeInTooltip 0.2s ease-out;
  line-height: 1.4;
  text-align: center;
  overflow: hidden;
  text-overflow: ellipsis;
}

/* Tooltip arrow */
.hint-tooltip::after {
  content: '';
  position: absolute;
  top: 100%;
  left: 50%;
  transform: translateX(-50%);
  border: 6px solid transparent;
  border-top-color: #1f2937;
}

/* Dark mode tooltip */
.dark .hint-tooltip {
  background-color: #374151;
  color: #f9fafb;
}

.dark .hint-tooltip::after {
  border-top-color: #374151;
  border-width: 6px;
}

/* Tooltip animation */
@keyframes fadeInTooltip {
  from {
    opacity: 0;
    transform: translateX(-50%) translateY(4px);
  }

  to {
    opacity: 1;
    transform: translateX(-50%) translateY(0);
  }
}

/* Ensure tooltip appears above other elements */
.hint-node {
  position: relative;
  z-index: 1;
}

.hint-tooltip {
  z-index: 1001;
}

/* ===== TABLE WRAPPER AND SEARCHBAR STYLES ===== */

/* Table wrapper container */
.table-wrapper {
  position: relative;
  margin: 1.5rem 0;
  display: block;
  width: 100%;
}

/* Searchbar toggle button positioning */
.table-wrapper .absolute {
  position: absolute;
  top: -8px;
  right: -8px;
  z-index: 20;
}

/* Button styling for searchbar toggle */
.table-wrapper button {
  display: flex;
  align-items: center;
  gap: 4px;
  padding: 6px 12px;
  font-size: 11px;
  font-weight: 500;
  border-radius: 8px;
  border: 1px solid #e5e7eb;
  background: #ffffff;
  color: #374151;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  transition: all 0.2s ease;
  cursor: pointer;
  white-space: nowrap;
  user-select: none;
}

.table-wrapper button:hover {
  background: #f9fafb;
  border-color: #d1d5db;
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

.table-wrapper button:active {
  transform: translateY(0);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

/* Dark mode button styling */
.dark .table-wrapper button {
  background: #1f2937;
  border-color: #374151;
  color: #f9fafb;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.dark .table-wrapper button:hover {
  background: #374151;
  border-color: #4b5563;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.4);
}

/* Icon styling in button */
.table-wrapper button svg {
  width: 12px;
  height: 12px;
  flex-shrink: 0;
}

/* Button visibility control */
.table-wrapper .opacity-0 {
  opacity: 0;
  pointer-events: none;
}

.table-wrapper .opacity-100 {
  opacity: 1;
  pointer-events: auto;
}

.table-wrapper .group:hover .opacity-0,
.table-wrapper:hover .opacity-0 {
  opacity: 1;
  pointer-events: auto;
}

/* Smooth transitions for button appearance */
.table-wrapper .transition-opacity {
  transition: opacity 0.2s ease-in-out;
}

/* Visual indicator for searchbar enabled */
.table-wrapper .searchbar-enabled {
  border: 2px solid #3b82f6 !important;
  box-shadow: 0 0 0 1px rgba(59, 130, 246, 0.2);
}

.dark .table-wrapper .searchbar-enabled {
  border-color: #60a5fa !important;
  box-shadow: 0 0 0 1px rgba(96, 165, 250, 0.2);
}

/* Status indicator text */
.table-wrapper .mt-2 {
  margin-top: 8px;
  font-size: 11px;
  color: #6b7280;
  display: flex;
  align-items: center;
  gap: 4px;
  font-weight: 500;
}

.dark .table-wrapper .mt-2 {
  color: #9ca3af;
}

/* Table selection styling when searchbar is active */
.table-wrapper.ProseMirror-selectednode {
  outline: 2px solid #3b82f6 !important;
  outline-offset: 4px;
  border-radius: 8px;
}

/* Ensure table content is properly contained */
.table-wrapper .group {
  position: relative;
  width: 100%;
  overflow: visible;
}

/* Animation for button appearance */
@keyframes searchbarButtonSlideIn {
  from {
    opacity: 0;
    transform: translateY(-4px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

.table-wrapper button {
  animation: searchbarButtonSlideIn 0.2s ease-out;
}

/* Responsive adjustments for table wrapper */
@media (max-width: 768px) {
  .table-wrapper .absolute {
    top: -6px;
    right: -6px;
  }

  .table-wrapper button {
    padding: 4px 8px;
    font-size: 10px;
  }

  .table-wrapper button svg {
    width: 10px;
    height: 10px;
  }

  .table-wrapper .mt-2 {
    font-size: 10px;
  }
}

/* Ensure proper z-index stacking */
.table-wrapper {
  z-index: 1;
}

.table-wrapper .absolute {
  z-index: 10;
}

/* Prevent text selection on button */
.table-wrapper button * {
  user-select: none;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
}

/* ===== MERMAID DIAGRAM STYLES ===== */

/* Mermaid node wrapper */
.mermaid-node-wrapper {
  margin: 1.5rem 0;
  position: relative;
}

/* Mermaid diagram container */
.mermaid-diagram {
  overflow-x: auto;
  overflow-y: visible;
  max-width: 100%;
}

/* Mermaid SVG styling */
.mermaid-diagram svg {
  max-width: 100%;
  height: auto;
  display: block;
  margin: 0 auto;
}

/* Dark mode adjustments for Mermaid */
.dark .mermaid-diagram svg {
  filter: invert(0.9) hue-rotate(180deg);
}

/* Mermaid node in editor */
.mermaid-node {
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transition: all 0.2s ease;
}

.mermaid-node:hover {
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
}

/* Mermaid editing state */
.mermaid-node-wrapper .mermaid-editing {
  border: 2px dashed #3b82f6;
  background: #f8fafc;
}

.dark .mermaid-node-wrapper .mermaid-editing {
  background: #1e293b;
  border-color: #60a5fa;
}

/* Mermaid code editor */
.mermaid-code-editor {
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 14px;
  line-height: 1.5;
  resize: vertical;
  min-height: 200px;
}

/* Mermaid template buttons */
.mermaid-templates {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
  margin-bottom: 1rem;
}

.mermaid-template-btn {
  padding: 0.25rem 0.75rem;
  font-size: 0.75rem;
  border-radius: 0.375rem;
  border: 1px solid #d1d5db;
  background: #f9fafb;
  color: #374151;
  cursor: pointer;
  transition: all 0.2s ease;
}

.mermaid-template-btn:hover {
  background: #f3f4f6;
  border-color: #9ca3af;
}

.dark .mermaid-template-btn {
  background: #374151;
  border-color: #4b5563;
  color: #d1d5db;
}

.dark .mermaid-template-btn:hover {
  background: #4b5563;
  border-color: #6b7280;
}

/* Mermaid error state */
.mermaid-error {
  background: #fef2f2;
  border: 1px solid #fecaca;
  color: #dc2626;
  padding: 1rem;
  border-radius: 0.5rem;
  font-size: 0.875rem;
}

.dark .mermaid-error {
  background: #7f1d1d;
  border-color: #dc2626;
  color: #fca5a5;
}

/* Mermaid loading state */
.mermaid-loading {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 2rem;
  color: #6b7280;
}

.dark .mermaid-loading {
  color: #9ca3af;
}