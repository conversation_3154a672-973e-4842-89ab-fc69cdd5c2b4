import { getProject } from "@/utils/create-repo/cloudflare";
import { NextRequest, NextResponse } from "next/server";

export const runtime = "edge";

export async function GET(
	request: NextRequest,
	{ params }: { params: Promise<{ name: string }> }
) {
	const { name } = await params;

	if (!name) {
		return NextResponse.json(
			{ error: "Project name is required" },
			{ status: 400 }
		);
	}

	const project = await getProject(name);

	return NextResponse.json({ project });
}
