import { NextRequest, NextResponse } from "next/server";

export const runtime = 'edge';

// This is the API route that fetches bot statistics from DocsBot
export async function GET(request: NextRequest) {
	try {
		// Get timeDelta from query parameters (default: 30 days)
		const searchParams = request.nextUrl.searchParams;
		const timeDelta = searchParams.get("timeDelta") || "30";
		const docsbotKey = searchParams.get("docsbotKey");

		// Get API key from environment variables
		const apiKey = process.env.DOCSBOT_API_KEY;

		// Return failure response if no docsbotKey or API key is provided
		if (!docsbotKey || !apiKey) {
			return NextResponse.json({ success: false, data: null });
		}

		// Split the docsbot key to get team ID and bot ID (format: teamId/botId)
		const [teamId, botId] = docsbotKey.split("/");

		if (!teamId || !botId) {
			return NextResponse.json({ success: false, data: null });
		}

		// Making the request to the DocsBot API
		const docsBotUrl = `https://docsbot.ai/api/teams/${teamId}/bots/${botId}/stats?timeDelta=${timeDelta}`;

		const response = await fetch(docsBotUrl, {
			method: "GET",
			headers: {
				Authorization: `Bearer ${apiKey}`,
			},
		});

		if (!response.ok) {
			const errorData = await response.json().catch(() => ({}));
			console.error("Error in DocsBot API:", errorData);

			// In case of error, return unsuccessful response
			return NextResponse.json({ success: false, data: null });
		}

		const data = await response.json();

		// Return successful response with the API data
		return NextResponse.json({ success: true, data });
	} catch (error) {
		console.error("Error processing request:", error);

		// In case of error, return unsuccessful response
		return NextResponse.json({ success: false, data: null });
	}
}
