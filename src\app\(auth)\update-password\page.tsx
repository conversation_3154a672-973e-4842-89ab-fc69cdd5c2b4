"use client";

import { useEffect, useState } from "react";
import { useRouter } from "next/navigation";
import { But<PERSON> } from "@/components/ui/button";
import { PasswordInput } from "@/components/auth/password-input";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
} from "@/components/ui/form";
import { zodResolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import { z } from "zod";
import { useToast } from "@/components/ToastProvider";
import { updateUserPassword } from "./actions";
import { createClient } from "@/utils/supabase/client";
import { passwordSchema } from "@/lib/schemas";

const updatePasswordSchema = z
  .object({
    password: passwordSchema,
    confirmPassword: z.string().min(1, "Confirm password is required"),
  })
  .refine((data) => data.password === data.confirmPassword, {
    message: "Passwords do not match",
    path: ["confirmPassword"],
  });

export default function UpdatePasswordPage() {
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [userEmail, setUserEmail] = useState<string | null>(null);
  const { addToast } = useToast();
  const router = useRouter();
  const supabase = createClient();

  const form = useForm<z.infer<typeof updatePasswordSchema>>({
    resolver: zodResolver(updatePasswordSchema),
    defaultValues: {
      password: "",
      confirmPassword: "",
    },
    mode: "onChange",
  });

  // Effect to load user data and email
  useEffect(() => {
    async function loadUserData() {
      try {
        const { data } = await supabase.auth.getUser();
        if (!data?.user) {
          router.push("/login");
          return;
        }

        setUserEmail(data.user.email || null);
      } catch (error) {
        console.error("Error fetching user data:", error);
        router.push("/login");
      }
    }

    loadUserData();
  }, [router, supabase]);

  useEffect(() => {
    const password = form.watch("password");
    const TOAST_ID = "password-requirements";

    const checks = [
      { test: password.length >= 8, message: "At least 8 characters" },
      { test: /[A-Z]/.test(password), message: "At least 1 capital letter" },
      { test: /[a-z]/.test(password), message: "At least 1 lower case letter" },
      { test: /[0-9]/.test(password), message: "At least 1 number" },
      {
        test: /[!@#$%^&*\-_]/.test(password),
        message: "At least 1 special character (!@#$%^&*_-)",
      },
    ];

    if (form.getFieldState("password").isDirty) {
      const failedChecks = checks.filter((check) => !check.test);
      const allValid = failedChecks.length === 0;

      if (allValid) {
        addToast(
          "✓ All requirements met!",
          "success",
          "Password Requirements",
          TOAST_ID
        );
      } else {
        const message = failedChecks
          .map((check) => `• ${check.message}`)
          .join("\n");
        addToast(message, "info", "Password Requirements", TOAST_ID);
      }
    }
  }, [form.watch("password"), addToast, form]);

  useEffect(() => {
    const { errors } = form.formState;
    Object.entries(errors).forEach(([field, error]) => {
      if (
        error?.message &&
        form.getFieldState(field as keyof z.infer<typeof updatePasswordSchema>)
          .isTouched
      ) {
        addToast(error.message, "error", `Invalid ${field}`);
      }
    });
  }, [form.formState.errors, addToast, form]);

  async function onSubmit(values: z.infer<typeof updatePasswordSchema>) {
    setIsSubmitting(true);
    try {
      const formData = new FormData();
      formData.append("password", values.password);

      const result = await updateUserPassword(formData);

      if (result.success) {
        addToast(result.message || "Password updated successfully!", "success");
        addToast("You will be redirected to the login page.", "loading");
        router.push("/login");
      } else {
        addToast(
          result.error?.message || "Failed to update password.",
          "error"
        );
      }
    } catch (error) {
      console.error("Error updating password:", error);
      addToast("An unexpected error occurred. Please try again.", "error");
    } finally {
      setIsSubmitting(false);
    }
  }

  return (
    <div className="w-full max-w-md">
      <div className="bg-white shadow-sm rounded-lg border border-gray-200">
        <div className="px-6 py-5 pb-3 border-b border-gray-200">
          <h1 className="text-xl font-semibold text-gray-800">
            Update Password
          </h1>
          <div className="h-5 mt-1">
            {userEmail ? (
              <p className="text-sm text-gray-500">{userEmail}</p>
            ) : (
              <div className="h-4 w-48 bg-gray-200 rounded animate-pulse"></div>
            )}
          </div>
        </div>

        <div className="p-6 pt-4">
          <Form {...form}>
            <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-5">
              <FormField
                control={form.control}
                name="password"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className="text-sm font-medium text-gray-700">
                      New Password
                    </FormLabel>
                    <FormControl>
                      <PasswordInput
                        placeholder="Enter new password"
                        className={`h-10 ${
                          form.formState.errors.password
                            ? "border-red-500 focus-visible:ring-red-500"
                            : "border-gray-300 focus-visible:ring-blue-500"
                        }`}
                        {...field}
                      />
                    </FormControl>
                  </FormItem>
                )}
              />
              <FormField
                control={form.control}
                name="confirmPassword"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className="text-sm font-medium text-gray-700">
                      Confirm New Password
                    </FormLabel>
                    <FormControl>
                      <PasswordInput
                        placeholder="Confirm new password"
                        className={`h-10 ${
                          form.formState.errors.confirmPassword
                            ? "border-red-500 focus-visible:ring-red-500"
                            : "border-gray-300 focus-visible:ring-blue-500"
                        }`}
                        {...field}
                      />
                    </FormControl>
                  </FormItem>
                )}
              />
              <Button
                type="submit"
                className="w-full h-10 bg-wd-blue hover:bg-wd-blueDark text-white font-medium rounded-md"
                disabled={isSubmitting || !form.formState.isValid}
              >
                {isSubmitting ? "Updating..." : "Update Password"}
              </Button>
            </form>
          </Form>
        </div>
      </div>
    </div>
  );
}
