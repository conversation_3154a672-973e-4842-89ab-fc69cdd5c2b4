import { NextRequest, NextResponse } from "next/server";
import ExcelJS from "exceljs";
import { BotQuestion } from "@/app/(dashboard)/[projectId]/ask-ai/types";

export const runtime = "edge";

interface ExportLogsRequest {
	questions: BotQuestion[];
	projectName: string;
}

function getRatingText(rating: number | null): string {
	if (rating === 1) return "Positive";
	if (rating === -1) return "Negative";
	return "No Rating";
}

async function generateLogsExcel(
	data: ExportLogsRequest
): Promise<ArrayBuffer> {
	const workbook = new ExcelJS.Workbook();
	workbook.creator = "WriteDocs";
	workbook.created = new Date();

	const sheet = workbook.addWorksheet("Question Logs");

	// Add Headers
	sheet.columns = [
		{ header: "Question", key: "question", width: 50 },
		{ header: "Answer", key: "answer", width: 80 },
		{ header: "Status", key: "status", width: 15 },
		{ header: "Rating", key: "rating", width: 15 },
		{ header: "Escalated", key: "escalated", width: 15 },
		{ header: "Timestamp", key: "timestamp", width: 25 },
	];

	// Style Headers
	sheet.getRow(1).font = { bold: true };

	// Add Rows
	data.questions.forEach((q) => {
		sheet.addRow({
			question: q.question,
			answer: q.answer,
			status: q.couldAnswer ? "Answered" : "Not Answered",
			rating: getRatingText(q.rating),
			escalated: q.escalation ? "Yes" : "No",
			timestamp: new Date(q.createdAt).toLocaleString("en-US", {
				year: "numeric",
				month: "long",
				day: "numeric",
				hour: "2-digit",
				minute: "2-digit",
			}),
		});
	});

	// Auto-wrap text in the 'Answer' column
	sheet.getColumn("answer").alignment = { wrapText: true };

	const buffer = await workbook.xlsx.writeBuffer();
	return buffer;
}

export async function POST(request: NextRequest) {
	try {
		const body: ExportLogsRequest = await request.json();

		if (!body.questions || !body.projectName) {
			return NextResponse.json(
				{ error: "Missing required parameters" },
				{ status: 400 }
			);
		}

		const excelBuffer = await generateLogsExcel(body);

		const timestamp = new Date()
			.toISOString()
			.replace(/[:.]/g, "-")
			.slice(0, -5);
		const filename = `${body.projectName}_ask-ai-logs_${timestamp}.xlsx`;

		return new NextResponse(excelBuffer, {
			status: 200,
			headers: {
				"Content-Type":
					"application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
				"Content-Disposition": `attachment; filename="${filename}"`,
			},
		});
	} catch (error) {
		console.error("Error generating logs export:", error);
		return NextResponse.json(
			{ error: "Failed to generate export" },
			{ status: 500 }
		);
	}
}
