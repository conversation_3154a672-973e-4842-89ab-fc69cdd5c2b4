import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@/utils/supabase/server';

export const runtime = 'edge';

// GET /api/images/[id] - Get a specific image
export async function GET(
	request: NextRequest,
	{ params }: { params: Promise<{ id: string }> }
) {
	try {
		const { id } = await params;

		if (!id) {
			return NextResponse.json(
				{ error: 'Image ID is required' },
				{ status: 400 }
			);
		}

		const supabase = createClient();

		// Get user session for authentication
		const {
			data: { user },
			error: userError,
		} = await supabase.auth.getUser();

		if (userError || !user) {
			return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
		}

		const { data: image, error } = await supabase
			.from('organization_images')
			.select('*')
			.eq('id', id)
			.neq('deleting', true) // Exclude images marked for deletion
			.single();

		if (error) {
			if (error.code === 'PGRST116') {
				return NextResponse.json({ error: 'Image not found' }, { status: 404 });
			}
			console.error('Error loading image:', error);
			return NextResponse.json(
				{ error: 'Failed to load image', details: error.message },
				{ status: 500 }
			);
		}

		return NextResponse.json({
			success: true,
			data: image,
		});
	} catch (error) {
		console.error('Image GET API error:', error);
		return NextResponse.json(
			{
				success: false,
				error: 'Internal server error',
				details: error instanceof Error ? error.message : 'Unknown error',
			},
			{ status: 500 }
		);
	}
}

// PATCH /api/images/[id] - Update image metadata
export async function PATCH(
	request: NextRequest,
	{ params }: { params: Promise<{ id: string }> }
) {
	try {
		const { id } = await params;

		if (!id) {
			return NextResponse.json(
				{ error: 'Image ID is required' },
				{ status: 400 }
			);
		}

		const supabase = createClient();

		// Get user session for authentication
		const {
			data: { user },
			error: userError,
		} = await supabase.auth.getUser();

		if (userError || !user) {
			return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
		}

		const body = (await request.json()) as {
			altText?: string;
			tags?: string[];
			metadata?: Record<string, unknown>;
			imageName?: string;
		};
		const { altText, tags, metadata, imageName } = body;

		// Build update object with only provided fields
		const updateData: {
			updated_at: string;
			alt_text?: string;
			tags?: string[];
			metadata?: Record<string, unknown>;
			image_name?: string;
		} = {
			updated_at: new Date().toISOString(),
		};

		if (altText !== undefined) updateData.alt_text = altText;
		if (tags !== undefined) updateData.tags = tags;
		if (metadata !== undefined) updateData.metadata = metadata;
		if (imageName !== undefined) updateData.image_name = imageName;

		const { data: image, error } = await supabase
			.from('organization_images')
			.update(updateData)
			.eq('id', id)
			.neq('deleting', true) // Ensure we don't update images marked for deletion
			.select()
			.single();

		if (error) {
			if (error.code === 'PGRST116') {
				return NextResponse.json({ error: 'Image not found' }, { status: 404 });
			}
			console.error('Error updating image:', error);
			return NextResponse.json(
				{ error: 'Failed to update image', details: error.message },
				{ status: 500 }
			);
		}

		return NextResponse.json({
			success: true,
			data: image,
		});
	} catch (error) {
		console.error('Image PATCH API error:', error);
		return NextResponse.json(
			{
				success: false,
				error: 'Internal server error',
				details: error instanceof Error ? error.message : 'Unknown error',
			},
			{ status: 500 }
		);
	}
}

// DELETE /api/images/[id] - Mark image for deletion
export async function DELETE(
	request: NextRequest,
	{ params }: { params: Promise<{ id: string }> }
) {
	try {
		const { id } = await params;

		if (!id) {
			return NextResponse.json(
				{ error: 'Image ID is required' },
				{ status: 400 }
			);
		}

		const supabase = createClient();

		// Get user session for authentication
		const {
			data: { user },
			error: userError,
		} = await supabase.auth.getUser();

		if (userError || !user) {
			return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
		}

		// Mark image as deleting instead of hard delete
		const { data: image, error } = await supabase
			.from('organization_images')
			.update({
				deleting: true,
				updated_at: new Date().toISOString(),
			})
			.eq('id', id)
			.neq('deleting', true) // Only update if not already marked for deletion
			.select()
			.single();

		if (error) {
			if (error.code === 'PGRST116') {
				return NextResponse.json({ error: 'Image not found' }, { status: 404 });
			}
			console.error('Error marking image for deletion:', error);
			return NextResponse.json(
				{ error: 'Failed to delete image', details: error.message },
				{ status: 500 }
			);
		}

		return NextResponse.json({
			success: true,
			message: 'Image marked for deletion',
			data: image,
		});
	} catch (error) {
		console.error('Image DELETE API error:', error);
		return NextResponse.json(
			{
				success: false,
				error: 'Internal server error',
				details: error instanceof Error ? error.message : 'Unknown error',
			},
			{ status: 500 }
		);
	}
}
