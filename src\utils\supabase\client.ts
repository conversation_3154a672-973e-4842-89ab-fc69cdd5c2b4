import { createBrowserClient } from "@supabase/ssr";

export function createClient() {
	const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
	const anonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY;

	if (!supabaseUrl) {
		throw new Error(
			"Supabase URL not configured. Please set NEXT_PUBLIC_SUPABASE_URL environment variable"
		);
	}

	if (!anonKey) {
		throw new Error(
			"Supabase Anon Key not configured. Please set NEXT_PUBLIC_SUPABASE_ANON_KEY environment variable"
		);
	}

	return createBrowserClient(supabaseUrl, anonKey);
}
