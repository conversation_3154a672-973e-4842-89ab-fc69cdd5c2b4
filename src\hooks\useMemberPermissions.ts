import { useMemo } from "react";
import { useUser } from "@/contexts/UserContext/UserContextProvider";
import type { ProjectMember, MemberRole } from "@/types/members";

export const useMemberPermissions = (currentUserRole: MemberRole | null) => {
	const { user } = useUser();

	const canManageMember = useMemo(() => {
		return (member: ProjectMember) => {
			if (!user || !currentUserRole) return false;
			if (member.user_id === user.id) return false;
			if (member.is_creator) return false;
			if (currentUserRole === "Editor") return false;

			if (currentUserRole === "Admin") {
				return member.role === "Editor" || member.role === "Admin";
			}

			if (currentUserRole === "Owner") {
				return !member.is_creator;
			}

			return false;
		};
	}, [user, currentUserRole]);

	const getAvailableRoleActions = useMemo(() => {
		return (member: ProjectMember) => {
			if (!currentUserRole || member.is_creator) return [];

			const actions = [];

			if (currentUserRole === "Owner") {
				if (member.role !== "Owner") {
					actions.push({ label: "Make Owner", role: "Owner" as MemberRole });
				}
				if (member.role !== "Admin") {
					actions.push({
						label: member.role === "Owner" ? "Demote to Admin" : "Make Admin",
						role: "Admin" as MemberRole,
					});
				}
				if (member.role !== "Editor") {
					actions.push({
						label:
							member.role === "Owner" || member.role === "Admin"
								? "Demote to Editor"
								: "Make Editor",
						role: "Editor" as MemberRole,
					});
				}
			} else if (currentUserRole === "Admin") {
				if (member.role === "Editor") {
					actions.push({
						label: "Promote to Admin",
						role: "Admin" as MemberRole,
					});
				}
				if (member.role === "Admin") {
					actions.push({
						label: "Demote to Editor",
						role: "Editor" as MemberRole,
					});
				}
			}

			return actions;
		};
	}, [currentUserRole]);

	const canInviteMembers = useMemo(() => {
		return currentUserRole === "Owner" || currentUserRole === "Admin";
	}, [currentUserRole]);

	return {
		canManageMember,
		getAvailableRoleActions,
		canInviteMembers,
	};
};
