import { NextRequest, NextResponse } from "next/server";
import <PERSON><PERSON> from "stripe";
import { createClient } from "@/utils/supabase/server";

export const runtime = 'edge';

const stripe = new Stripe(process.env.STRIPE_SECRET_KEY!, {
	apiVersion: "2025-05-28.basil",
});

export async function GET(request: NextRequest) {
	try {
		if (!process.env.STRIPE_SECRET_KEY) {
			throw new Error("STRIPE_SECRET_KEY environment variable is not set");
		}

		const { searchParams } = new URL(request.url);
		const projectId = searchParams.get("projectId");

		if (!projectId) {
			console.error("❌ [CUSTOMER_API] Project ID não fornecido");
			return NextResponse.json(
				{ error: "Project ID is required" },
				{ status: 400 }
			);
		}

		// Get user from Supabase
		const supabase = await createClient();
		const {
			data: { user },
			error: userError,
		} = await supabase.auth.getUser();

		if (userError || !user) {
			console.error("❌ [CUSTOMER_API] Usuário não autorizado:", {
				userError: userError?.message,
				hasUser: !!user,
				projectId,
			});
			return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
		}

		// First check if there's a customer record in our database for this project
		try {
			const { data: dbCustomerData, error: dbError } = await supabase.rpc(
				"get_customer_data_by_project",
				{
					p_project_id: projectId,
					p_recent_only: true,
				}
			);

			if (dbError) {
				console.error("❌ [CUSTOMER_API] Erro na busca do customer:", {
					error: dbError instanceof Error ? dbError.message : dbError,
					stack: dbError instanceof Error ? dbError.stack : undefined,
					timestamp: new Date().toISOString(),
				});
			}

			// If we have customer data in our database, use it as the base
			if (dbCustomerData && dbCustomerData.customer) {
				const dbCustomer = dbCustomerData.customer;

				// Get additional data from database - filter only essential data
				const lastPayment =
					dbCustomerData.payments?.length > 0
						? {
								payment_method_brand:
									dbCustomerData.payments[0].payment_method_brand,
								payment_method_last4:
									dbCustomerData.payments[0].payment_method_last4,
								currency: dbCustomerData.payments[0].currency,
								amount: dbCustomerData.payments[0].amount,
						  }
						: null;

				const lastInvoice =
					dbCustomerData.invoices?.length > 0
						? {
								status: dbCustomerData.invoices[0].status,
								amount_due: dbCustomerData.invoices[0].amount_due,
								currency: dbCustomerData.invoices[0].currency,
						  }
						: null;

				// Determine billing interval using only the customer's price_id
				let billingInterval = "Monthly";
				if (dbCustomer.price_id && dbCustomerData.prices?.length > 0) {
					const customerPrice = dbCustomerData.prices.find(
						(price: { id: string; interval?: string }) =>
							price.id === dbCustomer.price_id
					);
					if (customerPrice && customerPrice.interval) {
						billingInterval =
							customerPrice.interval === "year" ? "Annual" : "Monthly";
					}
				}

				// Format response data using database information - only essential fields
				const customerData = {
					id: dbCustomer.id,
					project_id: parseInt(projectId),
					email: dbCustomer.email,
					name: dbCustomer.name,
					status: dbCustomer.status || "inactive",
					subscription_id: dbCustomer.subscription_id,
					price_id: dbCustomer.price_id,
					current_period_start: dbCustomer.current_period_start,
					current_period_end: dbCustomer.current_period_end,
					cancel_at_period_end: dbCustomer.cancel_at_period_end,
					billingInterval,
					amount:
						lastPayment?.amount ||
						(typeof dbCustomer.amount === "number"
							? dbCustomer.amount
							: undefined),
					lastPayment,
					lastInvoice,
				};

				return NextResponse.json({ customer: customerData });
			}
		} catch (rpcError) {
			console.error("❌ [CUSTOMER_API] Erro na busca do customer:", {
				error: rpcError instanceof Error ? rpcError.message : rpcError,
				stack: rpcError instanceof Error ? rpcError.stack : undefined,
				timestamp: new Date().toISOString(),
			});
			// Continue with Stripe-only approach as fallback
		}

		// Fallback to Stripe-only approach if no database function or data found

		// Find customer by email in Stripe
		const customers = await stripe.customers.list({
			email: user.email,
			limit: 1,
		});

		const stripeCustomer = customers.data[0] || null;
		let subscription: Stripe.Subscription | null = null;

		if (stripeCustomer) {
			// Get the most recent subscription for this customer
			const subscriptions = await stripe.subscriptions.list({
				customer: stripeCustomer.id,
				status: "all",
				limit: 1,
			});

			subscription = subscriptions.data[0] || null;
		}

		// Format response data from Stripe - only essential fields
		const customerData = stripeCustomer
			? {
					id: stripeCustomer.id,
					project_id: parseInt(projectId),
					email: stripeCustomer.email,
					name: stripeCustomer.name,
					status: subscription?.status || "inactive",
					subscription_id: subscription?.id,
					price_id: subscription?.items.data[0]?.price.id,
					current_period_start: undefined,
					current_period_end: undefined,
					cancel_at_period_end: subscription?.cancel_at_period_end,
					created: stripeCustomer.created
						? new Date(stripeCustomer.created * 1000).toISOString()
						: undefined,
					billingInterval:
						subscription?.items.data[0]?.price.recurring?.interval === "year"
							? "Annual"
							: "Monthly",
					amount: subscription?.items.data[0]?.price.unit_amount,
			  }
			: null;

		return NextResponse.json({ customer: customerData });
	} catch (error) {
		console.error("❌ [CUSTOMER_API] Erro na busca do customer:", {
			error: error instanceof Error ? error.message : error,
			stack: error instanceof Error ? error.stack : undefined,
			timestamp: new Date().toISOString(),
		});

		const errorMessage =
			error instanceof Error ? error.message : "Unknown error";

		return NextResponse.json(
			{
				error: "Failed to fetch customer",
				details: errorMessage,
			},
			{ status: 500 }
		);
	}
}
