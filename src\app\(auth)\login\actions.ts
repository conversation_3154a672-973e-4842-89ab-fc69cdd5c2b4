"use server";

import { createClient } from "@/utils/supabase/server";
import { Provider } from "@supabase/supabase-js";
import { getURL } from "@/utils/helpers";

/**
 * Logs in a user with their email and password using Supabase.
 * If successful, it redirects the user to the "/onboarding" page.
 * Throws an error if the login fails or if the user is not found.
 *
 * @param formData - A FormData object containing the user's email and password.
 * @returns A promise that resolves to a redirect response or throws an error.
 */
export async function emailLogin(formData: FormData) {
  const supabase = createClient();

  const emailValue = formData.get("email");
  const passwordValue = formData.get("password");

  if (typeof emailValue !== "string" || emailValue.trim() === "") {
    throw new Error("Email is required.");
  }

  if (typeof passwordValue !== "string" || passwordValue === "") {
    throw new Error("Password is required.");
  }

  const email = emailValue.trim();
  const password = passwordValue;

  const { error: signInError } = await supabase.auth.signInWithPassword({
    email,
    password,
  });

  if (signInError) {
    if (signInError.code === "email_not_confirmed") {
      return {
        success: false,
        error: {
          type: "email_not_confirmed",
          message:
            "Email not confirmed. Please check your email to confirm your account.",
        },
      };
    }
    return {
      success: false,
      error: { message: "Credentials are incorrect" },
    };
  }

  return { success: true };
}

/**
 * Signs in a user with an OAuth provider using Supabase.
 *
 * @param provider - The OAuth provider to use.
 * @returns A promise that resolves to the OAuth provider's login page URL.
 */
export async function oAuthSignIn(provider: Provider) {
  const supabase = createClient();

  const redirectURL = getURL("/auth/callback");

  const { data, error } = await supabase.auth.signInWithOAuth({
    provider,
    options: {
      redirectTo: redirectURL,
      scopes: "read:user",
    },
  });

  if (error) {
    throw new Error(error.message);
  }

  return data;
}

/**
 * Signs in a user with SAML using Supabase.
 *
 * @param email - The user's email address.
 * @returns A promise that resolves to the SAML provider's login page URL.
 */
export async function samlSignIn(email: string) {
  // Extract domain from email
  const domainMatch = email.match(/@(.+)$/);
  if (!domainMatch || !domainMatch[1]) {
    throw new Error("Invalid email format");
  }

  const domain = domainMatch[1];
  const supabase = createClient();
  const redirectURL = getURL("/auth/callback");

  try {
    const { data, error } = await supabase.auth.signInWithSSO({
      domain,
      options: {
        redirectTo: redirectURL,
      },
    });

    if (error) {
      // Verify if it's the specific error sso_provider_not_found
      if (error.code === "sso_provider_not_found") {
        return {
          success: false,
          error: {
            code: "sso_provider_not_found",
            message:
              'This feature is only available for Enterprise plan customers. <a href="https://writedocs.io/book-a-demo/" target="_blank" class="text-blue-600 underline hover:text-blue-800">Click here</a> for more information about upgrading your plan.',
          },
        };
      }

      // For other errors, keep the original behavior
      throw new Error(error.message);
    }

    if (!data?.url) {
      throw new Error("No SSO provider available for this domain");
    }

    // Redirect to the SSO provider's login page
    return { success: true, url: data.url };
  } catch (error: unknown) {
    if (error instanceof Error) {
      throw new Error(error.message);
    }
    throw new Error("Error initiating SSO login");
  }
}
