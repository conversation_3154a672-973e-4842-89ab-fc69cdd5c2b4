export interface DocusaurusPageGroup {
  page?: string;
  subpages: (string | DocusaurusPageGroup)[];
  groupName: string;
}

export type DocusaurusPageItem = string | DocusaurusPageGroup;

export interface DocusaurusCategory {
  pages: DocusaurusPageItem[];
  categoryName: string;
}

export interface DocusaurusSidebar {
  categories: DocusaurusCategory[];
  sidebarRef: string;
}

export interface NavbarItem {
  label: string;
  icon?: string;
  dropdown?: NavbarItem[];
  sidebarRef?: string;
  link?: string;
}

export interface ConfigJson {
  // Propriedades obrigatórias do schema
  websiteName?: string;
  description?: string;
  homepage?: string;
  images?: {
    logo: string;
    favicon: string;
    darkLogo?: string;
    metadata?: string;
    background?: string;
    darkBackground?: string;
  };
  styles?: {
    mainColor: string;
    darkModeMainColor?: string;
    navbarColor?: string;
    navbarDarkModeColor?: string;
    backgroundDarkModeColor?: string;
    logoSize?: string;
    pagination?: boolean;
  };
  navbar: NavbarItem[];
  sidebars: DocusaurusSidebar[];

  // Propriedades opcionais do schema
  hideWatermark?: boolean;
  colorMode?: {
    default: string;
    switchOff: boolean;
  };
  apiFiles?: (string | { file: string; outputDir: string })[];
  apiOptions?: {
    hideSendButton?: boolean;
  };
  translatedApiFiles?: {
    es?: unknown[];
    en?: unknown[];
    fr?: unknown[];
    de?: unknown[];
    pt?: unknown[];
    it?: unknown[];
    ja?: unknown[];
  };
  codeLanguages?: string[];
  changelog?: boolean;
  externalLinks?: {
    link: string;
    name: string;
    style?: string;
  }[];
  integrations?: {
    gtag?: string;
    posthog?: {
      api_key: string;
      api_host: string;
    };
    askAi?: {
      freshdesk?: string;
      helpscout?: string;
      hubspot?: string;
      intercom?: string;
      zendesk?: string;
    };
  };
  footer?: {
    copyright: string;
  };
  protected?: boolean | string[];

  // Permitir propriedades adicionais
  [key: string]: unknown;
}
