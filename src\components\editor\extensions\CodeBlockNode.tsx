import { Node, mergeAttributes } from "@tiptap/core";
import { Node<PERSON>iewWrapper, ReactNodeViewRenderer } from "@tiptap/react";
import React, { useState, useCallback, useEffect, useRef } from "react";
import { createPortal } from "react-dom";
import { ChevronDown, Copy, Check } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import type { NodeViewProps } from "@tiptap/react";
import hljs from "highlight.js/lib/core";

// Import only the languages we need to keep bundle size small
import javascript from "highlight.js/lib/languages/javascript";
import typescript from "highlight.js/lib/languages/typescript";
import python from "highlight.js/lib/languages/python";
import java from "highlight.js/lib/languages/java";
import xml from "highlight.js/lib/languages/xml"; // for HTML
import css from "highlight.js/lib/languages/css";
import json from "highlight.js/lib/languages/json";
import sql from "highlight.js/lib/languages/sql";
import bash from "highlight.js/lib/languages/bash";
import php from "highlight.js/lib/languages/php";
import go from "highlight.js/lib/languages/go";
import rust from "highlight.js/lib/languages/rust";
import cpp from "highlight.js/lib/languages/cpp";
import csharp from "highlight.js/lib/languages/csharp";
import yaml from "highlight.js/lib/languages/yaml";
import markdown from "highlight.js/lib/languages/markdown";
import dockerfile from "highlight.js/lib/languages/dockerfile";

// Register languages
hljs.registerLanguage("javascript", javascript);
hljs.registerLanguage("typescript", typescript);
hljs.registerLanguage("python", python);
hljs.registerLanguage("java", java);
hljs.registerLanguage("html", xml);
hljs.registerLanguage("xml", xml);
hljs.registerLanguage("css", css);
hljs.registerLanguage("json", json);
hljs.registerLanguage("sql", sql);
hljs.registerLanguage("bash", bash);
hljs.registerLanguage("php", php);
hljs.registerLanguage("go", go);
hljs.registerLanguage("rust", rust);
hljs.registerLanguage("cpp", cpp);
hljs.registerLanguage("csharp", csharp);
hljs.registerLanguage("yaml", yaml);
hljs.registerLanguage("markdown", markdown);
hljs.registerLanguage("docker", dockerfile);

// Linguagens suportadas
const LANGUAGES = [
  { value: "javascript", label: "JavaScript", color: "#f7df1e" },
  { value: "typescript", label: "TypeScript", color: "#3178c6" },
  { value: "python", label: "Python", color: "#3776ab" },
  { value: "java", label: "Java", color: "#ed8b00" },
  { value: "html", label: "HTML", color: "#e34f26" },
  { value: "css", label: "CSS", color: "#1572b6" },
  { value: "json", label: "JSON", color: "#000000" },
  { value: "sql", label: "SQL", color: "#336791" },
  { value: "bash", label: "Bash", color: "#4eaa25" },
  { value: "php", label: "PHP", color: "#777bb4" },
  { value: "go", label: "Go", color: "#00add8" },
  { value: "rust", label: "Rust", color: "#000000" },
  { value: "cpp", label: "C++", color: "#00599c" },
  { value: "csharp", label: "C#", color: "#239120" },
  { value: "xml", label: "XML", color: "#ff6600" },
  { value: "yaml", label: "YAML", color: "#cb171e" },
  { value: "markdown", label: "Markdown", color: "#083fa1" },
  { value: "docker", label: "Dockerfile", color: "#2496ed" },
  { value: "text", label: "Plain Text", color: "#6b7280" },
];

// Componente React para o code block
const CodeBlockComponent: React.FC<NodeViewProps> = ({
  node,
  updateAttributes,
  selected,
}) => {
  const [isDropdownOpen, setIsDropdownOpen] = useState(false);
  const [copied, setCopied] = useState(false);
  const [code, setCode] = useState(node.attrs.code || "");
  const [highlightedCode, setHighlightedCode] = useState("");
  const [dropdownPosition, setDropdownPosition] = useState({ top: 0, left: 0 });
  const preRef = useRef<HTMLPreElement>(null);
  const textareaRef = useRef<HTMLTextAreaElement>(null);
  const dropdownButtonRef = useRef<HTMLButtonElement>(null);

  const currentLanguage =
    LANGUAGES.find((lang) => lang.value === node.attrs.language) ||
    LANGUAGES[LANGUAGES.length - 1];

  // Helper function to resize textarea
  const resizeTextarea = useCallback(() => {
    if (textareaRef.current) {
      const textarea = textareaRef.current;
      textarea.style.height = "auto";
      const contentHeight = textarea.scrollHeight;
      const minHeight = 128;
      const newHeight = Math.max(minHeight, contentHeight);
      textarea.style.height = `${newHeight}px`;

      if (preRef.current) {
        preRef.current.style.height = `${newHeight}px`;
      }
    }
  }, []);

  // Sync local state with node attributes when they change
  useEffect(() => {
    if (node.attrs.code !== code) {
      setCode(node.attrs.code || "");
      // Resize after state update
      setTimeout(resizeTextarea, 10);
    }
  }, [node.attrs.code, code, resizeTextarea]);

  // Initial resize on mount and when code is first loaded
  useEffect(() => {
    if (code) {
      setTimeout(resizeTextarea, 50);
    }
  }, [code, resizeTextarea]);

  // Auto-resize textarea based on content changes
  useEffect(() => {
    resizeTextarea();
  }, [code, resizeTextarea]);

  // Calculate dropdown position when opened
  useEffect(() => {
    if (isDropdownOpen && dropdownButtonRef.current) {
      const rect = dropdownButtonRef.current.getBoundingClientRect();
      setDropdownPosition({
        top: rect.bottom + window.scrollY + 4,
        left: rect.left + window.scrollX,
      });
    }
  }, [isDropdownOpen]);

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        dropdownButtonRef.current &&
        !dropdownButtonRef.current.contains(event.target as HTMLElement)
      ) {
        // Check if the click is on the dropdown itself
        const dropdownElement = document.querySelector(".language-dropdown");
        if (
          !dropdownElement ||
          !dropdownElement.contains(event.target as HTMLElement)
        ) {
          setIsDropdownOpen(false);
        }
      }
    };

    if (isDropdownOpen) {
      document.addEventListener("mousedown", handleClickOutside);
      return () =>
        document.removeEventListener("mousedown", handleClickOutside);
    }
  }, [isDropdownOpen]);

  // Apply syntax highlighting when code or language changes
  useEffect(() => {
    if (code && currentLanguage.value !== "text") {
      try {
        const highlighted = hljs.highlight(code, {
          language: currentLanguage.value,
        }).value;
        setHighlightedCode(highlighted);
      } catch {
        // If highlighting fails, fallback to plain text with proper escaping
        setHighlightedCode(
          code
            .replace(/&/g, "&amp;")
            .replace(/</g, "&lt;")
            .replace(/>/g, "&gt;")
        );
      }
    } else {
      // For plain text, escape HTML chars but don't highlight
      setHighlightedCode(
        code.replace(/&/g, "&amp;").replace(/</g, "&lt;").replace(/>/g, "&gt;")
      );
    }
  }, [code, currentLanguage.value]);

  const handleLanguageChange = useCallback(
    (language: string) => {
      updateAttributes({ language });
      setIsDropdownOpen(false);
    },
    [updateAttributes]
  );

  const handleCodeChange = useCallback(
    (newCode: string) => {
      setCode(newCode);
      updateAttributes({ code: newCode });
    },
    [updateAttributes]
  );

  const handleCopy = useCallback(async () => {
    try {
      await navigator.clipboard.writeText(code);
      setCopied(true);
      setTimeout(() => setCopied(false), 2000);
    } catch (err) {
      console.error("Failed to copy code:", err);
    }
  }, [code]);

  return (
    <NodeViewWrapper
      className="code-block-node"
      as="div"
      data-drag-handle=""
      contentEditable={false}
    >
      <div
        className={`
        my-4 bg-gray-900 dark:bg-gray-950 rounded-lg overflow-hidden
        border border-gray-700 dark:border-gray-800
        ${selected ? "ring-2 ring-blue-400/50" : ""}
        transition-all duration-300 group relative
      `}
      >
        {/* Header com seletor de linguagem */}
        <div className="flex items-center justify-between px-4 py-3 bg-gray-800 dark:bg-gray-900 border-b border-gray-700 dark:border-gray-800">
          <div className="flex items-center space-x-2">
            <div className="relative">
              <button
                ref={dropdownButtonRef}
                onClick={() => setIsDropdownOpen(!isDropdownOpen)}
                className="flex items-center space-x-2 px-3 py-1.5 text-sm font-medium text-gray-300 hover:text-white bg-gray-700 hover:bg-gray-600 rounded-md transition-colors duration-200"
              >
                <div
                  className="w-3 h-3 rounded-full"
                  style={{ backgroundColor: currentLanguage.color }}
                />
                <span>{currentLanguage.label}</span>
                <ChevronDown
                  className={`w-3 h-3 transition-transform duration-200 ${
                    isDropdownOpen ? "rotate-180" : ""
                  }`}
                />
              </button>
            </div>
          </div>

          {/* Botão de copiar - só aparece no hover */}
          <Button
            variant="ghost"
            size="sm"
            onClick={handleCopy}
            className="h-7 px-3 text-xs text-gray-400 hover:text-white hover:bg-gray-700 flex-shrink-0 transition-all duration-200 opacity-0 group-hover:opacity-100"
          >
            {copied ? (
              <>
                <Check className="w-3 h-3 mr-1.5" />
                Copied!
              </>
            ) : (
              <>
                <Copy className="w-3 h-3 mr-1.5" />
                Copy
              </>
            )}
          </Button>
        </div>

        {/* Área do código */}
        <div className="relative bg-gray-900 dark:bg-gray-950">
          {/* Highlighted code overlay */}
          <pre
            ref={preRef}
            className="absolute inset-0 p-4 bg-transparent text-sm font-mono pointer-events-none whitespace-pre-wrap break-words z-10"
            style={{
              fontFamily: "'Fira Code', 'JetBrains Mono', Consolas, monospace",
              lineHeight: "1.6",
              fontSize: "14px",
              color: "#f8f8f2",
              background: "transparent",
              margin: 0,
              border: "none",
              outline: "none",
              padding: "16px",
              boxSizing: "border-box",
              top: 0,
              left: 0,
              right: 0,
              bottom: 0,
            }}
            dangerouslySetInnerHTML={{ __html: highlightedCode }}
          />

          {/* Transparent textarea for editing */}
          <textarea
            ref={textareaRef}
            value={code}
            onChange={(e) => handleCodeChange(e.target.value)}
            placeholder={`Enter your ${currentLanguage.label} code here...`}
            className="relative w-full min-h-32 bg-transparent text-transparent font-mono text-sm resize-none outline-none border-none placeholder-gray-500 caret-white z-20 overflow-hidden"
            style={{
              fontFamily: "'Fira Code', 'JetBrains Mono', Consolas, monospace",
              lineHeight: "1.6",
              fontSize: "14px",
              tabSize: 2,
              background: "transparent",
              margin: 0,
              padding: "16px",
              boxSizing: "border-box",
              border: "none",
              outline: "none",
            }}
          />
        </div>
      </div>

      {/* Dropdown de linguagens renderizado via portal */}
      {isDropdownOpen &&
        createPortal(
          <div
            className="fixed w-48 bg-gray-800 border border-gray-700 rounded-md shadow-lg z-[9999] max-h-64 overflow-y-auto language-dropdown"
            style={{
              top: `${dropdownPosition.top}px`,
              left: `${dropdownPosition.left}px`,
            }}
          >
            {LANGUAGES.map((language) => (
              <button
                key={language.value}
                onClick={(e) => {
                  e.stopPropagation();
                  handleLanguageChange(language.value);
                }}
                className={`
                  w-full flex items-center space-x-2 px-3 py-2 text-sm text-left
                  ${
                    language.value === currentLanguage.value
                      ? "bg-gray-700 text-white"
                      : "text-gray-300 hover:bg-gray-700 hover:text-white"
                  }
                  transition-colors duration-200
                `}
              >
                <div
                  className="w-3 h-3 rounded-full"
                  style={{ backgroundColor: language.color }}
                />
                <span>{language.label}</span>
              </button>
            ))}
          </div>,
          document.body
        )}
    </NodeViewWrapper>
  );
};

// Definição da extensão Tiptap
export const CodeBlockNode = Node.create({
  name: "codeBlock",

  group: "block",

  content: "",

  marks: "",

  code: true,

  defining: true,

  addAttributes() {
    return {
      language: {
        default: "text",
        parseHTML: (element) => element.getAttribute("data-language"),
        renderHTML: (attributes) => {
          if (!attributes.language) return {};
          return { "data-language": attributes.language };
        },
      },
      code: {
        default: "",
        parseHTML: (element) => element.textContent || "",
        renderHTML: () => {
          return {};
        },
      },
    };
  },

  parseHTML() {
    return [
      {
        tag: "pre",
        preserveWhitespace: "full",
        getAttrs: (element) => {
          const codeElement = element.querySelector("code");
          const className = codeElement?.className || "";
          const language = className.match(/language-(\w+)/)?.[1] || "text";

          return {
            language,
            code: element.textContent || "",
          };
        },
      },
    ];
  },

  renderHTML({ HTMLAttributes, node }) {
    const code = node?.attrs?.code || "";
    return [
      "pre",
      mergeAttributes(HTMLAttributes, { "data-type": "codeBlock" }),
      [
        "code",
        {
          class: `language-${HTMLAttributes["data-language"] || "text"}`,
        },
        code,
      ],
    ];
  },

  addCommands() {
    return {
      setCodeBlock:
        (attributes) =>
        ({ commands }) => {
          return commands.setNode(this.name, attributes);
        },
      toggleCodeBlock:
        (attributes) =>
        ({ commands }) => {
          return commands.toggleNode(this.name, "paragraph", attributes);
        },
    };
  },

  addKeyboardShortcuts() {
    return {
      "Mod-Alt-c": () => this.editor.commands.toggleCodeBlock(),
      "Shift-Ctrl-\\": () => this.editor.commands.toggleCodeBlock(),
    };
  },

  addNodeView() {
    return ReactNodeViewRenderer(CodeBlockComponent);
  },
});

export default CodeBlockNode;
