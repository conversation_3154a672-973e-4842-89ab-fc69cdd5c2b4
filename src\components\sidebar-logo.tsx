import Image from "next/image";
import writedocsIcon from "@/assets/images/writedocsIcon.svg";
import { useSidebar } from "./ui/sidebar";

export function SidebarLogo() {
	const { state } = useSidebar();

	const isCollapsed = state === "collapsed";

	return (
		<div className='pt-4 flex items-center justify-center'>
			<Image
				className={`transition-all duration-300 ease-in-out`}
				src={writedocsIcon}
				alt='Logo'
				width={isCollapsed ? 32 : 40}
				height={isCollapsed ? 32 : 40}
			/>
		</div>
	);
}
