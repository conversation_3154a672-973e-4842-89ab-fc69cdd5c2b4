'use client';

import { useState, useEffect, useCallback } from 'react';
import { useRouter } from 'next/navigation';
import TiptapEditor from '@/components/editor/TiptapEditor';
import { createClient } from '@/utils/supabase/client';
import { Button } from '@/components/ui/button';
import {
	Save,
	Loader2,
	CheckCircle,
	XCircle,
	AlertTriangle,
} from 'lucide-react';
import {
	AlertDialog,
	AlertDialogAction,
	AlertDialogCancel,
	AlertDialogContent,
	AlertDialogDescription,
	AlertDialogFooter,
	AlertDialogHeader,
	AlertDialogTitle,
} from '@/components/ui/alert-dialog';
import { Skeleton } from '@/components/ui/skeleton';
import { useToast } from '@/components/ToastProvider';
import { cn } from '@/lib/utils';

interface ProjectPage {
	id: number;
	project_id: number;
	path: string;
	title: string;
	content?: string | null;
	updated_at: string;
}

interface PageDisplayProps {
	projectId: string;
	pagePath: string; // Caminho completo, ex: "/docs/intro"
}

export default function PageDisplay({ projectId, pagePath }: PageDisplayProps) {
	const [pageData, setPageData] = useState<ProjectPage | null>(null);
	const [initialContent, setInitialContent] = useState<string>('');
	const [editorContent, setEditorContent] = useState<string>('');
	const [isLoadingPage, setIsLoadingPage] = useState(true);
	const [errorLoading, setErrorLoading] = useState<string | null>(null);
	const [isSaving, setIsSaving] = useState(false);
	const [saveStatus, setSaveStatus] = useState<
		'idle' | 'saving' | 'saved' | 'error' | 'unsaved'
	>('idle');
	const [hasUnsavedChanges, setHasUnsavedChanges] = useState(false);
	const [showExitDialog, setShowExitDialog] = useState(false);
	const [pendingNavigation, setPendingNavigation] = useState<string | null>(
		null
	);

	const router = useRouter();
	const supabase = createClient();
	const { addToast } = useToast();

	// Interceptar tentativas de saída da página
	useEffect(() => {
		const handleBeforeUnload = (e: BeforeUnloadEvent) => {
			if (hasUnsavedChanges) {
				e.preventDefault();
				e.returnValue =
					'You have unsaved changes. Do you want to exit without saving?';
				return 'You have unsaved changes. Do you want to exit without saving?';
			}
		};

		window.addEventListener('beforeunload', handleBeforeUnload);
		return () => window.removeEventListener('beforeunload', handleBeforeUnload);
	}, [hasUnsavedChanges]);

	// Interceptar mudanças de página via Next.js router
	useEffect(() => {
		// Note: Next.js App Router não tem eventos de router como o Pages Router
		// Vamos interceptar cliques em links
		const handleLinkClick = (e: MouseEvent) => {
			const target = e.target as HTMLElement;
			const link = target.closest('a[href]') as HTMLAnchorElement;

			if (link && hasUnsavedChanges) {
				const href = link.getAttribute('href');
				if (href && href !== window.location.pathname) {
					e.preventDefault();
					setPendingNavigation(href);
					setShowExitDialog(true);
				}
			}
		};

		document.addEventListener('click', handleLinkClick, true);
		return () => document.removeEventListener('click', handleLinkClick, true);
	}, [hasUnsavedChanges]);

	const handleSaveAndExit = async () => {
		if (pendingNavigation) {
			await handleSaveChanges();
			setShowExitDialog(false);
			setPendingNavigation(null);
			setHasUnsavedChanges(false);
			router.push(pendingNavigation);
		}
	};

	const handleExitWithoutSaving = () => {
		if (pendingNavigation) {
			setShowExitDialog(false);
			setHasUnsavedChanges(false);
			setPendingNavigation(null);
			router.push(pendingNavigation);
		}
	};

	const handleCancelExit = () => {
		setShowExitDialog(false);
		setPendingNavigation(null);
	};

	const fetchPageData = useCallback(async () => {
		if (!projectId || !pagePath) {
			setIsLoadingPage(false);
			setErrorLoading('Project ID or page path not provided.');
			setPageData(null); // Clear page data if context is missing
			const errorContent = '<p>Configuration error.</p>';
			setEditorContent(errorContent);
			setInitialContent(errorContent);
			return;
		}

		// For root path, immediately show welcome screen without loading
		if (pagePath === '/') {
			setIsLoadingPage(false);
			setErrorLoading(null);
			setPageData(null);
			return;
		}

		setIsLoadingPage(true);
		setErrorLoading(null);

		// Normalize pagePath before querying
		let queryPath = pagePath;
		try {
			queryPath = decodeURIComponent(queryPath);
		} catch (e) {
			console.error('Error decoding page path:', e);
			// Potentially handle error, or proceed with original path if decoding fails
		}
		if (queryPath.startsWith('/')) {
			queryPath = queryPath.substring(1);
		}

		const { data, error } = await supabase
			.from('project_pages')
			.select('*')
			.eq('project_id', projectId)
			.ilike('path', queryPath) // .ilike for case-insensitive search
			.single();

		if (error && error.code !== 'PGRST116') {
			// PGRST116 means no rows found, which is not an error for us here
			console.error('Error fetching page data:', error.message);
			setErrorLoading('Error loading page. ' + error.message);
			setPageData(null);
			const defaultErrorContent = '<p>Error loading content.</p>';
			setEditorContent(defaultErrorContent);
			setInitialContent(defaultErrorContent);
			setHasUnsavedChanges(false);
			setSaveStatus('idle');
		} else if (data) {
			setPageData(data as ProjectPage);
			const content =
				data.content || '<p>This page has no content yet. Start writing!</p>';
			setEditorContent(content);
			setInitialContent(content);
			setHasUnsavedChanges(false); // Reset for the new page
			setSaveStatus('idle'); // Reset for the new page
		} else {
			// No data and no error (PGRST116 or other non-error scenario)
			setErrorLoading(null); // Explicitly clear any previous loading error
			setPageData(null); // Page not found
			const notFoundContent =
				'<p>Page not found. Select an existing page or create a new one.</p>';
			setEditorContent(notFoundContent);
			setInitialContent(notFoundContent);
			setHasUnsavedChanges(false);
			setSaveStatus('idle');
		}
		setIsLoadingPage(false);
	}, [projectId, pagePath, supabase]);

	useEffect(() => {
		fetchPageData();
	}, [fetchPageData]);

	const handleContentChange = (newContent: string) => {
		setEditorContent(newContent);
		if (newContent !== initialContent) {
			setHasUnsavedChanges(true);
			setSaveStatus('unsaved');
		} else {
			setHasUnsavedChanges(false);
			// If content matches initial, revert to idle unless it was just saved
			if (saveStatus !== 'saved') setSaveStatus('idle');
		}
	};

	const handleSaveChanges = async () => {
		if (!pageData || !hasUnsavedChanges || isLoadingPage) return; // Don't save if no page or loading

		setIsSaving(true);
		setSaveStatus('saving');

		const { error } = await supabase
			.from('project_pages')
			.update({ content: editorContent, updated_at: new Date().toISOString() })
			.eq('id', pageData.id);

		setIsSaving(false);
		if (error) {
			console.error('Error saving page content:', error.message);
			setSaveStatus('error');
			addToast(
				'Something went wrong. If the problem persists, please contact WriteDocs support.',
				'error',
				'Save Error'
			);
			// Revert to unsaved state after a 2-second delay
			setTimeout(() => {
				setSaveStatus((currentStatus) =>
					currentStatus === 'error' ? 'unsaved' : currentStatus
				);
			}, 2000);
		} else {
			setSaveStatus('saved');
			setHasUnsavedChanges(false);
			setInitialContent(editorContent); // Update initial content to the new saved state
			setPageData((prev) =>
				prev
					? {
							...prev,
							content: editorContent,
							updated_at: new Date().toISOString(),
						}
					: null
			);
			// Revert to idle state after a 2-second delay if no new changes were made
			setTimeout(() => {
				setSaveStatus((currentStatus) =>
					currentStatus === 'saved' ? 'idle' : currentStatus
				);
			}, 2000);
		}
	};

	// The isLoadingPage check is removed from here to keep the editor mounted.
	// Loading state will be handled by a status message.

	if (errorLoading) {
		return (
			<div className='p-6'>
				<p className='text-red-500'>{errorLoading}</p>
			</div>
		);
	}

	// This handles "Page not found" more explicitly after loading has finished and no data was found
	if (!pageData && !isLoadingPage) {
		// Special case for root path - show welcome screen instead of "not found"
		if (pagePath === '/') {
			return (
				<div className='flex items-center justify-center h-full bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-50 dark:from-slate-900 dark:via-slate-800 dark:to-slate-900'>
					<div className='max-w-2xl mx-auto px-8 py-12'>
						<div className='text-center space-y-8'>
							{/* Hero Icon */}
							<div className='relative'>
								<div className='absolute inset-0 bg-gradient-to-r from-blue-500 to-indigo-500 rounded-full blur-xl opacity-20 animate-pulse'></div>
								<div className='relative w-24 h-24 mx-auto bg-gradient-to-r from-blue-500 to-indigo-500 rounded-full flex items-center justify-center shadow-lg'>
									<svg
										className='w-12 h-12 text-white'
										fill='none'
										stroke='currentColor'
										viewBox='0 0 24 24'
									>
										<path
											strokeLinecap='round'
											strokeLinejoin='round'
											strokeWidth={1.5}
											d='M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z'
										/>
									</svg>
								</div>
							</div>

							{/* Main Content */}
							<div className='space-y-6'>
								<div className='space-y-3'>
									<h1 className='text-4xl font-bold bg-gradient-to-r from-slate-900 to-slate-600 dark:from-white dark:to-slate-300 bg-clip-text text-transparent'>
										Welcome to the Editor
									</h1>
									<p className='text-lg text-slate-600 dark:text-slate-400 max-w-lg mx-auto leading-relaxed'>
										Start creating amazing content. Select a page from the
										sidebar to begin editing, or create a new page to start
										writing.
									</p>
								</div>

								{/* Feature Cards */}
								<div className='grid grid-cols-1 md:grid-cols-2 gap-4 mt-8'>
									<div className='bg-white/80 dark:bg-slate-800/80 backdrop-blur-sm rounded-xl p-4 border border-slate-200/50 dark:border-slate-700/50 shadow-sm hover:shadow-md transition-all duration-200'>
										<div className='flex items-center space-x-3'>
											<div className='w-8 h-8 bg-blue-100 dark:bg-blue-900/30 rounded-lg flex items-center justify-center'>
												<svg
													className='w-4 h-4 text-blue-600 dark:text-blue-400'
													fill='none'
													stroke='currentColor'
													viewBox='0 0 24 24'
												>
													<path
														strokeLinecap='round'
														strokeLinejoin='round'
														strokeWidth={2}
														d='M8 7H5a2 2 0 00-2 2v9a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2h-3m-1 0V5a2 2 0 00-2-2H9a2 2 0 00-2 2v2m1 0h4'
													/>
												</svg>
											</div>
											<div className='text-left'>
												<p className='font-medium text-slate-900 dark:text-slate-100 text-sm'>
													Drag & Drop
												</p>
												<p className='text-xs text-slate-500 dark:text-slate-400'>
													Organize with ease
												</p>
											</div>
										</div>
									</div>

									<div className='bg-white/80 dark:bg-slate-800/80 backdrop-blur-sm rounded-xl p-4 border border-slate-200/50 dark:border-slate-700/50 shadow-sm hover:shadow-md transition-all duration-200'>
										<div className='flex items-center space-x-3'>
											<div className='w-8 h-8 bg-purple-100 dark:bg-purple-900/30 rounded-lg flex items-center justify-center'>
												<svg
													className='w-4 h-4 text-purple-600 dark:text-purple-400'
													fill='none'
													stroke='currentColor'
													viewBox='0 0 24 24'
												>
													<path
														strokeLinecap='round'
														strokeLinejoin='round'
														strokeWidth={2}
														d='M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z'
													/>
												</svg>
											</div>
											<div className='text-left'>
												<p className='font-medium text-slate-900 dark:text-slate-100 text-sm'>
													Rich Editor
												</p>
												<p className='text-xs text-slate-500 dark:text-slate-400'>
													Full formatting support
												</p>
											</div>
										</div>
									</div>
								</div>

								{/* Quick Actions */}
								<div className='pt-4'>
									<p className='text-sm text-slate-500 dark:text-slate-400 mb-3'>
										Quick tip
									</p>
									<div className='bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-blue-900/20 dark:to-indigo-900/20 rounded-lg p-4 border border-blue-200/50 dark:border-blue-800/50'>
										<p className='text-sm text-slate-700 dark:text-slate-300'>
											<span className='font-medium'>Right-click</span> on any
											folder in the sidebar to create new pages, categories, or
											groups.
										</p>
									</div>
								</div>
							</div>
						</div>
					</div>
				</div>
			);
		}

		// For other paths that don't exist, show the not found message
		return (
			<div className='p-6'>
				<p>Page not found. Select a page from the sidebar.</p>
				<p className='text-sm text-gray-400'>Path: {pagePath}</p>
			</div>
		);
	}

	return (
		<div className='p-6 flex flex-col h-full bg-gradient-to-br from-slate-50 via-blue-50/30 to-indigo-50/50 dark:from-slate-900 dark:via-slate-800/50 dark:to-slate-900'>
			<div className='flex justify-between items-center mb-5'>
				<h1 className='text-2xl font-semibold text-gray-800 dark:text-gray-100 h-8'>
					{isLoadingPage ? (
						<Skeleton className='h-7 w-64' />
					) : (
						pageData?.title || 'Page'
					)}
				</h1>
				<div className='flex items-center space-x-3'>
					<Button
						onClick={handleSaveChanges}
						disabled={
							!hasUnsavedChanges || isSaving || isLoadingPage || !pageData
						}
						size='sm'
						className={cn(
							'relative w-36 h-9 flex items-center justify-center gap-2 text-white rounded-lg shadow-md hover:shadow-lg focus:ring-4 transition-all duration-300 disabled:opacity-60 disabled:shadow-none disabled:cursor-not-allowed',
							{
								'bg-gradient-to-r from-blue-500 to-indigo-500 hover:from-blue-600 hover:to-indigo-600 focus:ring-blue-300/50 dark:focus:ring-blue-800/50':
									saveStatus !== 'saved' && saveStatus !== 'error',
								'bg-gradient-to-r from-green-500 to-green-600 hover:from-green-600 hover:to-green-700 focus:ring-green-300/50 dark:focus:ring-green-800/50':
									saveStatus === 'saved',
								'bg-gradient-to-r from-red-500 to-red-600 hover:from-red-600 hover:to-red-700 focus:ring-red-300/50 dark:focus:ring-red-800/50':
									saveStatus === 'error',
							}
						)}
					>
						<span
							className={cn(
								'absolute inset-0 flex items-center justify-center gap-2 transition-opacity duration-300',
								isSaving ? 'opacity-100' : 'opacity-0 pointer-events-none'
							)}
						>
							<Loader2 className='h-4 w-4 animate-spin' />
							<span>Saving...</span>
						</span>
						<span
							className={cn(
								'absolute inset-0 flex items-center justify-center gap-2 transition-opacity duration-300',
								saveStatus === 'saved' && !isSaving
									? 'opacity-100'
									: 'opacity-0 pointer-events-none'
							)}
						>
							<CheckCircle className='h-4 w-4' />
							<span>Saved!</span>
						</span>
						<span
							className={cn(
								'absolute inset-0 flex items-center justify-center gap-2 transition-opacity duration-300',
								saveStatus === 'error' && !isSaving
									? 'opacity-100'
									: 'opacity-0 pointer-events-none'
							)}
						>
							<XCircle className='h-4 w-4' />
							<span>Error</span>
						</span>
						<span
							className={cn(
								'absolute inset-0 flex items-center justify-center gap-2 transition-opacity duration-300',
								saveStatus !== 'saved' && saveStatus !== 'error' && !isSaving
									? 'opacity-100'
									: 'opacity-0 pointer-events-none'
							)}
						>
							<Save className='h-4 w-4' />
							<span>Save Changes</span>
						</span>
					</Button>
				</div>
			</div>
			<div className='flex-grow h-0 pt-2'>
				{/* Editor Container with Modern Design */}
				<div className='relative h-full group'>
					{/* Subtle background glow effect */}
					<div className='absolute inset-0 bg-gradient-to-r from-blue-500/5 to-indigo-500/5 dark:from-blue-400/10 dark:to-indigo-400/10 rounded-2xl blur-xl opacity-50'></div>

					{/* Main editor container */}
					<div className='relative h-full bg-white/95 dark:bg-slate-800/95 backdrop-blur-sm rounded-2xl shadow-xl border border-slate-200/70 dark:border-slate-700/70 overflow-hidden transition-all duration-300 group-hover:shadow-2xl group-hover:border-blue-300/50 dark:group-hover:border-blue-600/50'>
						{/* Subtle top gradient line */}
						<div className='absolute top-0 left-0 right-0 h-px bg-gradient-to-r from-transparent via-blue-500/30 to-transparent'></div>

						{/* Editor content */}
						<TiptapEditor
							content={editorContent}
							onUpdate={async (editor) => {
								const content = await editor.getHTML();
								handleContentChange(content);
							}}
						/>
					</div>
				</div>
			</div>

			{/* Diálogo de confirmação para saída sem salvar */}
			<AlertDialog open={showExitDialog} onOpenChange={setShowExitDialog}>
				<AlertDialogContent className='bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg shadow-lg max-w-md p-0'>
					<AlertDialogHeader className='px-4 pt-4'>
						<div className='flex items-center space-x-3'>
							<div className='text-orange-500'>
								<AlertTriangle className='w-6 h-6' />
							</div>
							<AlertDialogTitle className='text-lg font-semibold text-gray-900 dark:text-gray-100'>
								Unsaved Changes
							</AlertDialogTitle>
						</div>
					</AlertDialogHeader>

					<div className='border-t border-gray-200 dark:border-gray-700'></div>

					<div className='px-6 pt-0'>
						<AlertDialogDescription className='text-sm text-gray-600 dark:text-gray-400 mb-4'>
							You have unsaved changes on this page. What would you like to do?
						</AlertDialogDescription>

						<div className='p-3 bg-gray-50 dark:bg-gray-700/50 rounded-md'>
							<p className='text-sm font-medium text-gray-900 dark:text-gray-100'>
								Your work isn&apos;t lost yet
							</p>
							<p className='text-xs text-gray-600 dark:text-gray-400 mt-1'>
								Save your changes to keep your progress, or discard them to
								start fresh.
							</p>
						</div>
					</div>

					<AlertDialogFooter className='p-6 pt-0 flex gap-3'>
						<AlertDialogCancel
							onClick={handleCancelExit}
							className='flex-1 bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-600 h-10 px-4 rounded-md'
						>
							Cancel
						</AlertDialogCancel>

						<Button
							variant='outline'
							onClick={handleExitWithoutSaving}
							className='flex-1 bg-red-500 hover:bg-red-600 text-white border-red-500 dark:border-red-500 h-10 px-4 rounded-md font-medium'
						>
							Discard
						</Button>

						<AlertDialogAction
							onClick={handleSaveAndExit}
							className='flex-1 bg-green-500 hover:bg-green-600 text-white h-10 px-4 rounded-md font-medium'
						>
							Save & Continue
						</AlertDialogAction>
					</AlertDialogFooter>
				</AlertDialogContent>
			</AlertDialog>
		</div>
	);
}
