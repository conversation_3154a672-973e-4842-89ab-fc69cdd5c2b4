import type { User } from "@supabase/supabase-js";
import type { ReactNode, ReactElement } from "react";

// Define a forma dos dados do contexto
export interface UserContextType {
	user: User | null;
	userProfile: {
		email: string | undefined;
		avatarUrl: string | ReactElement | null;
		name: string | undefined;
		fullName: string | undefined;
	} | null;
	isUserLoading: boolean;
}

// Define as props para o componente provedor
export interface UserProviderProps {
	children: ReactNode;
}
