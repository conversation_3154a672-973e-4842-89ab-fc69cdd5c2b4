import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>Title } from "@/components/ui/card";
import { Skeleton } from "@/components/ui/skeleton";
import { <PERSON><PERSON><PERSON><PERSON>, BsPeople } from "react-icons/bs";

interface MetricsCardsProps {
	analyticsData: {
		dashboard?: {
			totals?: {
				requests?: number;
				pageViews?: number;
				uniques?: number;
				bandwidth?: number;
			};
		};
	};
	isLoading?: boolean;
	timeRangeLabel: string;
}

const formatNumber = (num: number | undefined): string => {
	if (!num) return "0";
	return new Intl.NumberFormat("en-US").format(num);
};

function MetricsCardSkeleton() {
	return (
		<Card className='h-full'>
			<CardHeader className='flex flex-row items-center justify-between space-y-0 py-6'>
				<Skeleton className='h-4 w-24' />
				<Skeleton className='h-5 w-5 rounded-full' />
			</CardHeader>
			<CardContent className='pb-6'>
				<Skeleton className='h-9 w-20 mb-2' />
				<Skeleton className='h-3 w-16' />
			</CardContent>
		</Card>
	);
}

export function MetricsCards({
	analyticsData,
	isLoading = false,
	timeRangeLabel,
}: MetricsCardsProps) {
	if (isLoading) {
		return (
			<div className='grid grid-cols-1 md:grid-cols-3 gap-6 w-full'>
				<MetricsCardSkeleton />
				<MetricsCardSkeleton />
				<MetricsCardSkeleton />
			</div>
		);
	}

	return (
		<div className='grid grid-cols-1 md:grid-cols-2 gap-6 w-full max-w-7xl mx-auto'>
			{/* Total Requests */}
			{/* <Card className='h-full'>
				<CardHeader className='flex flex-row items-center justify-between space-y-0 py-6'>
					<CardTitle className='text-sm font-medium text-gray-600'>
						Total Requests
					</CardTitle>
					<BsGlobe2 className='h-5 w-5 text-blue-500' />
				</CardHeader>
				<CardContent className='pb-6'>
					<div className='text-3xl font-bold text-gray-900 mb-1'>
						{formatNumber(analyticsData.dashboard?.totals?.requests)}
					</div>
					<p className='text-xs text-gray-500'>{timeRangeLabel}</p>
				</CardContent>
			</Card> */}

			{/* Page Views */}
			<Card className='h-full'>
				<CardHeader className='flex flex-row items-center justify-between space-y-0 py-6'>
					<CardTitle className='text-sm font-medium text-gray-600'>
						Page Views
					</CardTitle>
					<BsEye className='h-5 w-5 text-green-500' />
				</CardHeader>
				<CardContent className='pb-6'>
					<div className='text-3xl font-bold text-gray-900 mb-1'>
						{formatNumber(analyticsData.dashboard?.totals?.pageViews)}
					</div>
					<p className='text-xs text-gray-500'>{timeRangeLabel}</p>
				</CardContent>
			</Card>

			{/* Unique Visitors */}
			<Card className='h-full'>
				<CardHeader className='flex flex-row items-center justify-between space-y-0 py-6'>
					<CardTitle className='text-sm font-medium text-gray-600'>
						Unique Visitors
					</CardTitle>
					<BsPeople className='h-5 w-5 text-purple-500' />
				</CardHeader>
				<CardContent className='pb-6'>
					<div className='text-3xl font-bold text-gray-900 mb-1'>
						{formatNumber(analyticsData.dashboard?.totals?.uniques)}
					</div>
					<p className='text-xs text-gray-500'>{timeRangeLabel} (estimated)</p>
				</CardContent>
			</Card>
		</div>
	);
}
