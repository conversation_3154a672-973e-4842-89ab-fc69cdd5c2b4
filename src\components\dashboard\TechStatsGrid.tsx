import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>itle } from "@/components/ui/card";
import { Skeleton } from "@/components/ui/skeleton";
import {
	FiMonitor,
	FiSmartphone,
	FiTablet,
	FiGlobe,
	FiSettings,
	FiCpu,
} from "react-icons/fi";
import {
	SiGooglechrome,
	SiFirefox,
	SiSafari,
	SiOpera,
	SiApple,
	SiLinux,
	SiAndroid,
} from "react-icons/si";

interface TechStat {
	count: number;
	sum: { visits: number };
	dimensions: { metric: string };
}

interface TechStatsGridProps {
	browsers: TechStat[];
	operatingSystems: TechStat[];
	deviceTypes: TechStat[];
	isLoading?: boolean;
}

const formatNumber = (num: number): string => {
	return new Intl.NumberFormat("en-US").format(num);
};

function TechStatsItemSkeleton() {
	return (
		<div className='flex items-center justify-between gap-3'>
			<div className='flex items-center gap-2 flex-1 min-w-0'>
				<Skeleton className='h-4 w-4 rounded-full' />
				<Skeleton className='h-4 w-24' />
			</div>
			<div className='flex items-center gap-3 flex-shrink-0'>
				<Skeleton className='h-4 w-12' />
				<Skeleton className='w-24 h-2 rounded-full' />
			</div>
		</div>
	);
}

function TechStatsCardSkeleton() {
	return (
		<Card className='h-full'>
			<CardHeader className='py-6'>
				<div className='flex items-center gap-2'>
					<Skeleton className='h-5 w-5 rounded-full' />
					<Skeleton className='h-5 w-24' />
				</div>
				<Skeleton className='h-4 w-32 mt-2' />
			</CardHeader>
			<CardContent className='pb-6'>
				<div className='space-y-3 max-h-72 overflow-y-auto pr-2'>
					{Array.from({ length: 5 }).map((_, index) => (
						<TechStatsItemSkeleton key={index} />
					))}
				</div>
			</CardContent>
		</Card>
	);
}

function TechStatsGridSkeleton() {
	return (
		<div className='grid grid-cols-1 lg:grid-cols-3 gap-6'>
			<TechStatsCardSkeleton />
			<TechStatsCardSkeleton />
			<TechStatsCardSkeleton />
		</div>
	);
}

const getBrowserIcon = (browser: string) => {
	switch (browser.toLowerCase()) {
		case "chrome":
			return <SiGooglechrome className='h-4 w-4 text-blue-500' />;
		case "safari":
			return <SiSafari className='h-4 w-4 text-blue-400' />;
		case "firefox":
			return <SiFirefox className='h-4 w-4 text-orange-500' />;
		case "edge":
			return <FiGlobe className='h-4 w-4 text-blue-600' />;
		case "chromemobile":
			return <SiGooglechrome className='h-4 w-4 text-green-500' />;
		case "opera":
			return <SiOpera className='h-4 w-4 text-red-500' />;
		default:
			return <FiMonitor className='h-4 w-4 text-gray-500' />;
	}
};

const getOSIcon = (os: string) => {
	switch (os.toLowerCase()) {
		case "windows":
			return <FiSettings className='h-4 w-4 text-blue-500' />;
		case "macosx":
			return <SiApple className='h-4 w-4 text-gray-700' />;
		case "linux":
			return <SiLinux className='h-4 w-4 text-orange-500' />;
		case "android":
			return <SiAndroid className='h-4 w-4 text-green-500' />;
		case "ios":
			return <SiApple className='h-4 w-4 text-gray-600' />;
		case "chromeos":
			return <SiGooglechrome className='h-4 w-4 text-blue-400' />;
		default:
			return <FiMonitor className='h-4 w-4 text-gray-500' />;
	}
};

const getDeviceIcon = (device: string) => {
	switch (device.toLowerCase()) {
		case "desktop":
			return <FiMonitor className='h-4 w-4 text-blue-500' />;
		case "mobile":
			return <FiSmartphone className='h-4 w-4 text-green-500' />;
		case "tablet":
			return <FiTablet className='h-4 w-4 text-purple-500' />;
		default:
			return <FiMonitor className='h-4 w-4 text-gray-500' />;
	}
};

export function TechStatsGrid({
	browsers,
	operatingSystems,
	deviceTypes,
	isLoading = false,
}: TechStatsGridProps) {
	if (isLoading) {
		return <TechStatsGridSkeleton />;
	}

	const maxBrowserVisits = browsers?.[0]?.sum.visits || 1;
	const maxOSVisits = operatingSystems?.[0]?.sum.visits || 1;
	const maxDeviceVisits = deviceTypes?.[0]?.sum.visits || 1;

	return (
		<div className='grid grid-cols-1 lg:grid-cols-3 gap-6'>
			{/* Top Browsers */}
			<Card className='h-full'>
				<CardHeader className='py-6'>
					<CardTitle className='text-lg font-semibold flex items-center gap-2'>
						<SiGooglechrome className='h-5 w-5 text-blue-500' />
						Browsers{" "}
						<span className='text-xs text-gray-500 ml-3'>
							({browsers?.length || 0} browsers)
						</span>
					</CardTitle>
					{/* <CardDescription className='text-sm text-gray-500'>
						All browsers ({browsers?.length || 0})
					</CardDescription> */}
				</CardHeader>
				<CardContent className='pb-6'>
					<div className='space-y-3 max-h-72 overflow-y-auto pr-2'>
						{browsers?.map((browser, index) => {
							const percentage = (browser.sum.visits / maxBrowserVisits) * 100;

							return (
								<div
									key={index}
									className='flex items-center justify-between gap-3'
								>
									<div className='flex items-center gap-2 flex-1 min-w-0'>
										{getBrowserIcon(browser.dimensions.metric)}
										<span className='font-medium text-sm text-gray-900 truncate'>
											{browser.dimensions.metric}
										</span>
									</div>
									<div className='flex items-center gap-3 flex-shrink-0'>
										<span className='text-sm text-gray-600 min-w-[3rem] text-right'>
											{formatNumber(browser.sum.visits)}
										</span>
										<div className='w-24 h-2 bg-gray-200 rounded-full overflow-hidden'>
											<div
												className='h-full bg-blue-500 transition-all duration-300'
												style={{
													width: `${percentage}%`,
												}}
											/>
										</div>
									</div>
								</div>
							);
						})}
					</div>
				</CardContent>
			</Card>

			{/* Top Operating Systems */}
			<Card className='h-full'>
				<CardHeader className='py-6'>
					<CardTitle className='text-lg font-semibold flex items-center gap-2'>
						<FiCpu className='h-5 w-5 text-blue-500' />
						Operating Systems{" "}
						<span className='text-xs text-gray-500 ml-3'>
							({operatingSystems?.length || 0} OS)
						</span>
					</CardTitle>
					{/* <CardDescription className='text-sm text-gray-500'>
						All OS ({operatingSystems?.length || 0})
					</CardDescription> */}
				</CardHeader>
				<CardContent className='pb-6'>
					<div className='space-y-3 max-h-72 overflow-y-auto pr-2'>
						{operatingSystems?.map((os, index) => {
							const percentage = (os.sum.visits / maxOSVisits) * 100;
							const displayName =
								os.dimensions.metric === "MacOSX"
									? "macOS"
									: os.dimensions.metric === "ChromeOS"
									? "Chrome OS"
									: os.dimensions.metric;

							return (
								<div
									key={index}
									className='flex items-center justify-between gap-3'
								>
									<div className='flex items-center gap-2 flex-1 min-w-0'>
										{getOSIcon(os.dimensions.metric)}
										<span className='font-medium text-sm text-gray-900 truncate'>
											{displayName}
										</span>
									</div>
									<div className='flex items-center gap-3 flex-shrink-0'>
										<span className='text-sm text-gray-600 min-w-[3rem] text-right'>
											{formatNumber(os.sum.visits)}
										</span>
										<div className='w-24 h-2 bg-gray-200 rounded-full overflow-hidden'>
											<div
												className='h-full bg-green-500 transition-all duration-300'
												style={{
													width: `${percentage}%`,
												}}
											/>
										</div>
									</div>
								</div>
							);
						})}
					</div>
				</CardContent>
			</Card>

			{/* Top Device Types */}
			<Card className='h-full'>
				<CardHeader className='py-6'>
					<CardTitle className='text-lg font-semibold flex items-center gap-2'>
						<FiMonitor className='h-5 w-5 text-purple-500' />
						Device Types{" "}
						<span className='text-xs text-gray-500 ml-3'>
							({deviceTypes?.length || 0} devices)
						</span>
					</CardTitle>
					{/* <CardDescription className='text-sm text-gray-500'>
						All devices ({deviceTypes?.length || 0})
					</CardDescription> */}
				</CardHeader>
				<CardContent className='pb-6'>
					<div className='space-y-3 max-h-72 overflow-y-auto pr-2'>
						{deviceTypes?.map((device, index) => {
							const percentage = (device.sum.visits / maxDeviceVisits) * 100;
							const displayName =
								device.dimensions.metric === "desktop"
									? "Desktop"
									: device.dimensions.metric === "mobile"
									? "Mobile"
									: device.dimensions.metric === "tablet"
									? "Tablet"
									: device.dimensions.metric;

							return (
								<div
									key={index}
									className='flex items-center justify-between gap-3'
								>
									<div className='flex items-center gap-2 flex-1 min-w-0'>
										{getDeviceIcon(device.dimensions.metric)}
										<span className='font-medium text-sm text-gray-900 truncate'>
											{displayName}
										</span>
									</div>
									<div className='flex items-center gap-3 flex-shrink-0'>
										<span className='text-sm text-gray-600 min-w-[3rem] text-right'>
											{formatNumber(device.sum.visits)}
										</span>
										<div className='w-24 h-2 bg-gray-200 rounded-full overflow-hidden'>
											<div
												className='h-full bg-purple-500 transition-all duration-300'
												style={{
													width: `${percentage}%`,
												}}
											/>
										</div>
									</div>
								</div>
							);
						})}
					</div>
				</CardContent>
			</Card>
		</div>
	);
}
