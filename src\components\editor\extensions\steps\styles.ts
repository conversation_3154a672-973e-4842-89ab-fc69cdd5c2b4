export const stepLineStyles = `
.step-line-fade {
  background: linear-gradient(to bottom, 
    rgb(209 213 219) 0%, 
    rgb(209 213 219) 60%, 
    rgba(209, 213, 219, 0.3) 85%, 
    transparent 100%) !important;
}

.dark .step-line-fade {
  background: linear-gradient(to bottom, 
    rgb(75 85 99) 0%, 
    rgb(75 85 99) 60%, 
    rgba(75, 85, 99, 0.3) 85%, 
    transparent 100%) !important;
}

.steps-node {
  -webkit-user-drag: none !important;
  -khtml-user-drag: none !important;
  -moz-user-drag: none !important;
  -o-user-drag: none !important;
  user-drag: none !important;
  user-select: none !important;
  -webkit-user-select: none !important;
  -moz-user-select: none !important;
  -ms-user-select: none !important;
}

.steps-node * {
  -webkit-user-drag: none !important;
  -khtml-user-drag: none !important;
  -moz-user-drag: none !important;
  -o-user-drag: none !important;
  user-drag: none !important;
}

.steps-node [draggable="true"] {
  -webkit-user-drag: element !important;
  -khtml-user-drag: element !important;
  -moz-user-drag: element !important;
  -o-user-drag: element !important;
  user-drag: element !important;
}

/* Hover isolado para cada step - controlado por JavaScript */
.step-hover-zone {
  position: relative;
  isolation: isolate;
}

/* Evita que sub-steps ativem o hover do pai */
.step-hover-zone .steps-container {
  isolation: isolate;
}

/* Garante isolamento de hover */
.steps-node .step-hover-zone {
  pointer-events: auto;
}

/* Isola o componente do editor */
.steps-node-container {
  isolation: isolate;
}
`;

// Inject styles into document head
export const injectStyles = () => {
	if (typeof document !== "undefined") {
		const styleId = "step-line-fade-styles";
		if (!document.getElementById(styleId)) {
			const style = document.createElement("style");
			style.id = styleId;
			style.textContent = stepLineStyles;
			document.head.appendChild(style);
		}
	}
};
