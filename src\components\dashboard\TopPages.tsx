import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>Title } from "@/components/ui/card";
import { Skeleton } from "@/components/ui/skeleton";
import { AiOutlineFileText } from "react-icons/ai";

interface Page {
	count: number;
	sum: { visits: number };
	dimensions: { metric: string };
}

interface TopPagesProps {
	pages: Page[];
	isLoading?: boolean;
}

const formatNumber = (num: number): string => {
	return new Intl.NumberFormat("en-US").format(num);
};

function TopPagesItemSkeleton() {
	return (
		<div className='flex items-center justify-between gap-3'>
			<div className='flex-1 min-w-0'>
				<Skeleton className='h-6 w-full max-w-xs rounded' />
			</div>
			<div className='flex items-center gap-3 flex-shrink-0'>
				<div className='text-right'>
					<Skeleton className='h-4 w-12 mb-1' />
					<Skeleton className='h-3 w-16' />
				</div>
				<Skeleton className='w-20 h-2 rounded-full' />
			</div>
		</div>
	);
}

function TopPagesSkeleton() {
	return (
		<Card className='h-full'>
			<CardHeader className='py-6'>
				<div className='flex items-center gap-2'>
					<Skeleton className='h-5 w-5 rounded-full' />
					<Skeleton className='h-5 w-40' />
				</div>
				<Skeleton className='h-4 w-48 mt-2' />
			</CardHeader>
			<CardContent className='pb-6'>
				<div className='space-y-4 max-h-80 overflow-y-auto pr-2'>
					{Array.from({ length: 7 }).map((_, index) => (
						<TopPagesItemSkeleton key={index} />
					))}
				</div>
			</CardContent>
		</Card>
	);
}

export function TopPages({ pages, isLoading = false }: TopPagesProps) {
	if (isLoading) {
		return <TopPagesSkeleton />;
	}

	if (!pages || pages.length === 0) {
		return null;
	}

	const maxVisits = pages[0]?.sum.visits || 1;

	return (
		<Card className='h-full'>
			<CardHeader className='py-6'>
				<div className='flex items-center gap-2'>
					<AiOutlineFileText className='h-5 w-5 text-purple-500' />
					<CardTitle className='text-lg font-semibold flex items-center'>
						Most Visited Pages{" "}
						<span className='text-xs text-gray-500 ml-3'>
							({pages.length} pages)
						</span>
					</CardTitle>
				</div>
				{/* <CardDescription className='text-sm text-gray-500'>
					All pages by number of visits ({pages.length} pages)
				</CardDescription> */}
			</CardHeader>
			<CardContent className='pb-6'>
				<div className='space-y-4 max-h-80 overflow-y-auto pr-2'>
					{pages.map((path, index) => {
						const percentage = (path.sum.visits / maxVisits) * 100;

						return (
							<div
								key={index}
								className='flex items-center justify-between gap-3'
							>
								<div className='flex-1 min-w-0'>
									<span className='font-medium text-xs sm:text-sm text-gray-900 font-mono bg-gray-100 px-1 sm:px-2 py-1 rounded truncate block'>
										{path.dimensions.metric}
									</span>
								</div>
								<div className='flex items-center gap-2 sm:gap-3 flex-shrink-0'>
									<div className='text-right'>
										<div className='text-sm font-medium text-gray-900'>
											{formatNumber(path.sum.visits)}
										</div>
										<div className='text-xs text-gray-500'>
											{formatNumber(path.count)} views
										</div>
									</div>
									<div className='w-16 sm:w-20 lg:w-24 h-2 bg-gray-200 rounded-full overflow-hidden'>
										<div
											className='h-full bg-purple-500 transition-all duration-300'
											style={{
												width: `${percentage}%`,
											}}
										/>
									</div>
								</div>
							</div>
						);
					})}
				</div>
			</CardContent>
		</Card>
	);
}
