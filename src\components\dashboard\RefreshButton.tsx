import { But<PERSON> } from "@/components/ui/button";
import { BiRefresh } from "react-icons/bi";
import { AiOutlineLoading3Quarters } from "react-icons/ai";

interface RefreshButtonProps {
	isLoading: boolean;
	onClick: () => void;
}

export function RefreshButton({ isLoading, onClick }: RefreshButtonProps) {
	return (
		<Button
			variant='outline'
			onClick={onClick}
			disabled={isLoading}
			className='flex items-center gap-2 px-4 py-2 font-medium transition-all duration-200 hover:bg-blue-50 hover:border-blue-300 hover:text-blue-700'
		>
			{isLoading ? (
				<AiOutlineLoading3Quarters className='h-4 w-4 animate-spin text-blue-600' />
			) : (
				<BiRefresh className='h-4 w-4 text-blue-600' />
			)}
			{isLoading ? "Refreshing..." : "Refresh Metrics"}
		</Button>
	);
}
