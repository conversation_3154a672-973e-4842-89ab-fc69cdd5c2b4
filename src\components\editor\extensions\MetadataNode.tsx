import { Node } from '@tiptap/core';
import { NodeViewWrapper, ReactNodeViewRenderer } from '@tiptap/react';
import React, { useState, useCallback, useRef, useEffect } from 'react';
import { createPortal } from 'react-dom';
import { Settings, Edit3, Save, X, Info, HelpCircle } from 'lucide-react';
import { Button } from '@/components/ui/button';
import type { NodeViewProps } from '@tiptap/react';

// Interface for metadata
interface MetadataAttrs {
	title: string;
	description: string;
	slug: string;
	route?: string | null; // For API files
}

// Improved Tooltip component with overflow protection and portal rendering
const Tooltip: React.FC<{
	children: React.ReactNode;
	content: string;
}> = ({ children, content }) => {
	const [isVisible, setIsVisible] = useState(false);
	const [position, setPosition] = useState({ top: 0, left: 0 });
	const [actualPosition, setActualPosition] = useState<'top' | 'bottom'>(
		'bottom'
	);
	const [arrowOffset, setArrowOffset] = useState(0);
	const triggerRef = useRef<HTMLDivElement>(null);
	const tooltipRef = useRef<HTMLDivElement>(null);

	useEffect(() => {
		if (isVisible && triggerRef.current) {
			const trigger = triggerRef.current;
			const rect = trigger.getBoundingClientRect();
			const viewport = {
				width: window.innerWidth,
				height: window.innerHeight,
			};

			// Calculate optimal position
			let newPosition: 'top' | 'bottom' = 'bottom';
			let top = rect.bottom + 8; // Default bottom position
			let left = rect.left + rect.width / 2; // Center horizontally on trigger

			// Check if tooltip would overflow bottom of viewport
			const tooltipHeight = 100; // Estimated tooltip height
			if (top + tooltipHeight > viewport.height - 20) {
				newPosition = 'top';
				top = rect.top - tooltipHeight - 8;
			}

			// Calculate tooltip width and constraints
			const tooltipWidth = Math.min(320, viewport.width - 40); // max-w-80 = 320px, but adapt to screen
			const halfWidth = tooltipWidth / 2;

			// Store the original centered position for arrow calculation
			const originalLeft = left;

			// Adjust tooltip position to stay within viewport
			if (left - halfWidth < 20) {
				left = halfWidth + 20;
			} else if (left + halfWidth > viewport.width - 20) {
				left = viewport.width - halfWidth - 20;
			}

			// Calculate arrow offset based on how much we shifted the tooltip
			const arrowShift = originalLeft - left;
			const maxArrowOffset = halfWidth - 20; // Keep arrow within tooltip bounds
			const finalArrowOffset = Math.max(
				-maxArrowOffset,
				Math.min(maxArrowOffset, arrowShift)
			);

			setPosition({ top, left });
			setActualPosition(newPosition);
			setArrowOffset(finalArrowOffset);
		}
	}, [isVisible]);

	const tooltipElement = isVisible ? (
		<div
			ref={tooltipRef}
			className={`fixed z-[99999] px-4 py-3 text-sm text-white bg-gray-900 dark:bg-gray-700 rounded-lg shadow-xl pointer-events-none`}
			style={{
				top: `${position.top}px`,
				left: `${position.left}px`,
				transform: 'translateX(-50%)',
				maxWidth: '320px',
				minWidth: '256px',
				width: 'max-content',
			}}
		>
			<div className='whitespace-normal text-left leading-relaxed'>
				{content}
			</div>
			<div
				className={`absolute w-2 h-2 bg-gray-900 dark:bg-gray-700 ${
					actualPosition === 'bottom' ? '-top-1' : '-bottom-1'
				}`}
				style={{
					left: `calc(50% + ${arrowOffset}px)`,
					transform: `translateX(-50%) rotate(45deg)`,
				}}
			></div>
		</div>
	) : null;

	return (
		<>
			<div ref={triggerRef} className='relative inline-block'>
				<div
					onMouseEnter={() => setIsVisible(true)}
					onMouseLeave={() => setIsVisible(false)}
				>
					{children}
				</div>
			</div>
			{tooltipElement && createPortal(tooltipElement, document.body)}
		</>
	);
};

// Function to generate API route from file path
const generateApiRoute = (
	filePath: string,
	method: string = 'POST'
): string => {
	// Extract the API path from file path
	// Example: /workflow/api/v1/organizations/{id}/projects/route.ts -> POST /workflow/api/v1/organizations/{id}/projects
	const apiPath = filePath
		.replace(/^.*\/api/, '/api') // Keep everything from /api onwards
		.replace(/\/route\.(ts|js)$/, '') // Remove /route.ts or /route.js
		.replace(/\/\[([^\]]+)\]/g, '/{$1}'); // Convert [id] to {id}

	return `${method} ${apiPath}`;
};

// Function to detect if current content is an API file
const isApiFile = (attrs: MetadataAttrs): boolean => {
	// Check if route exists in metadata (indicating API file)
	if (attrs.route) return true;

	// Additional check could be added here for is_api_file column if needed
	return false;
};

// React component that will be rendered for the node
const MetadataComponent: React.FC<NodeViewProps> = ({
	node,
	updateAttributes,
	selected,
}) => {
	const [isEditing, setIsEditing] = useState(false);
	const [isApiType, setIsApiType] = useState(false);
	const [formData, setFormData] = useState<MetadataAttrs>({
		title: node.attrs.title || '',
		description: node.attrs.description || '',
		slug: node.attrs.slug || '',
		route: node.attrs.route || null,
	});

	// Check if this is an API file based on attributes
	React.useEffect(() => {
		const apiType = isApiFile(node.attrs as MetadataAttrs);
		setIsApiType(apiType);
	}, [node.attrs]);

	const handleSave = useCallback(() => {
		updateAttributes(formData);
		setIsEditing(false);
	}, [formData, updateAttributes]);

	const handleCancel = useCallback(() => {
		setFormData({
			title: node.attrs.title || '',
			description: node.attrs.description || '',
			slug: node.attrs.slug || '',
			route: node.attrs.route || null,
		});
		setIsEditing(false);
	}, [node.attrs]);

	const handleInputChange = useCallback(
		(field: keyof MetadataAttrs, value: string) => {
			setFormData((prev) => ({ ...prev, [field]: value }));
		},
		[]
	);

	if (!isEditing) {
		return (
			<NodeViewWrapper
				className='metadata-node'
				as='div'
				data-drag-handle=''
				contentEditable={false}
			>
				<div
					className={`
          mb-4 p-3 bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-blue-900/20 dark:to-indigo-900/20 
          border border-blue-200/50 dark:border-blue-700/50 rounded-lg shadow-sm
          ${selected ? 'ring-2 ring-blue-400/50' : ''}
          transition-all duration-300 hover:shadow-md hover:border-blue-300/60 dark:hover:border-blue-600/60
          relative group
        `}
				>
					{/* Badge indicating it cannot be deleted */}
					<div className='absolute -top-1.5 -right-1.5 bg-blue-500 text-white text-xs px-1.5 py-0.5 rounded-full font-medium shadow-md protection-badge transition-all duration-300 group-hover:scale-105'>
						<span className='flex items-center space-x-1'>
							<svg
								className='w-2.5 h-2.5'
								fill='currentColor'
								viewBox='0 0 20 20'
							>
								<path
									fillRule='evenodd'
									d='M5 9V7a5 5 0 0110 0v2a2 2 0 012 2v5a2 2 0 01-2 2H5a2 2 0 01-2-2v-5a2 2 0 012-2zm8-2v2H7V7a3 3 0 016 0z'
									clipRule='evenodd'
								/>
							</svg>
							<span className='text-[10px]'>Protected</span>
						</span>
					</div>

					{/* Header with info tooltip */}
					<div className='flex justify-between mb-2 items-center align-middle text-center text-sm font-semibold'>
						<div className='flex items-center gap-2'>
							<div className='w-5 h-5 bg-blue-100 dark:bg-blue-900/40 rounded-md flex items-center justify-center flex-shrink-0 transition-colors duration-300'>
								<Settings className='w-3 h-3 text-blue-600 dark:text-blue-400' />
							</div>
							Page Metadata
							<div className='flex items-center w-5 h-5'>
								<Tooltip content='Page title and information for search engines and social media.'>
									<HelpCircle className='w-3.5 h-3.5 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 cursor-help transition-colors duration-200' />
								</Tooltip>
							</div>
						</div>
						<Button
							variant='ghost'
							size='sm'
							onClick={() => setIsEditing(true)}
							className='h-6 px-2 text-xs hover:bg-blue-100/50 dark:hover:bg-blue-900/30 flex-shrink-0 transition-all duration-200 hover:scale-105'
						>
							<Edit3 className='w-2.5 h-2.5 mr-1' />
							Edit
						</Button>
					</div>

					{/* Metadata Preview - Compact */}
					<div
						className={`grid grid-cols-1 ${isApiType ? 'md:grid-cols-4' : 'md:grid-cols-3'} gap-2 text-xs`}
					>
						<div className='bg-white/70 dark:bg-slate-800/70 rounded-md p-2 border border-blue-100 dark:border-blue-800/30 transition-all duration-200 hover:bg-white/90 dark:hover:bg-slate-800/90'>
							<div className='flex items-center space-x-1 mb-1'>
								<label className='block text-[10px] font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wide'>
									Title
								</label>
								<div className='flex items-center'>
									<Tooltip content='Main title for browser tabs and search results.'>
										<Info className='w-2.5 h-2.5 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 cursor-help transition-colors duration-200' />
									</Tooltip>
								</div>
							</div>
							<p className='text-gray-900 dark:text-gray-100 font-medium truncate text-xs leading-tight'>
								{node.attrs.title || 'No title set'}
							</p>
						</div>
						<div className='bg-white/70 dark:bg-slate-800/70 rounded-md p-2 border border-blue-100 dark:border-blue-800/30 transition-all duration-200 hover:bg-white/90 dark:hover:bg-slate-800/90'>
							<div className='flex items-center space-x-1 mb-1'>
								<label className='block text-[10px] font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wide'>
									Slug
								</label>
								<div className='flex items-center'>
									<Tooltip content='URL-friendly page address. Use lowercase letters, numbers, hyphens and slashes only.'>
										<Info className='w-2.5 h-2.5 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 cursor-help transition-colors duration-200' />
									</Tooltip>
								</div>
							</div>
							<p className='text-gray-900 dark:text-gray-100 font-mono text-xs truncate leading-tight'>
								{node.attrs.slug || 'no-slug-set'}
							</p>
						</div>
						<div className='bg-white/70 dark:bg-slate-800/70 rounded-md p-2 border border-blue-100 dark:border-blue-800/30 md:col-span-1 transition-all duration-200 hover:bg-white/90 dark:hover:bg-slate-800/90'>
							<div className='flex items-center space-x-1 mb-1'>
								<label className='block text-[10px] font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wide'>
									Description
								</label>
								<div className='flex items-center'>
									<Tooltip content='Appears in search results and social media. Write compelling text to encourage clicks.'>
										<HelpCircle className='w-2.5 h-2.5 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 cursor-help transition-colors duration-200' />
									</Tooltip>
								</div>
							</div>
							<p className='text-gray-900 dark:text-gray-100 text-xs line-clamp-2 leading-tight'>
								{node.attrs.description || 'No description provided'}
							</p>
						</div>
						{isApiType && (
							<div className='bg-orange-50/70 dark:bg-orange-900/20 rounded-md p-2 border border-orange-200 dark:border-orange-800/30 transition-all duration-200 hover:bg-orange-50/90 dark:hover:bg-orange-900/30'>
								<div className='flex items-center space-x-1 mb-1'>
									<label className='block text-[10px] font-medium text-orange-600 dark:text-orange-400 uppercase tracking-wide'>
										Route
									</label>
									<div className='flex items-center'>
										<Tooltip content='API endpoint route. This field is automatically filled and locked for API files.'>
											<Info className='w-2.5 h-2.5 text-orange-400 hover:text-orange-600 dark:hover:text-orange-300 cursor-help transition-colors duration-200' />
										</Tooltip>
									</div>
								</div>
								<p className='text-orange-900 dark:text-orange-100 font-mono text-xs truncate leading-tight'>
									{node.attrs.route || 'No route set'}
								</p>
							</div>
						)}
					</div>
				</div>
			</NodeViewWrapper>
		);
	}

	return (
		<NodeViewWrapper
			className='metadata-node'
			as='div'
			data-drag-handle=''
			contentEditable={false}
		>
			<div
				className={`
        mb-4 p-4 bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-blue-900/20 dark:to-indigo-900/20 
        border-2 border-blue-400/50 dark:border-blue-600/50 rounded-lg shadow-lg
        transition-all duration-300 ease-in-out
        relative
      `}
			>
				{/* Badge indicating it cannot be deleted */}
				<div className='absolute -top-1.5 -right-1.5 bg-orange-500 text-white text-xs px-1.5 py-0.5 rounded-full font-medium shadow-md transition-all duration-300'>
					<span className='flex items-center space-x-1'>
						<svg
							className='w-2.5 h-2.5'
							fill='currentColor'
							viewBox='0 0 20 20'
						>
							<path
								fillRule='evenodd'
								d='M5 9V7a5 5 0 0110 0v2a2 2 0 012 2v5a2 2 0 01-2 2H5a2 2 0 01-2-2v-5a2 2 0 012-2zm8-2v2H7V7a3 3 0 016 0z'
								clipRule='evenodd'
							/>
						</svg>
						<span className='text-[10px]'>Editing</span>
					</span>
				</div>

				{/* Header */}
				<div className='flex items-center justify-between mb-3'>
					<div className='flex items-center gap-2 text-sm font-semibold'>
						<div className='w-5 h-5 bg-blue-100 dark:bg-blue-900/40 rounded-md flex items-center justify-center flex-shrink-0 transition-colors duration-300'>
							<Settings className='w-3 h-3 text-blue-600 dark:text-blue-400' />
						</div>
						Page Metadata
					</div>
					<div className='flex items-center space-x-2'>
						<Button
							variant='ghost'
							size='sm'
							onClick={handleCancel}
							className='h-6 px-2 text-xs hover:bg-red-100/50 dark:hover:bg-red-900/30 text-red-600 dark:text-red-400 flex-shrink-0 transition-all duration-200 hover:scale-105'
						>
							<X className='w-2.5 h-2.5 mr-1' />
							Cancel
						</Button>
						<Button
							variant='default'
							size='sm'
							onClick={handleSave}
							className='h-6 px-2 text-xs bg-blue-600 hover:bg-blue-700 text-white flex-shrink-0 transition-all duration-200 hover:scale-105'
						>
							<Save className='w-2.5 h-2.5 mr-1' />
							Save
						</Button>
					</div>
				</div>

				{/* Edit Form */}
				<div className='space-y-3'>
					<div>
						<div className='flex items-center space-x-2 mb-1.5'>
							<label className='block text-[10px] font-medium text-gray-700 dark:text-gray-300 uppercase tracking-wide'>
								{isApiType ? 'API Title' : 'Page Title'}
							</label>
							<div className='flex items-center'>
								<Tooltip
									content={
										isApiType
											? 'API endpoint title for documentation.'
											: 'Main page title for search results and browser tabs.'
									}
								>
									<HelpCircle className='w-2.5 h-2.5 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 cursor-help transition-colors duration-200' />
								</Tooltip>
							</div>
						</div>
						<input
							type='text'
							value={formData.title}
							onChange={(e) => handleInputChange('title', e.target.value)}
							placeholder={
								isApiType
									? "Enter API endpoint title (e.g. 'Create Project')"
									: "Enter your page title (e.g. 'Complete Guide to React')"
							}
							className='w-full px-3 py-2 text-sm bg-white dark:bg-slate-800 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:text-white transition-all duration-200'
							maxLength={50}
						/>
						<p className='text-[10px] text-gray-500 dark:text-gray-400 mt-1'>
							{formData.title.length}/50 characters{' '}
						</p>
					</div>

					<div>
						<div className='flex items-center space-x-2 mb-1.5'>
							<label className='block text-[10px] font-medium text-gray-700 dark:text-gray-300 uppercase tracking-wide'>
								{isApiType ? 'API Slug' : 'URL Slug'}
							</label>
							<div className='flex items-center'>
								<Tooltip
									content={
										isApiType
											? 'API endpoint slug for documentation.'
											: 'URL slug for your page address.'
									}
								>
									<HelpCircle className='w-2.5 h-2.5 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 cursor-help transition-colors duration-200' />
								</Tooltip>
							</div>
						</div>
						<input
							type='text'
							value={formData.slug}
							onChange={(e) =>
								handleInputChange(
									'slug',
									e.target.value
										.toLowerCase()
										.replace(/[^a-z0-9-/]/g, '-')
										.replace(/\/+/g, '/')
								)
							}
							placeholder={
								isApiType
									? "api-endpoint-slug (e.g. 'create-project')"
									: "page-url-slug (e.g. '/docs-introduction/start')"
							}
							className='w-full px-3 py-2 text-sm bg-white dark:bg-slate-800 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:text-white font-mono transition-all duration-200'
							maxLength={100}
						/>
						<p className='text-[10px] text-gray-500 dark:text-gray-400 mt-1'>
							Auto-formatted: only lowercase letters, numbers, hyphens and
							slashes allowed
						</p>
					</div>

					<div>
						<div className='flex items-center space-x-2 mb-1.5'>
							<label className='block text-[10px] font-medium text-gray-700 dark:text-gray-300 uppercase tracking-wide'>
								{isApiType ? 'API Description' : 'Meta Description'}
							</label>
							<div className='flex items-center'>
								<Tooltip
									content={
										isApiType
											? 'Description of what this API endpoint does.'
											: 'Appears in search results and social media. Write compelling text to encourage clicks.'
									}
								>
									<HelpCircle className='w-2.5 h-2.5 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 cursor-help transition-colors duration-200' />
								</Tooltip>
							</div>
						</div>
						<textarea
							value={formData.description}
							onChange={(e) => handleInputChange('description', e.target.value)}
							placeholder={
								isApiType
									? 'Describe what this API endpoint does and its purpose...'
									: 'Write a compelling description that will appear in search results and social media previews...'
							}
							rows={2}
							maxLength={200}
							className='w-full px-3 py-2 text-sm bg-white dark:bg-slate-800 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:text-white resize-none transition-all duration-200'
						/>
						<div className='flex justify-between items-center mt-1'>
							<p className='text-[10px] text-gray-500 dark:text-gray-400'>
								{formData.description.length}/200 characters
							</p>
						</div>
					</div>

					{isApiType && (
						<div>
							<div className='flex items-center space-x-2 mb-1.5'>
								<label className='block text-[10px] font-medium text-orange-600 dark:text-orange-400 uppercase tracking-wide'>
									API Route
								</label>
								<div className='flex items-center'>
									<Tooltip content='API endpoint route. This field is automatically filled and cannot be edited.'>
										<HelpCircle className='w-2.5 h-2.5 text-orange-400 hover:text-orange-600 dark:hover:text-orange-300 cursor-help transition-colors duration-200' />
									</Tooltip>
								</div>
							</div>
							<input
								type='text'
								value={formData.route || ''}
								readOnly
								disabled
								placeholder='Route will be automatically filled'
								className='w-full px-3 py-2 text-sm bg-gray-100 dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-md text-gray-600 dark:text-gray-400 font-mono cursor-not-allowed transition-all duration-200'
							/>
							<p className='text-[10px] text-orange-500 dark:text-orange-400 mt-1'>
								🔒 This field is automatically populated and cannot be edited
							</p>
						</div>
					)}
				</div>
			</div>
		</NodeViewWrapper>
	);
};

// Tiptap extension definition
export const MetadataNode = Node.create({
	name: 'metadataNode',

	group: 'block',

	atom: true,

	draggable: false,

	selectable: true,

	addAttributes() {
		return {
			title: {
				default: '',
			},
			description: {
				default: '',
			},
			slug: {
				default: '',
			},
			route: {
				default: null,
			},
		};
	},

	parseHTML() {
		return [
			{
				tag: 'metadata',
				getAttrs: (element) => {
					console.log(
						'🔍 MetadataNode parseHTML (metadata tag) chamado:',
						element
					);

					if (!(element instanceof HTMLElement)) {
						console.log('❌ Element não é HTMLElement:', element);
						return false;
					}

					const title = element.getAttribute('title') || '';
					const description = element.getAttribute('description') || '';
					const slug = element.getAttribute('slug') || '';
					const route = element.getAttribute('route') || null;

					console.log('🔍 Metadata extraída (attributes):', {
						title,
						description,
						slug,
						route,
					});

					return { title, description, slug, route };
				},
			},
			{
				tag: 'head[data-type="metadata"]',
				getAttrs: (element) => {
					console.log('🔍 MetadataNode parseHTML (head tag) chamado:', element);

					if (!(element instanceof HTMLElement)) {
						console.log('❌ Element não é HTMLElement:', element);
						return false;
					}

					const title =
						element
							.querySelector('meta[name="title"]')
							?.getAttribute('content') || '';
					const description =
						element
							.querySelector('meta[name="description"]')
							?.getAttribute('content') || '';
					const slug =
						element
							.querySelector('meta[name="slug"]')
							?.getAttribute('content') || '';
					const route =
						element
							.querySelector('meta[name="route"]')
							?.getAttribute('content') || null;

					console.log('🔍 Metadata extraída (meta tags):', {
						title,
						description,
						slug,
						route,
					});

					return { title, description, slug, route };
				},
			},
		];
	},

	renderHTML({ HTMLAttributes }) {
		const attrs: Record<string, string> = {
			'data-type': 'metadata',
		};

		// Only include attributes that have non-empty values
		if (HTMLAttributes['title'] && typeof HTMLAttributes['title'] === 'string' && HTMLAttributes['title'].trim() !== '') {
			attrs.title = HTMLAttributes['title'];
		}

		if (
			HTMLAttributes['description'] &&
			typeof HTMLAttributes['description'] === 'string' &&
			HTMLAttributes['description'].trim() !== ''
		) {
			attrs.description = HTMLAttributes['description'];
		}

		if (HTMLAttributes['slug'] && typeof HTMLAttributes['slug'] === 'string' && HTMLAttributes['slug'].trim() !== '') {
			attrs.slug = HTMLAttributes['slug'];
		}

		// Only include route attribute if it exists and is not empty (API files only)
		if (
			HTMLAttributes['route'] &&
			HTMLAttributes['route'] !== null &&
			typeof HTMLAttributes['route'] === 'string' &&
			HTMLAttributes['route'].trim() !== ''
		) {
			attrs.route = HTMLAttributes['route'];
		}

		return ['metadata', attrs];
	},

	addNodeView() {
		return ReactNodeViewRenderer(MetadataComponent);
	},

	addKeyboardShortcuts() {
		return {
			Backspace: ({ editor }) => {
				const { selection } = editor.state;
				const { $from } = selection;

				// Check if cursor is in a metadata node or right after
				if ($from.parent.type.name === this.name) {
					// Prevent deletion by moving cursor outside the node
					const pos = $from.after();
					editor.commands.setTextSelection(pos);
					return true; // Prevent default behavior
				}

				// Check if trying to delete a metadata node
				const nodeBefore = $from.nodeBefore;
				if (nodeBefore && nodeBefore.type.name === this.name) {
					// Don't allow deleting the metadata node
					return true; // Prevent default behavior
				}

				return false; // Allow default behavior
			},
			Delete: ({ editor }) => {
				const { selection } = editor.state;
				const { $from } = selection;

				// Check if cursor is in a metadata node
				if ($from.parent.type.name === this.name) {
					// Prevent deletion by moving cursor after the node
					const pos = $from.after();
					editor.commands.setTextSelection(pos);
					return true; // Prevent default behavior
				}

				// Check if trying to delete a metadata node ahead
				const nodeAfter = $from.nodeAfter;
				if (nodeAfter && nodeAfter.type.name === this.name) {
					// Don't allow deleting the metadata node
					return true; // Prevent default behavior
				}

				return false; // Allow default behavior
			},
			'Ctrl-a': ({ editor }) => {
				// Prevent Ctrl+A from selecting metadata node for accidental deletion
				const { tr } = editor.state;
				const { doc } = tr;

				// Find position of first editable content (after metadata)
				let firstEditablePos = 0;
				doc.descendants((node, pos) => {
					if (node.type.name === this.name) {
						firstEditablePos = pos + node.nodeSize;
						return false;
					}
				});

				if (firstEditablePos > 0) {
					// Select everything except the metadata node
					editor.commands.setTextSelection({
						from: firstEditablePos,
						to: doc.content.size,
					});
					return true; // Prevent default behavior
				}

				return false; // Allow default behavior
			},
		};
	},
});

// Utility function to extract metadata from HTML
export const parseMetadataFromHTML = (html: string): MetadataAttrs => {
	const parser = new DOMParser();
	const doc = parser.parseFromString(html, 'text/html');

	const routeContent = doc
		.querySelector('meta[name="route"]')
		?.getAttribute('content');

	return {
		title:
			doc.querySelector('meta[name="title"]')?.getAttribute('content') || '',
		description:
			doc.querySelector('meta[name="description"]')?.getAttribute('content') ||
			'',
		slug: doc.querySelector('meta[name="slug"]')?.getAttribute('content') || '',
		route: routeContent || null,
	};
};

// Export utility function for API route generation
export { generateApiRoute };

export default MetadataNode;
