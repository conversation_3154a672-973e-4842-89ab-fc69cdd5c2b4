"use client";

import React, { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import {
	Table,
	TableBody,
	TableCell,
	TableHead,
	TableHeader,
	TableRow,
} from "@/components/ui/table";
import { UserPlus } from "lucide-react";
import { useMembers } from "@/hooks/useMembers";
import { useMemberPermissions } from "@/hooks/useMemberPermissions";
import { MemberTableRow } from "./MemberTableRow";
import { InviteTableRow } from "./InviteTableRow";
import { InviteMemberDialog } from "./InviteMemberDialog";
import { MembersTabSkeleton } from "./MembersTabSkeleton";

export const MembersTab: React.FC = () => {
	const [isInviteDialogOpen, setIsInviteDialogOpen] = useState(false);

	const {
		members,
		pendingInvites,
		isLoading,
		currentUserRole,
		optimisticChanges,
		handleInviteMember,
		handleRemoveMember,
		handleLeaveProject,
		handleUpdateRole,
		handleCancelInvite,
	} = useMembers();

	const { canManageMember, getAvailableRoleActions, canInviteMembers } =
		useMemberPermissions(currentUserRole);

	if (isLoading) {
		return <MembersTabSkeleton />;
	}

	const totalCount = members.length + pendingInvites.length;

	return (
		<div className='space-y-6'>
			{/* Header */}
			<div className='flex items-center justify-between'>
				<div>
					<h2 className='text-lg font-semibold'>Project Members</h2>
					<p className='text-sm text-muted-foreground'>
						Manage who has access to this project ({totalCount} total)
					</p>
				</div>
				{canInviteMembers && (
					<Button
						onClick={() => setIsInviteDialogOpen(true)}
						variant='outline'
						size='sm'
					>
						<UserPlus className='h-3 w-3 mr-2' />
						Invite Member
					</Button>
				)}
			</div>

			{/* Members Table */}
			<div className='border rounded-lg'>
				<Table>
					<TableHeader>
						<TableRow>
							<TableHead>Email</TableHead>
							<TableHead>Role</TableHead>
							<TableHead>Status</TableHead>
							<TableHead className='w-[50px]'></TableHead>
						</TableRow>
					</TableHeader>
					<TableBody>
						{totalCount === 0 ? (
							<TableRow>
								<TableCell
									colSpan={4}
									className='text-center py-8 text-muted-foreground'
								>
									No members or invites found
								</TableCell>
							</TableRow>
						) : (
							<>
								{/* Active Members */}
								{members.map((member) => (
									<MemberTableRow
										key={`member-${member.user_id}`}
										member={member}
										canManage={canManageMember(member)}
										roleActions={getAvailableRoleActions(member)}
										isProcessingRemoval={optimisticChanges.processingRemovals.has(
											member.user_id
										)}
										isProcessingRoleChange={
											optimisticChanges.roleChanges[member.user_id]
												?.isProcessing || false
										}
										onUpdateRole={handleUpdateRole}
										onRemoveMember={handleRemoveMember}
										onLeaveProject={handleLeaveProject}
									/>
								))}

								{/* Pending Invites */}
								{pendingInvites.map((invite) => (
									<InviteTableRow
										key={`invite-${invite.id}`}
										invite={invite}
										canManage={canInviteMembers}
										isProcessingCancel={optimisticChanges.processingInviteRemovals.has(
											invite.id
										)}
										onCancelInvite={handleCancelInvite}
									/>
								))}
							</>
						)}
					</TableBody>
				</Table>
			</div>

			{/* Invite Member Dialog */}
			<InviteMemberDialog
				isOpen={isInviteDialogOpen}
				onOpenChange={setIsInviteDialogOpen}
				onInvite={handleInviteMember}
				currentUserRole={currentUserRole}
			/>
		</div>
	);
};
