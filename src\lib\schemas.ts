import { z } from "zod";

export const passwordSchema = z
  .string()
  .min(1, "Password is required")
  .min(8, "Password must have at least 8 characters")
  .refine((password) => /[A-Z]/.test(password), {
    message: "Password must contain at least 1 uppercase letter",
  })
  .refine((password) => /[a-z]/.test(password), {
    message: "Password must contain at least 1 lowercase letter",
  })
  .refine((password) => /[0-9]/.test(password), {
    message: "Password must contain at least 1 number",
  })
  .refine((password) => /[!@#$%^&*\-_]/.test(password), {
    message: "Password must contain at least 1 special character (!@#$%^&*_-)",
  });

export const emailSchema = z
  .string()
  .min(1, "Email is required")
  .email("Invalid email format");

export const nameSchema = z
  .string()
  .min(1, "Name is required")
  .transform((s) => s.replace(/\s\s+/g, " ").trim())
  .refine((s) => /^[a-zA-Z\s]{2,}$/.test(s), {
    message:
      "Name must have 2 or more characters and contain only letters and spaces",
  })
  .refine((s) => !s.startsWith(" ") && !s.endsWith(" "), {
    message: "Name cannot start or end with space",
  });
