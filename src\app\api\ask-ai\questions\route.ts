import { NextRequest, NextResponse } from "next/server";
import { validateDocsBotKey, fetchDocsBotQuestions } from "../utils/docsbot";

export const runtime = 'edge';

export async function GET(request: NextRequest) {
	try {
		// Get API key from environment variables
		const apiKey = process.env.DOCSBOT_API_KEY;
		if (!apiKey) {
			return NextResponse.json(
				{ error: "API key not configured" },
				{ status: 500 }
			);
		}

		// Get query parameters from the request
		const { searchParams } = new URL(request.url);
		const docsbotKey = searchParams.get("docsbotKey");
		const page = searchParams.get("page") || "0";
		const rating = searchParams.get("rating");
		const escalated = searchParams.get("escalated");
		const couldAnswer = searchParams.get("couldAnswer");
		const startDate = searchParams.get("startDate");
		const endDate = searchParams.get("endDate");

		try {
			// Validate docsbotKey and extract teamId and botId
			const { teamId, botId } = validateDocsBotKey(docsbotKey);

			// Fetch questions from DocsBot API
			const data = await fetchDocsBotQuestions(
				teamId,
				botId,
				apiKey,
				page,
				rating,
				escalated,
				couldAnswer,
				startDate,
				endDate
			);

			return NextResponse.json(
				{
					success: true,
					data,
				},
				{ status: 200 }
			);
		} catch (error) {
			if (error instanceof Error) {
				return NextResponse.json({ error: error.message }, { status: 400 });
			}
			throw error;
		}
	} catch (error) {
		console.error("Error in questions API route:", error);

		return NextResponse.json(
			{
				success: false,
				error: "Internal server error",
				details: error instanceof Error ? error.message : String(error),
			},
			{ status: 500 }
		);
	}
}
