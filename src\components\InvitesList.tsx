"use client";
import { useEffect, useState } from "react";
import { useRouter } from "next/navigation";
import { Button } from "./ui/button";
import { createClient } from "@/utils/supabase/client";
import { useToast } from "@/components/ToastProvider";
import {
  <PERSON>alog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";

type Invite = {
  id: number;
  project_id: number;
  email: string;
  status: string;
  project: {
    project_name: string;
  };
};

interface InvitesListProps {
  variant?: "sidebar" | "page" | "dropdown" | "modal";
  open?: boolean;
  onOpenChange?: (open: boolean) => void;
  onInvitesChange?: (count: number) => void;
}

export function InvitesList({
  variant = "sidebar",
  open,
  onOpenChange,
  onInvitesChange,
}: InvitesListProps) {
  const [invites, setInvites] = useState<Invite[]>([]);
  const router = useRouter();
  const { addToast } = useToast();

  useEffect(() => {
    // Notifica o componente pai sobre mudanças na quantidade de convites
    onInvitesChange?.(invites.length);
  }, [invites, onInvitesChange]);

  async function loadInvites() {
    try {
      const res = await fetch("/api/my-invites");
      const data = (await res.json()) as { invites?: Invite[] };
      setInvites(data.invites || []);
      onInvitesChange?.(data.invites?.length || 0);
    } catch (error) {
      console.error("Error loading invites:", error);
    }
  }

  useEffect(() => {
    loadInvites();
  }, []); // Movemos o loadInvites inicial para um useEffect separado

  useEffect(() => {
    const supabase = createClient();

    const channel = supabase
      .channel("invite_changes")
      .on(
        "postgres_changes",
        {
          event: "*",
          schema: "public",
          table: "project_invites",
          filter: `status=eq.invited`,
        },
        async (payload) => {
          if (payload) {
            await loadInvites();
            if (variant === "modal" && onOpenChange) {
              onOpenChange(true);
            }
          }
        }
      )
      .subscribe();

    // Cleanup na desmontagem
    return () => {
      supabase.removeChannel(channel);
    };
  }, [variant, onOpenChange]);

  const handleAccept = async (inviteId: number, projectId: number) => {
    try {
      const res = await fetch("/api/my-invites", {
        method: "PATCH",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({ inviteId, projectId }),
      });

      const data = (await res.json()) as { error?: string };

      if (!res.ok) {
        throw new Error(data.error || "Error accepting invite");
      }

      setInvites((prev) => {
        const newInvites = prev.filter((inv) => inv.id !== inviteId);
        onInvitesChange?.(newInvites.length);
        return newInvites;
      });

      addToast("Invite accepted successfully!", "success");

      // Redirecionar para o dashboard do projeto específico
      if (projectId) {
        router.push(`/${projectId}/dashboard`);
      } else {
        // Se estiver na página de onboarding, redireciona para home
        if (window.location.pathname.includes("/onboarding")) {
          router.push("/dashboard");
        } else {
          router.refresh();
        }
      }
    } catch (error) {
      console.error("Detailed error:", error);
      addToast("Error accepting invite", "error");
    }
  };

  const handleReject = async (inviteId: number) => {
    try {
      const res = await fetch("/api/my-invites", {
        method: "DELETE",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({ inviteId }),
      });

      if (res.ok) {
        setInvites((prev) => {
          const newInvites = prev.filter((inv) => inv.id !== inviteId);
          onInvitesChange?.(newInvites.length);
          return newInvites;
        });
        addToast("Invite declined successfully!", "success");
        return;
      }

      throw new Error("Failed to decline invite");
    } catch (error) {
      console.error("Detailed error:", error);
      addToast("Error declining invite", "error");
    }
  };

  if (invites.length === 0) return null;

  if (variant === "dropdown") {
    return (
      <div className="border-t border-gray-100 mt-2">
        <div className="px-3 py-1.5 text-[11px] font-medium text-gray-500 bg-gray-50">
          Pending Invites
        </div>
        {invites.map((invite, index) => (
          <div key={invite.id}>
            <div className="px-3 py-2 flex flex-col">
              <span className="text-sm text-gray-700 font-medium truncate mb-2">
                {invite.project?.project_name || `Project ${invite.project_id}`}
              </span>
              <div className="flex justify-end gap-2">
                <button
                  onClick={() => handleReject(invite.id)}
                  className="text-xs bg-red-600 hover:bg-red-700 text-white px-2 py-1 rounded"
                >
                  Decline
                </button>
                <button
                  onClick={() => handleAccept(invite.id, invite.project_id)}
                  className="text-xs bg-green-600 hover:bg-green-700 text-white px-2 py-1 rounded"
                >
                  Accept
                </button>
              </div>
            </div>
            {index < invites.length - 1 && (
              <div className="border-b border-gray-100" />
            )}
          </div>
        ))}
      </div>
    );
  }

  if (variant === "page") {
    return (
      <div className="max-w-4xl mx-auto mt-8">
        <h2 className="text-2xl font-bold text-gray-900 mb-6">
          Pending Invites
        </h2>
        <div className="space-y-4">
          {invites.map((invite) => (
            <div
              key={invite.id}
              className="flex items-center justify-between p-6 border border-gray-200 rounded-lg bg-white shadow-sm hover:shadow-md transition-shadow duration-200"
            >
              <div className="space-y-1">
                <h3 className="text-lg font-semibold text-gray-900">
                  {invite.project?.project_name ||
                    `Project ${invite.project_id}`}
                </h3>
                <p className="text-sm text-gray-500">
                  Invitation to join project as collaborator
                </p>
              </div>
              <div className="flex gap-3">
                <Button
                  onClick={() => handleReject(invite.id)}
                  className="px-4 bg-white text-red-600 border border-red-600 hover:bg-red-50 hover:border-red-700 hover:text-red-700"
                >
                  Decline
                </Button>
                <Button
                  onClick={() => handleAccept(invite.id, invite.project_id)}
                  className="px-4 bg-green-600 text-white hover:bg-green-700"
                >
                  Accept
                </Button>
              </div>
            </div>
          ))}
        </div>
      </div>
    );
  }

  if (variant === "modal") {
    return (
      <Dialog open={open} onOpenChange={onOpenChange}>
        <DialogContent className="sm:max-w-[425px] p-0 bg-gray-50 overflow-hidden rounded-lg">
          <DialogHeader className="p-4 pb-3 bg-white border-b border-gray-100">
            <DialogTitle className="text-base font-medium text-gray-900">
              Pending Invites
            </DialogTitle>
          </DialogHeader>
          <div className="p-4 pt-0 space-y-3">
            {invites.map((invite) => (
              <div
                key={invite.id}
                className="flex flex-col gap-3 p-3 border border-gray-200 rounded-md bg-white"
              >
                <div className="space-y-1">
                  <h3 className="text-sm font-medium text-gray-900">
                    {invite.project?.project_name ||
                      `Project ${invite.project_id}`}
                  </h3>
                  <p className="text-xs text-gray-500">
                    Invitation to join project as collaborator
                  </p>
                </div>
                <div className="flex justify-end gap-2">
                  <Button
                    onClick={() => handleReject(invite.id)}
                    type="button"
                    variant="outline"
                    size="sm"
                    className="h-8 bg-white border border-gray-200 text-gray-700 hover:bg-gray-50"
                  >
                    Decline
                  </Button>
                  <Button
                    onClick={() => handleAccept(invite.id, invite.project_id)}
                    size="sm"
                    className="h-8 bg-blue-600 text-white hover:bg-blue-700"
                  >
                    Accept
                  </Button>
                </div>
              </div>
            ))}
          </div>
        </DialogContent>
      </Dialog>
    );
  }

  // Versão sidebar (mantém o design existente)
  return (
    <div className="text-white text-sm mt-6 border-t border-[#B4BBDE] pt-4 px-2">
      <p className="mb-2 text-xs font-bold">Pending Invites</p>
      {invites.map((invite) => (
        <div key={invite.id} className="flex items-center justify-between mb-2">
          <span>
            {invite.project?.project_name || `Project ${invite.project_id}`}
          </span>
          <div className="flex gap-2">
            <button
              className="text-xs bg-red-600 px-2 py-1 rounded"
              onClick={() => handleReject(invite.id)}
            >
              Decline
            </button>
            <button
              className="text-xs bg-green-600 px-2 py-1 rounded"
              onClick={() => handleAccept(invite.id, invite.project_id)}
            >
              Accept
            </button>
          </div>
        </div>
      ))}
    </div>
  );
}
