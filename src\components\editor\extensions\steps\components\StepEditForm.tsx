import React, { useState, useCallback } from "react";
import { Save, X } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { RichTextInput } from "@/components/ui/RichTextInput";
import { useToast } from "@/components/ToastProvider";
import { StepEditFormProps, StepData } from "../types";

export const StepEditForm: React.FC<StepEditFormProps> = ({
  step,
  onSave,
  onCancel,
  isFirstStepInLevel = false,
}) => {
  const [formData, setFormData] = useState<StepData>({
    ...step,
    subSteps: step.subSteps || [],
  });
  const { addToast } = useToast();

  const handleInputChange = useCallback(
    (field: keyof StepData, value: string) => {
      setFormData((prev) => ({ ...prev, [field]: value }));
    },
    []
  );

  const handleSave = useCallback(() => {
    if (!formData.title.trim()) {
      addToast("Title is required", "warning");
      return;
    }

    const contentText = formData.content.replace(/<[^>]*>/g, "").trim();
    if (!contentText) {
      addToast("Content is required", "warning");
      return;
    }

    onSave({
      ...formData,
      content: formData.content || "<p></p>",
    });
  }, [formData, onSave, addToast]);

  return (
    <div className="border border-gray-200 dark:border-gray-700 rounded-lg p-4 bg-white dark:bg-gray-800 w-full">
      <div className="flex items-center justify-between mb-4">
        <h4 className="text-sm font-semibold text-gray-900 dark:text-gray-100">
          Edit Step
        </h4>
        <div className="flex items-center space-x-2">
          <Button
            variant="ghost"
            size="sm"
            onClick={onCancel}
            className="h-6 px-2 text-xs hover:bg-red-100/50 dark:hover:bg-red-900/30 text-red-600 dark:text-red-400"
          >
            <X className="w-3 h-3 mr-1" />
            Cancel
          </Button>
          <Button
            variant="default"
            size="sm"
            onClick={handleSave}
            className="h-6 px-2 text-xs bg-blue-600 hover:bg-blue-700 text-white"
          >
            <Save className="w-3 h-3 mr-1" />
            Save
          </Button>
        </div>
      </div>

      <div className="space-y-4">
        <div>
          <label className="block text-xs font-medium text-gray-700 dark:text-gray-300 mb-1">
            Title *
          </label>
          <input
            type="text"
            value={formData.title}
            onChange={(e) => handleInputChange("title", e.target.value)}
            placeholder="Enter step title"
            className="w-full px-3 py-2 text-sm bg-white dark:bg-slate-700 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:text-white"
            required
          />
        </div>

        {isFirstStepInLevel && (
          <div>
            <label className="block text-xs font-medium text-gray-700 dark:text-gray-300 mb-1">
              Title Size (applies to all steps in this level)
            </label>
            <select
              value={formData.titleSize}
              onChange={(e) => handleInputChange("titleSize", e.target.value)}
              className="w-full px-3 py-2 text-sm bg-white dark:bg-slate-700 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:text-white"
            >
              <option value="h2">H2 - Large</option>
              <option value="h3">H3 - Medium (Default)</option>
              <option value="h4">H4 - Small</option>
              <option value="h5">H5 - Extra Small</option>
              <option value="h6">H6 - Tiny</option>
            </select>
          </div>
        )}

        <div>
          <label className="block text-xs font-medium text-gray-700 dark:text-gray-300 mb-1">
            Content *
          </label>
          <RichTextInput
            value={formData.content}
            onChange={(html) => handleInputChange("content", html)}
            placeholder="Enter step content"
            variant="default"
            className="w-full"
            enableCodeBlock={true}
            enableLink={true}
          />
        </div>
      </div>
    </div>
  );
};
