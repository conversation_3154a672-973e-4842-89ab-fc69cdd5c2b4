'use client';

import { useState, useCallback, useMemo } from 'react';
import { type OrganizationImage } from '@/hooks/useOrganizationImages';

interface UseImageSelectionProps {
	images: OrganizationImage[];
	onSelectionChange?: (selectedImages: OrganizationImage[]) => void;
}

export const useImageSelection = ({
	images,
	onSelectionChange,
}: UseImageSelectionProps) => {
	const [selectedImageIds, setSelectedImageIds] = useState<Set<string>>(new Set());

	// Get selected images
	const selectedImages = useMemo(() => {
		return images.filter(image => selectedImageIds.has(image.id));
	}, [images, selectedImageIds]);

	// Selection count
	const selectionCount = selectedImageIds.size;

	// Check if all images are selected
	const isAllSelected = useMemo(() => {
		return images.length > 0 && selectedImageIds.size === images.length;
	}, [images.length, selectedImageIds.size]);

	// Check if some (but not all) images are selected
	const isIndeterminate = useMemo(() => {
		return selectedImageIds.size > 0 && selectedImageIds.size < images.length;
	}, [images.length, selectedImageIds.size]);

	// Toggle single image selection
	const toggleImageSelection = useCallback((imageId: string) => {
		setSelectedImageIds(prev => {
			const newSet = new Set(prev);
			if (newSet.has(imageId)) {
				newSet.delete(imageId);
			} else {
				newSet.add(imageId);
			}
			
			// Call callback with updated selection
			const updatedImages = images.filter(img => newSet.has(img.id));
			onSelectionChange?.(updatedImages);
			
			return newSet;
		});
	}, [images, onSelectionChange]);

	// Check if specific image is selected
	const isImageSelected = useCallback((imageId: string) => {
		return selectedImageIds.has(imageId);
	}, [selectedImageIds]);

	// Select all images
	const selectAll = useCallback(() => {
		const allIds = new Set(images.map(img => img.id));
		setSelectedImageIds(allIds);
		onSelectionChange?.(images);
	}, [images, onSelectionChange]);

	// Deselect all images
	const deselectAll = useCallback(() => {
		setSelectedImageIds(new Set());
		onSelectionChange?.([]);
	}, [onSelectionChange]);

	// Toggle all selection
	const toggleSelectAll = useCallback(() => {
		if (isAllSelected) {
			deselectAll();
		} else {
			selectAll();
		}
	}, [isAllSelected, selectAll, deselectAll]);

	// Clear selection (useful after deletion)
	const clearSelection = useCallback(() => {
		setSelectedImageIds(new Set());
		onSelectionChange?.([]);
	}, [onSelectionChange]);

	// Remove specific images from selection (useful when images are deleted)
	const removeFromSelection = useCallback((imageIds: string[]) => {
		setSelectedImageIds(prev => {
			const newSet = new Set(prev);
			imageIds.forEach(id => newSet.delete(id));
			
			const updatedImages = images.filter(img => newSet.has(img.id));
			onSelectionChange?.(updatedImages);
			
			return newSet;
		});
	}, [images, onSelectionChange]);

	// Select specific images
	const selectImages = useCallback((imageIds: string[]) => {
		setSelectedImageIds(prev => {
			const newSet = new Set(prev);
			imageIds.forEach(id => newSet.add(id));
			
			const updatedImages = images.filter(img => newSet.has(img.id));
			onSelectionChange?.(updatedImages);
			
			return newSet;
		});
	}, [images, onSelectionChange]);

	return {
		// Selection state
		selectedImages,
		selectedImageIds,
		selectionCount,
		isAllSelected,
		isIndeterminate,
		
		// Selection actions
		toggleImageSelection,
		isImageSelected,
		selectAll,
		deselectAll,
		toggleSelectAll,
		clearSelection,
		removeFromSelection,
		selectImages,
	};
};
