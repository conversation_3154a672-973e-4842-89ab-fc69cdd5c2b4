import React from "react";
import { Mail, Check, X } from "lucide-react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import type { ProjectInvite } from "@/contexts/ProjectContext/types";

interface InviteCardProps {
	invite: ProjectInvite;
	onAccept: (inviteId: number, projectId: number) => Promise<boolean>;
	onDecline: (inviteId: number) => Promise<boolean>;
	isProcessing?: boolean;
}

export const InviteCard: React.FC<InviteCardProps> = ({
	invite,
	onAccept,
	onDecline,
	isProcessing = false,
}) => {
	const handleAccept = () => {
		onAccept(invite.id, invite.project_id);
	};

	const handleDecline = () => {
		onDecline(invite.id);
	};

	return (
		<Card className='relative overflow-hidden border-2 border-blue-200 bg-gradient-to-br from-blue-50 to-indigo-50 hover:shadow-lg transition-all duration-200 ease-in-out transform hover:-translate-y-1 cursor-pointer'>
			{/* Decorative accent */}
			<div className='absolute top-0 left-0 w-full h-1 bg-gradient-to-r from-blue-500 to-indigo-500' />

			<CardHeader className='items-center text-center p-6 pb-4'>
				<div className='w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center mb-3'>
					<Mail className='w-6 h-6 text-blue-600' />
				</div>
				<Badge
					variant='secondary'
					className='mb-2 bg-blue-100 text-blue-700 hover:bg-blue-100'
				>
					Invitation
				</Badge>
				<CardTitle className='text-lg font-semibold text-gray-800 text-center leading-tight'>
					{invite.project?.project_name || `Project ${invite.project_id}`}
				</CardTitle>
			</CardHeader>

			<CardContent className='text-center px-6 pb-6'>
				<p className='text-sm text-gray-600 mb-4'>
					You&apos;ve been invited to join this project as a collaborator
				</p>

				<div className='flex gap-2 justify-center'>
					<Button
						onClick={handleDecline}
						variant='outline'
						size='sm'
						disabled={isProcessing}
						className='flex items-center gap-1 border-red-200 text-red-600 hover:bg-red-50 hover:border-red-300'
					>
						<X className='w-4 h-4' />
						Decline
					</Button>
					<Button
						onClick={handleAccept}
						size='sm'
						disabled={isProcessing}
						className='flex items-center gap-1 bg-blue-600 hover:bg-blue-700 text-white'
					>
						<Check className='w-4 h-4' />
						Accept
					</Button>
				</div>
			</CardContent>
		</Card>
	);
};
