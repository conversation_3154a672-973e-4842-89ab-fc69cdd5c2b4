'use client';

import { useState, useEffect, useRef } from 'react';
import { Button } from '@/components/ui/button';
import { useToast } from '@/components/ToastProvider';
import { useProject } from '@/contexts';
import { RotateCcw, Loader2, Timer } from 'lucide-react';
import { createClient } from '@/utils/supabase/client';
import { cn } from '@/lib/utils';
import type { ConfigJson } from '@/types/sidebarConfig';
import {
	Tooltip,
	TooltipProvider,
	TooltipTrigger,
} from '@/components/ui/tooltip';

interface RetryDeploymentButtonProps {
	className?: string;
	variant?:
		| 'default'
		| 'secondary'
		| 'destructive'
		| 'outline'
		| 'ghost'
		| 'link';
	size?: 'default' | 'sm' | 'lg' | 'icon';
}

interface RetryDeploymentResponse {
	success?: boolean;
	message?: string;
	error?: string;
	deployment?: {
		id: string;
		short_id: string;
		url: string;
		environment: string;
		status: string;
		created_on: string;
	};
}

export function RetryDeploymentButton({
	className,
	variant = 'outline',
	size = 'default',
}: RetryDeploymentButtonProps) {
	const [isLoading, setIsLoading] = useState(false);
	const [isHolding, setIsHolding] = useState(false);
	const [holdProgress, setHoldProgress] = useState(0);
	const [cooldownRemaining, setCooldownRemaining] = useState(0);
	const [canDeploy, setCanDeploy] = useState(true);
	const [homepageExists, setHomepageExists] = useState(true);
	const [homepageError, setHomepageError] = useState<string | null>(null);

	const holdTimerRef = useRef<NodeJS.Timeout | null>(null);
	const cooldownTimerRef = useRef<NodeJS.Timeout | null>(null);
	const progressTimerRef = useRef<NodeJS.Timeout | null>(null);

	const { addToast } = useToast();
	const { selectedProject } = useProject();
	const supabase = createClient();

	const HOLD_DURATION = 4000; // 4 seconds
	const COOLDOWN_DURATION = 120000; // 2 minutes

	useEffect(() => {
		if (!selectedProject) return;
		checkDeploymentCooldown();
		checkHomepageExists();

		const subscription = supabase
			.channel(`project-deployments-${selectedProject.id}`)
			.on(
				'postgres_changes',
				{
					event: 'UPDATE',
					schema: 'public',
					table: 'projects',
					filter: `id=eq.${selectedProject.id}`,
				},
				() => {
					checkDeploymentCooldown();
					checkHomepageExists();
				}
			)
			.subscribe();

		return () => {
			subscription.unsubscribe();
			if (cooldownTimerRef.current) {
				clearInterval(cooldownTimerRef.current);
			}
		};
	}, [selectedProject]);

	useEffect(() => {
		return () => {
			if (holdTimerRef.current) clearTimeout(holdTimerRef.current);
			if (progressTimerRef.current) clearInterval(progressTimerRef.current);
		};
	}, []);

	const checkDeploymentCooldown = async () => {
		if (!selectedProject) return;

		try {
			const { data, error } = await supabase
				.from('projects')
				.select('last_deployment_at')
				.eq('id', selectedProject.id)
				.single();

			if (error) {
				console.error('Error checking deployment cooldown:', error);
				return;
			}

			if (data?.last_deployment_at) {
				const lastDeployment = new Date(data.last_deployment_at);
				const now = new Date();
				const timeDiff = now.getTime() - lastDeployment.getTime();

				if (timeDiff < COOLDOWN_DURATION) {
					const remaining = COOLDOWN_DURATION - timeDiff;
					setCooldownRemaining(remaining);
					setCanDeploy(false);
					startCooldownTimer(remaining);
				} else {
					setCanDeploy(true);
					setCooldownRemaining(0);
				}
			} else {
				setCanDeploy(true);
				setCooldownRemaining(0);
			}
		} catch (error) {
			console.error('Error checking deployment cooldown:', error);
		}
	};

	const checkHomepageExists = async () => {
		if (!selectedProject) return;

		try {
			const { data, error } = await supabase
				.from('projects')
				.select('configjson')
				.eq('id', selectedProject.id)
				.single();

			if (error) {
				console.error('Error checking homepage:', error);
				setHomepageError('Error checking homepage');
				setHomepageExists(false);
				return;
			}

			const config = data?.configjson as ConfigJson;

			// Check if homepage is defined
			if (!config?.homepage) {
				setHomepageError(
					'No homepage is set. Right-click on any page in the file tree and select "Set as Homepage" to configure it.'
				);
				setHomepageExists(false);
				return;
			}

			// Check if the homepage page exists in the database
			// Special case: if homepage is 'homepage.html', check for custom homepage with path '/'
			let homepagePathToCheck = config.homepage;
			if (config.homepage === 'homepage.html') {
				homepagePathToCheck = '/';
			}

			const { data: pageData, error: pageError } = await supabase
				.from('project_pages')
				.select('path')
				.eq('project_id', selectedProject.id)
				.eq('path', homepagePathToCheck)
				.single();

			if (pageError || !pageData) {
				setHomepageError(
					`Right-click on an existing page in the file tree and select "Set as Homepage".`
				);
				setHomepageExists(false);
				return;
			}

			// Homepage exists
			setHomepageExists(true);
			setHomepageError(null);
		} catch (error) {
			console.error('Error checking homepage:', error);
			setHomepageError('Error checking homepage');
			setHomepageExists(false);
		}
	};

	const startCooldownTimer = (initialRemaining: number) => {
		if (cooldownTimerRef.current) {
			clearInterval(cooldownTimerRef.current);
		}

		let remaining = initialRemaining;
		cooldownTimerRef.current = setInterval(() => {
			remaining -= 1000;
			if (remaining <= 0) {
				setCanDeploy(true);
				setCooldownRemaining(0);
				if (cooldownTimerRef.current) {
					clearInterval(cooldownTimerRef.current);
				}
			} else {
				setCooldownRemaining(remaining);
			}
		}, 1000);
	};

	const handleMouseDown = () => {
		if (!canDeploy || isLoading) return;

		// If homepage doesn't exist, show error immediately without hold
		if (!homepageExists) {
			handleRetryDeployment();
			return;
		}

		setIsHolding(true);
		setHoldProgress(0);

		let progress = 0;
		progressTimerRef.current = setInterval(() => {
			progress += 100 / (HOLD_DURATION / 100);
			setHoldProgress(progress);
		}, 100);

		holdTimerRef.current = setTimeout(() => {
			handleRetryDeployment();
		}, HOLD_DURATION);
	};

	const handleMouseUp = () => {
		// Don't cancel if homepage doesn't exist (immediate action)
		if (!homepageExists) return;

		if (holdTimerRef.current) clearTimeout(holdTimerRef.current);
		if (progressTimerRef.current) clearInterval(progressTimerRef.current);

		setIsHolding(false);
		setHoldProgress(0);
	};

	const handleRetryDeployment = async () => {
		if (!selectedProject) {
			addToast('No project selected', 'error', 'Error');
			return;
		}

		if (!homepageExists) {
			addToast(
				homepageError ||
					'Right-click on any page in the file tree and select "Set as Homepage" to configure it.',
				'error',
				'Homepage Required'
			);
			return;
		}

		setIsLoading(true);
		setIsHolding(false);
		setHoldProgress(0);

		try {
			// Update project's last deployment timestamp
			const { error: updateError } = await supabase
				.from('projects')
				.update({
					last_deployment_at: new Date().toISOString(),
				})
				.eq('id', selectedProject.id);

			if (updateError) {
				console.error('Error updating deployment timestamp:', updateError);
			}

			const response = await fetch('/api/cloudflare/deployments/retry', {
				method: 'POST',
				headers: {
					'Content-Type': 'application/json',
				},
				body: JSON.stringify({
					projectName: selectedProject.project_name,
				}),
			});

			const data = (await response.json()) as RetryDeploymentResponse;

			if (response.ok) {
				addToast(
					'Deployment retry started successfully!',
					'success',
					'Success'
				);
				// Start cooldown
				setCanDeploy(false);
				setCooldownRemaining(COOLDOWN_DURATION);
				startCooldownTimer(COOLDOWN_DURATION);
			} else {
				addToast(data.error || 'Failed to retry deployment', 'error', 'Error');
			}
		} catch (error) {
			console.error('Error retrying deployment:', error);
			addToast('Error connecting to server', 'error', 'Error');
		} finally {
			setIsLoading(false);
		}
	};

	const formatCooldownTime = (ms: number) => {
		const minutes = Math.floor(ms / 60000);
		const seconds = Math.floor((ms % 60000) / 1000);
		return `${minutes}:${seconds.toString().padStart(2, '0')}`;
	};

	if (!selectedProject) {
		return null;
	}

	return (
		<div className='relative group'>
			<TooltipProvider>
				<Tooltip>
					<TooltipTrigger asChild>
						<Button
							onMouseDown={handleMouseDown}
							onMouseUp={handleMouseUp}
							onMouseLeave={handleMouseUp}
							onTouchStart={handleMouseDown}
							onTouchEnd={handleMouseUp}
							disabled={!canDeploy || isLoading}
							className={cn(
								'relative overflow-hidden transition-all duration-300 select-none font-medium shadow-lg',
								'bg-gradient-to-r from-blue-600 to-indigo-600 border-blue-600',
								'hover:from-blue-700 hover:to-indigo-700 hover:shadow-xl hover:scale-102',
								'disabled:opacity-60 disabled:cursor-not-allowed disabled:hover:scale-100',
								'focus:ring-4 focus:ring-blue-300/50',
								'text-white hover:text-white focus:text-white active:text-white',
								className,
								{
									'from-gray-500 to-gray-600 hover:from-gray-500 hover:to-gray-600':
										!canDeploy || isLoading,
									'from-emerald-600 to-teal-600 hover:from-emerald-700 hover:to-teal-700 shadow-emerald-500/25':
										isHolding,
								}
							)}
							variant={variant}
							size={size}
						>
							{/* Shimmer effect on hover */}
							<div className='absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent -translate-x-full group-hover:translate-x-full transition-transform duration-700 ease-in-out' />

							{/* Progress bar */}
							{isHolding && (
								<>
									{/* Background overlay for contrast */}
									<div className='absolute inset-0 bg-black/10' />
									{/* Progress fill with gradient */}
									<div
										className='absolute inset-0 bg-gradient-to-r from-emerald-400 to-teal-400 transition-all duration-100 ease-out'
										style={{ width: `${holdProgress}%` }}
									/>
									{/* Progress shine effect */}
									<div
										className='absolute inset-0 bg-gradient-to-r from-transparent via-white/30 to-transparent animate-pulse'
										style={{ width: `${holdProgress}%` }}
									/>
								</>
							)}

							{/* Pulse effect during loading */}
							{isLoading && (
								<div className='absolute inset-0 bg-gradient-to-r from-blue-500/20 to-indigo-500/20 animate-pulse' />
							)}

							{/* Content */}
							<div className='relative z-10 flex items-center'>
								{isLoading ? (
									<Loader2 className='h-4 w-4 animate-spin' />
								) : !canDeploy ? (
									<Timer className='h-4 w-4' />
								) : (
									<RotateCcw
										className={cn('h-4 w-4 transition-transform duration-500', {
											'animate-spin-reverse': isHolding,
										})}
									/>
								)}
								{size !== 'icon' && (
									<span className='ml-2 font-medium text-white'>
										{isLoading
											? 'Deploying...'
											: !canDeploy
												? `Wait ${formatCooldownTime(cooldownRemaining)}`
												: 'Hold to Publish'}
									</span>
								)}
							</div>

							{/* Border glow effect during hold */}
							{isHolding && (
								<div className='absolute inset-0 rounded-md border-2 border-emerald-400/50 animate-pulse' />
							)}
						</Button>
					</TooltipTrigger>
				</Tooltip>
			</TooltipProvider>
		</div>
	);
}
