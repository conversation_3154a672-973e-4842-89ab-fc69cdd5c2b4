import {
	Popover,
	PopoverContent,
	PopoverTrigger,
} from "@/components/ui/popover";
import { useState, useCallback, useMemo, useEffect } from "react";
import { AnimatedClockIcon } from "./AnimatedClockIcon";
import { DateRange } from "react-day-picker";
import { Calendar } from "../ui/calendar";
import { Button } from "@/components/ui/button";
import { CalendarIcon } from "lucide-react";
import { cn } from "@/lib/utils";
import { isAfter, isBefore } from "date-fns";

export interface TimeRange {
	label: string;
	value: string;
	since: string;
	until: string;
	isCustom?: boolean;
}

interface PresetsResponse {
	success: boolean;
	presets: TimeRange[];
}

interface DateLimitsResponse {
	success: boolean;
	minDate: string; // ISO string
	maxDate: string; // ISO string
	notOlderThanDays: number;
	maxDurationDays: number;
}

interface CreateTimeRangeResponse {
	success: boolean;
	timeRange: TimeRange;
}

interface TimeRangeSelectorProps {
	isLoading?: boolean;
	onRefresh: (timeRange: TimeRange) => void;
	selectedTimeRange: TimeRange;
}

/**
 * TIME RANGE SELECTOR FOR CLOUDFLARE WEB ANALYTICS
 *
 * This component handles time range selection with specific Cloudflare Web Analytics limitations:
 *
 * KEY CLOUDFLARE LIMITATIONS:
 * • Historical Data Retention: 184 days maximum (notOlderThan limit)
 * • Maximum Query Range: 93 days per single request (maxDuration limit)
 * • These limits are fetched dynamically from Cloudflare's GraphQL API
 * • Fallback values are used if API limits cannot be retrieved
 *
 * CALENDAR BEHAVIOR SPECIFICS:
 * • Single-click on calendar creates same-day range but DOES NOT trigger API fetch
 * • Only different date selections (real ranges) trigger the onRefresh callback
 * • This prevents unwanted API calls on initial date selection
 * • Dynamic date disabling based on first selected date to respect 93-day limit
 *
 * DATE VALIDATION:
 * • All validation logic is handled by backend APIs for consistency
 * • Frontend only handles UI state and user interaction flow
 * • Backend applies 1-day safety buffer to maximum duration (92 days usable)
 */
export function TimeRangeSelector({
	isLoading = false,
	onRefresh,
	selectedTimeRange,
}: TimeRangeSelectorProps) {
	const [isOpen, setIsOpen] = useState(false);
	const [selectedRange, setSelectedRange] = useState<DateRange | undefined>();
	const [availablePresets, setAvailablePresets] = useState<TimeRange[]>([]);

	// Store Cloudflare Web Analytics limits fetched from API
	// These determine which dates can be selected and how long ranges can be
	const [dateLimits, setDateLimits] = useState<{
		minDate: Date; // Oldest selectable date (today - 184 days)
		maxDate: Date; // Latest selectable date (today)
		maxDurationDays: number; // Maximum range length (93 days with safety buffer)
	} | null>(null);

	const [validationError, setValidationError] = useState<string | null>(null);

	// Default month to show (previous month, so we see previous + current month)
	const defaultMonth = useMemo(() => {
		const date = new Date();
		date.setMonth(date.getMonth() - 1); // Go back 1 month
		return date;
	}, []);

	// Reset state when popover opens - always start fresh
	const handleOpenChange = useCallback((open: boolean) => {
		setIsOpen(open);
		if (open) {
			// Always reset to clean state when opening
			setSelectedRange(undefined);
			setValidationError(null);
		}
	}, []);

	// Fetch presets and date limits on component mount
	useEffect(() => {
		const fetchData = async () => {
			try {
				// Fetch available presets from backend (filtered by account limits)
				const presetsResponse = await fetch(
					"/api/cloudflare/web-analytics-sites?action=presets"
				);
				if (presetsResponse.ok) {
					const presetsData = (await presetsResponse.json()) as PresetsResponse;
					if (presetsData.success) {
						setAvailablePresets(presetsData.presets);
					}
				}

				// Fetch Cloudflare Web Analytics date limits from backend
				// This includes the 184-day retention and 93-day max duration limits
				const limitsResponse = await fetch(
					"/api/cloudflare/web-analytics-sites?action=date-limits"
				);
				if (limitsResponse.ok) {
					const limitsData =
						(await limitsResponse.json()) as DateLimitsResponse;
					if (limitsData.success) {
						setDateLimits({
							minDate: new Date(limitsData.minDate),
							maxDate: new Date(limitsData.maxDate),
							maxDurationDays: limitsData.maxDurationDays,
						});
					}
				}
			} catch (error) {
				console.error("Failed to fetch Web Analytics data:", error);
			}
		};

		fetchData();
	}, []);

	/**
	 * DYNAMIC DATE DISABLING LOGIC
	 *
	 * This function implements sophisticated date disabling based on Cloudflare limits:
	 *
	 * PHASE 1 - Basic Range Check:
	 * • Disables dates outside the 184-day historical window
	 * • Disables future dates beyond today
	 *
	 * PHASE 2 - Dynamic Range Limiting (when first date selected):
	 * • When user selects first date, calculates which dates would exceed 93-day limit
	 * • Disables dates that would create ranges longer than maxDurationDays
	 * • This provides immediate visual feedback before API validation
	 *
	 * TECHNICAL DETAILS:
	 * • Uses absolute difference calculation to work with both forward/backward selections
	 * • Only applies dynamic limiting when exactly one date is selected
	 * • Resets when range is complete or cleared
	 */
	const isDateDisabled = useCallback(
		(date: Date): boolean => {
			if (!dateLimits) return true; // Disable all if limits not loaded

			const checkDate = new Date(date);
			checkDate.setHours(0, 0, 0, 0);

			// PHASE 1: Basic range validation (184-day historical limit)
			if (
				isAfter(checkDate, dateLimits.maxDate) ||
				isBefore(checkDate, dateLimits.minDate)
			) {
				console.log(
					`[Date Disabled] Outside basic range: ${checkDate.toDateString()}`
				);
				return true;
			}

			// PHASE 2: Dynamic range limiting when first date is selected
			// This prevents users from selecting ranges that would exceed 93-day limit
			const isFirstDateSelected =
				selectedRange?.from &&
				(!selectedRange.to ||
					selectedRange.from.toDateString() ===
						selectedRange.to.toDateString());

			if (isFirstDateSelected && selectedRange.from) {
				const startDate = new Date(selectedRange.from);
				startDate.setHours(0, 0, 0, 0);

				// Calculate the absolute difference in days
				const daysDiff = Math.abs(
					Math.floor(
						(checkDate.getTime() - startDate.getTime()) / (1000 * 60 * 60 * 24)
					)
				);

				// Disable dates that would create a range exceeding maxDuration (93 days)
				if (daysDiff > dateLimits.maxDurationDays) {
					console.log(
						`[Date Disabled] Would exceed max duration: ${checkDate.toDateString()} (${daysDiff} days from ${startDate.toDateString()})`
					);
					return true;
				}
			}

			return false;
		},
		[dateLimits, selectedRange?.from, selectedRange?.to]
	);

	// Handle preset range selection
	const handlePresetSelection = useCallback(
		(range: TimeRange) => {
			onRefresh(range);
			setIsOpen(false);
		},
		[onRefresh]
	);

	/**
	 * CALENDAR DATE SELECTION HANDLER
	 *
	 * This is the critical function that prevents unwanted API calls on single clicks.
	 *
	 * BEHAVIOR FLOW:
	 * 1. ALWAYS updates UI state immediately (selectedRange)
	 * 2. Clears any previous validation errors
	 * 3. EARLY RETURN if range is incomplete (only one date selected)
	 * 4. EARLY RETURN if same-day range (single click creates from=to)
	 * 5. Only proceeds to API validation and onRefresh for real date ranges
	 *
	 * SINGLE-CLICK PREVENTION:
	 * • react-day-picker automatically creates same-day ranges on single clicks
	 * • We detect this by comparing from.toDateString() === to.toDateString()
	 * • This prevents triggering analytics refresh on calendar navigation
	 *
	 * API VALIDATION:
	 * • Uses backend create-timerange API for final validation
	 * • Backend applies Cloudflare limits and creates proper TimeRange object
	 * • Only successful validation triggers the onRefresh callback
	 */
	const handleDateSelection = useCallback(
		async (range: DateRange | undefined) => {
			console.log(`[Calendar Selection] Range selected:`, range);

			// ALWAYS update the UI state first
			setSelectedRange(range);
			setValidationError(null);

			// EARLY RETURN: Do NOTHING if range is incomplete OR if it's the same day (single click)
			if (!range || !range.from || !range.to) {
				console.log(
					`[Calendar Selection] Incomplete range, not triggering API call`
				);
				return; // CRITICAL: Exit immediately if incomplete
			}

			// CHECK IF IT'S THE SAME DAY (single click behavior)
			// This is the key fix for preventing unwanted API calls
			if (range.from.toDateString() === range.to.toDateString()) {
				console.log(
					`[Calendar Selection] Same-day selection detected, not triggering API call`
				);
				return; // CRITICAL: Don't trigger search for single day selection
			}

			console.log(
				`[Calendar Selection] Valid range detected, proceeding with API validation`
			);

			// Create TimeRange using backend API (with built-in validation)
			try {
				const createResponse = await fetch(
					`/api/cloudflare/web-analytics-sites?action=create-timerange&fromDate=${range.from.toISOString()}&toDate=${range.to.toISOString()}`
				);

				if (createResponse.ok) {
					const createData =
						(await createResponse.json()) as CreateTimeRangeResponse;

					if (createData.success) {
						console.log(
							`[Calendar Selection] API validation successful, triggering onRefresh`
						);
						onRefresh(createData.timeRange);
						setIsOpen(false);
					} else {
						// Handle validation error from create-timerange
						console.log(`[Calendar Selection] API validation failed`);
						setValidationError("Invalid date range");
						setSelectedRange({ from: range.from });
					}
				} else {
					// Handle HTTP error response
					const errorData = (await createResponse.json()) as { error?: string };
					setValidationError(errorData.error || "Invalid date range");
					setSelectedRange({ from: range.from });
				}
			} catch (error) {
				console.error("Failed to process date range:", error);
				setValidationError("Failed to validate date range");
			}
		},
		[onRefresh]
	);

	return (
		<div className='flex items-center gap-3'>
			<div className='flex items-center gap-2'>
				<AnimatedClockIcon
					isLoading={isLoading}
					className='w-5 h-5 text-muted-foreground'
				/>

				<Popover open={isOpen} onOpenChange={handleOpenChange}>
					<PopoverTrigger asChild>
						<Button
							id='date-range-trigger'
							variant='outline'
							className={cn("w-[250px] justify-start text-left font-normal")}
							disabled={isLoading}
						>
							<CalendarIcon className='mr-2 h-4 w-4' />
							<span>{selectedTimeRange.label}</span>
						</Button>
					</PopoverTrigger>

					<PopoverContent className='w-auto p-0 flex' align='end'>
						{/* Preset Options */}
						<div className='p-2 border-r flex flex-col'>
							{availablePresets.map((preset) => (
								<Button
									key={preset.value}
									variant='ghost'
									className='w-full justify-start'
									onClick={() => handlePresetSelection(preset)}
								>
									{preset.label}
								</Button>
							))}
						</div>

						{/* Custom Date Range Calendar */}
						<div>
							<Calendar
								initialFocus
								mode='range'
								defaultMonth={defaultMonth}
								selected={selectedRange}
								onSelect={handleDateSelection}
								numberOfMonths={2}
								toDate={dateLimits?.maxDate}
								disabled={isDateDisabled}
								modifiersClassNames={{
									disabled: "strikethrough-day",
								}}
								className='calendar-with-strikethrough'
							/>
							{validationError && (
								<p className='text-xs text-red-500 mt-2 px-3'>
									{validationError}
								</p>
							)}
							{/* Cloudflare Web Analytics Limits Info */}
							{dateLimits && (
								<div className='text-xs text-muted-foreground mt-2 px-3 space-y-1'>
									<p>• Maximum range: {dateLimits.maxDurationDays} days</p>
									<p>
										• Historical data:{" "}
										{Math.floor(
											(Date.now() - dateLimits.minDate.getTime()) /
												(1000 * 60 * 60 * 24)
										)}{" "}
										days available
									</p>
								</div>
							)}
						</div>
					</PopoverContent>
				</Popover>
			</div>
		</div>
	);
}

// Export empty array since presets are now fetched from backend
export const TIME_RANGES: TimeRange[] = [];
