import { NextRequest, NextResponse } from "next/server";
import ExcelJS from "exceljs";

export const runtime = "edge";

// Interfaces for data structures (can be shared with frontend)
interface WebAnalyticsData {
	countries?: Array<{
		count: number;
		sum: { visits: number };
		dimensions: { metric: string };
	}>;
	topReferers?: Array<{
		count: number;
		sum: { visits: number };
		dimensions: { metric: string };
	}>;
	topPaths?: Array<{
		count: number;
		sum: { visits: number };
		dimensions: { metric: string };
	}>;
	topBrowsers?: Array<{
		count: number;
		sum: { visits: number };
		dimensions: { metric: string };
	}>;
	topOSs?: Array<{
		count: number;
		sum: { visits: number };
		dimensions: { metric: string };
	}>;
	topDeviceTypes?: Array<{
		count: number;
		sum: { visits: number };
		dimensions: { metric: string };
	}>;
}

interface ExportDashboardRequest {
	projectName: string;
	timeRangeLabel: string;
	totalVisits: number;
	totalPageViews: number;
	webAnalyticsData: WebAnalyticsData;
	dateRange?: {
		since: string;
		until: string;
	};
}

// Helper function to calculate days between dates
function calculateDaysBetween(since: string, until: string): number {
	const sinceDate = new Date(since);
	const untilDate = new Date(until);
	const diffTime = Math.abs(untilDate.getTime() - sinceDate.getTime());
	const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
	// Include both start and end dates in the count
	return diffDays === 0 ? 1 : diffDays + 1;
}

// Helper function to format API dates
function formatApiDate(dateString: string): string {
	// Handle relative dates like "-7d", "-1d", "now"
	if (dateString === "now") {
		return new Date().toISOString().split("T")[0];
	}

	if (dateString.startsWith("-") && dateString.endsWith("d")) {
		const days = parseInt(dateString.slice(1, -1));
		const date = new Date();
		date.setDate(date.getDate() - days);
		return date.toISOString().split("T")[0];
	}

	// Handle other relative formats like "-1h", "-30m"
	if (
		dateString.startsWith("-") &&
		(dateString.endsWith("h") || dateString.endsWith("m"))
	) {
		const now = new Date();
		if (dateString.endsWith("h")) {
			const hours = parseInt(dateString.slice(1, -1));
			now.setHours(now.getHours() - hours);
		} else if (dateString.endsWith("m")) {
			const minutes = parseInt(dateString.slice(1, -1));
			now.setMinutes(now.getMinutes() - minutes);
		}
		return now.toISOString().split("T")[0];
	}

	// If it's already an ISO date or date-like string, format it
	try {
		const date = new Date(dateString);
		if (!isNaN(date.getTime())) {
			return date.toISOString().split("T")[0];
		}
		return dateString; // Return as-is if can't parse
	} catch {
		return dateString; // Return as-is if can't parse
	}
}

async function generateDashboardExcel(
	data: ExportDashboardRequest
): Promise<ArrayBuffer> {
	const workbook = new ExcelJS.Workbook();
	workbook.creator = "WriteDocs";
	workbook.created = new Date();

	// Calculate period information
	let periodInDays = 0;
	let formattedSince = "";
	let formattedUntil = "";

	if (data.dateRange) {
		formattedSince = formatApiDate(data.dateRange.since);
		formattedUntil = formatApiDate(data.dateRange.until);
		periodInDays = calculateDaysBetween(formattedSince, formattedUntil);
	}

	// Summary Sheet
	const summarySheet = workbook.addWorksheet("Summary");
	summarySheet.addRow(["Project Dashboard Export"]).font = {
		bold: true,
		size: 16,
	};
	summarySheet.addRow([]); // Empty row
	summarySheet.addRow(["Project Name", data.projectName]);
	summarySheet.addRow(["Time Range", data.timeRangeLabel]);

	// Add date range information
	if (data.dateRange) {
		summarySheet.addRow(["Period (Days)", periodInDays]);
		summarySheet.addRow(["Start Date", formattedSince]);
		summarySheet.addRow(["End Date", formattedUntil]);
	}

	summarySheet.addRow(["Total Visits", data.totalVisits]);
	summarySheet.addRow(["Total Page Views", data.totalPageViews]);

	summarySheet.columns = [{ width: 20 }, { width: 50 }];

	const { webAnalyticsData } = data;

	// Function to add a data sheet
	const addDataSheet = (
		sheetName: string,
		headers: string[],
		rows: (string | number)[][]
	) => {
		if (rows.length === 0) return;
		const sheet = workbook.addWorksheet(sheetName);
		sheet.addRow(headers).font = { bold: true };
		rows.forEach((row) => sheet.addRow(row));
		sheet.columns = headers.map(() => ({ width: 30 }));
	};

	// Countries Data
	if (webAnalyticsData.countries && webAnalyticsData.countries.length > 0) {
		addDataSheet(
			"Top Countries",
			["Country", "Visits"],
			webAnalyticsData.countries.map((c) => [c.dimensions.metric, c.sum.visits])
		);
	}

	// Top Referers Data
	if (webAnalyticsData.topReferers && webAnalyticsData.topReferers.length > 0) {
		addDataSheet(
			"Top Traffic Sources",
			["Source", "Visits"],
			webAnalyticsData.topReferers.map((r) => [
				r.dimensions.metric || "Direct",
				r.sum.visits,
			])
		);
	}

	// Top Pages Data
	if (webAnalyticsData.topPaths && webAnalyticsData.topPaths.length > 0) {
		addDataSheet(
			"Top Pages",
			["Path", "Visits", "Page Views"],
			webAnalyticsData.topPaths.map((p) => [
				p.dimensions.metric,
				p.sum.visits,
				p.count,
			])
		);
	}

	// Browsers Data
	if (webAnalyticsData.topBrowsers && webAnalyticsData.topBrowsers.length > 0) {
		addDataSheet(
			"Top Browsers",
			["Browser", "Visits"],
			webAnalyticsData.topBrowsers.map((b) => [
				b.dimensions.metric,
				b.sum.visits,
			])
		);
	}

	// Operating Systems Data
	if (webAnalyticsData.topOSs && webAnalyticsData.topOSs.length > 0) {
		addDataSheet(
			"Top Operating Systems",
			["OS", "Visits"],
			webAnalyticsData.topOSs.map((os) => [os.dimensions.metric, os.sum.visits])
		);
	}

	// Device Types Data
	if (
		webAnalyticsData.topDeviceTypes &&
		webAnalyticsData.topDeviceTypes.length > 0
	) {
		addDataSheet(
			"Top Device Types",
			["Device Type", "Visits"],
			webAnalyticsData.topDeviceTypes.map((d) => [
				d.dimensions.metric,
				d.sum.visits,
			])
		);
	}

	const buffer = await workbook.xlsx.writeBuffer();
	return buffer;
}

export async function POST(request: NextRequest) {
	try {
		const body: ExportDashboardRequest = await request.json();

		if (!body.projectName || !body.timeRangeLabel || !body.webAnalyticsData) {
			return NextResponse.json(
				{ error: "Missing required parameters" },
				{ status: 400 }
			);
		}

		const excelBuffer = await generateDashboardExcel(body);

		const timestamp = new Date()
			.toISOString()
			.replace(/[:.]/g, "-")
			.slice(0, -5);
		const filename = `${body.projectName}_dashboard_export_${timestamp}.xlsx`;

		return new NextResponse(excelBuffer, {
			status: 200,
			headers: {
				"Content-Type":
					"application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
				"Content-Disposition": `attachment; filename="${filename}"`,
				"Cache-Control": "no-cache, no-store, must-revalidate",
			},
		});
	} catch (error) {
		console.error("Error generating dashboard export:", error);
		return NextResponse.json(
			{ error: "Failed to generate export" },
			{ status: 500 }
		);
	}
}
