import { Node } from '@tiptap/core';
import { <PERSON>de<PERSON>iew<PERSON>rapper, ReactNodeViewRenderer } from '@tiptap/react';
import React, { useState, useCallback } from 'react';
import { Edit3, Save, X, Plus, Trash2, Search } from 'lucide-react';
import { Button } from '@/components/ui/button';
import type { NodeViewProps } from '@tiptap/react';
import { ImageSelector } from '../ImageSelector';
import { RichTextInput } from '@/components/ui/RichTextInput';
import { useProject } from '@/contexts';
import { useToast } from '@/components/ToastProvider';
import { getPhosphorIcon } from '../utils/phosphorIconRenderer';

// Utility function to parse image name and remove timestamp
const parseImageName = (imagePath: string): string => {
	if (!imagePath) return 'Selected image';

	const fileName = imagePath.split('/').pop() || '';
	// Remove timestamp pattern: numbers_filename.extension -> filename.extension
	const withoutTimestamp = fileName.replace(/^\d+_/, '');

	return withoutTimestamp || fileName || 'Selected image';
};

// Interface for individual card data
interface CardData {
	title: string;
	description: string;
	link: string;
	icon?: string;
	iconType?: 'thin' | 'light' | 'regular' | 'bold' | 'fill' | 'duotone';
	iconSize?: string;
	image?: string;
}


// Individual Card Component for display
const CardDisplay: React.FC<{
	card: CardData;
	onEdit: () => void;
	onDelete: () => void;
	isEditing?: boolean;
	isDraggable?: boolean;
	onDragStart?: (e: React.DragEvent) => void;
	onDragOver?: (e: React.DragEvent) => void;
	onDrop?: (e: React.DragEvent) => void;
}> = ({
	card,
	onEdit,
	onDelete,
	isEditing = false,
	isDraggable = false,
	onDragStart,
	onDragOver,
	onDrop,
}) => {
	const handleCardClick = useCallback(() => {
		// Removed redirection - cards are now non-clickable for links
		// This prevents unwanted navigation when clicking on cards in the editor
	}, []);

	return (
		<div
			className={`
        bg-white dark:bg-slate-800 border border-gray-200 dark:border-gray-700
        rounded-lg shadow-sm transition-all duration-200
        ${card.link && !isEditing ? 'border-blue-300 dark:border-blue-600' : ''}
        ${isDraggable ? 'cursor-move' : ''}
        relative group overflow-hidden
      `}
			onClick={handleCardClick}
			draggable={isDraggable}
			onDragStart={onDragStart}
			onDragOver={onDragOver}
			onDrop={onDrop}
		>
			{/* Edit and Delete buttons - only visible on hover and when editing */}
			{isEditing && (
				<div className='absolute top-2 right-2 flex space-x-1 opacity-0 group-hover:opacity-100 transition-opacity duration-200 z-10'>
					<Button
						variant='ghost'
						size='sm'
						onClick={(e) => {
							e.stopPropagation();
							onEdit();
						}}
						className='h-6 px-2 text-xs bg-white/80 dark:bg-slate-800/80 hover:bg-gray-100 dark:hover:bg-slate-700'
					>
						<Edit3 className='w-3 h-3' />
					</Button>
					<Button
						variant='ghost'
						size='sm'
						onClick={(e) => {
							e.stopPropagation();
							onDelete();
						}}
						className='h-6 px-2 text-xs bg-white/80 dark:bg-slate-800/80 hover:bg-red-100 dark:hover:bg-red-900/30 text-red-600 dark:text-red-400'
					>
						<Trash2 className='w-3 h-3' />
					</Button>
				</div>
			)}

			{/* Image Banner - Full Width */}
			{card.image && (
				<div className='w-full p-2.5 pb-0'>
					<div className='relative w-full overflow-hidden rounded-lg aspect-video max-h-[150px] min-h-[50px]'>
						<img
							src={card.image}
							alt={card.title}
							className='object-cover rounded-lg'
							onError={(e) => {
								e.currentTarget.style.display = 'none';
							}}
						/>
					</div>
				</div>
			)}

			{/* Content Container */}
			<div className='p-4'>
				<div className='flex flex-col items-start'>
					{/* Icon (only when no image) */}
					{card.icon && !card.image && (
						<div className='flex-shrink-0 mb-3'>
							{getPhosphorIcon(
								card.icon || '',
								card.iconType || 'regular',
								card.iconSize || '48px'
							)}
						</div>
					)}

					{/* Content */}
					<div className='flex-1 min-w-0'>
						<p className='text-md font-semibold text-gray-900 dark:text-gray-100'>
							{card.title || 'Card Title'}
						</p>
						{card.description && (
							<div
								className='text-sm text-gray-600 dark:text-gray-400 leading-snug prose-sm'
								dangerouslySetInnerHTML={{
									__html: card.description || '',
								}}
							/>
						)}
					</div>
				</div>
			</div>
		</div>
	);
};

// Card Edit Form Component
const CardEditForm: React.FC<{
	card: CardData;
	onSave: (card: CardData) => void;
	onCancel: () => void;
}> = ({ card, onSave, onCancel }) => {
	const [formData, setFormData] = useState<CardData>(card);
	const [showImageSelector, setShowImageSelector] = useState(false);
	const { selectedProject } = useProject();
	const { addToast } = useToast();

	const handleInputChange = useCallback(
		(field: keyof CardData, value: string) => {
			setFormData((prev) => {
				const newData = { ...prev, [field]: value };
				// Clear image when icon is set, and vice versa
				if (field === 'icon' && value.trim()) {
					newData.image = '';
				} else if (field === 'image' && value.trim()) {
					newData.icon = '';
				}
				return newData;
			});
		},
		[]
	);

	const handleSave = useCallback(() => {
		// Validate required fields
		if (!formData.title.trim()) {
			addToast('Title is required', 'warning');
			return;
		}

		// Create a clean copy of formData to avoid mutation issues
		const cleanFormData = {
			...formData,
			// Ensure description is properly formatted
			description: formData.description || '<p></p>',
		};

		onSave(cleanFormData);
	}, [formData, onSave, addToast]);

	const handleImageSelect = useCallback(
		(imageUrl: string) => {
			handleInputChange('image', imageUrl);
			setShowImageSelector(false);
		},
		[handleInputChange]
	);

	const handleOpenImageSelector = useCallback(() => {
		setShowImageSelector(true);
	}, []);

	return (
		<div className='p-4 bg-white dark:bg-slate-800 border-2 border-blue-400/50 dark:border-blue-600/50 rounded-lg shadow-lg'>
			{/* Header */}
			<div className='flex items-center justify-between mb-4'>
				<h4 className='text-sm font-semibold text-gray-900 dark:text-gray-100'>
					Edit Card
				</h4>
				<div className='flex items-center space-x-2'>
					<Button
						variant='ghost'
						size='sm'
						onClick={onCancel}
						className='h-6 px-2 text-xs hover:bg-red-100/50 dark:hover:bg-red-900/30 text-red-600 dark:text-red-400'
					>
						<X className='w-3 h-3 mr-1' />
						Cancel
					</Button>
					<Button
						variant='default'
						size='sm'
						onClick={handleSave}
						className='h-6 px-2 text-xs bg-blue-600 hover:bg-blue-700 text-white'
					>
						<Save className='w-3 h-3 mr-1' />
						Save
					</Button>
				</div>
			</div>

			{/* Edit Form */}
			<div className='space-y-4'>
				{/* Title */}
				<div>
					<label className='block text-xs font-medium text-gray-700 dark:text-gray-300 mb-1'>
						Title *
					</label>
					<input
						type='text'
						value={formData.title}
						onChange={(e) => handleInputChange('title', e.target.value)}
						placeholder='Enter card title'
						className='w-full px-3 py-2 text-sm bg-white dark:bg-slate-700 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:text-white'
						required
					/>
				</div>

				{/* Description */}
				<div>
					<label className='block text-xs font-medium text-gray-700 dark:text-gray-300 mb-1'>
						Description
					</label>
					<RichTextInput
						value={formData.description}
						onChange={(html) => handleInputChange('description', html)}
						placeholder="Brief explanation of the card's purpose..."
						variant='default'
						className='w-full'
					/>
				</div>

				{/* Link */}
				<div>
					<label className='block text-xs font-medium text-gray-700 dark:text-gray-300 mb-1'>
						Link
					</label>
					<input
						type='url'
						value={formData.link}
						onChange={(e) => handleInputChange('link', e.target.value)}
						placeholder='https://example.com or /path'
						className='w-full px-3 py-2 text-sm bg-white dark:bg-slate-700 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:text-white'
					/>
				</div>

				{/* Icon and Image */}
				<div className='grid grid-cols-1 md:grid-cols-2 gap-4'>
					<div className='flex flex-col h-full'>
						<label className='block text-xs font-medium text-gray-700 dark:text-gray-300 mb-1'>
							Icon Name
						</label>
						<input
							type='text'
							value={formData.icon}
							onChange={(e) => handleInputChange('icon', e.target.value)}
							placeholder='e.g. house, user, gear, book, heart'
							disabled={!!formData.image}
							className={`h-10 px-3 py-2 text-sm border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 ${
								formData.image
									? 'bg-gray-100 dark:bg-gray-600 text-gray-400 dark:text-gray-500 border-gray-200 dark:border-gray-600 cursor-not-allowed'
									: 'bg-white dark:bg-slate-700 border-gray-300 dark:border-gray-600 dark:text-white'
							}`}
						/>
					</div>
					<div className='flex flex-col h-full'>
						<label className='block text-xs font-medium text-gray-700 dark:text-gray-300 mb-1'>
							Image
						</label>
						{!formData.image ? (
							<Button
								type='button'
								variant='outline'
								onClick={handleOpenImageSelector}
								disabled={!!formData.icon}
								className={`h-10 justify-start text-left ${
									formData.icon
										? 'bg-gray-100 dark:bg-gray-600 text-gray-400 dark:text-gray-500 border-gray-200 dark:border-gray-600 cursor-not-allowed'
										: 'hover:bg-gray-50 dark:hover:bg-slate-700'
								}`}
							>
								<Search className='w-4 h-4 mr-2' />
								Browse Images
							</Button>
						) : (
							<div className='h-10 flex items-center justify-between px-3 py-2 bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-md'>
								<div className='flex items-center space-x-2 flex-1 min-w-0'>
									<img
										src={formData.image}
										alt='Selected'
										width={32}
										height={32}
										className='object-cover rounded'
										onError={(e) => {
											e.currentTarget.style.display = 'none';
										}}
									/>
									<div className='flex-1 min-w-0'>
										<p className='text-xs font-medium text-green-700 dark:text-green-300 truncate'>
											{parseImageName(formData.image)}
										</p>
										<p className='text-xs text-green-600 dark:text-green-400'>
											Image selected
										</p>
									</div>
								</div>
								<div className='flex items-center space-x-1'>
									<Button
										type='button'
										variant='ghost'
										size='sm'
										onClick={handleOpenImageSelector}
										className='h-6 px-2 text-xs text-green-700 dark:text-green-300 hover:bg-green-100 dark:hover:bg-green-800/30'
									>
										Change
									</Button>
									<Button
										type='button'
										variant='ghost'
										size='sm'
										onClick={() => handleInputChange('image', '')}
										className='h-6 px-1 text-xs text-red-600 dark:text-red-400 hover:bg-red-100 dark:hover:bg-red-900/30'
									>
										<X className='w-3 h-3' />
									</Button>
								</div>
							</div>
						)}
					</div>
				</div>

				{/* Icon Settings */}
				{formData.icon && (
					<div className='grid grid-cols-1 md:grid-cols-2 gap-4'>
						<div>
							<label className='block text-xs font-medium text-gray-700 dark:text-gray-300 mb-1'>
								Icon Type
							</label>
							<select
								value={formData.iconType}
								onChange={(e) => handleInputChange('iconType', e.target.value)}
								className='w-full px-3 py-2 text-sm bg-white dark:bg-slate-700 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:text-white'
							>
								<option value='thin'>Thin</option>
								<option value='light'>Light</option>
								<option value='regular'>Regular</option>
								<option value='bold'>Bold</option>
								<option value='fill'>Fill</option>
								<option value='duotone'>Duotone</option>
							</select>
						</div>
						<div>
							<label className='block text-xs font-medium text-gray-700 dark:text-gray-300 mb-1'>
								Icon Size
							</label>
							<input
								type='text'
								value={formData.iconSize}
								onChange={(e) => handleInputChange('iconSize', e.target.value)}
								placeholder='48px'
								className='w-full px-3 py-2 text-sm bg-white dark:bg-slate-700 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:text-white'
							/>
						</div>
					</div>
				)}
			</div>

			{/* Image Selector Modal */}
			<ImageSelector
				isOpen={showImageSelector}
				onClose={() => setShowImageSelector(false)}
				onSelectImage={handleImageSelect}
				projectId={selectedProject?.id}
				organizationId={selectedProject?.owner_id}
			/>
		</div>
	);
};

// Main CardList Component
const CardListComponent: React.FC<NodeViewProps> = ({
	node,
	updateAttributes,
	selected,
}) => {
	const [isEditing, setIsEditing] = useState(false);
	const [editingCardIndex, setEditingCardIndex] = useState<number | null>(null);
	const [cols, setCols] = useState(node.attrs.cols || 2);
	const [cards, setCards] = useState<CardData[]>(() => {
		const nodeCards = node.attrs.cards;
		return Array.isArray(nodeCards) ? nodeCards : [];
	});
	const [draggedIndex, setDraggedIndex] = useState<number | null>(null);
	const [dragOverIndex, setDragOverIndex] = useState<number | null>(null);
	const { addToast } = useToast();

	const handleSave = useCallback(() => {
		updateAttributes({ cols, cards });
		setIsEditing(false);
		setEditingCardIndex(null);
		addToast('Card list updated successfully', 'success', 'Success');
	}, [cols, cards, updateAttributes, addToast]);

	const handleCancel = useCallback(() => {
		setCols(node.attrs.cols || 2);
		const nodeCards = node.attrs.cards;
		setCards(Array.isArray(nodeCards) ? nodeCards : []);
		setIsEditing(false);
		setEditingCardIndex(null);
	}, [node.attrs]);

	const handleAddCard = useCallback(() => {
		const newCard: CardData = {
			title: 'New Card',
			description: 'Set a description for your card',
			link: '',
			icon: '',
			iconType: 'regular',
			iconSize: '48px',
			image: '',
		};
		setCards([...cards, newCard]);
		setEditingCardIndex(cards.length);
		addToast('New card added', 'success', 'Card Added');
	}, [cards, addToast]);

	const handleEditCard = useCallback((index: number) => {
		setEditingCardIndex(index);
	}, []);

	const handleSaveCard = useCallback(
		(index: number, updatedCard: CardData) => {
			const newCards = [...cards];
			newCards[index] = updatedCard;
			setCards(newCards);
			setEditingCardIndex(null);
		},
		[cards]
	);

	const handleDeleteCard = useCallback(
		(index: number) => {
			const newCards = cards.filter((_, i) => i !== index);
			setCards(newCards);
			addToast('Card deleted successfully', 'info', 'Card Removed');
		},
		[cards, addToast]
	);

	const handleDragStart = useCallback((index: number) => {
		return (e: React.DragEvent) => {
			setDraggedIndex(index);
			e.dataTransfer.effectAllowed = 'move';
			e.dataTransfer.setData('text/plain', index.toString());
		};
	}, []);

	const handleDragOver = useCallback((targetIndex: number) => {
		return (e: React.DragEvent) => {
			e.preventDefault();
			e.dataTransfer.dropEffect = 'move';
			setDragOverIndex(targetIndex);
		};
	}, []);

	const handleDragLeave = useCallback(() => {
		setDragOverIndex(null);
	}, []);

	const handleDragEnd = useCallback(() => {
		setDraggedIndex(null);
		setDragOverIndex(null);
	}, []);

	const handleDrop = useCallback(
		(targetIndex: number) => {
			return (e: React.DragEvent) => {
				e.preventDefault();
				setDragOverIndex(null);

				if (draggedIndex === null || draggedIndex === targetIndex) {
					setDraggedIndex(null);
					return;
				}

				const newCards = [...cards];
				const draggedCard = newCards[draggedIndex];
				newCards.splice(draggedIndex, 1);
				newCards.splice(targetIndex, 0, draggedCard);

				setCards(newCards);
				setDraggedIndex(null);
			};
		},
		[cards, draggedIndex]
	);

	const getGridCols = (cols: number) => {
		switch (cols) {
			case 1:
				return 'grid-cols-1';
			case 2:
				return 'grid-cols-1 md:grid-cols-2';
			case 3:
				return 'grid-cols-1 md:grid-cols-2 lg:grid-cols-3';
			case 4:
				return 'grid-cols-1 md:grid-cols-2 lg:grid-cols-4';
			case 5:
				return 'grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-5';
			default:
				return 'grid-cols-1 md:grid-cols-2';
		}
	};

	if (!isEditing) {
		return (
			<NodeViewWrapper
				className='card-list-node'
				as='div'
				data-drag-handle=''
				contentEditable={false}
			>
				<div
					className={`
            my-4 p-2 border-2 border-dashed border-gray-200 dark:border-gray-700 
            rounded-lg transition-all duration-200 cursor-pointer group
            hover:border-blue-300 dark:hover:border-blue-600
            ${selected ? 'ring-2 ring-blue-400/50' : ''}
            relative
          `}
					onClick={() => setIsEditing(true)}
				>
					{/* Edit button - only visible on hover */}
					<Button
						variant='ghost'
						size='sm'
						onClick={(e) => {
							e.stopPropagation();
							setIsEditing(true);
						}}
						className='absolute top-2 right-2 h-6 px-2 text-xs opacity-0 group-hover:opacity-100 transition-opacity duration-200 bg-white/80 dark:bg-slate-800/80 hover:bg-gray-100 dark:hover:bg-slate-700 z-50'
					>
						<Edit3 className='w-3 h-3 mr-1' />
						Edit
					</Button>

					{/* Cards Grid */}
					<div className={`grid ${getGridCols(cols)} gap-2`}>
						{cards.map((card, index) => (
							<CardDisplay
								key={index}
								card={card}
								onEdit={() => handleEditCard(index)}
								onDelete={() => handleDeleteCard(index)}
								isEditing={false}
								isDraggable={false}
							/>
						))}
					</div>

					{cards.length === 0 && (
						<div className='text-center py-8'>
							<p className='text-gray-500 dark:text-gray-400 mb-4'>
								No cards yet. Click to add cards to this list.
							</p>
						</div>
					)}
				</div>
			</NodeViewWrapper>
		);
	}

	return (
		<NodeViewWrapper
			className='card-list-node'
			as='div'
			data-drag-handle=''
			contentEditable={false}
		>
			<div className='my-4 p-4 bg-white dark:bg-slate-800 border-2 border-blue-400/50 dark:border-blue-600/50 rounded-lg shadow-lg'>
				{/* Header */}
				<div className='flex items-center justify-between mb-4'>
					<h4 className='text-sm font-semibold text-gray-900 dark:text-gray-100'>
						Edit Card List
					</h4>
					<div className='flex items-center space-x-2'>
						<Button
							variant='ghost'
							size='sm'
							onClick={handleCancel}
							className='h-6 px-2 text-xs hover:bg-red-100/50 dark:hover:bg-red-900/30 text-red-600 dark:text-red-400'
						>
							<X className='w-3 h-3 mr-1' />
							Cancel
						</Button>
						<Button
							variant='default'
							size='sm'
							onClick={handleSave}
							className='h-6 px-2 text-xs bg-blue-600 hover:bg-blue-700 text-white'
						>
							<Save className='w-3 h-3 mr-1' />
							Save
						</Button>
					</div>
				</div>

				{/* Columns Setting */}
				<div className='mb-4'>
					<label className='block text-xs font-medium text-gray-700 dark:text-gray-300 mb-2'>
						Number of Columns (1-5)
					</label>
					<div className='flex gap-2'>
						{[1, 2, 3, 4, 5].map((colNum) => (
							<button
								key={colNum}
								onClick={() => setCols(colNum)}
								className={`
                  flex flex-col items-center justify-center p-3 rounded-lg border-2 transition-all duration-200 min-w-[60px]
                  ${
										cols === colNum
											? 'border-blue-500 bg-blue-50 dark:bg-blue-900/20 text-blue-700 dark:text-blue-300'
											: 'border-gray-200 dark:border-gray-600 bg-white dark:bg-slate-700 text-gray-600 dark:text-gray-400 hover:border-blue-300 dark:hover:border-blue-500 hover:bg-blue-50/50 dark:hover:bg-blue-900/10'
									}
                `}
							>
								{/* Visual grid representation */}
								<div className='flex gap-0.5 mb-1 justify-center'>
									{Array.from({ length: colNum }).map((_, i) => (
										<div
											key={i}
											className={`w-1.5 h-4 rounded-sm ${
												cols === colNum
													? 'bg-blue-500 dark:bg-blue-400'
													: 'bg-gray-400 dark:bg-gray-500'
											}`}
										/>
									))}
								</div>
								<span className='text-xs font-medium'>{colNum}</span>
							</button>
						))}
					</div>
				</div>

				{/* Cards Management */}
				<div className='space-y-4'>
					{cards.map((card, index) => (
						<div key={index}>
							{editingCardIndex === index ? (
								<CardEditForm
									card={card}
									onSave={(updatedCard) => handleSaveCard(index, updatedCard)}
									onCancel={() => setEditingCardIndex(null)}
								/>
							) : (
								<div className='relative'>
									{/* Drop indicator - top */}
									{dragOverIndex === index &&
										draggedIndex !== null &&
										draggedIndex !== index && (
											<div className='absolute -top-2 left-0 right-0 h-1 bg-gradient-to-r from-transparent via-blue-500 to-transparent rounded-full z-10 opacity-80' />
										)}

									<div
										className={`
                       p-3 rounded-lg flex items-center justify-between cursor-move transition-all duration-200
                       ${
													draggedIndex === index
														? 'bg-blue-50 dark:bg-blue-900/20 border border-blue-300 dark:border-blue-600 opacity-60 scale-98 shadow-lg'
														: 'bg-gray-50 dark:bg-slate-700 hover:bg-gray-100 dark:hover:bg-slate-600 border border-transparent'
												}
                       ${
													dragOverIndex === index &&
													draggedIndex !== null &&
													draggedIndex !== index
														? 'bg-blue-50/50 dark:bg-blue-900/10 border-blue-300 dark:border-blue-600'
														: ''
												}
                     `}
										draggable
										onDragStart={handleDragStart(index)}
										onDragOver={handleDragOver(index)}
										onDragLeave={handleDragLeave}
										onDragEnd={handleDragEnd}
										onDrop={handleDrop(index)}
									>
										<div className='flex items-center'>
											<div className='mr-3 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 transition-colors duration-200'>
												<svg
													className='w-4 h-4'
													fill='none'
													stroke='currentColor'
													viewBox='0 0 24 24'
												>
													<path
														strokeLinecap='round'
														strokeLinejoin='round'
														strokeWidth={2}
														d='M4 8h16M4 16h16'
													/>
												</svg>
											</div>
											<div>
												<p className='font-medium text-gray-900 dark:text-gray-100'>
													{card.title || 'Untitled Card'}
												</p>
											</div>
										</div>
										<div className='flex space-x-2'>
											<Button
												variant='ghost'
												size='sm'
												onClick={() => handleEditCard(index)}
												className='h-6 px-2 text-xs'
											>
												<Edit3 className='w-3 h-3' />
											</Button>
											<Button
												variant='ghost'
												size='sm'
												onClick={() => handleDeleteCard(index)}
												className='h-6 px-2 text-xs text-red-600 dark:text-red-400 hover:bg-red-100 dark:hover:bg-red-900/30'
											>
												<Trash2 className='w-3 h-3' />
											</Button>
										</div>
									</div>

									{/* Drop indicator - bottom */}
									{dragOverIndex === index &&
										draggedIndex !== null &&
										draggedIndex !== index && (
											<div className='absolute -bottom-2 left-0 right-0 h-1 bg-gradient-to-r from-transparent via-blue-500 to-transparent rounded-full z-10 opacity-80' />
										)}
								</div>
							)}
						</div>
					))}

					{/* Drop zone at the end of the list */}
					{draggedIndex !== null && (
						<div
							className='h-12 flex items-center justify-center border-2 border-dashed border-blue-300 dark:border-blue-500 rounded-lg bg-blue-50/50 dark:bg-blue-900/10 transition-all duration-200 hover:bg-blue-100/50 dark:hover:bg-blue-900/20'
							onDragOver={(e) => {
								e.preventDefault();
								setDragOverIndex(cards.length);
							}}
							onDragLeave={handleDragLeave}
							onDrop={(e) => {
								e.preventDefault();
								setDragOverIndex(null);

								if (draggedIndex === null) return;

								const newCards = [...cards];
								const draggedCard = newCards[draggedIndex];
								newCards.splice(draggedIndex, 1);
								newCards.push(draggedCard);

								setCards(newCards);
								setDraggedIndex(null);
							}}
						>
							<div className='flex items-center space-x-2 text-blue-600 dark:text-blue-400'>
								<svg
									className='w-4 h-4'
									fill='none'
									stroke='currentColor'
									viewBox='0 0 24 24'
								>
									<path
										strokeLinecap='round'
										strokeLinejoin='round'
										strokeWidth={2}
										d='M19 14l-7 7m0 0l-7-7m7 7V3'
									/>
								</svg>
								<span className='text-sm font-medium'>
									Drop here to move to end
								</span>
							</div>
						</div>
					)}

					{/* Add Card Button */}
					<Button
						variant='outline'
						onClick={handleAddCard}
						className={`w-full h-10 border-dashed border-2 transition-all ${'border-gray-300 dark:border-gray-600 hover:border-blue-400 dark:hover:border-blue-500'}`}
					>
						<Plus className='w-4 h-4 mr-2' />
						Add Card
					</Button>
				</div>
			</div>
		</NodeViewWrapper>
	);
};

// Tiptap extension definition
export const CardListNode = Node.create({
	name: 'cardListNode',

	group: 'block',

	atom: true,

	draggable: false,

	selectable: true,

	addAttributes() {
		return {
			cols: {
				default: 2,
				parseHTML: (element) => {
					const cols =
						element.getAttribute('cols') || element.getAttribute('data-cols');
					return cols ? parseInt(cols) : 2;
				},
				renderHTML: (attributes) => {
					if (!attributes.cols) return {};
					return { cols: attributes.cols };
				},
			},
			cards: {
				default: [],
				parseHTML: (element) => {
					try {
						const cardsData =
							element.getAttribute('cards') ||
							element.getAttribute('data-cards');
						return cardsData ? JSON.parse(cardsData) : [];
					} catch {
						return [];
					}
				},
				renderHTML: (attributes) => {
					if (!attributes.cards || !Array.isArray(attributes.cards)) return {};
					return { cards: JSON.stringify(attributes.cards) };
				},
			},
		};
	},

	parseHTML() {
		return [
			{
				tag: 'CardList',
				getAttrs: (element) => {
					if (!(element instanceof HTMLElement)) return false;

					const cols =
						element.getAttribute('cols') || element.getAttribute('data-cols');

					// First try to get cards from data-cards attribute (legacy format)
					const cardsData =
						element.getAttribute('cards') || element.getAttribute('data-cards');

					let cards = [];

					if (cardsData) {
						try {
							cards = JSON.parse(cardsData);
						} catch {
							cards = [];
						}
					} else {
						// Extract cards from child Card elements (new format)
						const cardElements = element.querySelectorAll('Card, card');
						cards = Array.from(cardElements).map((cardEl) => ({
							title: cardEl.getAttribute('title') || '',
							description: cardEl.textContent || cardEl.innerHTML || '',
							link: cardEl.getAttribute('link') || '',
							icon: cardEl.getAttribute('icon') || '',
							iconType: cardEl.getAttribute('iconType') || 'regular',
							iconSize: cardEl.getAttribute('iconSize') || '48px',
							image: cardEl.getAttribute('image') || '',
						}));
					}

					return {
						cols: cols ? parseInt(cols) : 2,
						cards,
					};
				},
			},
		];
	},

	renderHTML({ HTMLAttributes }) {
		const attrs = HTMLAttributes;
		const cols = attrs['cols'] || 2;

		// Ensure cards is always an array
		let cards = attrs['cards'] || [];
		if (typeof cards === 'string') {
			try {
				cards = JSON.parse(cards);
			} catch {
				cards = [];
			}
		}
		if (!Array.isArray(cards)) {
			cards = [];
		}

		// Generate the children Card elements
		const cardElements = cards.map((card: CardData) => {
			const cardAttrs: Record<string, string> = {};

			// Só adiciona atributos que têm valor e não são defaults
			if (card.title) cardAttrs['title'] = card.title;
			if (card.link) cardAttrs['link'] = card.link;
			if (card.icon) cardAttrs['icon'] = card.icon;
			if (card.iconType && card.iconType !== 'regular')
				cardAttrs['iconType'] = card.iconType;
			if (card.iconSize && card.iconSize !== '48px')
				cardAttrs['iconSize'] = card.iconSize;
			if (card.image) cardAttrs['image'] = card.image;

			// Para description, usa o conteúdo interno do Card
			const description = card.description || '';

			return ['Card', cardAttrs, description];
		});

		return ['CardList', { cols: cols.toString() }, ...cardElements];
	},

	addNodeView() {
		return ReactNodeViewRenderer(CardListComponent);
	},
});

export default CardListNode;
