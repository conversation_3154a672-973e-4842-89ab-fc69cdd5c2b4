export interface StepData {
  id: string;
  title: string;
  content: string;
  titleSize: "h2" | "h3" | "h4" | "h5" | "h6";
  subSteps?: StepData[];
}

export interface StepsAttrs {
  steps: StepData[];
}

export interface FlatStep extends StepData {
  level: number;
  parentId?: string;
}

export interface EditModeStepProps {
  step: FlatStep;
  index: number;
  onDragStart: (e: React.DragEvent, index: number) => void;
  onDragOver: (e: React.DragEvent, index: number) => void;
  onDrop: (e: React.DragEvent, index: number) => void;
  onDragEnd: (e?: React.DragEvent) => void;
  isDragging: boolean;
  isDragOver: boolean;
  onEdit: (step: FlatStep) => void;
  onDelete: (stepId: string) => void;
  descendantCount?: number;
}

export interface EditModeProps {
  steps: StepData[];
  onSave: (steps: StepData[]) => void;
  onCancel: () => void;
  onEditStep: (step: FlatStep) => void;
}

export interface ActionButtonsProps {
  onEdit: () => void;
  onAddSubStep: () => void;
  onDelete: () => void;
}

export interface StepEditFormProps {
  step: StepData;
  onSave: (step: StepData) => void;
  onCancel: () => void;
  isFirstStepInLevel?: boolean;
}

export interface VerticalLineProps {
  titleSize: string;
  hasSubSteps: boolean;
  isLast: boolean;
}

export interface StepItemProps {
  step: StepData;
  index: number;
  level: number;
  isLast: boolean;
  onEdit: (step: StepData) => void;
  onDelete: (stepId: string) => void;
  onAddSubStep: (parentId: string) => void;
  isEditing: boolean;
  editingStepId: string | null;
  onSaveEdit: (step: StepData) => void;
  onCancelEdit: () => void;
  isFirstStepInLevel?: boolean;
}

export interface StepsListProps {
  steps: StepData[];
  level: number;
  onEdit: (step: StepData) => void;
  onDelete: (stepId: string) => void;
  onAddSubStep: (parentId: string) => void;
  editingStepId: string | null;
  onSaveEdit: (step: StepData) => void;
  onCancelEdit: () => void;
}
