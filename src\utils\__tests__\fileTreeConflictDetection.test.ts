import {
	checkMoveConflicts,
	executeSelectiveMove,
	generateConflictMessage,
	generateMoveSuccessMessage,
} from '../fileTreeUtils';
import type { TreeNode } from '../../components/editor/sidebar/types';

// Mock tree structure for testing
const createMockTree = (): TreeNode[] => [
	{
		id: 'root-category',
		name: 'Documentation',
		type: 'category',
		path: 'docs',
		children: [
			{
				id: 'existing-file',
				name: 'getting-started',
				type: 'page',
				path: 'docs/getting-started',
			},
			{
				id: 'existing-folder',
				name: 'guides',
				type: 'category',
				path: 'docs/guides',
				children: [
					{
						id: 'guide-file',
						name: 'installation',
						type: 'page',
						path: 'docs/guides/installation',
					},
				],
			},
		],
	},
	{
		id: 'source-category',
		name: 'Source',
		type: 'category',
		path: 'src',
		children: [
			{
				id: 'file-to-move',
				name: 'getting-started', // Same name as existing file
				type: 'page',
				path: 'src/getting-started',
			},
			{
				id: 'folder-to-move',
				name: 'tutorials',
				type: 'category',
				path: 'src/tutorials',
				children: [
					{
						id: 'tutorial-file-1',
						name: 'basic-tutorial',
						type: 'page',
						path: 'src/tutorials/basic-tutorial',
					},
					{
						id: 'tutorial-file-2',
						name: 'installation', // Same name as existing file in guides
						type: 'page',
						path: 'src/tutorials/installation',
					},
				],
			},
		],
	},
];

describe('File Tree Conflict Detection', () => {
	let mockTree: TreeNode[];

	beforeEach(() => {
		mockTree = createMockTree();
	});

	describe('checkMoveConflicts', () => {
		it('should detect file name conflict when moving file to folder with same name', () => {
			const draggedNode = mockTree[1].children![0]; // file-to-move (getting-started)
			const targetNode = mockTree[0]; // root-category (Documentation)

			const result = checkMoveConflicts(draggedNode, targetNode, 'inside', mockTree);

			expect(result.hasConflicts).toBe(true);
			expect(result.conflicts).toHaveLength(1);
			expect(result.conflicts[0].itemName).toBe('getting-started');
			expect(result.conflicts[0].itemType).toBe('file');
			expect(result.conflicts[0].reason).toContain('already exists');
		});

		it('should detect partial conflicts when moving folder with some conflicting files', () => {
			const draggedNode = mockTree[1].children![1]; // folder-to-move (tutorials)
			const targetNode = mockTree[0].children![1]; // existing-folder (guides)

			const result = checkMoveConflicts(draggedNode, targetNode, 'inside', mockTree);

			expect(result.hasConflicts).toBe(true);
			expect(result.canMove.length).toBe(2); // folder + basic-tutorial
			expect(result.cannotMove.length).toBe(1); // installation (conflict)
			expect(result.conflicts[0].itemName).toBe('installation');
		});

		it('should allow move when no conflicts exist', () => {
			const draggedNode = mockTree[1].children![1].children![0]; // basic-tutorial
			const targetNode = mockTree[0]; // root-category

			const result = checkMoveConflicts(draggedNode, targetNode, 'inside', mockTree);

			expect(result.hasConflicts).toBe(false);
			expect(result.canMove.length).toBe(1);
			expect(result.cannotMove.length).toBe(0);
			expect(result.conflicts.length).toBe(0);
		});
	});

	describe('generateConflictMessage', () => {
		it('should generate appropriate message for file conflicts', () => {
			const conflictResult = {
				hasConflicts: true,
				canMove: [],
				cannotMove: [
					{
						node: { id: 'test', name: 'test-file', type: 'page' } as TreeNode,
						reason: 'File already exists',
					},
				],
				conflicts: [
					{
						itemName: 'test-file',
						itemType: 'file' as const,
						reason: 'File already exists',
					},
				],
			};

			const message = generateConflictMessage(conflictResult);

			expect(message).toContain('📄 Files:');
			expect(message).toContain('test-file - File already exists');
		});

		it('should return empty string when no conflicts', () => {
			const conflictResult = {
				hasConflicts: false,
				canMove: [],
				cannotMove: [],
				conflicts: [],
			};

			const message = generateConflictMessage(conflictResult);
			expect(message).toBe('');
		});
	});

	describe('generateMoveSuccessMessage', () => {
		it('should generate success message for complete move', () => {
			const movedItems = [{ id: '1', name: 'file1', type: 'page' } as TreeNode];
			const skippedItems: TreeNode[] = [];

			const message = generateMoveSuccessMessage(movedItems, skippedItems);

			expect(message).toContain('✅ Successfully moved 1 item(s)');
		});

		it('should generate partial success message with skipped items', () => {
			const movedItems = [{ id: '1', name: 'file1', type: 'page' } as TreeNode];
			const skippedItems = [{ id: '2', name: 'file2', type: 'page' } as TreeNode];

			const message = generateMoveSuccessMessage(movedItems, skippedItems);

			expect(message).toContain('Partially completed move operation');
			expect(message).toContain('1 item(s) moved successfully');
			expect(message).toContain('1 item(s) skipped due to conflicts');
			expect(message).toContain('file2 (file)');
		});
	});
});
