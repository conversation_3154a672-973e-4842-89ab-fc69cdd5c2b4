import React, { useEffect, useState } from "react";
import { FaCreditCard } from "react-icons/fa";
import Link from "next/link";

// Interface for component props
interface UpgradePlanProps {
	// Flag to control if the upgrade plan message should be displayed
	showUpgradePlan: boolean;
	// Optional overlay class for background styling
	overlayClassName?: string;
	// Optional message container class
	messageClassName?: string;
	// Link to the upgrade page
	upgradeLink: string;
}

/**
 * UpgradePlan component that displays a message encouraging users to upgrade to
 * access more features. Can be used across different parts of the application.
 */
const UpgradePlan: React.FC<UpgradePlanProps> = ({
	showUpgradePlan,
	overlayClassName = "bg-white/50 backdrop-blur-[2px]",
	messageClassName = "bg-white border border-gray-200 rounded-lg p-8 shadow-lg max-w-3xl mx-4",
	upgradeLink,
}) => {
	// State to track if upgrade message has been removed via DOM manipulation
	const [upgradeOverlayRemoved, setUpgradeOverlayRemoved] = useState(false);

	// Anti-tampering effect - check if the upgrade message is still in the DOM
	useEffect(() => {
		if (!showUpgradePlan) return;

		const observer = new MutationObserver(() => {
			// If the upgrade message has been removed from the DOM, restore it
			const upgradeMsgElements =
				document.querySelectorAll("[data-upgrade-msg]");
			if (upgradeMsgElements.length === 0 && !upgradeOverlayRemoved) {
				setUpgradeOverlayRemoved(true);
				setTimeout(() => setUpgradeOverlayRemoved(false), 100);
			}
		});

		observer.observe(document.body, {
			childList: true,
			subtree: true,
			attributes: true,
			attributeFilter: ["style", "class"],
		});

		return () => observer.disconnect();
	}, [showUpgradePlan, upgradeOverlayRemoved]);

	if (!showUpgradePlan && !upgradeOverlayRemoved) return null;

	return (
		<div className='absolute inset-0 z-10' data-upgrade-msg='overlay'>
			<div
				className={`w-full h-full flex items-start justify-center ${overlayClassName}`}
			>
				<div className={messageClassName} data-upgrade-msg='main'>
					<div className='flex flex-col items-center'>
						<h2 className='text-2xl font-bold text-center mb-4'>
							Access advanced AI reports
						</h2>
						<p className='text-center mb-4'>
							Upgrade to the Business plan or higher to unlock monthly advanced
							AI question topic reports.
						</p>
						<p className='text-center mb-6'>
							These reports are useful for identifying the most common questions
							asked by users so you can improve the user experience for these
							parts of your product or business. We also generate a report for
							questions rated as inaccurate or that led to an escalation to
							human support to help you improve your documentation and bot
							training.
						</p>
						<p className='text-center mb-8'>
							We use topic clustering to group similar questions together and
							identify the main topics as well as the most common sub-topics
							with an example question from each.
						</p>
						<Link
							href={upgradeLink}
							target='_blank'
							rel='noopener noreferrer'
							className='bg-blue-500 hover:bg-blue-600 text-white font-medium px-6 py-3 rounded-md flex items-center'
						>
							<FaCreditCard className='h-5 w-5 mr-2' />
							Upgrade Plan
						</Link>
					</div>
				</div>
			</div>
		</div>
	);
};

export default UpgradePlan;
