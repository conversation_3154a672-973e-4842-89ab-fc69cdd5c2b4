import { NextRequest, NextResponse } from "next/server";
import <PERSON><PERSON> from "stripe";
import { createClient } from "@/utils/supabase/server";

export const runtime = 'edge';

const stripe = new Stripe(process.env.STRIPE_SECRET_KEY!, {
	apiVersion: "2025-05-28.basil",
});

export async function POST(request: NextRequest) {
	try {
		if (!process.env.STRIPE_SECRET_KEY) {
			throw new Error("STRIPE_SECRET_KEY environment variable is not set");
		}

		const body = (await request.json()) as {
			priceId: string;
			projectId: string;
		};
		const { priceId, projectId } = body;

		// Log da entrada da requisição
		console.log("🎯 [CHECKOUT] Iniciando criação do checkout session:", {
			priceId,
			projectId,
			timestamp: new Date().toISOString(),
		});

		if (!priceId || !projectId) {
			console.error("❌ [CHECKOUT] Dados obrigatórios não fornecidos:", {
				priceId: !!priceId,
				projectId: !!projectId,
			});
			return NextResponse.json(
				{ error: "Price ID and Project ID are required" },
				{ status: 400 }
			);
		}

		// Get user from Supabase
		const supabase = await createClient();
		const {
			data: { user },
			error: userError,
		} = await supabase.auth.getUser();

		if (userError || !user) {
			console.error("❌ [CHECKOUT] Usuário não autorizado:", {
				userError: userError?.message,
				hasUser: !!user,
			});
			return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
		}

		console.log("✅ [CHECKOUT] Usuário autenticado:", {
			userId: user.id,
			email: user.email,
		});

		// Buscar detalhes do preço no Stripe para log detalhado
		try {
			await stripe.prices.retrieve(priceId, {
				expand: ["product"],
			});
		} catch (priceError) {
			console.error("❌ [CHECKOUT] Erro ao buscar detalhes do price:", {
				priceId,
				error: priceError instanceof Error ? priceError.message : priceError,
			});
			return NextResponse.json({ error: "Invalid price ID" }, { status: 400 });
		}

		// Get the return URLs from environment
		const baseUrl =
			process.env.NEXT_PUBLIC_BASE_URL || process.env.VERCEL_URL
				? `https://${process.env.VERCEL_URL}`
				: "http://localhost:3000";

		const successUrl = `${baseUrl}/${projectId}/settings?tab=billing&success=true`;
		const cancelUrl = `${baseUrl}/${projectId}/settings?tab=billing&canceled=true`;

		console.log("🔗 [CHECKOUT] URLs de retorno configuradas:", {
			successUrl,
			cancelUrl,
		});

		// Look for existing customer
		let customerId: string | undefined;

		const customers = await stripe.customers.list({
			email: user.email,
			limit: 1,
		});

		if (customers.data.length > 0) {
			customerId = customers.data[0].id;
			console.log("👤 [CHECKOUT] Cliente existente encontrado:", {
				customerId,
				email: user.email,
			});
		} else {
			// Create new customer
			const customer = await stripe.customers.create({
				email: user.email,
				name: user.user_metadata?.full_name || user.email,
				metadata: {
					project_id: projectId,
				},
			});
			customerId = customer.id;
			console.log("👤 [CHECKOUT] Novo cliente criado:", {
				customerId,
				email: user.email,
				name: user.user_metadata?.full_name || user.email,
			});
		}

		// Create Checkout Session
		const sessionData: Stripe.Checkout.SessionCreateParams = {
			customer: customerId,
			payment_method_types: ["card"],
			billing_address_collection: "required",
			line_items: [
				{
					price: priceId,
					quantity: 1,
				},
			],
			mode: "subscription",
			success_url: successUrl,
			cancel_url: cancelUrl,
			metadata: {
				project_id: projectId,
			},
		};

		console.log("🛒 [CHECKOUT] Dados da session a ser criada:", {
			customerId,
			priceId,
			projectId,
			mode: sessionData.mode,
		});

		const session = await stripe.checkout.sessions.create(sessionData);

		console.log("✅ [CHECKOUT] Session criada com sucesso:", {
			sessionId: session.id,
			sessionUrl: session.url,
			customerId,
			priceId,
			projectId,
			timestamp: new Date().toISOString(),
		});

		return NextResponse.json({ url: session.url });
	} catch (error) {
		console.error("❌ [CHECKOUT] Erro na criação do checkout session:", {
			error: error instanceof Error ? error.message : error,
			stack: error instanceof Error ? error.stack : undefined,
			timestamp: new Date().toISOString(),
		});

		const errorMessage =
			error instanceof Error ? error.message : "Unknown error";

		return NextResponse.json(
			{
				error: "Failed to create checkout session",
				details: errorMessage,
			},
			{ status: 500 }
		);
	}
}
