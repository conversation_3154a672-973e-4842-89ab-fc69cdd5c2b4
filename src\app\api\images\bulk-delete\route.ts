import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@/utils/supabase/server';

export const runtime = 'edge';

// POST /api/images/bulk-delete - Mark multiple images for deletion
export async function POST(request: NextRequest) {
	try {
		const body = (await request.json()) as {
			imageIds: string[];
		};
		const { imageIds } = body;

		if (!imageIds || !Array.isArray(imageIds) || imageIds.length === 0) {
			return NextResponse.json(
				{ error: 'Image IDs array is required' },
				{ status: 400 }
			);
		}

		const supabase = createClient();

		// Get user session for authentication
		const {
			data: { user },
			error: userError,
		} = await supabase.auth.getUser();

		if (userError || !user) {
			return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
		}

		// Mark all images as deleting
		const { data: images, error } = await supabase
			.from('organization_images')
			.update({
				deleting: true,
				updated_at: new Date().toISOString(),
			})
			.in('id', imageIds)
			.neq('deleting', true) // Only update if not already marked for deletion
			.select();

		if (error) {
			console.error('Error marking images for deletion:', error);
			return NextResponse.json(
				{ error: 'Failed to delete images', details: error.message },
				{ status: 500 }
			);
		}

		return NextResponse.json({
			success: true,
			message: `${images?.length || 0} images marked for deletion`,
			data: {
				deletedIds: images?.map((img) => img.id) || [],
				deletedCount: images?.length || 0,
			},
		});
	} catch (error) {
		console.error('Bulk delete API error:', error);
		return NextResponse.json(
			{
				success: false,
				error: 'Internal server error',
				details: error instanceof Error ? error.message : 'Unknown error',
			},
			{ status: 500 }
		);
	}
}
