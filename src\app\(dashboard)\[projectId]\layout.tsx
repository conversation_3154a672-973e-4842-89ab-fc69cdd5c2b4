"use server";

import DocsBot from "@/components/docsBotAi";
import { SidebarProvider, SidebarTrigger } from "@/components/ui/sidebar";
import { AppSidebar } from "@/components/app-sidebar";
import { cookies } from "next/headers";
import { getUserProjects } from "@/lib/supabase/userProjects";
import type { Project } from "@/contexts/ProjectContext/types";
import { notFound } from "next/navigation";
import { LayoutWrapper } from "@/components/LayoutWrapper";
import { fetchProjectInfo } from "@/utils/create-repo/cloudflare";

export default async function DashboardLayout({
	children,
	params: paramsPromise,
}: {
	children: React.ReactNode;
	params: Promise<{ projectId: string }>;
}) {
	const params = await paramsPromise;
	const projectId = params.projectId;

	const cookieStore = await cookies();
	const defaultOpen = cookieStore.get("sidebar_state")?.value === "true";

	if (!projectId) {
		notFound();
	}

	const projects: Project[] | null = await getUserProjects();

	const projectExists = projects?.some((p) => p.id.toString() === projectId);

	if (!projectExists) {
		notFound();
	}

	// Busca informações do projeto no Cloudflare (Web Analytics + Pages)
	const currentProject = projects?.find((p) => p.id.toString() === projectId);
	let projectInfo = {
		siteName: undefined as string | undefined,
		siteTag: undefined as string | undefined,
		hasWebAnalytics: false as boolean | undefined,
	};

	if (currentProject) {
		try {
			projectInfo = await fetchProjectInfo(currentProject.project_name);
		} catch (error) {
			// Log apenas erros não esperados (não 404)
			const isExpectedError =
				error &&
				typeof error === "object" &&
				"response" in error &&
				(error as { response?: { status?: number } }).response?.status === 404;

			if (!isExpectedError) {
				console.error(
					`Unexpected error fetching project info for ${currentProject.project_name}:`,
					error
				);
			}
			// Para erros 404, mantém os valores padrão sem log de erro
		}
	}

	return (
		<SidebarProvider defaultOpen={defaultOpen}>
			<AppSidebar />
			<main className='w-full h-full'>
				<SidebarTrigger />
				<DocsBot />
				<LayoutWrapper projectId={projectId} projectInfo={projectInfo}>
					{children}
				</LayoutWrapper>
			</main>
		</SidebarProvider>
	);
}
