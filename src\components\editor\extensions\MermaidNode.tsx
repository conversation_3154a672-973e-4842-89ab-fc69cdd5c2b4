import { Node } from '@tiptap/core';
import { <PERSON>de<PERSON><PERSON>w<PERSON><PERSON>per, ReactNodeViewRenderer } from '@tiptap/react';

declare module '@tiptap/core' {
	interface Commands<ReturnType> {
		mermaid: {
			/**
			 * Insert a mermaid diagram with the specified attributes.
			 */
			setMermaid: (attributes?: MermaidAttrs) => ReturnType;
		};
	}
}
import React, { useState, useCallback, useEffect, useRef } from 'react';
import { Edit3, Save, X } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { useToast } from '@/components/ToastProvider';
import type { NodeViewProps } from '@tiptap/react';
import mermaid from 'mermaid';

// Interface for mermaid attributes
interface MermaidAttrs {
	code: string;
	theme?: 'default' | 'dark' | 'forest' | 'neutral';
}

// Mermaid diagram templates
const MERMAID_TEMPLATES = {
	flowchart: `graph TD
    A[Start] --> B{Decision}
    B -->|Yes| C[Action 1]
    B -->|No| D[Action 2]
    C --> E[End]
    D --> E`,
	sequence: `sequenceDiagram
    participant Alice
    participant Bob
    Alice->>John: Hello <PERSON>, how are you?
    loop HealthCheck
        John->>John: Fight against hypochondria
    end
    Note right of John: Rational thoughts <br/>prevail!
    John-->>Alice: Great!
    John->>Bob: How about you?
    Bob-->>John: Jolly good!`,
	gantt: `gantt
    title A Gantt Diagram
    dateFormat  YYYY-MM-DD
    section Section
    A task           :a1, 2024-01-01, 30d
    Another task     :after a1  , 20d`,
	pie: `pie title Pie Chart
    "Dogs" : 386
    "Cats" : 85
    "Rats" : 15`,
	gitgraph: `gitgraph
    commit
    branch develop
    checkout develop
    commit
    checkout main
    merge develop`,
};

// Mermaid Component
const MermaidComponent: React.FC<NodeViewProps> = ({
	node,
	updateAttributes,
	deleteNode,
}) => {
	const { code, theme = 'default' } = node.attrs as MermaidAttrs;
	const [isEditing, setIsEditing] = useState(!code);
	const [editingCode, setEditingCode] = useState(
		code || MERMAID_TEMPLATES.flowchart
	);
	const [svgContent, setSvgContent] = useState<string>('');
	const [error, setError] = useState<string>('');
	const [isLoading, setIsLoading] = useState(false);
	const mermaidRef = useRef<HTMLDivElement>(null);
	const { addToast } = useToast();

	// Initialize Mermaid
	useEffect(() => {
		mermaid.initialize({
			startOnLoad: false,
			theme: theme,
			securityLevel: 'loose',
			fontFamily: 'inherit',
		});
	}, [theme]);

	// Render diagram
	const renderDiagram = useCallback(async (diagramCode: string) => {
		if (!diagramCode.trim()) return;

		setIsLoading(true);
		setError('');

		try {
			const id = `mermaid-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
			const { svg } = await mermaid.render(id, diagramCode);
			setSvgContent(svg);
		} catch (err) {
			setError(err instanceof Error ? err.message : 'Failed to render diagram');
			setSvgContent('');
		} finally {
			setIsLoading(false);
		}
	}, []);

	// Effect to render diagram when code changes
	useEffect(() => {
		if (code) {
			renderDiagram(code);
		}
	}, [code, renderDiagram]);

	const handleSave = useCallback(async () => {
		if (!editingCode.trim()) {
			addToast('Please enter some Mermaid code', 'error');
			return;
		}

		try {
			// Test render to validate syntax
			const id = `test-${Date.now()}`;
			await mermaid.render(id, editingCode);

			updateAttributes({ code: editingCode });
			setIsEditing(false);
			addToast('Mermaid diagram saved successfully', 'success');
		} catch (err) {
			addToast(
				`Invalid Mermaid syntax: ${err instanceof Error ? err.message : 'Unknown error'}`,
				'error'
			);
		}
	}, [editingCode, updateAttributes, addToast]);

	const handleCancel = useCallback(() => {
		setEditingCode(code || MERMAID_TEMPLATES.flowchart);
		setIsEditing(false);
	}, [code]);

	const handleTemplateSelect = useCallback(
		(template: keyof typeof MERMAID_TEMPLATES) => {
			setEditingCode(MERMAID_TEMPLATES[template]);
		},
		[]
	);

	if (isEditing) {
		return (
			<NodeViewWrapper className='mermaid-node-wrapper'>
				<div className='border border-gray-200 dark:border-gray-700 rounded-lg p-4 bg-white dark:bg-gray-800'>
					<div className='flex items-center justify-between mb-3'>
						<h3 className='text-sm font-medium text-gray-700 dark:text-gray-300'>
							Edit Mermaid Diagram
						</h3>
						<div className='flex gap-2'>
							<Button
								size='sm'
								variant='outline'
								onClick={handleCancel}
								className='h-8 px-3'
							>
								<X className='w-4 h-4 mr-1' />
								Cancel
							</Button>
							<Button size='sm' onClick={handleSave} className='h-8 px-3'>
								<Save className='w-4 h-4 mr-1' />
								Save
							</Button>
						</div>
					</div>

					{/* Template buttons */}
					<div className='flex flex-wrap gap-2 mb-3'>
						{Object.keys(MERMAID_TEMPLATES).map((template) => (
							<Button
								key={template}
								size='sm'
								variant='outline'
								onClick={() =>
									handleTemplateSelect(
										template as keyof typeof MERMAID_TEMPLATES
									)
								}
								className='h-7 px-2 text-xs capitalize'
							>
								{template}
							</Button>
						))}
					</div>

					<textarea
						value={editingCode}
						onChange={(e) => setEditingCode(e.target.value)}
						placeholder='Enter Mermaid diagram code...'
						className='w-full h-48 p-3 border border-gray-200 dark:border-gray-600 rounded-md bg-gray-50 dark:bg-gray-900 text-sm font-mono resize-none focus:outline-none focus:ring-2 focus:ring-blue-500'
					/>

					<div className='mt-2 text-xs text-gray-500 dark:text-gray-400'>
						Use Mermaid syntax to create diagrams.
						<a
							href='https://mermaid.js.org/syntax/flowchart.html'
							target='_blank'
							rel='noopener noreferrer'
							className='text-blue-500 hover:underline ml-1'
						>
							View documentation
						</a>
					</div>
				</div>
			</NodeViewWrapper>
		);
	}

	return (
		<NodeViewWrapper className='mermaid-node-wrapper'>
			<div className='border border-gray-200 dark:border-gray-700 rounded-lg bg-white dark:bg-gray-800 overflow-hidden'>
				<div className='flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-900 border-b border-gray-200 dark:border-gray-700'>
					<div className='flex items-center gap-2'>
						<div className='w-3 h-3 bg-blue-500 rounded-full'></div>
						<span className='text-sm font-medium text-gray-700 dark:text-gray-300'>
							Mermaid Diagram
						</span>
					</div>
					<div className='flex gap-1'>
						<Button
							size='sm'
							variant='ghost'
							onClick={() => setIsEditing(true)}
							className='h-8 w-8 p-0'
							title='Edit diagram'
						>
							<Edit3 className='w-4 h-4' />
						</Button>
						<Button
							size='sm'
							variant='ghost'
							onClick={deleteNode}
							className='h-8 w-8 p-0 text-red-500 hover:text-red-700'
							title='Delete diagram'
						>
							<X className='w-4 h-4' />
						</Button>
					</div>
				</div>

				<div className='p-4'>
					{isLoading && (
						<div className='flex items-center justify-center h-32'>
							<div className='animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500'></div>
						</div>
					)}

					{error && (
						<div className='bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-md p-3'>
							<p className='text-sm text-red-600 dark:text-red-400'>
								Error rendering diagram: {error}
							</p>
						</div>
					)}

					{svgContent && !isLoading && (
						<div
							ref={mermaidRef}
							className='mermaid-diagram flex justify-center'
							dangerouslySetInnerHTML={{ __html: svgContent }}
						/>
					)}
				</div>
			</div>
		</NodeViewWrapper>
	);
};

// Mermaid Node Extension
export const MermaidNode = Node.create({
	name: 'mermaid',
	group: 'block',
	content: '',
	atom: true,
	draggable: false,

	addAttributes() {
		return {
			code: {
				default: '',
				parseHTML: (element) => element.getAttribute('data-code') || '',
				renderHTML: (attributes) => {
					if (!attributes.code) return {};
					return { 'data-code': attributes.code };
				},
			},
			theme: {
				default: 'default',
				parseHTML: (element) => element.getAttribute('data-theme') || 'default',
				renderHTML: (attributes) => {
					if (!attributes.theme) return {};
					return { 'data-theme': attributes.theme };
				},
			},
		};
	},

	parseHTML() {
		return [
			{
				tag: 'div[data-type="mermaid"]',
			},
			{
				tag: 'mermaid',
			},
			{
				tag: 'pre',
				getAttrs: (element) => {
					const codeElement = element.querySelector('code.language-mermaid');
					if (codeElement) {
						return {
							code: codeElement.textContent || '',
						};
					}
					return false;
				},
			},
		];
	},

	renderHTML({ HTMLAttributes }) {
		return [
			'div',
			{
				...HTMLAttributes,
				'data-type': 'mermaid',
				class: 'mermaid-node',
			},
		];
	},

	addNodeView() {
		return ReactNodeViewRenderer(MermaidComponent);
	},

	addCommands() {
		return {
			setMermaid:
				(attributes?: MermaidAttrs) =>
				({ commands }) => {
					return commands.insertContent({
						type: this.name,
						attrs: attributes,
					});
				},
		};
	},
});

export default MermaidNode;
