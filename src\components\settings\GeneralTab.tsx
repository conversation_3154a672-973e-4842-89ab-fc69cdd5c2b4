import React, { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Card, CardContent } from "@/components/ui/card";
import { Copy, Plus } from "lucide-react";
import {
	Tooltip,
	TooltipContent,
	TooltipTrigger,
} from "@/components/ui/tooltip";
import { Project } from "@/contexts/ProjectContext/types";

interface GeneralTabProps {
	selectedProject: Project | null;
}

export const GeneralTab: React.FC<GeneralTabProps> = ({ selectedProject }) => {
	const [showCopiedTooltip, setShowCopiedTooltip] = useState(false);

	const handleCopyToClipboard = async (text: string) => {
		try {
			await navigator.clipboard.writeText(text);
			setShowCopiedTooltip(true);
			setTimeout(() => setShowCopiedTooltip(false), 2000);
		} catch (err) {
			console.error("Failed to copy text: ", err);
		}
	};

	return (
		<div className='space-y-8'>
			{/* Project Name, Subdomain, Custom Domain Section */}
			<div className='grid grid-cols-1 lg:grid-cols-3 gap-8'>
				{/* Project Name */}
				<div className='space-y-2'>
					<Label className='text-sm font-medium text-foreground'>
						Project Name
					</Label>
					<Input
						value={selectedProject?.project_name || ""}
						readOnly
						className='bg-muted text-muted-foreground'
					/>
				</div>

				{/* Subdomain */}
				<div className='space-y-2'>
					<div className='flex items-center space-x-1'>
						<Label className='text-sm font-medium text-foreground'>
							Subdomain
						</Label>
						<div className='w-4 h-4 rounded-full border border-muted-foreground flex items-center justify-center'>
							<span className='text-xs text-muted-foreground'>?</span>
						</div>
					</div>
					<div className='relative'>
						<Input
							value={`${selectedProject?.project_name || ""}.writedocs.io`}
							readOnly
							className='bg-muted text-muted-foreground pr-8'
						/>
						<Tooltip open={showCopiedTooltip}>
							<TooltipTrigger asChild>
								<Copy
									className='absolute right-2 top-1/2 transform -translate-y-1/2 w-4 h-4 text-muted-foreground cursor-pointer hover:text-foreground transition-colors'
									onClick={() =>
										handleCopyToClipboard(
											`${selectedProject?.project_name || ""}.writedocs.io`
										)
									}
								/>
							</TooltipTrigger>
							<TooltipContent>
								<p>Copied!</p>
							</TooltipContent>
						</Tooltip>
					</div>
				</div>

				{/* Custom Domain */}
				<div className='space-y-2'>
					<Label className='text-sm font-medium text-foreground'>
						Custom Domain
					</Label>
					<div className='space-y-1'>
						<p className='text-sm text-muted-foreground'>
							No custom domain set
						</p>
						<p className='text-xs text-muted-foreground'>
							Contact support to set up a custom domain
						</p>
					</div>
				</div>
			</div>

			{/* Custom Domain Table Section */}
			<Card>
				<CardContent className='p-6'>
					<div className='flex items-center justify-between mb-4'>
						<h3 className='text-lg font-medium text-foreground'>
							Custom Domain
						</h3>
						<Button
							variant='outline'
							size='sm'
							className='text-sm disabled cursor-not-allowed hover:bg-transparent opacity-60'
							// onClick={() => setIsAddDomainOpen(true)}
						>
							<Plus className='w-4 h-4 mr-2' />
							Add Domain
						</Button>
					</div>

					{/* Table Headers */}
					<div className='grid grid-cols-6 gap-4 pb-3 border-b text-sm font-medium text-muted-foreground'>
						<div>Type</div>
						<div>Host</div>
						<div>Value</div>
						<div>TTL</div>
						<div>Status</div>
						<div>Actions</div>
					</div>

					{/* Empty State */}
					<div className='py-12 text-center'>
						<p className='text-muted-foreground'>No DNS has been configured.</p>
					</div>
				</CardContent>
			</Card>
		</div>
	);
};
