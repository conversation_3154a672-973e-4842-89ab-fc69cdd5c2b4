'use client';

import { useState, useCallback } from 'react';
import { createClient } from '@/utils/supabase/client';
import { useToast } from '@/components/ToastProvider';
import { type OrganizationImage } from '@/hooks/useOrganizationImages';

interface PendingImage {
	id: string;
	file: File;
	preview: string;
	name: string;
	customName: string;
	size: number;
	altText: string;
	tags: string[];
}

interface UploadingImage {
	id: string;
	name: string;
	status: 'uploading' | 'completed' | 'error';
	preview?: string;
	error?: string;
	progress?: number;
}

interface UploadResult {
	name: string;
	url: string;
	size: number;
	id: string;
	originalSize?: number;
	compressionRatio?: number;
}

interface UploadSummary {
	total_attempted: number;
	successful: number;
	failed: number;
	total_size_mb: number;
	total_original_size_mb?: number;
	compression_ratio?: number;
}

interface UseImageUploadProps {
	projectId?: number;
	organizationId?: string;
	onUploadComplete?: (images: OrganizationImage[]) => void;
}

export const useImageUpload = ({
	projectId,
	organizationId,
	onUploadComplete,
}: UseImageUploadProps) => {
	const [uploading, setUploading] = useState(false);
	const [uploadingImages, setUploadingImages] = useState<UploadingImage[]>([]);
	const { addToast } = useToast();

	// Fixed limits
	const LIMITS = {
		max_images: 9999999,
		max_size_mb_per_image: 50, // Increased for server processing
		max_batch_upload: 20,
	};

	// Toast IDs for consistent messaging
	const TOAST_IDS = {
		UPLOAD_SUCCESS: 'image-upload-success',
		UPLOAD_ERROR: 'image-upload-error',
		DELETE_SUCCESS: 'image-delete-success',
		DELETE_ERROR: 'image-delete-error',
		VALIDATION_ERROR: 'image-validation-error',
	};

	// Helper function to create user-friendly error messages
	const createUserFriendlyErrorMessage = useCallback(
		(error: string): string => {
			// Database/technical errors
			if (
				error.includes('Database error') ||
				error.includes('duplicate key') ||
				error.includes('constraint')
			) {
				return 'Unable to save image. Please try again.';
			}

			// Network/connection errors
			if (
				error.includes('fetch') ||
				error.includes('Network') ||
				error.includes('connection')
			) {
				return 'Connection problem. Please check your internet and try again.';
			}

			// Authentication errors
			if (
				error.includes('authentication') ||
				error.includes('unauthorized') ||
				error.includes('session')
			) {
				return 'Session expired. Please refresh the page and try again.';
			}

			// File size/format errors
			if (error.includes('too large') || error.includes('size')) {
				return 'Image file is too large. Please use a smaller image.';
			}

			if (
				error.includes('format') ||
				error.includes('type') ||
				error.includes('unsupported')
			) {
				return 'Image format not supported. Please use JPG, PNG, or WebP.';
			}

			// Compression errors
			if (error.includes('compression') || error.includes('compress')) {
				return 'Unable to process image. Please try a different image.';
			}

			// Generic server errors
			if (error.includes('500') || error.includes('Internal Server Error')) {
				return 'Server temporarily unavailable. Please try again in a moment.';
			}

			// If error doesn't expose sensitive info, show it
			const sensitiveKeywords = [
				'database',
				'sql',
				'query',
				'table',
				'column',
				'constraint',
				'key',
				'index',
			];
			const lowerError = error.toLowerCase();
			const hasSensitiveInfo = sensitiveKeywords.some((keyword) =>
				lowerError.includes(keyword)
			);

			if (!hasSensitiveInfo && error.length < 100) {
				return error;
			}

			// Default fallback
			return 'Something went wrong. Please try again.';
		},
		[]
	);

	// Enhanced image compression function with transparency preservation
	const compressImage = useCallback(
		async (file: File, targetSizeMB: number = 8): Promise<File> => {
			return new Promise((resolve, reject) => {
				const canvas = document.createElement('canvas');
				const ctx = canvas.getContext('2d');
				const img = new Image();

				img.onload = () => {
					try {
						// Calculate optimal dimensions based on file size
						let { width, height } = img;
						const originalSizeMB = file.size / (1024 * 1024);

						// More aggressive scaling for larger files
						let maxDimension = 1920;
						if (originalSizeMB > 15) maxDimension = 1200;
						else if (originalSizeMB > 10) maxDimension = 1400;
						else if (originalSizeMB > 5) maxDimension = 1600;

						// Scale down if needed
						if (width > maxDimension || height > maxDimension) {
							const ratio = Math.min(
								maxDimension / width,
								maxDimension / height
							);
							width = Math.round(width * ratio);
							height = Math.round(height * ratio);
						}

						canvas.width = width;
						canvas.height = height;

						// Check if image has transparency
						const hasTransparency = file.type === 'image/png' || file.type === 'image/webp';
						
						// For PNG with potential transparency, clear canvas with transparent background
						if (hasTransparency && ctx) {
							ctx.clearRect(0, 0, width, height);
						}

						// Draw image
						ctx?.drawImage(img, 0, 0, width, height);

						// Determine output format and quality
						let outputFormat: string;
						let quality: number;

						if (hasTransparency) {
							// Preserve transparency - use PNG or WebP
							outputFormat = file.type === 'image/webp' ? 'image/webp' : 'image/png';
							// PNG doesn't use quality parameter, WebP does
							quality = file.type === 'image/webp' ? 0.8 : 1;
							
							// Adjust WebP quality based on file size
							if (file.type === 'image/webp') {
								if (originalSizeMB > 15) quality = 0.4;
								else if (originalSizeMB > 10) quality = 0.5;
								else if (originalSizeMB > 5) quality = 0.6;
								else if (originalSizeMB > 2) quality = 0.7;
							}
						} else {
							// No transparency - use JPEG for better compression
							outputFormat = 'image/jpeg';
							if (originalSizeMB > 15) quality = 0.4;
							else if (originalSizeMB > 10) quality = 0.5;
							else if (originalSizeMB > 5) quality = 0.6;
							else if (originalSizeMB > 2) quality = 0.7;
							else quality = 0.8;
						}

						// Convert to blob
						canvas.toBlob(
							(blob) => {
								if (blob) {
									const compressedFile = new File([blob], file.name, {
										type: outputFormat,
										lastModified: Date.now(),
									});

									const compressionRatio =
										((file.size - compressedFile.size) / file.size) * 100;

									console.log(`Client-side compression for ${file.name}:`);
									console.log(
										`Original: ${(file.size / 1024 / 1024).toFixed(2)}MB (${file.type})`
									);
									console.log(
										`Compressed: ${(compressedFile.size / 1024 / 1024).toFixed(2)}MB (${outputFormat})`
									);
									console.log(`Compression: ${compressionRatio.toFixed(1)}%`);
									console.log(`Quality used: ${quality}`);
									console.log(`Transparency preserved: ${hasTransparency}`);

									// If still too large and we can apply more aggressive compression
									// Note: PNG doesn't support quality compression, so we skip aggressive compression for PNG
									if (
										compressedFile.size > targetSizeMB * 1024 * 1024 &&
										outputFormat !== 'image/png' &&
										((outputFormat === 'image/jpeg' && quality > 0.3) ||
										 (outputFormat === 'image/webp' && quality > 0.3))
									) {
										console.log(
											'Still too large, applying more aggressive compression...'
										);

										const aggressiveQuality = outputFormat === 'image/webp' ? 0.3 : 0.3;

										canvas.toBlob(
											(aggressiveBlob) => {
												if (aggressiveBlob) {
													const veryCompressedFile = new File(
														[aggressiveBlob],
														file.name,
														{
															type: outputFormat,
															lastModified: Date.now(),
														}
													);

													const newRatio =
														((file.size - veryCompressedFile.size) /
															file.size) *
														100;
													console.log(
														`Very compressed: ${(veryCompressedFile.size / 1024 / 1024).toFixed(2)}MB (${newRatio.toFixed(1)}%)`
													);

													resolve(veryCompressedFile);
												} else {
													resolve(compressedFile);
												}
											},
											outputFormat,
											aggressiveQuality
										);
									} else {
										resolve(compressedFile);
									}
								} else {
									reject(new Error('Failed to compress image'));
								}
							},
							outputFormat,
							quality
						);
					} catch (error) {
						reject(error);
					}
				};

				img.onerror = () => reject(new Error('Failed to load image'));
				img.src = URL.createObjectURL(file);
			});
		},
		[]
	);

	// Validate files
	const validateFiles = useCallback(
		(
			files: FileList | File[]
		): {
			validFiles: File[];
			errors: string[];
		} => {
			const validFiles: File[] = [];
			const errors: string[] = [];
			const fileArray = Array.from(files);

			// Batch limit check
			if (fileArray.length > LIMITS.max_batch_upload) {
				errors.push(
					`You can upload a maximum of ${LIMITS.max_batch_upload} images at once.`
				);
				return { validFiles: [], errors };
			}

			fileArray.forEach((file) => {
				// File type validation
				if (!file.type.startsWith('image/')) {
					errors.push(`${file.name}: Unsupported file type`);
					return;
				}

				// File size validation
				if (file.size > LIMITS.max_size_mb_per_image * 1024 * 1024) {
					const maxMB = LIMITS.max_size_mb_per_image;
					const fileMB = (file.size / (1024 * 1024)).toFixed(2);
					errors.push(
						`${file.name}: File too large (${fileMB}MB). Maximum allowed: ${maxMB}MB`
					);
					return;
				}

				validFiles.push(file);
			});

			return { validFiles, errors };
		},
		[LIMITS]
	);

	// Create OrganizationImage from upload result
	const createOrganizationImageFromUpload = useCallback(
		(
			uploadResult: UploadResult,
			pendingImage?: PendingImage
		): OrganizationImage => {
			const now = new Date().toISOString();
			return {
				id: uploadResult.id,
				organization_id: organizationId!,
				project_id: projectId!,
				image_name: uploadResult.name,
				image_path: '',
				image_url: uploadResult.url,
				file_size: uploadResult.size,
				content_type: pendingImage?.file.type || 'image/jpeg',
				alt_text: pendingImage?.altText || '',
				tags: pendingImage?.tags || [],
				metadata: {
					originalSize: uploadResult.originalSize,
					compressionRatio: uploadResult.compressionRatio,
					uploadedAt: now,
				},
				created_by: '',
				created_at: now,
				updated_at: now,
			};
		},
		[organizationId, projectId]
	);

	// Upload images using FormData (no base64)
	const uploadImages = useCallback(
		async (
			imagesToUpload: PendingImage[]
		): Promise<{
			success: boolean;
			uploadedImages: OrganizationImage[];
			errors: string[];
			summary?: UploadSummary;
		}> => {
			if (!projectId || !organizationId) {
				throw new Error('Project or organization ID not found');
			}

			setUploading(true);

			try {
				const supabase = createClient();
				const {
					data: { session },
				} = await supabase.auth.getSession();

				if (!session) {
					throw new Error('User not authenticated');
				}

				const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
				if (!supabaseUrl) {
					throw new Error('Supabase URL not found');
				}

				// Create FormData for file upload
				const formData = new FormData();

				// Add metadata
				const metadata = {
					projectId,
					organizationId,
					images: imagesToUpload.map((img) => ({
						name: img.customName || img.name,
						contentType: img.file.type,
						altText: img.altText,
						tags: img.tags,
						metadata: {
							originalName: img.name,
							uploadedAt: new Date().toISOString(),
						},
					})),
				};

				formData.append('metadata', JSON.stringify(metadata));

				// Add files
				imagesToUpload.forEach((img, index) => {
					formData.append(`file_${index}`, img.file);
				});

				// Call advanced Edge Function
				const response = await fetch(
					`${supabaseUrl}/functions/v1/advanced-image-upload`,
					{
						method: 'POST',
						headers: {
							Authorization: `Bearer ${session.access_token}`,
							// Don't set Content-Type for FormData - browser sets it with boundary
						},
						body: formData,
					}
				);

				if (!response.ok) {
					const errorText = await response.text();
					console.error('Upload error response:', errorText);
					throw new Error(
						`Upload failed: ${response.statusText} - ${errorText}`
					);
				}

				const result = (await response.json()) as {
					success: boolean;
					uploadedImages?: UploadResult[];
					errors?: string[];
					summary?: UploadSummary;
					error?: string;
				};

				if (result.success && result.uploadedImages) {
					const newImages: OrganizationImage[] = result.uploadedImages.map(
						(uploadedImg) => {
							const pendingImg = imagesToUpload.find(
								(p) => (p.customName || p.name) === uploadedImg.name
							);
							return createOrganizationImageFromUpload(uploadedImg, pendingImg);
						}
					);

					return {
						success: true,
						uploadedImages: newImages,
						errors: result.errors || [],
						summary: result.summary,
					};
				} else {
					throw new Error(
						result.error || result.errors?.join(', ') || 'Unknown error'
					);
				}
			} finally {
				setUploading(false);
			}
		},
		[projectId, organizationId, createOrganizationImageFromUpload]
	);

	// Handle file selection and upload
	const handleFileUpload = useCallback(
		async (files: FileList | File[]) => {
			const { validFiles, errors } = validateFiles(files);

			// Show validation errors with consistent ID
			if (errors.length > 0) {
				const errorMessage =
					errors.length === 1
						? errors[0]
						: `${errors.length} files could not be added:\n${errors.join('\n')}`;

				addToast(
					errorMessage,
					'warning',
					'File Validation',
					TOAST_IDS.VALIDATION_ERROR
				);
			}

			if (validFiles.length === 0) return;

			// Create uploading images with loading status
			const newUploadingImages: UploadingImage[] = validFiles.map((file) => ({
				id: Math.random().toString(36).substring(2, 15),
				name: file.name.replace(/\.[^/.]+$/, ''),
				status: 'uploading' as const,
			}));

			setUploadingImages((prev) => [...prev, ...newUploadingImages]);

			// Compress images in parallel and create pending images
			console.log(
				`Starting parallel compression of ${validFiles.length} images...`
			);
			const compressionStartTime = Date.now();

			const compressionPromises = validFiles.map(async (file, index) => {
				try {
					// Compress image if it's larger than 1MB
					let processedFile = file;
					if (file.size > 1024 * 1024) {
						console.log(`Compressing ${file.name}...`);
						const compressionStart = Date.now();
						processedFile = await compressImage(file, 8); // Target 8MB max
						const compressionTime = Date.now() - compressionStart;
						console.log(`${file.name} compressed in ${compressionTime}ms`);
					}

					return {
						id: newUploadingImages[index].id,
						file: processedFile,
						preview: URL.createObjectURL(processedFile),
						name: file.name,
						customName: file.name.replace(/\.[^/.]+$/, ''),
						size: processedFile.size,
						altText: file.name.replace(/\.[^/.]+$/, ''),
						tags: [],
					};
				} catch (compressionError) {
					console.warn(
						`Failed to compress ${file.name}, using original:`,
						compressionError
					);
					// Fallback to original file
					return {
						id: newUploadingImages[index].id,
						file,
						preview: URL.createObjectURL(file),
						name: file.name,
						customName: file.name.replace(/\.[^/.]+$/, ''),
						size: file.size,
						altText: file.name.replace(/\.[^/.]+$/, ''),
						tags: [],
					};
				}
			});

			// Wait for all compressions to complete in parallel
			const pendingImages = await Promise.all(compressionPromises);
			const totalCompressionTime = Date.now() - compressionStartTime;
			console.log(
				`All ${validFiles.length} images compressed in ${totalCompressionTime}ms (parallel)`
			);

			// Calculate total compression stats
			const originalTotalSize = validFiles.reduce(
				(sum, file) => sum + file.size,
				0
			);
			const compressedTotalSize = pendingImages.reduce(
				(sum, img) => sum + img.size,
				0
			);
			const overallCompression =
				((originalTotalSize - compressedTotalSize) / originalTotalSize) * 100;

			console.log(`Batch compression summary:`);
			console.log(
				`Original total: ${(originalTotalSize / 1024 / 1024).toFixed(2)}MB`
			);
			console.log(
				`Compressed total: ${(compressedTotalSize / 1024 / 1024).toFixed(2)}MB`
			);
			console.log(`Overall compression: ${overallCompression.toFixed(1)}%`);

			try {
				// Upload in smaller batches for better performance with progressive display
				const batchSize = 5; // Upload 5 images at a time
				const batches: PendingImage[][] = [];

				for (let i = 0; i < pendingImages.length; i += batchSize) {
					batches.push(pendingImages.slice(i, i + batchSize));
				}

				console.log(
					`Uploading ${pendingImages.length} images in ${batches.length} batches of ${batchSize}`
				);

				const uploadStartTime = Date.now();
				const allResults: OrganizationImage[] = [];
				const allErrors: string[] = [];

				// Upload batches with progressive display (max 2 concurrent batches)
				const maxConcurrentBatches = 2;
				for (let i = 0; i < batches.length; i += maxConcurrentBatches) {
					const currentBatches = batches.slice(i, i + maxConcurrentBatches);

					const batchPromises = currentBatches.map(
						async (batch, batchIndex) => {
							const actualBatchIndex = i + batchIndex;
							console.log(
								`Starting upload batch ${actualBatchIndex + 1}/${batches.length} (${batch.length} images)`
							);

							try {
								const result = await uploadImages(batch);
								console.log(
									`Batch ${actualBatchIndex + 1} completed: ${result.uploadedImages.length} uploaded, ${result.errors.length} failed`
								);

								// Show completed images immediately
								if (result.uploadedImages.length > 0) {
									console.log(
										`Showing ${result.uploadedImages.length} completed images from batch ${actualBatchIndex + 1}`
									);
									onUploadComplete?.(result.uploadedImages);

									// Remove completed uploads from uploading state
									const completedIds = result.uploadedImages
										.map((img) => {
											const pendingImg = batch.find(
												(p) => (p.customName || p.name) === img.image_name
											);
											return pendingImg?.id;
										})
										.filter(Boolean);

									setUploadingImages((prev) =>
										prev.filter((img) => !completedIds.includes(img.id))
									);
								}

								return result;
							} catch (error) {
								console.error(`Batch ${actualBatchIndex + 1} failed:`, error);
								return {
									success: false,
									uploadedImages: [],
									errors: [
										`Batch ${actualBatchIndex + 1} failed: ${error instanceof Error ? error.message : 'Unknown error'}`,
									],
									summary: undefined,
								};
							}
						}
					);

					const batchResults = await Promise.all(batchPromises);

					// Collect results
					batchResults.forEach((result) => {
						allResults.push(...result.uploadedImages);
						allErrors.push(...result.errors);
					});
				}

				const totalUploadTime = Date.now() - uploadStartTime;
				console.log(`All uploads completed in ${totalUploadTime}ms`);

				// Create final result summary
				const result = {
					success: allResults.length > 0,
					uploadedImages: allResults,
					errors: allErrors,
					summary: {
						total_attempted: pendingImages.length,
						successful: allResults.length,
						failed: allErrors.length,
						compression_ratio: overallCompression,
					},
				};

				if (result.success) {
					// Clean up preview URLs
					pendingImages.forEach((img) => URL.revokeObjectURL(img.preview));

					// Show final summary message without compression details
					let message = `${result.uploadedImages.length} image(s) uploaded successfully`;
					if (result.errors.length > 0) {
						message += `, ${result.errors.length} failed`;
					}

					addToast(
						message,
						'success',
						'Upload Complete',
						TOAST_IDS.UPLOAD_SUCCESS
					);

					// Note: onUploadComplete is now called progressively for each batch
					// so we don't call it again here
				}
			} catch (error) {
				console.error('Error uploading images:', error);

				// Update uploading images status to error
				setUploadingImages((prev) =>
					prev.map((img) =>
						pendingImages.find((p) => p.id === img.id)
							? { ...img, status: 'error' as const, error: 'Upload failed' }
							: img
					)
				);

				// Remove error images after delay
				setTimeout(() => {
					setUploadingImages((prev) =>
						prev.filter((img) => img.status !== 'error')
					);
				}, 5000);

				const rawError =
					error instanceof Error ? error.message : 'Unknown error';
				const userFriendlyMessage = createUserFriendlyErrorMessage(rawError);

				addToast(
					userFriendlyMessage,
					'error',
					'Upload Failed',
					TOAST_IDS.UPLOAD_ERROR
				);
			}
		},
		[
			validateFiles,
			uploadImages,
			addToast,
			onUploadComplete,
			compressImage,
			createUserFriendlyErrorMessage,
			TOAST_IDS.UPLOAD_SUCCESS,
			TOAST_IDS.UPLOAD_ERROR,
			TOAST_IDS.VALIDATION_ERROR,
		]
	);

	return {
		uploading,
		uploadingImages,
		handleFileUpload,
		validateFiles,
		LIMITS,
	};
};
