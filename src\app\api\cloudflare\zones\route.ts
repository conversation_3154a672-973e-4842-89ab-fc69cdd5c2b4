import { NextResponse } from "next/server";
import axios, { isAxiosError } from "axios";

export const runtime = 'edge';

const CLOUDFLARE_API_TOKEN = process.env.CLOUDFLARE_API_TOKEN as string;
const CLOUDFLARE_ACCOUNT_ID = process.env.CLOUDFLARE_ACCOUNT_ID as string;

interface CloudflareZone {
	id: string;
	name: string;
	status: string;
	account: {
		id: string;
		name: string;
	};
	plan?: {
		name: string;
	};
}

export async function GET() {
	try {
		const response = await axios.get(
			`https://api.cloudflare.com/client/v4/zones`,
			{
				headers: {
					Authorization: `Bearer ${CLOUDFLARE_API_TOKEN}`,
					"Content-Type": "application/json",
				},
			}
		);

		const zones =
			response.data.result?.map((zone: CloudflareZone) => ({
				id: zone.id,
				name: zone.name,
				status: zone.status,
				account: zone.account,
				plan: zone.plan?.name,
			})) || [];

		return NextResponse.json({
			success: true,
			data: {
				zones,
				totalZones: zones.length,
				accountId: CLOUDFLARE_ACCOUNT_ID,
				hasToken: !!CLOUDFLARE_API_TOKEN,
			},
		});
	} catch (error) {
		if (isAxiosError(error)) {
			console.error(
				"Error fetching zones:",
				error.response?.data || error.message
			);
			return NextResponse.json(
				{
					error: "Failed to fetch zones",
					details: error.response?.data?.errors || error.message,
					debug: {
						accountId: CLOUDFLARE_ACCOUNT_ID,
						hasToken: !!CLOUDFLARE_API_TOKEN,
					},
				},
				{ status: error.response?.status || 500 }
			);
		}
		console.error("Error fetching zones:", error);
		return NextResponse.json(
			{
				error: "Failed to fetch zones",
				details: "An unexpected error occurred",
				debug: {
					accountId: CLOUDFLARE_ACCOUNT_ID,
					hasToken: !!CLOUDFLARE_API_TOKEN,
				},
			},
			{ status: 500 }
		);
	}
}
