"use client";

import { CardContent } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { IgithubOnboarding } from "@/app/interfaces/onboarding";
import { useEffect, useState } from "react";
import Link from "next/link";

export default function Resume() {
	const [githubValues, setGithubValues] = useState<IgithubOnboarding>({
		companyName: "",
		orgSelected: "",
		orgId: null,
		projectName: "",
		repoLink: "",
		deployLink: "",
	});

	useEffect(() => {
		const fetchRepoData = async () => {
			const res = await fetch("/api/onboarding/get-github-cookie");
			const data = (await res.json()) as string;
			return JSON.parse(data);
		};
		fetchRepoData().then((data) => {
			setGithubValues(data);
		});
	}, []);

	return (
		<CardContent>
			<h2 className='pt-3 text-lg font-semibold tracking-tight leading-none break-words'>
				Start editing your docs
			</h2>
			<div className='flex flex-col gap-4 break-normal '>
				<p className='break-words'>
					Clone{" "}
					<a
						target='_blank'
						href={githubValues.repoLink}
						className='text-blue-500'
					>
						your repository
					</a>{" "}
					and explore our documentation to create your portal.
				</p>
				<p className='break-words'>
					Your docs will be live in five minutes at{" "}
					<a
						target='_blank'
						href={githubValues.deployLink}
						className='text-blue-500 break-words'
					>
						{githubValues.deployLink}
					</a>
					.
				</p>
			</div>
			<Button asChild>
				<Link href='/'>Projects</Link>
			</Button>
		</CardContent>
	);
}
