import { Node, mergeAttributes } from '@tiptap/core';
import { NodeViewWrapper, ReactNodeViewRenderer } from '@tiptap/react';
import React, { useState, useCallback } from 'react';
import { Edit3, Trash2, Save, X } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { RichTextInput } from '@/components/ui/RichTextInput';
import { useToast } from '@/components/ToastProvider';
import type { NodeViewProps } from '@tiptap/react';

// Parameter data interface
interface ParameterData {
	id: string;
	name: string;
	type: string;
	required: boolean;
	default: string;
	description: string;
}

// Parse link format [Label](/path) to extract label and path
const parseLinkType = (
	type: string
): { isLink: boolean; label?: string; path?: string } => {
	const linkMatch = type.match(/^\[([^\]]+)\]\(([^)]+)\)$/);
	if (linkMatch) {
		return {
			isLink: true,
			label: linkMatch[1],
			path: linkMatch[2],
		};
	}
	return { isLink: false };
};

// Format link from label and path
const formatLinkType = (label: string, path: string): string => {
	return `[${label}](${path})`;
};

const ParameterComponent: React.FC<NodeViewProps> = ({
	node,
	updateAttributes,
	selected,
	deleteNode,
}) => {
	const [isEditing, setIsEditing] = useState(false);
	const [isHovered, setIsHovered] = useState(false);
	const { addToast } = useToast();

	const parameter = (node.attrs as { parameter: ParameterData }).parameter || {
		id: Date.now().toString(),
		name: 'Parameter Name',
		type: 'string',
		required: false,
		default: '',
		description: 'Parameter description',
	};

	const [formData, setFormData] = useState<ParameterData>(parameter);

	const handleEdit = useCallback(() => {
		setFormData(parameter);
		setIsEditing(true);
	}, [parameter]);

	const handleSave = useCallback(() => {
		if (!formData.name.trim()) {
			addToast('Parameter name is required', 'warning');
			return;
		}

		if (!formData.type.trim()) {
			addToast('Parameter type is required', 'warning');
			return;
		}

		updateAttributes({
			parameter: { ...formData },
		});
		setIsEditing(false);
		addToast('Parameter updated successfully', 'success');
	}, [formData, updateAttributes, addToast]);

	const handleCancel = useCallback(() => {
		setFormData(parameter);
		setIsEditing(false);
	}, [parameter]);

	const handleDelete = useCallback(() => {
		deleteNode();
		addToast('Parameter deleted', 'info');
	}, [deleteNode, addToast]);

	const handleInputChange = useCallback(
		(field: keyof ParameterData, value: string | boolean) => {
			setFormData((prev) => ({ ...prev, [field]: value }));
		},
		[]
	);

	// Parse type for display
	const typeInfo = parseLinkType(parameter.type);

	if (isEditing) {
		const editingTypeInfo = parseLinkType(formData.type);

		return (
			<NodeViewWrapper
				className='parameter-node w-full border-dashed border-2 border-gray-300 dark:border-gray-600 rounded-lg'
				draggable={false}
				contentEditable={false}
			>
				<div className='parameter-node-container p-4 bg-white dark:bg-gray-50 relative w-full'>
					<div className='border border-gray-200 dark:border-gray-700 rounded-lg p-4 bg-white dark:bg-gray-800 w-full'>
						<div className='flex items-center justify-between mb-4'>
							<h4 className='text-sm font-semibold text-gray-900 dark:text-gray-100'>
								Edit Parameter
							</h4>
							<div className='flex items-center space-x-2'>
								<Button
									variant='ghost'
									size='sm'
									onClick={handleCancel}
									className='h-6 px-2 text-xs hover:bg-red-100/50 dark:hover:bg-red-900/30 text-red-600 dark:text-red-400'
								>
									<X className='w-3 h-3 mr-1' />
									Cancel
								</Button>
								<Button
									variant='default'
									size='sm'
									onClick={handleSave}
									className='h-6 px-2 text-xs bg-blue-600 hover:bg-blue-700 text-white'
								>
									<Save className='w-3 h-3 mr-1' />
									Save
								</Button>
							</div>
						</div>

						<div className='space-y-4'>
							{/* Name Field */}
							<div>
								<label className='block text-xs font-medium text-gray-700 dark:text-gray-300 mb-1'>
									Name *
								</label>
								<input
									type='text'
									value={formData.name}
									onChange={(e) => handleInputChange('name', e.target.value)}
									placeholder='Parameter name'
									className='w-full px-3 py-2 text-sm bg-white dark:bg-slate-700 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:text-white'
									required
								/>
							</div>

							{/* Type Field */}
							<div>
								<label className='block text-xs font-medium text-gray-700 dark:text-gray-300 mb-1'>
									Type *
								</label>
								{editingTypeInfo.isLink ? (
									<div className='space-y-2'>
										<input
											type='text'
											value={editingTypeInfo.label || ''}
											onChange={(e) => {
												const newLabel = e.target.value;
												const path = editingTypeInfo.path || '';
												handleInputChange(
													'type',
													formatLinkType(newLabel, path)
												);
											}}
											placeholder='Link label'
											className='w-full px-3 py-2 text-sm bg-white dark:bg-slate-700 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:text-white'
										/>
										<input
											type='text'
											value={editingTypeInfo.path || ''}
											onChange={(e) => {
												const newPath = e.target.value;
												const label = editingTypeInfo.label || '';
												handleInputChange(
													'type',
													formatLinkType(label, newPath)
												);
											}}
											placeholder='Link path (e.g., /path/to/page)'
											className='w-full px-3 py-2 text-sm bg-white dark:bg-slate-700 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:text-white'
										/>
										<button
											type='button'
											onClick={() => handleInputChange('type', '')}
											className='text-xs text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300'
										>
											Convert to plain text
										</button>
									</div>
								) : (
									<div className='space-y-2'>
										<input
											type='text'
											value={formData.type}
											onChange={(e) =>
												handleInputChange('type', e.target.value)
											}
											placeholder='Parameter type (start with / for links)'
											className='w-full px-3 py-2 text-sm bg-white dark:bg-slate-700 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:text-white'
											required
										/>
										<div className='text-xs text-gray-500 dark:text-gray-400'>
											💡 Tip: Start with &ldquo;/&rdquo; to create a link (e.g.,
											/api/users)
										</div>
										{formData.type.startsWith('/') && (
											<button
												type='button'
												onClick={() => {
													const path = formData.type;
													const label = 'Link Text';
													handleInputChange(
														'type',
														formatLinkType(label, path)
													);
												}}
												className='text-xs text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300'
											>
												Convert to link format
											</button>
										)}
									</div>
								)}
							</div>

							{/* Required Checkbox */}
							<div className='flex items-center space-x-2'>
								<input
									type='checkbox'
									id='required'
									checked={formData.required}
									onChange={(e) =>
										handleInputChange('required', e.target.checked)
									}
									className='w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-gray-600'
								/>
								<label
									htmlFor='required'
									className='text-xs font-medium text-gray-700 dark:text-gray-300'
								>
									Required
								</label>
							</div>

							{/* Default Value */}
							<div>
								<label className='block text-xs font-medium text-gray-700 dark:text-gray-300 mb-1'>
									Default Value
								</label>
								<input
									type='text'
									value={formData.default}
									onChange={(e) => handleInputChange('default', e.target.value)}
									placeholder='Default value (optional)'
									className='w-full px-3 py-2 text-sm bg-white dark:bg-slate-700 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:text-white placeholder:italic'
								/>
							</div>

							{/* Description */}
							<div>
								<label className='block text-xs font-medium text-gray-700 dark:text-gray-300 mb-1'>
									Description
								</label>
								<RichTextInput
									value={formData.description}
									onChange={(html) => handleInputChange('description', html)}
									placeholder='Parameter description'
									variant='default'
									className='w-full'
									enableSteps={false}
									enableLink={false}
								/>
							</div>
						</div>
					</div>
				</div>
			</NodeViewWrapper>
		);
	}

	return (
		<NodeViewWrapper
			className='parameter-node w-full border-dashed border-2 border-gray-300 dark:border-gray-600 rounded-lg hover:border-blue-400/50 dark:hover:border-blue-600/50 transition-colors duration-200'
			draggable={false}
			contentEditable={false}
			data-drag-handle=''
		>
			<div
				className={`parameter-node-container p-4 bg-white dark:bg-gray-50 relative w-full group ${
					selected ? 'ring-2 ring-blue-400/50 rounded-lg' : ''
				}`}
				draggable={false}
				onMouseEnter={() => setIsHovered(true)}
				onMouseLeave={() => setIsHovered(false)}
			>
				{/* Hover Controls */}
				{isHovered && (
					<div className='absolute top-2 right-2 flex items-center space-x-1 transition-all duration-200 z-30 bg-white dark:bg-slate-800 px-2 py-1 rounded-md shadow-lg border border-gray-200 dark:border-gray-600 backdrop-blur-sm'>
						<Button
							variant='ghost'
							size='sm'
							onClick={(e) => {
								e.stopPropagation();
								handleEdit();
							}}
							className='h-6 px-2 text-xs hover:bg-gray-100 dark:hover:bg-slate-700'
							title='Edit'
						>
							<Edit3 className='w-3 h-3' />
							<span className='ml-1'>Edit</span>
						</Button>
						<Button
							variant='ghost'
							size='sm'
							onClick={(e) => {
								e.stopPropagation();
								handleDelete();
							}}
							className='h-6 px-2 text-xs hover:bg-red-100 dark:hover:bg-red-900/30 text-red-600 dark:text-red-400'
							title='Delete'
						>
							<Trash2 className='w-3 h-3' />
							<span className='ml-1'>Delete</span>
						</Button>
					</div>
				)}

				{/* Parameter Display */}
				<div className='space-y-3'>
					{/* Parameter Name and Badges in same row */}
					<div className='flex items-center gap-2 flex-wrap'>
						<h3 className='text-xl font-bold text-gray-900 dark:text-gray-100'>
							{parameter.name}
						</h3>

						{/* Type Badge */}
						{typeInfo.isLink ? (
							<Badge
								variant='outline'
								className='bg-blue-100 text-blue-700 border-blue-300'
							>
								{typeInfo.label}
							</Badge>
						) : (
							<Badge
								variant='outline'
								className='bg-gray-100 text-gray-900 border-gray-300'
							>
								{parameter.type}
							</Badge>
						)}

						{/* Required Badge */}
						{parameter.required && (
							<Badge
								variant='outline'
								className='bg-red-100 text-red-700 border-red-300'
							>
								required
							</Badge>
						)}

						{/* Default Badge */}
						{parameter.default && (
							<Badge
								variant='outline'
								className='bg-gray-100 text-gray-700 border-gray-300'
							>
								default: {parameter.default}
							</Badge>
						)}
					</div>

					{/* Description */}
					{parameter.description && (
						<div
							className='text-sm text-gray-700 dark:text-gray-300 prose prose-sm dark:prose-invert max-w-none'
							dangerouslySetInnerHTML={{ __html: parameter.description }}
						/>
					)}
				</div>
			</div>
		</NodeViewWrapper>
	);
};

export const ParameterNode = Node.create({
	name: 'parameterNode',

	group: 'block',

	atom: true,

	addAttributes() {
		return {
			parameter: {
				default: {
					id: Date.now().toString(),
					name: 'Parameter Name',
					type: 'string',
					required: false,
					default: '',
					description: 'Parameter description',
				},
				parseHTML: (element) => {
					try {
						const parameterData = element.getAttribute('data-parameter');
						if (parameterData) {
							return JSON.parse(parameterData);
						}
						return {
							id: Date.now().toString(),
							name: 'Parameter Name',
							type: 'string',
							required: false,
							default: '',
							description: 'Parameter description',
						};
					} catch (error) {
						console.error('Error parsing parameter data:', error);
						return {
							id: Date.now().toString(),
							name: 'Parameter Name',
							type: 'string',
							required: false,
							default: '',
							description: 'Parameter description',
						};
					}
				},
				renderHTML: (attributes) => {
					return { 'data-parameter': JSON.stringify(attributes.parameter) };
				},
			},
		};
	},

	parseHTML() {
		return [
			{
				tag: 'Parameter',
			},
			{
				tag: 'div[data-type="parameter"]',
			},
		];
	},

	renderHTML({ HTMLAttributes }) {
		const parameter =
			HTMLAttributes.parameter || HTMLAttributes['data-parameter'] || {};
		let parameterData: ParameterData;

		if (typeof parameter === 'string') {
			try {
				parameterData = JSON.parse(parameter);
			} catch {
				parameterData = {
					id: Date.now().toString(),
					name: 'Parameter Name',
					type: 'string',
					required: false,
					default: '',
					description: 'Parameter description',
				};
			}
		} else {
			parameterData = parameter;
		}

		return [
			'div',
			mergeAttributes(HTMLAttributes, {
				'data-type': 'parameter',
				'data-parameter': JSON.stringify(parameterData),
				class: 'parameter-node',
			}),
			['h3', {}, parameterData.name || 'Parameter Name'],
			[
				'p',
				{},
				`Type: ${parameterData.type || 'string'}${parameterData.required ? ' (required)' : ''}${
					parameterData.default ? ` | Default: ${parameterData.default}` : ''
				}`,
			],
			['div', {}, parameterData.description || 'Parameter description'],
		];
	},

	addNodeView() {
		return ReactNodeViewRenderer(ParameterComponent);
	},
});

export default ParameterNode;
