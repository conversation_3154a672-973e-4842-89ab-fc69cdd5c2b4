"use client";

import React, { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { <PERSON><PERSON>, Eye, EyeOff, Code } from "lucide-react";
import { cn } from "@/lib/utils";
import type { ConfigJson } from "@/types/sidebarConfig";

interface ConfigJsonPreviewProps {
  config: ConfigJson | null;
  className?: string;
}

const ConfigJsonPreview: React.FC<ConfigJsonPreviewProps> = ({
  config,
  className,
}) => {
  const [isVisible, setIsVisible] = useState(true);
  const [copied, setCopied] = useState(false);

  const formatJson = (obj: ConfigJson | null): string => {
    if (!obj) return "{}";
    return JSON.stringify(obj, null, 2);
  };

  const handleCopy = async () => {
    if (!config) return;

    try {
      await navigator.clipboard.writeText(formatJson(config));
      setCopied(true);
      setTimeout(() => setCopied(false), 2000);
    } catch (err) {
      console.error("Erro ao copiar:", err);
    }
  };

  const toggleVisibility = () => {
    setIsVisible(!isVisible);
  };

  if (!isVisible) {
    return (
      <div
        className={cn(
          "w-80 border-l border-gray-200 dark:border-gray-700 bg-gray-50 dark:bg-gray-800",
          className
        )}
      >
        <div className="p-4 border-b border-gray-200 dark:border-gray-700">
          <div className="flex items-center justify-between">
            <h3 className="text-sm font-medium text-gray-700 dark:text-gray-200 flex items-center gap-2">
              <Code className="w-4 h-4" />
              ConfigJSON
            </h3>
            <Button
              onClick={toggleVisibility}
              size="sm"
              variant="ghost"
              className="h-8 w-8 p-0"
            >
              <Eye className="w-4 h-4" />
            </Button>
          </div>
        </div>
        <div className="p-4 text-center">
          <p className="text-sm text-gray-500 dark:text-gray-400">
            Preview oculto
          </p>
        </div>
      </div>
    );
  }

  return (
    <div
      className={cn(
        " border-l border-gray-200 dark:border-gray-700 bg-gray-50 dark:bg-gray-800",
        className
      )}
    >
      <div className="p-4 border-b border-gray-200 dark:border-gray-700">
        <div className="flex items-center justify-between mb-2">
          <h3 className="text-sm font-medium text-gray-700 dark:text-gray-200 flex items-center gap-2">
            <Code className="w-4 h-4" />
            ConfigJSON Preview
          </h3>
          <div className="flex items-center gap-1">
            <Button
              onClick={handleCopy}
              size="sm"
              variant="ghost"
              className="h-8 w-8 p-0"
              disabled={!config}
            >
              <Copy className="w-4 h-4" />
            </Button>
            <Button
              onClick={toggleVisibility}
              size="sm"
              variant="ghost"
              className="h-8 w-8 p-0"
            >
              <EyeOff className="w-4 h-4" />
            </Button>
          </div>
        </div>

        {copied && (
          <p className="text-xs text-green-600 dark:text-green-400">
            Copiado para a área de transferência!
          </p>
        )}

        <p className="text-xs text-gray-600 dark:text-gray-400">
          Preview em tempo real das mudanças
        </p>
      </div>

      <div className="flex-1 overflow-y-auto p-4">
        <pre className="text-xs text-gray-800 dark:text-gray-200 whitespace-pre-wrap break-words bg-white dark:bg-gray-900 p-3 rounded-md border border-gray-200 dark:border-gray-600 font-mono">
          {formatJson(config)}
        </pre>
      </div>
    </div>
  );
};

export default ConfigJsonPreview;
