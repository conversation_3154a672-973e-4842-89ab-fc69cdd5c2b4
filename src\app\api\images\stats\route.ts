import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@/utils/supabase/server';

export const runtime = 'edge';

// GET /api/images/stats - Get image statistics for an organization
export async function GET(request: NextRequest) {
	try {
		const { searchParams } = new URL(request.url);
		const organizationId = searchParams.get('organizationId');

		if (!organizationId) {
			return NextResponse.json(
				{ error: 'Organization ID is required' },
				{ status: 400 }
			);
		}

		const supabase = createClient();

		// Get user session for authentication
		const {
			data: { user },
			error: userError,
		} = await supabase.auth.getUser();

		if (userError || !user) {
			return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
		}

		// Use the existing RPC function for organization stats
		const { data: statsData, error: statsError } = await supabase.rpc(
			'get_organization_image_stats',
			{
				p_organization_id: organizationId,
			}
		);

		if (statsError) {
			console.error('Error loading organization stats:', statsError);
			return NextResponse.json(
				{
					error: 'Failed to load statistics',
					details: statsError.message,
				},
				{ status: 500 }
			);
		}

		const stats = statsData?.[0] || {
			total_images: 0,
			total_size_mb: 0,
			images_by_project: [],
			most_used_tags: [],
		};

		return NextResponse.json({
			success: true,
			data: stats,
		});
	} catch (error) {
		console.error('Image stats API error:', error);
		return NextResponse.json(
			{
				success: false,
				error: 'Internal server error',
				details: error instanceof Error ? error.message : 'Unknown error',
			},
			{ status: 500 }
		);
	}
}

// GET /api/images/stats?projectId=X - Get image statistics for a specific project
export async function POST(request: NextRequest) {
	try {
		const body = (await request.json()) as {
			projectId: string;
			tags?: string[];
			searchTerm?: string;
			limit?: number;
			offset?: number;
		};
		const { projectId, tags, searchTerm, limit = 100, offset = 0 } = body;

		if (!projectId) {
			return NextResponse.json(
				{ error: 'Project ID is required' },
				{ status: 400 }
			);
		}

		const supabase = createClient();

		// Get user session for authentication
		const {
			data: { user },
			error: userError,
		} = await supabase.auth.getUser();

		if (userError || !user) {
			return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
		}

		// Use the existing RPC function for project images
		const { data: imagesData, error: imagesError } = await supabase.rpc(
			'get_project_images',
			{
				p_project_id: projectId,
				p_tags: tags && tags.length > 0 ? tags : null,
				p_search_term: searchTerm || null,
				p_limit: limit,
				p_offset: offset,
			}
		);

		if (imagesError) {
			console.error('Error loading project images:', imagesError);
			return NextResponse.json(
				{
					error: 'Failed to load project images',
					details: imagesError.message,
				},
				{ status: 500 }
			);
		}

		// Filter out images that are being deleted (client-side filtering as backup)
		const filteredImages = (imagesData || []).filter(
			(img: { deleting?: boolean }) => !img.deleting
		);

		return NextResponse.json({
			success: true,
			data: filteredImages,
			pagination: {
				offset,
				limit,
				total: filteredImages.length,
			},
		});
	} catch (error) {
		console.error('Project images API error:', error);
		return NextResponse.json(
			{
				success: false,
				error: 'Internal server error',
				details: error instanceof Error ? error.message : 'Unknown error',
			},
			{ status: 500 }
		);
	}
}
