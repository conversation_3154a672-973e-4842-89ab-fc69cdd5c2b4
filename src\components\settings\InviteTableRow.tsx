import React from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { TableCell, TableRow } from "@/components/ui/table";
import {
	DropdownMenu,
	DropdownMenuContent,
	DropdownMenuItem,
	DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { MoreHorizontal, X, Mail, Loader2 } from "lucide-react";
import type { PendingInvite } from "@/types/members";

interface InviteTableRowProps {
	invite: PendingInvite;
	canManage: boolean;
	isProcessingCancel: boolean;
	onCancelInvite: (inviteId: number) => void;
	onResendInvite?: (inviteId: number) => void;
}

export const InviteTableRow: React.FC<InviteTableRowProps> = ({
	invite,
	canManage,
	isProcessingCancel,
	onCancelInvite,
	onResendInvite,
}) => {
	const formatDate = (dateString: string) => {
		const date = new Date(dateString);
		return date.toLocaleDateString("pt-BR", {
			day: "2-digit",
			month: "2-digit",
			year: "numeric",
		});
	};

	return (
		<TableRow className={isProcessingCancel ? "opacity-50" : ""}>
			<TableCell className='font-medium'>
				{invite.email}
				<Badge variant='outline' className='ml-2 text-xs'>
					Invited
				</Badge>
			</TableCell>
			<TableCell>
				<Badge variant='secondary' className='flex items-center gap-1 w-fit'>
					<Mail className='h-3 w-3' />
					Editor
				</Badge>
			</TableCell>
			<TableCell>
				<div className='flex flex-col'>
					<Badge variant='outline' className='text-xs w-fit'>
						Pending
					</Badge>
					<span className='text-xs text-muted-foreground mt-1'>
						Sent {formatDate(invite.created_at)}
					</span>
				</div>
			</TableCell>
			<TableCell>
				{canManage && (
					<DropdownMenu>
						<DropdownMenuTrigger asChild>
							<Button
								variant='ghost'
								className='h-8 w-8 p-0'
								disabled={isProcessingCancel}
							>
								{isProcessingCancel ? (
									<Loader2 className='h-4 w-4 animate-spin' />
								) : (
									<MoreHorizontal className='h-4 w-4' />
								)}
							</Button>
						</DropdownMenuTrigger>
						<DropdownMenuContent align='end'>
							{onResendInvite && (
								<DropdownMenuItem
									onClick={() => onResendInvite(invite.id)}
									disabled={isProcessingCancel}
								>
									<Mail className='h-4 w-4 mr-2' />
									Resend Invite
								</DropdownMenuItem>
							)}
							<DropdownMenuItem
								onClick={() => onCancelInvite(invite.id)}
								className='text-destructive'
								disabled={isProcessingCancel}
							>
								<X className='h-4 w-4 mr-2' />
								Cancel Invite
							</DropdownMenuItem>
						</DropdownMenuContent>
					</DropdownMenu>
				)}
			</TableCell>
		</TableRow>
	);
};
