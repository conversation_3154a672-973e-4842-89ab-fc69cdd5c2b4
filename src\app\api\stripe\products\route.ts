import { NextResponse } from "next/server";
import <PERSON><PERSON> from "stripe";

export const runtime = 'edge';

const stripe = new Stripe(process.env.STRIPE_SECRET_KEY!, {
	apiVersion: "2025-05-28.basil",
});

// Types para melhor tipagem
interface ProcessedPrice {
	id: string;
	unit_amount: number | null;
	currency: string;
	recurring: {
		interval: "month" | "year";
		interval_count: number;
	} | null;
	product: {
		id: string;
		name: string;
		description: string | null;
		active: boolean;
		marketing_features?: Array<{ name?: string }>;
		metadata: Record<string, string>;
		default_price?: string;
	};
	active: boolean;
}

interface ProductDisplayPrices {
	monthly: ProcessedPrice | null;
	yearly: ProcessedPrice | null;
}

interface FilteredProduct {
	id: string;
	name: string;
	description: string | null;
	active: boolean;
	display_prices: ProductDisplayPrices;
	features?: string[] | null;
}

// Function to extract features from metadata
function extractFeaturesFromMetadata(
	metadata: Record<string, string>
): string[] | null {
	const features: string[] = [];

	// Look for feature keys in metadata (feature_1, feature_2, etc.)
	Object.keys(metadata).forEach((key) => {
		if (key.startsWith("feature_") && metadata[key]) {
			features.push(metadata[key]);
		}
	});

	// If no features found in metadata, return null
	return features.length > 0 ? features : null;
}

export async function GET() {
	try {
		if (!process.env.STRIPE_SECRET_KEY) {
			throw new Error("STRIPE_SECRET_KEY environment variable is not set");
		}

		// Fetch all active prices with their products expanded
		const prices = await stripe.prices.list({
			active: true,
			expand: ["data.product"],
			limit: 100,
		});

		// Filter out prices where product is not active and process data
		const activePrices = prices.data.filter((price) => {
			const product = price.product as Stripe.Product;
			return product && product.active && price.active;
		});

		// Group prices by product and process server-side filtering
		const productMap = new Map<string, FilteredProduct>();

		activePrices.forEach((price) => {
			const stripeProduct = price.product as Stripe.Product;
			const productId = stripeProduct.id;

			// Initialize product if not exists
			if (!productMap.has(productId)) {
				// Extract features from marketing_features or metadata
				let features: string[] | null = null;
				if (
					stripeProduct.marketing_features &&
					stripeProduct.marketing_features.length > 0
				) {
					features = stripeProduct.marketing_features
						.map((feature: Stripe.Product.MarketingFeature) => feature.name)
						.filter((name): name is string => typeof name === "string");
				} else {
					features = extractFeaturesFromMetadata(stripeProduct.metadata);
				}

				productMap.set(productId, {
					id: productId,
					name: stripeProduct.name,
					description: stripeProduct.description,
					active: stripeProduct.active,
					display_prices: {
						monthly: null,
						yearly: null,
					},
					features: features,
				});
			}

			const product = productMap.get(productId)!;

			// Process price data - only keep essential fields
			const processedPrice: ProcessedPrice = {
				id: price.id,
				unit_amount: price.unit_amount,
				currency: price.currency,
				recurring: price.recurring
					? {
							interval: price.recurring.interval as "month" | "year",
							interval_count: price.recurring.interval_count,
					  }
					: null,
				product: {
					id: stripeProduct.id,
					name: stripeProduct.name,
					description: stripeProduct.description,
					active: stripeProduct.active,
					marketing_features: stripeProduct.marketing_features,
					metadata: stripeProduct.metadata,
					default_price:
						typeof stripeProduct.default_price === "string"
							? stripeProduct.default_price
							: undefined,
				},
				active: price.active,
			};

			// Determine which display price to set based on interval
			if (price.recurring?.interval === "month") {
				// For monthly, prefer default price or highest amount
				if (
					!product.display_prices.monthly ||
					stripeProduct.default_price === price.id ||
					(price.unit_amount || 0) >
						(product.display_prices.monthly.unit_amount || 0)
				) {
					product.display_prices.monthly = processedPrice;
				}
			} else if (price.recurring?.interval === "year") {
				// For yearly, prefer default price or highest amount
				if (
					!product.display_prices.yearly ||
					stripeProduct.default_price === price.id ||
					(price.unit_amount || 0) >
						(product.display_prices.yearly.unit_amount || 0)
				) {
					product.display_prices.yearly = processedPrice;
				}
			}
		});

		// Filter products that have at least one display price
		const filteredProducts = Array.from(productMap.values()).filter(
			(product) =>
				product.active &&
				(product.display_prices.monthly || product.display_prices.yearly)
		);

		return NextResponse.json(filteredProducts);
	} catch (error) {
		console.error("❌ [PRODUCTS_API] Erro ao buscar produtos do Stripe:", {
			error: error instanceof Error ? error.message : error,
			stack: error instanceof Error ? error.stack : undefined,
			timestamp: new Date().toISOString(),
		});

		const errorMessage =
			error instanceof Error ? error.message : "Unknown error";

		return NextResponse.json(
			{
				error: "Failed to fetch products",
				details: errorMessage,
			},
			{ status: 500 }
		);
	}
}
