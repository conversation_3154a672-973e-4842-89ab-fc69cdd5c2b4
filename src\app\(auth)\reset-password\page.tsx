"use client";

import Link from "next/link";
import { useState, useEffect } from "react";
import { <PERSON>, CardContent, CardHeader } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { cn } from "@/lib/utils";
import { useToast } from "@/components/ToastProvider";
import { sendPasswordResetEmail } from "./actions";
import { InfoIcon } from "lucide-react";

export default function ResetPassword() {
  const [email, setEmail] = useState("");
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [emailSent, setEmailSent] = useState(false);
  const [countdown, setCountdown] = useState(0);
  const { addToast } = useToast();

  // Effect to initialize and manage countdown timer
  useEffect(() => {
    // Check if there's a stored timer
    const storedData = localStorage.getItem("passwordResetTimer");
    if (storedData) {
      const { expiry, userEmail } = JSON.parse(storedData);
      const now = Date.now();

      // If timer is still valid
      if (expiry > now) {
        setEmailSent(true);
        setEmail(userEmail || "");
        setCountdown(Math.ceil((expiry - now) / 1000));
      } else {
        // Clear expired timer
        localStorage.removeItem("passwordResetTimer");
      }
    }
  }, []);

  // Effect to maintain the countdown
  useEffect(() => {
    if (countdown <= 0) return;

    const timer = setInterval(() => {
      setCountdown((prev) => {
        const newValue = prev - 1;
        if (newValue <= 0) {
          // Clear timer when finished
          localStorage.removeItem("passwordResetTimer");
          setEmailSent(false);
          return 0;
        }
        return newValue;
      });
    }, 1000);

    return () => clearInterval(timer);
  }, [countdown]);

  const formatTime = (seconds: number) => {
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    return `${minutes}:${remainingSeconds.toString().padStart(2, "0")}`;
  };

  const handleSubmit = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();

    if (!email || !email.trim()) {
      addToast("Please provide a valid email address.", "error");
      return;
    }

    setIsSubmitting(true);

    try {
      const formData = new FormData();
      formData.append("email", email);

      const result = await sendPasswordResetEmail(formData);

      if (result.success) {
        addToast(
          result.message || "Password reset email sent successfully.",
          "success"
        );

        // Set the timer for 2 minutes (120 seconds)
        const twoMinutesFromNow = Date.now() + 120 * 1000;
        localStorage.setItem(
          "passwordResetTimer",
          JSON.stringify({
            expiry: twoMinutesFromNow,
            userEmail: email,
          })
        );

        setEmailSent(true);
        setCountdown(120);
      } else if (result.error) {
        addToast(result.error.message, "error");
      }
    } catch (error) {
      addToast(
        error instanceof Error
          ? error.message
          : "An error occurred while processing your request.",
        "error"
      );
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div>
      <Card className={cn("py-2 min-h-[300px]")}>
        <CardHeader className="pt-6 pb-4">
          <h2 className="text-lg font-semibold text-primary-text">
            Password Recovery
          </h2>
          <p className="text-sm text-muted-foreground">
            Remembered your password?{" "}
            <Link href="/login" className="text-primary-link hover:underline">
              Back to login
            </Link>
          </p>
        </CardHeader>

        <div className="my-4">
          <div className="border-t border-wd-divider" />
        </div>

        <CardContent className="pt-2 pb-6">
          <form onSubmit={handleSubmit} className="space-y-6">
            {emailSent && (
              <div className="rounded-md bg-blue-50 p-4 mb-4">
                <div className="flex">
                  <div className="flex-shrink-0">
                    <InfoIcon
                      className="h-5 w-5 text-blue-400"
                      aria-hidden="true"
                    />
                  </div>
                  <div className="ml-3">
                    <h3 className="text-sm font-medium text-blue-800">
                      Check your email
                    </h3>
                    <div className="mt-2 text-sm text-blue-700">
                      <p>
                        We sent a password reset link to your email. Please
                        check your inbox (and spam/junk folder).
                      </p>
                    </div>
                  </div>
                </div>
              </div>
            )}

            <div className="space-y-1.5">
              <label
                htmlFor="email"
                className="block text-sm font-medium text-primary-text text-left"
              >
                Email
              </label>
              <Input
                id="email"
                name="email"
                type="email"
                placeholder="<EMAIL>"
                value={email}
                onChange={(e) => setEmail(e.target.value)}
                disabled={isSubmitting || emailSent}
                required
                className={emailSent ? "bg-gray-100" : ""}
              />
            </div>
            <Button
              type="submit"
              className="w-full bg-wd-blue hover:bg-wd-blueDark text-white font-semibold py-2.5 text-sm"
              disabled={isSubmitting || (emailSent && countdown > 0)}
            >
              {isSubmitting
                ? "Sending..."
                : emailSent
                ? `Request again in ${formatTime(countdown)}`
                : "Send Recovery Email"}
            </Button>
          </form>
        </CardContent>
      </Card>
    </div>
  );
}
