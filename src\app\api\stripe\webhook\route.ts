import { NextRequest, NextResponse } from "next/server";
import <PERSON><PERSON> from "stripe";
import { createClient } from "@/utils/supabase/server";
import { headers } from "next/headers";

export const runtime = 'edge';

const stripe = new Stripe(process.env.STRIPE_SECRET_KEY!, {
	apiVersion: "2025-05-28.basil",
});

const endpointSecret = process.env.STRIPE_WEBHOOK_SECRET!;

// Extended interfaces for Stripe objects that have additional properties
interface ExtendedStripeSubscription extends Stripe.Subscription {
	current_period_start: number;
	current_period_end: number;
}

interface ExtendedStripeInvoice extends Stripe.Invoice {
	subscription?: string;
	payment_intent?: string;
}

interface ExtendedStripePaymentIntent extends Stripe.PaymentIntent {
	invoice?: string;
}

export async function POST(request: NextRequest) {
	try {
		if (!process.env.STRIPE_SECRET_KEY || !endpointSecret) {
			console.error("❌ [WEBHOOK] Missing environment variables:", {
				hasStripeKey: !!process.env.STRIPE_SECRET_KEY,
				hasWebhookSecret: !!endpointSecret,
			});
			return NextResponse.json(
				{ error: "Missing required environment variables" },
				{ status: 500 }
			);
		}

		const body = await request.text();
		const headersList = await headers();
		const sig = headersList.get("stripe-signature");

		if (!sig) {
			console.error("❌ [WEBHOOK] Missing Stripe signature");
			return NextResponse.json(
				{ error: "Missing Stripe signature" },
				{ status: 400 }
			);
		}

		let event: Stripe.Event;

		try {
			event = stripe.webhooks.constructEvent(body, sig, endpointSecret);
		} catch (err) {
			console.error("❌ [WEBHOOK] Webhook signature verification failed:", {
				error: err instanceof Error ? err.message : err,
				signature: sig?.substring(0, 20) + "...",
			});
			return NextResponse.json({ error: "Invalid signature" }, { status: 400 });
		}

		// Inicializar Supabase client
		const supabase = await createClient();

		// Process different event types
		switch (event.type) {
			case "customer.created":
			case "customer.updated":
				await handleCustomerEvent(event, supabase);
				break;

			case "customer.subscription.created":
			case "customer.subscription.updated":
			case "customer.subscription.deleted":
				await handleSubscriptionEvent(event, supabase);
				break;

			case "invoice.created":
			case "invoice.updated":
			case "invoice.payment_succeeded":
			case "invoice.payment_failed":
			case "invoice.finalized":
				await handleInvoiceEvent(event, supabase);
				break;

			case "payment_intent.succeeded":
			case "payment_intent.payment_failed":
				await handlePaymentEvent(event, supabase);
				break;

			case "checkout.session.completed":
				await handleCheckoutSessionCompleted(event, supabase);
				break;

			default:
				break;
		}

		return NextResponse.json({ received: true });
	} catch (error) {
		console.error("❌ [WEBHOOK] Error processing webhook:", {
			error: error instanceof Error ? error.message : error,
			stack: error instanceof Error ? error.stack : undefined,
			timestamp: new Date().toISOString(),
		});

		return NextResponse.json(
			{ error: "Internal server error" },
			{ status: 500 }
		);
	}
}

async function handleCustomerEvent(
	event: Stripe.Event,
	supabase: Awaited<ReturnType<typeof createClient>>
) {
	const customer = event.data.object as Stripe.Customer;

	try {
		// Extract project_id from metadata if available
		const projectId = customer.metadata?.project_id;

		const customerData = {
			id: customer.id,
			project_id: projectId ? parseInt(projectId) : null,
			email: customer.email,
			name: customer.name,
			phone: customer.phone,
			address: customer.address ? JSON.stringify(customer.address) : null,
			created: new Date(customer.created * 1000).toISOString(),
			updated: new Date().toISOString(),
		};

		const { error } = await supabase
			.from("stripe_customers")
			.upsert(customerData, {
				onConflict: "id",
			});

		if (error) {
			console.error("❌ [WEBHOOK] Error upserting customer:", error);
			throw error;
		}
	} catch (error) {
		console.error("❌ [WEBHOOK] Error handling customer event:", {
			error: error instanceof Error ? error.message : error,
			customerId: customer.id,
		});
		throw error;
	}
}

async function handleSubscriptionEvent(
	event: Stripe.Event,
	supabase: Awaited<ReturnType<typeof createClient>>
) {
	const subscription = event.data.object as ExtendedStripeSubscription;

	try {
		// Get customer data to find project_id

		const firstItem = subscription.items.data[0];
		const price = firstItem?.price;
		const product = price?.product;

		// Update customer record with subscription info (only update stripe_customers table)
		const customerUpdateData = {
			subscription_id: subscription.id,
			status: subscription.status,
			price_id: price?.id,
			product_id: typeof product === "string" ? product : product?.id,
			current_period_start: new Date(
				subscription.current_period_start * 1000
			).toISOString(),
			current_period_end: new Date(
				subscription.current_period_end * 1000
			).toISOString(),
			cancel_at_period_end: subscription.cancel_at_period_end,
			updated: new Date().toISOString(),
		};

		const { error: customerError } = await supabase
			.from("stripe_customers")
			.update(customerUpdateData)
			.eq("id", subscription.customer);

		if (customerError) {
			console.error("❌ [WEBHOOK] Error updating customer:", customerError);
			throw customerError;
		}
	} catch (error) {
		console.error("❌ [WEBHOOK] Error handling subscription event:", {
			error: error instanceof Error ? error.message : error,
			subscriptionId: subscription.id,
		});
		throw error;
	}
}

async function handleInvoiceEvent(
	event: Stripe.Event,
	supabase: Awaited<ReturnType<typeof createClient>>
) {
	const invoice = event.data.object as ExtendedStripeInvoice;

	try {
		// Get customer data to find project_id
		const customer = await stripe.customers.retrieve(
			invoice.customer as string
		);
		const projectId = (customer as Stripe.Customer).metadata?.project_id;

		const invoiceData = {
			id: invoice.id,
			customer_id: invoice.customer as string,
			project_id: projectId ? parseInt(projectId) : null,
			subscription_id: invoice.subscription || null,
			status: invoice.status!,
			amount_due: invoice.amount_due,
			amount_paid: invoice.amount_paid,
			amount_remaining: invoice.amount_remaining,
			currency: invoice.currency,
			billing_reason: invoice.billing_reason,
			invoice_pdf: invoice.invoice_pdf,
			hosted_invoice_url: invoice.hosted_invoice_url,
			payment_intent_id: (invoice.payment_intent as string) || null,
			invoice_number: invoice.number,
			period_start: invoice.period_start
				? new Date(invoice.period_start * 1000).toISOString()
				: null,
			period_end: invoice.period_end
				? new Date(invoice.period_end * 1000).toISOString()
				: null,
			created: new Date(invoice.created * 1000).toISOString(),
			data: JSON.stringify(invoice),
		};

		const { error } = await supabase
			.from("stripe_invoices")
			.upsert(invoiceData, {
				onConflict: "id",
			});

		if (error) {
			console.error("❌ [WEBHOOK] Error upserting invoice:", error);
			throw error;
		}

		// Try to link any existing payments that might be waiting for this invoice
		if (invoice.payment_intent) {
			const { error: linkError } = await supabase
				.from("stripe_payments")
				.update({ invoice_id: invoice.id })
				.eq("id", invoice.payment_intent)
				.is("invoice_id", null);

			if (linkError) {
				console.error(
					"⚠️ [WEBHOOK] Error linking payment to invoice:",
					linkError
				);
			} else {
			}
		}
	} catch (error) {
		console.error("❌ [WEBHOOK] Error handling invoice event:", {
			error: error instanceof Error ? error.message : error,
			invoiceId: invoice.id,
		});
		throw error;
	}
}

async function handlePaymentEvent(
	event: Stripe.Event,
	supabase: Awaited<ReturnType<typeof createClient>>
) {
	const paymentIntent = event.data.object as ExtendedStripePaymentIntent;

	try {
		// Get customer data to find project_id
		let projectId: string | null = null;
		if (paymentIntent.customer) {
			const customer = await stripe.customers.retrieve(
				paymentIntent.customer as string
			);
			projectId = (customer as Stripe.Customer).metadata?.project_id || null;
		}

		// Get payment method details if available
		interface PaymentMethodData {
			payment_method_id?: string;
			payment_method_type?: string;
			payment_method_brand?: string;
			payment_method_last4?: string;
		}

		const paymentMethodData: PaymentMethodData = {};
		if (paymentIntent.payment_method) {
			try {
				const paymentMethod = await stripe.paymentMethods.retrieve(
					paymentIntent.payment_method as string
				);

				paymentMethodData.payment_method_id = paymentMethod.id;
				paymentMethodData.payment_method_type = paymentMethod.type;

				// Handle different payment method types
				if (paymentMethod.card) {
					paymentMethodData.payment_method_brand = paymentMethod.card.brand;
					paymentMethodData.payment_method_last4 = paymentMethod.card.last4;
				} else if (paymentMethod.us_bank_account) {
					paymentMethodData.payment_method_brand =
						paymentMethod.us_bank_account.bank_name || "bank_account";
					paymentMethodData.payment_method_last4 =
						paymentMethod.us_bank_account.last4 || undefined;
				} else if (paymentMethod.sepa_debit) {
					paymentMethodData.payment_method_brand = "sepa_debit";
					paymentMethodData.payment_method_last4 =
						paymentMethod.sepa_debit.last4 || undefined;
				} else {
					// For other payment types, use the type as brand
					paymentMethodData.payment_method_brand = paymentMethod.type;
				}
			} catch (pmError) {
				console.error("⚠️ [WEBHOOK] Error retrieving payment method:", pmError);
				// Continue without payment method details
			}
		}

		// Get subscription_id from invoice if available
		let subscriptionId: string | null = null;
		if (paymentIntent.invoice) {
			try {
				const invoice = await stripe.invoices.retrieve(paymentIntent.invoice);
				// Access subscription property safely
				const invoiceData = invoice as unknown as { subscription?: string };
				subscriptionId = invoiceData.subscription || null;
			} catch (invoiceError) {
				console.error(
					"❌ [WEBHOOK] Error getting subscription from invoice:",
					invoiceError
				);
			}
		}

		// If no subscription from invoice, try to get from customer's active subscription
		if (!subscriptionId && paymentIntent.customer) {
			try {
				const customer = await stripe.customers.retrieve(
					paymentIntent.customer as string
				);
				if (customer && !customer.deleted) {
					const subscriptions = await stripe.subscriptions.list({
						customer: customer.id,
						status: "active",
						limit: 1,
					});
					if (subscriptions.data.length > 0) {
						subscriptionId = subscriptions.data[0].id;
					}
				}
			} catch (customerError) {
				console.error(
					"❌ [WEBHOOK] Error getting subscription from customer:",
					customerError
				);
			}
		}

		// Check if invoice exists before creating payment record
		const invoiceId = paymentIntent.invoice || null;
		if (invoiceId) {
			// Remove the strict check - let the foreign key constraint handle it
			// If the invoice doesn't exist yet, it might be processed later
		}

		const paymentData = {
			id: paymentIntent.id,
			customer_id: paymentIntent.customer as string,
			project_id: projectId ? parseInt(projectId) : null,
			invoice_id: invoiceId,
			subscription_id: subscriptionId,
			amount: paymentIntent.amount,
			currency: paymentIntent.currency,
			status: paymentIntent.status,
			...paymentMethodData,
			error_code: paymentIntent.last_payment_error?.code || null,
			error_message: paymentIntent.last_payment_error?.message || null,
			error_type: paymentIntent.last_payment_error?.type || null,
			created: new Date(paymentIntent.created * 1000).toISOString(),
			updated: new Date().toISOString(),
		};

		const { error } = await supabase
			.from("stripe_payments")
			.upsert(paymentData, {
				onConflict: "id",
			});

		if (error) {
			// If foreign key constraint fails because invoice doesn't exist yet,
			// try to save without invoice_id
			if (error.code === "23503" && error.message?.includes("invoice_id")) {
				const paymentDataWithoutInvoice = {
					...paymentData,
					invoice_id: null,
				};

				const { error: retryError } = await supabase
					.from("stripe_payments")
					.upsert(paymentDataWithoutInvoice, {
						onConflict: "id",
					});

				if (retryError) {
					console.error(
						"❌ [WEBHOOK] Error upserting payment (retry):",
						retryError
					);
					throw retryError;
				}

				return;
			}

			console.error("❌ [WEBHOOK] Error upserting payment:", error);
			throw error;
		}
	} catch (error) {
		console.error("❌ [WEBHOOK] Error handling payment event:", {
			error: error instanceof Error ? error.message : error,
			paymentIntentId: paymentIntent.id,
		});
		throw error;
	}
}

async function handleCheckoutSessionCompleted(
	event: Stripe.Event,
	supabase: Awaited<ReturnType<typeof createClient>>
) {
	const session = event.data.object as Stripe.Checkout.Session;

	try {
		// This event is useful for tracking successful checkouts
		// Most of the actual data sync will be handled by subscription and payment events
		// But we can use this to update any checkout-specific metadata or tracking

		const projectId = session.metadata?.project_id;

		if (projectId && session.customer && session.subscription) {
			// Update customer with project association if not already set
			const { error } = await supabase
				.from("stripe_customers")
				.update({
					project_id: parseInt(projectId),
					updated: new Date().toISOString(),
				})
				.eq("id", session.customer)
				.is("project_id", null); // Only update if project_id is null

			if (error) {
				console.error("❌ [WEBHOOK] Error updating customer project:", error);
			} else {
			}
		}
	} catch (error) {
		console.error("❌ [WEBHOOK] Error handling checkout session:", {
			error: error instanceof Error ? error.message : error,
			sessionId: session.id,
		});
		throw error;
	}
}
