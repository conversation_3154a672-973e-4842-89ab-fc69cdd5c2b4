import { NextRequest, NextResponse } from "next/server";
import { createServerClient } from "@supabase/ssr";
import { createAdminClient } from "@/utils/supabase/admin";
import { cookies } from "next/headers";
import { Resend } from "resend";
import { createClient as createServiceClient } from "@supabase/supabase-js";

const resend = new Resend(process.env.RESEND_API_KEY);

// Cliente Supabase com service role para operações administrativas
const supabaseAdmin = createServiceClient(
	process.env.NEXT_PUBLIC_SUPABASE_URL!,
	process.env.NEXT_SUPABASE_SERVICE_ROLE_KEY!
);

export const runtime = "edge";

export async function POST(request: NextRequest) {
	try {
		const body = (await request.json()) as {
			email?: string;
			projectId?: string;
			role?: string;
		};
		const { email, projectId, role = "Editor" } = body;

		if (!email || !projectId) {
			return NextResponse.json(
				{ error: "Email and project ID are required" },
				{ status: 400 }
			);
		}

		// Convert projectId to number since it's stored as integer in database
		const projectIdNumber = parseInt(projectId, 10);
		if (isNaN(projectIdNumber)) {
			return NextResponse.json(
				{ error: "Invalid project ID format" },
				{ status: 400 }
			);
		}

		// Validate email format
		const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
		if (!emailRegex.test(email)) {
			return NextResponse.json(
				{ error: "Invalid email format" },
				{ status: 400 }
			);
		}

		// Validate role
		const validRoles = ["Owner", "Admin", "Editor"];
		if (!validRoles.includes(role)) {
			return NextResponse.json({ error: "Invalid role" }, { status: 400 });
		}

		// Authenticate the user with regular client
		const cookieStore = await cookies();
		const supabase = createServerClient(
			process.env.NEXT_PUBLIC_SUPABASE_URL!,
			process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
			{
				cookies: {
					get(name: string) {
						return cookieStore.get(name)?.value;
					},
				},
			}
		);

		// Get current user
		const {
			data: { user },
			error: userError,
		} = await supabase.auth.getUser();

		if (userError || !user) {
			return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
		}

		// Use admin client for data operations
		const adminClient = await createAdminClient();

		// Check if current user has permission to invite (Owner or Admin)
		const { data: userProject, error: userProjectError } = await adminClient
			.from("project_user")
			.select("role")
			.eq("project_id", projectIdNumber)
			.eq("user_id", user.id)
			.single();

		if (userProjectError || !userProject) {
			return NextResponse.json({ error: "Forbidden" }, { status: 403 });
		}

		if (!["Owner", "Admin"].includes(userProject.role)) {
			return NextResponse.json(
				{ error: "Only owners and admins can invite members" },
				{ status: 403 }
			);
		}

		// Additional permission check for role assignment
		if (userProject.role === "Admin" && role === "Owner") {
			return NextResponse.json(
				{ error: "Admins cannot invite owners" },
				{ status: 403 }
			);
		}

		// Check if user is already a member
		const { data: existingMember } = await adminClient
			.from("project_user")
			.select("user_id")
			.eq("project_id", projectIdNumber)
			.eq("email", email)
			.single();

		if (existingMember) {
			return NextResponse.json(
				{ error: "User is already a member of this project" },
				{ status: 400 }
			);
		}

		// Check if there's already a pending invite
		const { data: existingInvite } = await adminClient
			.from("project_invites")
			.select("id")
			.eq("project_id", projectIdNumber)
			.eq("email", email)
			.eq("status", "invited")
			.single();

		if (existingInvite) {
			return NextResponse.json(
				{ error: "User already has a pending invite" },
				{ status: 400 }
			);
		}

		// Get project information for email
		const { data: projectData } = await adminClient
			.from("projects")
			.select("project_name")
			.eq("id", projectIdNumber)
			.single();

		if (!projectData) {
			return NextResponse.json({ error: "Project not found" }, { status: 404 });
		}

		// Create the invite
		const { data: invite, error: inviteError } = await adminClient
			.from("project_invites")
			.insert({
				project_id: projectIdNumber,
				email: email,
				status: "invited",
			})
			.select()
			.single();

		if (inviteError) {
			console.error("Error creating invite:", inviteError);
			return NextResponse.json(
				{ error: "Failed to create invite" },
				{ status: 500 }
			);
		}

		// Check if user already has account in Supabase using admin client
		const { data: authUser } = await supabaseAdmin.auth.admin.listUsers({
			page: 1,
			perPage: 10000,
		});
		const existingAuthUser = authUser?.users.find(
			(user) => user.email === email
		);

		// Generate invite link - redirect to specific project dashboard
		const inviteUrl = existingAuthUser
			? `${process.env.SITE_URL}/invite?token=${invite.id}&redirect=dashboard&projectId=${projectIdNumber}`
			: `${process.env.SITE_URL}/signup?redirect=/invite?token=${invite.id}&redirect=dashboard&projectId=${projectIdNumber}`;

		// Send email using Resend with the same template from backup
		try {
			await resend.emails.send({
				from: "WriteDocs <<EMAIL>>",
				to: email,
				subject: `Invitation to join ${projectData.project_name}`,
				html: `
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Project Invitation</title>
    <style>
        body {
            font-family: Arial, 'Helvetica Neue', Helvetica, sans-serif;
            background-color: #f8fafc;
            margin: 0;
            padding: 40px 20px;
            color: #1e293b;
            line-height: 1.6;
            -webkit-text-size-adjust: 100%;
            -ms-text-size-adjust: 100%;
        }
        table {
            width: 100%;
            max-width: 600px;
            margin: 0 auto;
            background-color: #ffffff;
            border-spacing: 0;
            mso-table-lspace: 0pt;
            mso-table-rspace: 0pt;
            border: 1px solid #e2e8f0;
        }
        .main-table {
            border-collapse: separate;
            border-spacing: 0;
            border-radius: 8px;
            overflow: hidden;
            border: 1px solid #e2e8f0;
        }
        td {
            padding: 24px;
            text-align: center;
        }
        .header {
            background-color: #ffffff;
            border-bottom: 1px solid #e2e8f0;
            padding: 24px;
            text-align: center;
        }
        .logo {
            width: 220px;
            height: auto;
            margin: 24px auto;
            display: block;
            border: 0;
            -ms-interpolation-mode: bicubic;
        }
        .logo-container {
            text-align: center;
            font-size: 0;
        }
        .welcome-icon {
            width: 64px;
            height: 64px;
            margin: 16px 0;
            color: #0029f5;
        }
        h1 {
            font-size: 32px;
            font-weight: bold;
            color: #0f172a;
            margin: 16px 0;
            line-height: 1.2;
        }
        h2 {
            font-size: 24px;
            font-weight: bold;
            color: #0f172a;
            margin: 12px 0;
            line-height: 1.2;
        }
        p {
            color: #334155;
            font-size: 16px;
            line-height: 1.7;
            margin: 12px 0;
        }
        .next-steps {
            background-color: #f8fafc;
            border: 1px solid #e2e8f0;
            padding: 24px;
            margin: 24px;
            text-align: left;
            border-radius: 8px;
            -webkit-border-radius: 8px;
            -moz-border-radius: 8px;
        }
        .next-steps table {
            border: none;
            background-color: transparent;
            width: 100%;
            margin: 0 auto;
        }
        .step-number {
            width: 24px;
            height: 24px;
            line-height: 24px;
            background-color: #0029f5;
            color: #ffffff;
            border-radius: 50%;
            font-size: 14px;
            text-align: center;
            display: inline-block;
            font-weight: bold;
        }
        .footer {
            font-size: 13px;
            color: #64748b;
            border-top: 1px solid #e2e8f0;
            padding-top: 24px;
            margin-top: 24px;
            background-color: #f8fafc;
        }
        .social-links {
            margin: 16px 0;
        }
        .social-link {
            display: inline-block;
            margin: 0 8px;
            color: #0029f5;
            text-decoration: none;
            font-weight: 500;
            padding: 8px 16px;
            border-radius: 6px;
            -webkit-border-radius: 6px;
            -moz-border-radius: 6px;
        }
        .action-button {
            display: inline-block;
            background-color: #0029f5;
            color: #ffffff;
            padding: ${existingAuthUser ? "16px 48px" : "16px 32px"};
            border-radius: 6px;
            -webkit-border-radius: 6px;
            -moz-border-radius: 6px;
            text-decoration: none;
            font-weight: bold;
            font-size: ${existingAuthUser ? "18px" : "16px"};
            margin: ${existingAuthUser ? "32px 0" : "24px 0"};
            border: 1px solid #0024db;
            mso-padding-alt: ${existingAuthUser ? "16px 48px" : "16px 32px"};
            text-align: center;
        }
        strong {
            color: #0f172a;
            font-weight: bold;
        }

        /* Outlook-specific fixes */
        * {
            -webkit-text-size-adjust: 100%;
            -ms-text-size-adjust: 100%;
        }
        table, td {
            mso-table-lspace: 0pt !important;
            mso-table-rspace: 0pt !important;
        }
        img {
            -ms-interpolation-mode: bicubic;
        }
    </style>
</head>
<body>
    <table cellpadding="0" cellspacing="0" border="0" class="main-table">
        <tr>
            <td class="header">
                <div class="logo-container">
                    <img class="logo" src="https://ionebgognajfpbggxiid.supabase.co/storage/v1/object/public/Writedocs/Logo%20-%20WD.png?t=2025-01-14T12%3A14%3A55.957Z" alt="Writedocs Logo">
                </div>
            </td>
        </tr>
        <tr>
            <td>
                <svg class="welcome-icon" viewBox="0 0 24 24" fill="currentColor">
                    <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm0 18c-4.41 0-8-3.59-8-8s3.59-8 8-8 8 3.59 8 8-3.59 8-8 8zm4.59-12.42L10 14.17l-2.59-2.58L6 13l4 4 8-8z"/>
                </svg>
                <h1>You've Been Invited! 🎉</h1>
                <p>You have received an invitation to join <strong>${projectData.project_name}</strong>.</p>
                
                ${
									!existingAuthUser
										? `
                <div class="next-steps">
                    <h2>Next Steps:</h2>
                    <table>
                        <tr>
                            <td style="width: 30px; vertical-align: top; padding: 6px;">
                                <div class="step-number">1</div>
                            </td>
                            <td style="text-align: left; padding: 6px;">
                                Create your WriteDocs account
                            </td>
                        </tr>
                        <tr>
                            <td style="width: 30px; vertical-align: top; padding: 6px;">
                                <div class="step-number">2</div>
                            </td>
                            <td style="text-align: left; padding: 6px;">
                                Start collaborating on the project
                            </td>
                        </tr>
                    </table>
                </div>
                `
										: ""
								}

                <a href="${inviteUrl}" class="action-button" style="color: #0029f5; text-decoration: none;">
                    <span style="color: #ffffff; text-decoration: none;">
                        ${existingAuthUser ? "Accept Invitation" : "Create Account & Access"}
                    </span>
                </a>

                <div class="social-links">
                    <a href="https://docs.writedocs.io/" class="social-link">Documentation</a>
                </div>
            </td>
        </tr>
        <tr>
            <td class="footer">
                <p>If you weren't expecting this invitation, please ignore this email.</p>
                <p>&copy; 2025 WriteDocs. All rights reserved.</p>
            </td>
        </tr>
    </table>
</body>
</html>
				`,
			});
			console.log("Email sent successfully to:", email);
		} catch (emailError) {
			console.error("Error sending email:", emailError);
			// Don't fail the whole request if email fails, but log it
		}

		return NextResponse.json({
			message: "Invite sent successfully",
			invite: {
				...invite,
				intended_role: role,
			},
		});
	} catch (error) {
		console.error("Error inviting member:", error);
		return NextResponse.json(
			{ error: "Failed to send invite" },
			{ status: 500 }
		);
	}
}
