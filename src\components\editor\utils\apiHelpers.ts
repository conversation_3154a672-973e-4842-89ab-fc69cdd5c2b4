import { createClient } from "@/utils/supabase/client";
import {
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  DocusaurusPageItem,
  DocusaurusPageGroup,
} from "@/types/sidebarConfig";

interface ApiEndpoint {
  path: string;
  method: string;
  summary?: string;
  description?: string;
  operationId?: string;
}

interface ProjectPageInsert {
  project_id: number;
  path: string;
  content: string;
  title: string;
  is_api_file: boolean;
  api_file_source?: string;
  metadata?: {
    method: string;
    endpoint: string;
    operationId?: string;
  };
}

/**
 * Lists all API files from storage for a specific project
 */
async function listProjectApiFiles(
  projectId: string,
  organizationId?: string
): Promise<string[]> {
  console.log(
    `📁 Listing API files for project ${projectId}${
      organizationId ? ` (org: ${organizationId})` : ""
    }`
  );

  try {
    const supabase = createClient();

    // List files in the project folder
    const { data: files, error } = await supabase.storage
      .from("api-files")
      .list(projectId, {
        limit: 100,
        sortBy: { column: "created_at", order: "desc" },
      });

    if (error) {
      console.error("Error listing API files from storage:", error);
      return [];
    }

    if (!files || files.length === 0) {
      console.log("No API files found in storage for project:", projectId);
      return [];
    }

    // Filter out placeholder files and generate public URLs
    const apiFileUrls: string[] = [];
    for (const file of files) {
      // Skip placeholder files created by Supabase Storage
      if (
        file.name === ".emptyFolderPlaceholder" ||
        file.name.startsWith(".")
      ) {
        console.log("🚫 Skipping placeholder file:", file.name);
        continue;
      }

      const filePath = `${projectId}/${file.name}`;
      const { data: urlData } = supabase.storage
        .from("api-files")
        .getPublicUrl(filePath);

      if (urlData?.publicUrl) {
        apiFileUrls.push(urlData.publicUrl);
        console.log("📁 Found API file:", urlData.publicUrl);
      }
    }

    console.log(
      `✅ Found ${apiFileUrls.length} API files in storage (filtered out ${
        files.length - apiFileUrls.length
      } placeholder files)`
    );
    return apiFileUrls;
  } catch (error) {
    console.error("Error accessing API files storage:", error);
    return [];
  }
}

/**
 * Gets pages generated by a specific API file
 */
export async function getPagesByApiFile(
  projectId: string,
  apiFileUrl: string
): Promise<string[]> {
  try {
    const supabase = createClient();

    const { data: pages, error } = await supabase
      .from("project_pages")
      .select("path")
      .eq("project_id", parseInt(projectId, 10))
      .eq("api_file_source", apiFileUrl);

    if (error) {
      console.error("Error fetching pages by API file:", error);
      return [];
    }

    return pages?.map((page) => page.path) || [];
  } catch (error) {
    console.error("Error getting pages by API file:", error);
    return [];
  }
}

/**
 * Removes pages from the config.json sidebar structure
 */
export async function removePagePathsFromConfig(
  projectId: string,
  pagePaths: string[]
): Promise<void> {
  if (pagePaths.length === 0) return;

  try {
    const supabase = createClient();

    // Get current config
    const { data: projectData, error: projectError } = await supabase
      .from("projects")
      .select("configjson")
      .eq("id", parseInt(projectId, 10))
      .single();

    if (projectError) {
      console.error("Error fetching project config:", projectError);
      return;
    }

    const configJson = (projectData.configjson as ConfigJson) || {};

    if (!configJson.sidebars || configJson.sidebars.length === 0) {
      console.log("No sidebars found in config.json");
      return;
    }

    console.log(
      `🧹 Removing ${pagePaths.length} page paths from config.json sidebar structure`
    );
    console.log("Paths to remove:", pagePaths);

    let removedCount = 0;

    // Function to recursively remove paths from a page structure
    const removePathsFromPages = (
      pages: DocusaurusPageItem[]
    ): DocusaurusPageItem[] => {
      return pages.filter((pageItem) => {
        if (typeof pageItem === "string") {
          // It's a direct page path
          if (pagePaths.includes(pageItem)) {
            console.log(`🗑️ Removing page path: ${pageItem}`);
            removedCount++;
            return false; // Remove this page
          }
          return true; // Keep this page
        } else {
          // It's a page group
          const group = pageItem as DocusaurusPageGroup;

          // Check if the group's main page should be removed
          if (group.page && pagePaths.includes(group.page)) {
            console.log(`🗑️ Removing group page: ${group.page}`);
            removedCount++;
            group.page = undefined; // Remove the group's main page
          }

          // Recursively clean subpages
          if (group.subpages && group.subpages.length > 0) {
            group.subpages = removePathsFromPages(group.subpages);
          }

          // Keep the group if it still has subpages or a page
          return group.subpages.length > 0 || group.page;
        }
      });
    };

    // Process each sidebar
    for (const sidebar of configJson.sidebars) {
      if (!sidebar.categories) continue;

      for (const category of sidebar.categories) {
        if (!category.pages) continue;

        const originalLength = category.pages.length;
        category.pages = removePathsFromPages(category.pages);

        console.log(
          `📊 Category "${category.categoryName}": ${originalLength} -> ${category.pages.length} pages`
        );
      }

      // Remove empty categories
      sidebar.categories = sidebar.categories.filter(
        (category) => category.pages && category.pages.length > 0
      );
    }

    // Remove empty sidebars
    configJson.sidebars = configJson.sidebars.filter(
      (sidebar) => sidebar.categories && sidebar.categories.length > 0
    );

    console.log(`✅ Removed ${removedCount} page paths from sidebar structure`);

    // Save updated config
    const { error: updateError } = await supabase
      .from("projects")
      .update({ configjson: configJson })
      .eq("id", parseInt(projectId, 10));

    if (updateError) {
      console.error(
        "Error updating config.json after removing pages:",
        updateError
      );
      throw updateError;
    }

    console.log("✅ Config.json updated successfully after removing pages");
  } catch (error) {
    console.error("Error removing page paths from config:", error);
    throw error;
  }
}

/**
 * Deletes all pages generated by a specific API file
 */
export async function deletePagesByApiFile(
  projectId: string,
  apiFileUrl: string
): Promise<{ deletedCount: number; errors: string[] }> {
  try {
    const supabase = createClient();

    // First, get all pages that will be deleted for logging and config cleanup
    const pagesToDelete = await getPagesByApiFile(projectId, apiFileUrl);
    console.log(
      `🗑️ Deleting ${pagesToDelete.length} pages generated by API file: ${apiFileUrl}`
    );

    if (pagesToDelete.length === 0) {
      return { deletedCount: 0, errors: [] };
    }

    // Remove pages from config.json sidebar structure BEFORE deleting from database
    try {
      await removePagePathsFromConfig(projectId, pagesToDelete);
      console.log(
        "✅ Successfully removed pages from config.json sidebar structure"
      );
    } catch (configError) {
      console.error(
        "⚠️ Error removing pages from config.json (continuing with database deletion):",
        configError
      );
      // Continue with database deletion even if config update fails
    }

    // Delete all pages generated by this API file from database
    const { error, count } = await supabase
      .from("project_pages")
      .delete({ count: "exact" })
      .eq("project_id", parseInt(projectId, 10))
      .eq("api_file_source", apiFileUrl);

    if (error) {
      console.error("Error deleting pages by API file:", error);
      return { deletedCount: 0, errors: [error.message] };
    }

    console.log(`✅ Successfully deleted ${count || 0} pages from database`);
    return { deletedCount: count || 0, errors: [] };
  } catch (error) {
    console.error("Error in deletePagesByApiFile:", error);
    return {
      deletedCount: 0,
      errors: [error instanceof Error ? error.message : "Unknown error"],
    };
  }
}

/**
 * Creates API files in the database and updates the config.json
 */
export async function createApiFiles(
  projectId: string,
  endpoints: ApiEndpoint[],
  baseFolder: string = "api",
  organizationId?: string,
  apiFileUrl?: string
) {
  const supabase = createClient();

  // Get current config
  const { data: projectData, error: projectError } = await supabase
    .from("projects")
    .select("configjson")
    .eq("id", projectId)
    .single();

  if (projectError) throw projectError;

  const configJson = (projectData.configjson as ConfigJson) || {};

  // Ensure basic structure exists
  if (!configJson.sidebars) {
    configJson.sidebars = [];
  }
  if (!configJson.navbar) {
    configJson.navbar = [];
  }

  const createdPages = [];
  const skippedPages = [];
  const errorPages = [];
  const pathStructure = new Map<string, Set<string>>();

  // Check for existing pages to avoid duplicates
  console.log("🔍 Checking for existing pages to avoid duplicates...");
  const existingPaths = new Set<string>();

  try {
    const { data: existingPages, error: fetchError } = await supabase
      .from("project_pages")
      .select("path")
      .eq("project_id", parseInt(projectId, 10));

    if (fetchError) {
      console.warn(
        "⚠️ Could not fetch existing pages, proceeding without duplicate check:",
        fetchError
      );
    } else if (existingPages) {
      existingPages.forEach((page) => existingPaths.add(page.path));
      console.log(`📋 Found ${existingPaths.size} existing pages in project`);
    }
  } catch (error) {
    console.warn("⚠️ Error checking existing pages:", error);
  }

  // Process each endpoint
  for (const endpoint of endpoints) {
    const sanitizedPath = sanitizeApiPath(endpoint.path);
    const fileName = sanitizeFileName(
      endpoint.operationId ||
        endpoint.summary ||
        `${endpoint.method}-${endpoint.path}`
    );

    // Create the full path for the API file
    const fullPath = `${baseFolder}${sanitizedPath}/${fileName}`;

    console.log(`📝 Processing endpoint: ${endpoint.method} ${endpoint.path}`);
    console.log(`   Sanitized path: ${sanitizedPath}`);
    console.log(`   File name: ${fileName}`);
    console.log(`   Full path: ${fullPath}`);
    if (apiFileUrl) {
      console.log(`   API file source: ${apiFileUrl}`);
    }

    // Check if page already exists
    if (existingPaths.has(fullPath)) {
      console.log(`⚠️ Page already exists, skipping: ${fullPath}`);
      skippedPages.push({
        path: fullPath,
        endpoint: `${endpoint.method} ${endpoint.path}`,
        reason: "Page already exists",
      });
      continue;
    }

    // Generate content
    const content = generateEndpointContent(
      endpoint.method,
      endpoint.path,
      endpoint
    );

    // Insert into database
    try {
      const insertData: ProjectPageInsert = {
        project_id: parseInt(projectId, 10),
        path: fullPath,
        content: content,
        title: endpoint.summary || `${endpoint.method} ${endpoint.path}`,
        is_api_file: true,
      };

      // Add API file source if provided
      if (apiFileUrl) {
        insertData.api_file_source = apiFileUrl;
      }

      const { error: insertError } = await supabase
        .from("project_pages")
        .insert(insertData);

      if (insertError) {
        console.error(
          `❌ Error creating API page for ${endpoint.method} ${endpoint.path}:`,
          {
            error: insertError,
            path: fullPath,
            endpoint: endpoint,
            insertData: insertData,
          }
        );

        // Check if it's a duplicate key error
        if (
          insertError.code === "23505" ||
          insertError.message?.includes("duplicate") ||
          insertError.message?.includes("unique")
        ) {
          console.log(
            `🔄 Duplicate path detected: ${fullPath}, attempting to update existing page...`
          );

          // Try to update the existing page instead
          const updateData: Partial<ProjectPageInsert> = {
            content: content,
            title: insertData.title,
            is_api_file: true,
            metadata: insertData.metadata,
          };

          // Only update api_file_source if provided
          if (apiFileUrl) {
            updateData.api_file_source = apiFileUrl;
          }

          const { error: updateError } = await supabase
            .from("project_pages")
            .update(updateData)
            .eq("project_id", parseInt(projectId, 10))
            .eq("path", fullPath);

          if (updateError) {
            console.error(
              `❌ Error updating existing page: ${fullPath}`,
              updateError
            );
            errorPages.push({
              path: fullPath,
              endpoint: `${endpoint.method} ${endpoint.path}`,
              error: updateError.message || "Failed to update existing page",
            });
          } else {
            console.log(`✅ Successfully updated existing page: ${fullPath}`);
            createdPages.push(fullPath);
          }
        } else {
          // Other type of error
          errorPages.push({
            path: fullPath,
            endpoint: `${endpoint.method} ${endpoint.path}`,
            error: insertError.message || "Unknown database error",
          });
        }
        continue;
      }

      console.log(`✅ Successfully created page: ${fullPath}`);
      createdPages.push(fullPath);
      existingPaths.add(fullPath); // Add to our local set to prevent duplicates in this batch

      // Track path structure for config.json
      const pathParts = fullPath.split("/");
      console.log(`🔨 Building path structure for: ${fullPath}`);
      console.log(`   Path parts: [${pathParts.join(", ")}]`);

      let currentPath = "";
      for (let i = 0; i < pathParts.length - 1; i++) {
        currentPath = pathParts.slice(0, i + 1).join("/");
        console.log(`   Level ${i}: ${currentPath}`);

        if (!pathStructure.has(currentPath)) {
          pathStructure.set(currentPath, new Set());
          console.log(`   ➕ Created new level: ${currentPath}`);
        }

        if (i < pathParts.length - 2) {
          const childPath = pathParts.slice(0, i + 2).join("/");
          pathStructure.get(currentPath)!.add(childPath);
          console.log(
            `   ➕ Added child ${childPath} to parent ${currentPath}`
          );
        } else {
          // It's a file
          pathStructure.get(currentPath)!.add(fullPath);
          console.log(`   ➕ Added file ${fullPath} to parent ${currentPath}`);
        }
      }
    } catch (error) {
      console.error(
        `❌ Unexpected error processing endpoint ${endpoint.method} ${endpoint.path}:`,
        error
      );
      errorPages.push({
        path: fullPath,
        endpoint: `${endpoint.method} ${endpoint.path}`,
        error: error instanceof Error ? error.message : "Unexpected error",
      });
    }
  }

  // Log summary
  console.log(`📊 Processing Summary:`);
  console.log(`   ✅ Created/Updated: ${createdPages.length} pages`);
  console.log(`   ⚠️ Skipped: ${skippedPages.length} pages`);
  console.log(`   ❌ Errors: ${errorPages.length} pages`);

  if (skippedPages.length > 0) {
    console.log(`📋 Skipped pages:`, skippedPages);
  }

  if (errorPages.length > 0) {
    console.error(`📋 Error pages:`, errorPages);
  }

  // Update config.json with the new structure and API files
  console.log("📋 Created/Updated pages:", createdPages);
  console.log("🗺️ Complete path structure:", pathStructure);

  // Always update the sidebar structure if there are created pages
  if (createdPages.length > 0) {
    updateConfigJsonWithApiFiles(configJson, pathStructure, baseFolder);
  }

  // ALWAYS update API files list from storage, regardless of page creation results
  console.log("📁 Loading API files from storage...");
  const apiFiles = await listProjectApiFiles(projectId, organizationId);
  if (apiFiles.length > 0) {
    // Convert URLs to string array (compatible with ConfigJson type)
    configJson.apiFiles = apiFiles;
    console.log("✅ Updated apiFiles in config.json:", apiFiles);
  } else {
    // If no files found, clear the apiFiles array
    configJson.apiFiles = [];
    console.log(
      "🗑️ Cleared apiFiles in config.json (no files found in storage)"
    );
  }

  // Save updated config (always save to update apiFiles)
  console.log("💾 Saving updated config.json...");
  const { error: updateError } = await supabase
    .from("projects")
    .update({ configjson: configJson })
    .eq("id", parseInt(projectId, 10));

  if (updateError) {
    console.error("❌ Error saving config.json:", updateError);
    throw updateError;
  }

  console.log("✅ Config.json saved successfully!");

  // Return detailed results
  const results = {
    createdPages,
    skippedPages,
    errorPages,
    totalProcessed: endpoints.length,
    successCount: createdPages.length,
    skipCount: skippedPages.length,
    errorCount: errorPages.length,
  };

  console.log(`✅ Final results:`, results);
  return results;
}

/**
 * Sanitizes API path for file system
 */
function sanitizeApiPath(path: string): string {
  const parts = path.split("/").filter(part => part);
  const category = parts[0] ? sanitizeFileName(parts[0]) : '';
  return category ? `/${category}` : '';
}

/**
 * Sanitizes file name
 */
function sanitizeFileName(name: string): string {
  return name
    .toLowerCase()
    .replace(/[^a-z0-9_\-\[\]\{\}}]/g, "-") // Allow brackets and braces for path parameters
    .replace(/-+/g, "-")
    .replace(/^-|-$/g, "");
}

/**
 * Generates endpoint content
 */
function generateEndpointContent(
  method: string,
  pathUrl: string,
  endpoint: ApiEndpoint
): string {
  const title =
    endpoint.summary || endpoint.operationId || `${method} ${pathUrl}`;

  return `---
title: ${title}
route: ${method} ${pathUrl}
---

{/* Don't change the file name or the metadata in this file */}
${endpoint.description || `{/* You can add a description here */}`}
`;
}

/**
 * Updates config.json with API file structure
 */
function updateConfigJsonWithApiFiles(
  configJson: ConfigJson,
  pathStructure: Map<string, Set<string>>,
  baseFolder: string
) {
  console.log("🔄 Updating config.json with API files...");
  console.log("📁 Path structure:", pathStructure);

  // Find or create API navbar item
  let apiNavbarItem = configJson.navbar?.find(
    (item) => item.label === baseFolder
  );

  let sidebarRef: string;

  if (!apiNavbarItem) {
    // Create new navbar item with new sidebarRef
    const randomId = Math.random().toString(36).substring(2, 15);
    sidebarRef = baseFolder + "-" + randomId;

    apiNavbarItem = {
      label: baseFolder,
      sidebarRef: sidebarRef,
    };
    configJson.navbar!.push(apiNavbarItem);
    console.log("➕ Created new navbar item for API");
  } else {
    // Use existing sidebarRef
    sidebarRef = apiNavbarItem.sidebarRef!;
    console.log(`♻️ Using existing navbar item with sidebarRef: ${sidebarRef}`);
  }

  // Find or create API sidebar using the determined sidebarRef
  let apiSidebar = configJson.sidebars?.find(
    (sidebar) => sidebar.sidebarRef === sidebarRef
  );

  if (!apiSidebar) {
    apiSidebar = {
      sidebarRef: sidebarRef,
      categories: [],
    };
    configJson.sidebars!.push(apiSidebar);
    console.log("➕ Created new sidebar for API");
  } else {
    console.log(`♻️ Using existing sidebar with sidebarRef: ${sidebarRef}`);
  }

  // Rebuild categories
  apiSidebar.categories = [];

  function buildPageStructure(currentPath: string): DocusaurusPageItem[] {
    const children: string[] = Array.from(pathStructure.get(currentPath) || new Set<string>()).sort();
    const pages: DocusaurusPageItem[] = [];

    for (const child of children) {
      const isDir = pathStructure.has(child);
      const name = child.split('/').pop()!;

      if (isDir) {
        const subpages = buildPageStructure(child);
        if (subpages.length > 0) {
          pages.push({
            groupName: name,
            subpages,
          });
        }
      } else {
        pages.push(child);
      }
    }
    return pages;
  }

  const rootChildrenSet: Set<string> = pathStructure.get(baseFolder) || new Set<string>();

  // Handle direct files under baseFolder
  const directFiles: string[] = Array.from(rootChildrenSet).filter(child => !pathStructure.has(child)).sort();
  if (directFiles.length > 0) {
    apiSidebar.categories.push({
      categoryName: "General",
      pages: directFiles,
    });
  }

  // Handle subdirectories
  const subDirs: string[] = Array.from(rootChildrenSet).filter(child => pathStructure.has(child)).sort();
  for (const subDir of subDirs) {
    const categoryName = subDir.replace(`${baseFolder}/`, '');
    const pages = buildPageStructure(subDir);
    if (pages.length > 0) {
      apiSidebar.categories.push({
        categoryName,
        pages,
      });
    }
  }

  console.log("✅ Config.json updated with API files");
  console.log(
    "📋 Final sidebars:",
    JSON.stringify(configJson.sidebars, null, 2)
  );
}
