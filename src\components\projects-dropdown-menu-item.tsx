'use client';

import { FolderGit2 } from 'lucide-react';

import { SidebarMenuButton, SidebarMenuItem } from '@/components/ui/sidebar';
import { useProject } from '@/contexts/ProjectContext/ProjectContextProvider';
import React, { useState, useEffect } from 'react';
import Link from 'next/link';

export function ProjectsDropdownMenuItem() {
	const { projects, isLoadingProjects, hasInvites } = useProject();

	// Estado local para garantir consistência entre servidor e cliente
	const [isDisabled, setIsDisabled] = useState(() => {
		// Inicializa com o mesmo valor que será usado no cliente
		return isLoadingProjects || projects.length === 0;
	});

	// Atualiza o estado disabled apenas no cliente após hidratação
	useEffect(() => {
		setIsDisabled(isLoadingProjects || projects.length === 0);
	}, [isLoadingProjects, projects.length]);

	return (
		<SidebarMenuItem>
			<SidebarMenuButton
				className='w-full group cursor-pointer focus-visible:ring-0 focus:ring-transparent'
				size='lg'
				disabled={isDisabled}
			>
				<Link
					href='/'
					className='flex flex-col items-center justify-center text-center flex-grow h-fit py-1 relative'
				>
					<div className='relative'>
						<FolderGit2
							size={20}
							className='transition-colors group-data-[state=expanded]:h-5 group-data-[state=expanded]:w-5 h-4 w-4'
							strokeWidth={2}
						/>
						{hasInvites && (
							<span className='absolute -top-1 -right-1 h-2 w-2 rounded-full bg-red-600 animate-pulse' />
						)}
					</div>
					<span className='text-xs transition-colors group-data-[state=collapsed]:hidden'>
						Projects
					</span>
				</Link>
			</SidebarMenuButton>
		</SidebarMenuItem>
	);
}
