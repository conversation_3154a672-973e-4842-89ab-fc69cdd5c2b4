# RichTextInput Component

Um componente de input de texto rico reutilizável baseado no TipTap/ProseMirror, projetado para ser usado em qualquer lugar do projeto onde você precisa de edição de texto formatado.

## Características

- 🎨 **3 Variantes**: Default, Compact e Minimal
- ⌨️ **Toolbar Flutuante**: Aparece ao focar no campo
- 📊 **Contador de Caracteres**: Com limites personalizáveis
- 🎯 **Formatação Rica**: Negrito, itálico, código, listas, etc.
- 🌙 **Suporte a Dark Mode**: Estilos adaptativos
- ♿ **Acessibilidade**: ARIA labels e navegação por teclado
- 📱 **Responsivo**: Toolbar adapta em dispositivos móveis
- 🚨 **Estados de Erro**: Validação visual integrada

## Instalação

```tsx
import { RichTextInput } from "@/components/ui";
// ou
import { RichTextInput } from "@/components/ui/RichTextInput";
```

## Uso Básico

```tsx
import { useState } from "react";
import { RichTextInput } from "@/components/ui";

function MyComponent() {
  const [content, setContent] = useState("<p>Texto inicial</p>");

  return (
    <RichTextInput
      value={content}
      onChange={setContent}
      placeholder="Digite algo..."
      maxLength={200}
    />
  );
}
```

## Variantes

### Default (Padrão)

Editor completo com todas as funcionalidades.

```tsx
<RichTextInput
  value={content}
  onChange={setContent}
  variant="default"
  showToolbar={true}
  placeholder="Editor completo..."
/>
```

### Compact (Compacta)

Versão menor, ideal para cards e espaços reduzidos.

```tsx
<RichTextInput
  value={content}
  onChange={setContent}
  variant="compact"
  maxLength={200}
  placeholder="Descrição breve..."
/>
```

### Minimal (Mínima)

Uma linha com formatação básica.

```tsx
<RichTextInput
  value={title}
  onChange={setTitle}
  variant="minimal"
  maxLength={100}
  placeholder="Título..."
/>
```

## Props

| Prop          | Tipo                                  | Padrão             | Descrição                       |
| ------------- | ------------------------------------- | ------------------ | ------------------------------- |
| `value`       | `string`                              | `""`               | Conteúdo HTML do editor         |
| `onChange`    | `(value: string) => void`             | -                  | Callback quando o conteúdo muda |
| `variant`     | `"default" \| "compact" \| "minimal"` | `"default"`        | Variante visual do editor       |
| `placeholder` | `string`                              | `"Digite aqui..."` | Texto placeholder               |
| `maxLength`   | `number`                              | -                  | Limite máximo de caracteres     |
| `disabled`    | `boolean`                             | `false`            | Desabilita o editor             |
| `showToolbar` | `boolean`                             | `true`             | Mostra/esconde a toolbar        |
| `showCounter` | `boolean`                             | `true`             | Mostra/esconde o contador       |
| `autoFocus`   | `boolean`                             | `false`            | Foco automático ao carregar     |
| `error`       | `boolean`                             | `false`            | Estado de erro visual           |
| `required`    | `boolean`                             | `false`            | Campo obrigatório               |
| `helperText`  | `string`                              | -                  | Texto de ajuda abaixo do campo  |
| `className`   | `string`                              | -                  | Classes CSS adicionais          |
| `id`          | `string`                              | -                  | ID do elemento                  |
| `name`        | `string`                              | -                  | Nome do campo                   |

## Formatação Suportada

### Variante Default

- **Texto**: Negrito, itálico, riscado, código inline
- **Listas**: Com marcadores e numeradas
- **Cabeçalhos**: H1, H2, H3
- **Outros**: Blockquotes, linhas horizontais, blocos de código

### Variante Compact

- **Texto**: Negrito, itálico, riscado, código inline
- **Listas**: Com marcadores e numeradas

### Variante Minimal

- **Texto**: Negrito, itálico, código inline
- **Limitação**: Mantém em uma linha

## Atalhos de Teclado

- `Ctrl/Cmd + B` - Negrito
- `Ctrl/Cmd + I` - Itálico
- `Ctrl/Cmd + Shift + X` - Riscado
- `Ctrl/Cmd + E` - Código inline
- `Enter` - Nova linha (exceto na variante minimal)
- `Shift + Enter` - Quebra de linha

## Exemplos de Uso

### Em Formulários

```tsx
<form onSubmit={handleSubmit}>
  <div className="space-y-2">
    <label htmlFor="description">Descrição</label>
    <RichTextInput
      id="description"
      name="description"
      value={formData.description}
      onChange={(value) =>
        setFormData((prev) => ({ ...prev, description: value }))
      }
      required={true}
      maxLength={500}
      error={!!errors.description}
      helperText={errors.description || "Descreva o produto em detalhes"}
    />
  </div>
  <button type="submit">Salvar</button>
</form>
```

### Com Validação

```tsx
const [content, setContent] = useState("");
const [error, setError] = useState("");

const handleChange = (value: string) => {
  setContent(value);

  // Validação básica
  const textContent = value.replace(/<[^>]*>/g, "").trim();
  if (textContent.length < 10) {
    setError("Mínimo de 10 caracteres");
  } else {
    setError("");
  }
};

return (
  <RichTextInput
    value={content}
    onChange={handleChange}
    error={!!error}
    helperText={error || "Mínimo de 10 caracteres"}
    maxLength={200}
  />
);
```

### Para Cards/Componentes

```tsx
// Ideal para descrições em cards
<RichTextInput
  value={card.description}
  onChange={(desc) => updateCard({ ...card, description: desc })}
  variant="compact"
  maxLength={150}
  placeholder="Breve descrição do card..."
  showCounter={true}
/>
```

### Campo de Título

```tsx
// Para títulos com formatação simples
<RichTextInput
  value={post.title}
  onChange={(title) => setPost({ ...post, title })}
  variant="minimal"
  maxLength={80}
  placeholder="Título do post..."
  showToolbar={true}
/>
```

## Personalização

### CSS Classes

O componente usa classes CSS que podem ser personalizadas:

```css
/* Container principal */
.rich-text-input-container {
}

/* Área do editor */
.rich-text-input-content .ProseMirror {
}

/* Toolbar */
.rich-text-input-toolbar {
}

/* Botões da toolbar */
.toolbar-button {
}

/* Contador de caracteres */
.rich-text-input-counter {
}

/* Texto de ajuda */
.rich-text-input-helper {
}
```

### Estilos para Dark Mode

Todos os estilos incluem suporte automático ao dark mode usando classes `.dark`:

```css
.dark .rich-text-input-content .ProseMirror {
  background: #374151;
  color: white;
}
```

## Acessibilidade

- ✅ Labels ARIA apropriadas
- ✅ Navegação por teclado
- ✅ Foco visível
- ✅ Descrições assistivas
- ✅ Estados de erro anunciados
- ✅ Suporte a screen readers

## Performance

- ⚡ Renderização otimizada com `forwardRef`
- 🔄 Updates controlados para evitar re-renders desnecessários
- 💾 Memória gerenciada automaticamente
- 📦 Bundle size otimizado

## Troubleshooting

### Conteúdo não atualiza

Certifique-se de que está passando o valor correto para `value` e implementando `onChange`:

```tsx
// ❌ Errado
<RichTextInput value={content} />

// ✅ Correto
<RichTextInput value={content} onChange={setContent} />
```

### Formatação perdida

O componente trabalha com HTML. Se você estiver salvando como texto puro, a formatação será perdida:

```tsx
// ❌ Salva apenas texto
const text = editor.getText();

// ✅ Salva HTML com formatação
const html = editor.getHTML();
```

### Toolbar não aparece

A toolbar aparece apenas quando o campo está em foco. Certifique-se de que `showToolbar={true}`:

```tsx
<RichTextInput
  value={content}
  onChange={setContent}
  showToolbar={true} // ✅ Explicitamente habilitado
/>
```
