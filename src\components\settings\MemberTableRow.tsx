import React from "react";
import { useUser } from "@/contexts/UserContext/UserContextProvider";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { TableCell, TableRow } from "@/components/ui/table";
import {
	DropdownMenu,
	DropdownMenuContent,
	DropdownMenuItem,
	DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import {
	MoreHorizontal,
	Crown,
	Shield,
	Edit,
	LogOut,
	UserX,
	ChevronUp,
	Loader2,
} from "lucide-react";
import type { ProjectMember, MemberRole } from "@/types/members";

interface MemberTableRowProps {
	member: ProjectMember;
	canManage: boolean;
	roleActions: Array<{ label: string; role: MemberRole }>;
	isProcessingRemoval: boolean;
	isProcessingRoleChange: boolean;
	onUpdateRole: (userId: string, role: MemberRole) => void;
	onRemoveMember: (userId: string) => void;
	onLeaveProject: () => void;
}

const getRoleIcon = (role: MemberRole) => {
	switch (role) {
		case "Owner":
			return <Crown className='h-4 w-4' />;
		case "Admin":
			return <Shield className='h-4 w-4' />;
		case "Editor":
			return <Edit className='h-4 w-4' />;
		default:
			return null;
	}
};

const getRoleBadgeVariant = (role: MemberRole) => {
	switch (role) {
		case "Owner":
			return "default" as const;
		case "Admin":
			return "secondary" as const;
		case "Editor":
			return "outline" as const;
		default:
			return "outline" as const;
	}
};

const getRoleActionIcon = (label: string) => {
	if (label.includes("Owner")) return <Crown className='h-4 w-4 mr-2' />;
	if (label.includes("Admin")) return <Shield className='h-4 w-4 mr-2' />;
	if (label.includes("Editor")) return <Edit className='h-4 w-4 mr-2' />;
	if (label.includes("Promote")) return <ChevronUp className='h-4 w-4 mr-2' />;
	return <Edit className='h-4 w-4 mr-2' />;
};

export const MemberTableRow: React.FC<MemberTableRowProps> = ({
	member,
	canManage,
	roleActions,
	isProcessingRemoval,
	isProcessingRoleChange,
	onUpdateRole,
	onRemoveMember,
	onLeaveProject,
}) => {
	const { user } = useUser();
	const isCurrentUser = member.user_id === user?.id;

	return (
		<TableRow className={isProcessingRemoval ? "opacity-50" : ""}>
			<TableCell className='font-medium'>
				{member.email}
				{member.is_creator && (
					<Badge variant='outline' className='ml-2 text-xs'>
						Creator
					</Badge>
				)}
				{isCurrentUser && (
					<Badge variant='outline' className='ml-2 text-xs'>
						You
					</Badge>
				)}
			</TableCell>
			<TableCell>
				<Badge
					variant={getRoleBadgeVariant(member.role)}
					className={`flex items-center gap-1 w-fit ${
						isProcessingRoleChange ? "opacity-70" : ""
					}`}
				>
					{getRoleIcon(member.role)}
					{member.role}
					{isProcessingRoleChange && (
						<Loader2 className='ml-1 h-3 w-3 animate-spin' />
					)}
				</Badge>
			</TableCell>
			<TableCell>
				<Badge variant='outline'>Active</Badge>
			</TableCell>
			<TableCell>
				{(isCurrentUser || canManage) && (
					<DropdownMenu>
						<DropdownMenuTrigger asChild>
							<Button
								variant='ghost'
								className='h-8 w-8 p-0'
								disabled={isProcessingRemoval || isProcessingRoleChange}
							>
								<MoreHorizontal className='h-4 w-4' />
							</Button>
						</DropdownMenuTrigger>
						<DropdownMenuContent align='end'>
							{isCurrentUser ? (
								<DropdownMenuItem
									onClick={onLeaveProject}
									className='text-orange-600'
									disabled={isProcessingRemoval}
								>
									<LogOut className='h-4 w-4 mr-2' />
									Leave Project
								</DropdownMenuItem>
							) : (
								<>
									{roleActions.map((action) => (
										<DropdownMenuItem
											key={action.role}
											onClick={() => onUpdateRole(member.user_id, action.role)}
											disabled={isProcessingRoleChange}
										>
											{getRoleActionIcon(action.label)}
											{action.label}
										</DropdownMenuItem>
									))}
									{roleActions.length > 0 && <div className='border-t my-1' />}
									<DropdownMenuItem
										onClick={() => onRemoveMember(member.user_id)}
										className='text-destructive'
										disabled={isProcessingRemoval}
									>
										<UserX className='h-4 w-4 mr-2' />
										Remove Member
									</DropdownMenuItem>
								</>
							)}
						</DropdownMenuContent>
					</DropdownMenu>
				)}
			</TableCell>
		</TableRow>
	);
};
