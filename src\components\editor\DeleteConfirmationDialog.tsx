'use client';

import React from 'react';
import { AlertTriangle, Trash2, X } from 'lucide-react';
import { type OrganizationImage } from '@/hooks/useOrganizationImages';

interface DeleteConfirmationDialogProps {
	isOpen: boolean;
	onClose: () => void;
	onConfirm: () => void;
	images: OrganizationImage[];
	isDeleting?: boolean;
}

export const DeleteConfirmationDialog: React.FC<
	DeleteConfirmationDialogProps
> = ({ isOpen, onClose, onConfirm, images, isDeleting = false }) => {
	if (!isOpen) return null;

	const isSingleImage = images.length === 1;
	const imageCount = images.length;

	return (
		<div className='fixed inset-0 z-50 flex items-center justify-center'>
			{/* Backdrop */}
			<div
				className='absolute inset-0 bg-black/50 backdrop-blur-sm'
				onClick={onClose}
			/>

			{/* Dialog */}
			<div className='relative bg-white dark:bg-gray-800 rounded-lg shadow-xl max-w-md w-full mx-4 p-6'>
				{/* Header */}
				<div className='flex items-center justify-between mb-4'>
					<div className='flex items-center space-x-3'>
						<div className='flex-shrink-0 w-10 h-10 bg-red-100 dark:bg-red-900/20 rounded-full flex items-center justify-center'>
							<AlertTriangle className='w-5 h-5 text-red-600 dark:text-red-400' />
						</div>
						<h3 className='text-lg font-semibold text-gray-900 dark:text-white'>
							{isSingleImage ? 'Delete Image' : 'Delete Images'}
						</h3>
					</div>
					<button
						onClick={onClose}
						disabled={isDeleting}
						className='text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 transition-colors disabled:opacity-50'
					>
						<X className='w-5 h-5' />
					</button>
				</div>

				{/* Content */}
				<div className='mb-6'>
					<p className='text-gray-600 dark:text-gray-300 mb-4'>
						{isSingleImage
							? 'Are you sure you want to delete this image? This action cannot be undone.'
							: `Are you sure you want to delete ${imageCount} images? This action cannot be undone.`}
					</p>

					{/* Image details */}
					{isSingleImage ? (
						<div className='bg-gray-50 dark:bg-gray-700 rounded-lg p-3'>
							<div className='flex items-center space-x-3'>
								<div className='w-12 h-12 bg-gray-200 dark:bg-gray-600 rounded-lg overflow-hidden flex-shrink-0'>
									<img
										src={images[0].image_url}
										alt={images[0].alt_text || images[0].image_name}
										className='w-full h-full object-cover'
										onError={(e) => {
											const target = e.target as HTMLImageElement;
											target.src =
												"data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='48' height='48' viewBox='0 0 48 48'%3E%3Crect width='48' height='48' fill='%23f3f4f6'/%3E%3Ctext x='24' y='24' text-anchor='middle' dy='.3em' fill='%236b7280' font-size='10'%3EImage%3C/text%3E%3C/svg%3E";
										}}
									/>
								</div>
								<div className='flex-1 min-w-0'>
									<p className='text-sm font-medium text-gray-900 dark:text-white truncate'>
										{images[0].image_name}
									</p>
									{images[0].alt_text && (
										<p className='text-xs text-gray-500 dark:text-gray-400 truncate'>
											{images[0].alt_text}
										</p>
									)}
								</div>
							</div>
						</div>
					) : (
						<div className='bg-gray-50 dark:bg-gray-700 rounded-lg p-3'>
							<div className='flex items-center space-x-3'>
								<div className='w-12 h-12 bg-red-100 dark:bg-red-900/20 rounded-lg flex items-center justify-center flex-shrink-0'>
									<Trash2 className='w-6 h-6 text-red-600 dark:text-red-400' />
								</div>
								<div className='flex-1'>
									<p className='text-sm font-medium text-gray-900 dark:text-white'>
										{imageCount} images selected
									</p>
									<p className='text-xs text-gray-500 dark:text-gray-400'>
										All selected images will be permanently deleted
									</p>
								</div>
							</div>
						</div>
					)}
				</div>

				{/* Actions */}
				<div className='flex space-x-3'>
					<button
						onClick={onClose}
						disabled={isDeleting}
						className='flex-1 px-4 py-2 text-sm font-medium text-gray-700 dark:text-gray-300 bg-gray-100 dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-lg hover:bg-gray-200 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-gray-500 transition-colors disabled:opacity-50 disabled:cursor-not-allowed'
					>
						Cancel
					</button>
					<button
						onClick={onConfirm}
						disabled={isDeleting}
						className='flex-1 px-4 py-2 text-sm font-medium text-white bg-red-600 border border-transparent rounded-lg hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-red-500 transition-colors disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center space-x-2'
					>
						{isDeleting ? (
							<>
								<div className='w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin' />
								<span>Deleting...</span>
							</>
						) : (
							<>
								<Trash2 className='w-4 h-4' />
								<span>
									{isSingleImage
										? 'Delete Image'
										: `Delete ${imageCount} Images`}
								</span>
							</>
						)}
					</button>
				</div>
			</div>
		</div>
	);
};
