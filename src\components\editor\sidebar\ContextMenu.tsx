"use client";

import React, { useState, useRef } from "react";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Button } from "@/components/ui/button";
import { FilePlus, Edit3, Trash2, Folder, Menu, Minus, Home } from "lucide-react";
import type { TreeNode } from "./types";
import { validateFolderName } from "@/utils/fileTreeUtils";

interface ContextMenuProps {
  node: TreeNode;
  children: React.ReactNode;
  onCreateItem: (
    parentNode: TreeNode,
    type: "category" | "group" | "page" | "navbar-item" | "navbar-dropdown",
    name: string,
    path?: string
  ) => void;
  onRenameItem: (node: TreeNode, newName: string) => void;
  onDeleteItem: (node: TreeNode) => void;
  onSetAsHomepage?: (node: TreeNode) => void;
  editingNodeId?: string | null;
  setEditingNodeId?: (id: string | null) => void;
  isHomepage?: boolean;
}

interface CreateDialogState {
  isOpen: boolean;
  type:
    | "category"
    | "group"
    | "page"
    | "navbar-item"
    | "navbar-dropdown"
    | null;
  title: string;
  description: string;
}

interface RenameDialogState {
  isOpen: boolean;
  currentName: string;
}

interface DeleteDialogState {
  isOpen: boolean;
}

const ContextMenu: React.FC<ContextMenuProps> = ({
  node,
  children,
  onCreateItem,
  onRenameItem,
  onDeleteItem,
  onSetAsHomepage,
  // editingNodeId,
  setEditingNodeId,
  isHomepage = false,
}) => {
  const [isContextMenuOpen, setIsContextMenuOpen] = useState(false);
  const [menuPosition, setMenuPosition] = useState({ x: 0, y: 0 });
  const triggerRef = useRef<HTMLDivElement>(null);

  const [createDialog, setCreateDialog] = useState<CreateDialogState>({
    isOpen: false,
    type: null,
    title: "",
    description: "",
  });

  const [renameDialog, setRenameDialog] = useState<RenameDialogState>({
    isOpen: false,
    currentName: "",
  });

  const [deleteDialog, setDeleteDialog] = useState<DeleteDialogState>({
    isOpen: false,
  });

  const [createName, setCreateName] = useState("");
  const [createPath, setCreatePath] = useState("");
  const [renameName, setRenameName] = useState("");
  const [createValidationError, setCreateValidationError] = useState<string | null>(null);
  const [renameValidationError, setRenameValidationError] = useState<string | null>(null);

  // Determina se o nó pode ser deletado
  const canBeDeleted = () => {
    // The virtual root node cannot be deleted
    if (node.data?.type === "root") {
      return false;
    }
    // All other items can be deleted
    return true;
  };

  // Determina se o nó pode ser renomeado
  const canBeRenamed = () => {
    // The virtual root node cannot be renamed
    if (node.data?.type === "root") {
      return false;
    }
    // All other items can be renamed
    return true;
  };

  // Determina que tipos podem ser criados baseado no contexto
  const getAvailableCreateTypes = () => {
    const isRoot = node.data?.type === "root"; // It is the root ONLY if it is the virtual root node
    const isNavbarDropdown =
      node.type === "navbar" && node.data?.type === "navbar-dropdown";
    const isNavbarItem =
      node.type === "navbar" &&
      (node.data?.type === "navbar-item" ||
        node.data?.type === "navbar-dropdown-item");

    if (isRoot) {
      // At the root, you can create a navbar dropdown and a navbar item
      return ["navbar-dropdown", "navbar-item"];
    } else if (isNavbarDropdown) {
      // A navbar dropdown can ONLY create a navbar item (not another dropdown)
      return ["navbar-item"];
    } else if (isNavbarItem) {
      // A navbar item can only create a category (page removed)
      return ["category"];
    } else if (node.type === "category") {
      // A category can create a page and a group
      return ["page", "group"];
    } else if (node.type === "group") {
      // A group can create a page and a group
      return ["page", "group"];
    }

    return [];
  };

  // Handler for right-click
  const handleContextMenu = (e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation(); // Prevents the event from bubbling up to parent elements

    setMenuPosition({ x: e.clientX, y: e.clientY });
    setIsContextMenuOpen(true);
  };

  const handleCreateClick = (
    type: "category" | "group" | "page" | "navbar-item" | "navbar-dropdown"
  ) => {
    const titles = {
      category: "Create New Category",
      group: "Create New Group",
      page: "Create New Page",
      "navbar-item": "Create Navigation Item",
      "navbar-dropdown": "Create Navigation Dropdown",
    };

    const descriptions = {
      category: "Create a new category to organize your pages.",
      group: "Create a new group of related pages.",
      page: "Create a new documentation page.",
      "navbar-item": "Create a new navigation item.",
      "navbar-dropdown": "Create a new navigation dropdown.",
    };

    setCreateDialog({
      isOpen: true,
      type,
      title: titles[type],
      description: descriptions[type],
    });
    setCreateName("");
    setCreatePath("");
    setCreateValidationError(null);
    setIsContextMenuOpen(false);
  };

  const handleCreateConfirm = () => {
    if (!createDialog.type || !createName.trim()) return;

    // Validar nome para categorias, grupos e navbar items
    if (createDialog.type === "category" || createDialog.type === "group" || createDialog.type === "navbar-item" || createDialog.type === "navbar-dropdown") {
      const validation = validateFolderName(createName);
      if (!validation.isValid) {
        setCreateValidationError(validation.error || "Invalid name");
        return;
      }
    }

    // For pages, only pass a custom path if the user specified one
    // Otherwise, leave it as undefined so that ProjectPagesSidebar can build
    // the path based on the folder hierarchy
    let finalPath: string | undefined = undefined;

    if (createDialog.type === "page") {
      // Only use a custom path if the user typed something
      if (createPath.trim()) {
        finalPath = createPath.trim();
      }
      // If nothing is typed, leave it as undefined to use automatic hierarchy
    }

    onCreateItem(node, createDialog.type, createName.trim(), finalPath);

    // Reset
    setCreateDialog({ isOpen: false, type: null, title: "", description: "" });
    setCreateName("");
    setCreatePath("");
    setCreateValidationError(null);
  };

  const handleRenameClick = () => {
    setIsContextMenuOpen(false);
    if (setEditingNodeId) {
      setEditingNodeId(node.id);
    } else {
      // Fallback to the dialog if the props are not available
      setRenameDialog({
        isOpen: true,
        currentName: node.name,
      });
      setRenameName(node.name);
      setRenameValidationError(null);
    }
  };

  const handleRenameConfirm = () => {
    if (!renameName.trim() || renameName.trim() === node.name) {
      setRenameDialog({ isOpen: false, currentName: "" });
      return;
    }

    // Validar nome para categorias, grupos e navbar items
    if (node.type === "category" || node.type === "group" || node.type === "navbar") {
      const validation = validateFolderName(renameName);
      if (!validation.isValid) {
        setRenameValidationError(validation.error || "Invalid name");
        return;
      }
    }

    onRenameItem(node, renameName.trim());

    // Reset
    setRenameDialog({ isOpen: false, currentName: "" });
    setRenameName("");
    setRenameValidationError(null);
  };

  const handleDeleteClick = () => {
    setIsContextMenuOpen(false);
    setDeleteDialog({ isOpen: true });
  };

  const handleDeleteConfirm = () => {
    onDeleteItem(node);
    setDeleteDialog({ isOpen: false });
  };

  const handleSetAsHomepage = () => {
    setIsContextMenuOpen(false);
    if (onSetAsHomepage) {
      onSetAsHomepage(node);
    }
  };

  // Check if this node can be set as homepage (only pages)
  const canBeSetAsHomepage = () => {
    return (node.type === "page" || node.type === "subpage") && node.path && !isHomepage;
  };

  return (
    <>
      <div ref={triggerRef} onContextMenu={handleContextMenu}>
        {children}
      </div>

      {/* Context Menu using DropdownMenu */}
      <DropdownMenu
        open={isContextMenuOpen}
        onOpenChange={setIsContextMenuOpen}
      >
        <DropdownMenuTrigger asChild>
          <div
            style={{
              position: "fixed",
              left: menuPosition.x,
              top: menuPosition.y,
              width: 1,
              height: 1,
              pointerEvents: "none",
            }}
          />
        </DropdownMenuTrigger>
        <DropdownMenuContent
          className="w-52 bg-white/95 dark:bg-slate-800/95 backdrop-blur-md border border-slate-200/70 dark:border-slate-700/70 rounded-xl shadow-xl p-2"
          onContextMenu={(e) => {
            e.preventDefault();
            e.stopPropagation();
          }}
        >
          {(() => {
            const availableTypes = getAvailableCreateTypes();

            if (availableTypes.length === 0) {
              return null;
            }

            return (
              <>
                {/* Render navbar options if available */}
                {availableTypes.includes("navbar-dropdown") && (
                  <DropdownMenuItem
                    onClick={() => handleCreateClick("navbar-dropdown")}
                    className="flex items-center h-8 px-3 py-1 rounded-lg hover:bg-blue-50/80 dark:hover:bg-blue-900/30 transition-all duration-200 cursor-pointer group"
                    onContextMenu={(e) => {
                      e.preventDefault();
                      e.stopPropagation();
                    }}
                  >
                    <div className="w-6 h-6 rounded-md bg-blue-100 dark:bg-blue-900/40 flex items-center justify-center mr-3 group-hover:bg-blue-200 dark:group-hover:bg-blue-800/60 transition-colors">
                      <Menu className="w-3.5 h-3.5 text-blue-600 dark:text-blue-400" />
                    </div>
                    <span className="text-sm font-medium text-slate-700 dark:text-slate-300">
                      Navbar Dropdown
                    </span>
                  </DropdownMenuItem>
                )}

                {availableTypes.includes("navbar-item") && (
                  <DropdownMenuItem
                    onClick={() => handleCreateClick("navbar-item")}
                    className="flex items-center h-8 px-3 py-1 rounded-lg hover:bg-blue-50/80 dark:hover:bg-blue-900/30 transition-all duration-200 cursor-pointer group"
                    onContextMenu={(e) => {
                      e.preventDefault();
                      e.stopPropagation();
                    }}
                  >
                    <div className="w-6 h-6 rounded-md bg-blue-100 dark:bg-blue-900/40 flex items-center justify-center mr-3 group-hover:bg-blue-200 dark:group-hover:bg-blue-800/60 transition-colors">
                      <Minus className="w-3.5 h-3.5 text-blue-600 dark:text-blue-400" />
                    </div>
                    <span className="text-sm font-medium text-slate-700 dark:text-slate-300">
                      Navbar Item
                    </span>
                  </DropdownMenuItem>
                )}

                {/* Render content options if available */}
                {availableTypes.includes("category") && (
                  <DropdownMenuItem
                    onClick={() => handleCreateClick("category")}
                    className="flex items-center h-8 px-3 py-1 rounded-lg hover:bg-orange-50/80 dark:hover:bg-orange-900/20 transition-all duration-200 cursor-pointer group"
                    onContextMenu={(e) => {
                      e.preventDefault();
                      e.stopPropagation();
                    }}
                  >
                    <div className="w-6 h-6 rounded-md bg-orange-100 dark:bg-orange-900/40 flex items-center justify-center mr-3 group-hover:bg-orange-200 dark:group-hover:bg-orange-800/60 transition-colors">
                      <Folder className="w-3.5 h-3.5 text-orange-600 dark:text-orange-400" />
                    </div>
                    <span className="text-sm font-medium text-slate-700 dark:text-slate-300">
                      Category
                    </span>
                  </DropdownMenuItem>
                )}

                {availableTypes.includes("group") && (
                  <DropdownMenuItem
                    onClick={() => handleCreateClick("group")}
                    className="flex items-center h-8 px-3 py-1 rounded-lg hover:bg-indigo-50/80 dark:hover:bg-indigo-900/20 transition-all duration-200 cursor-pointer group"
                    onContextMenu={(e) => {
                      e.preventDefault();
                      e.stopPropagation();
                    }}
                  >
                    <div className="w-6 h-6 rounded-md bg-indigo-100 dark:bg-indigo-900/40 flex items-center justify-center mr-3 group-hover:bg-indigo-200 dark:group-hover:bg-indigo-800/60 transition-colors">
                      <Folder className="w-3.5 h-3.5 text-indigo-600 dark:text-indigo-400" />
                    </div>
                    <span className="text-sm font-medium text-slate-700 dark:text-slate-300">
                      Group
                    </span>
                  </DropdownMenuItem>
                )}

                {availableTypes.includes("page") && (
                  <DropdownMenuItem
                    onClick={() => handleCreateClick("page")}
                    className="flex items-center h-8 px-3 py-1 rounded-lg hover:bg-green-50/80 dark:hover:bg-green-900/20 transition-all duration-200 cursor-pointer group"
                    onContextMenu={(e) => {
                      e.preventDefault();
                      e.stopPropagation();
                    }}
                  >
                    <div className="w-6 h-6 rounded-md bg-green-100 dark:bg-green-900/40 flex items-center justify-center mr-3 group-hover:bg-green-200 dark:group-hover:bg-green-800/60 transition-colors">
                      <FilePlus className="w-3.5 h-3.5 text-green-600 dark:text-green-400" />
                    </div>
                    <span className="text-sm font-medium text-slate-700 dark:text-slate-300">
                      Page
                    </span>
                  </DropdownMenuItem>
                )}

                {availableTypes.length > 0 &&
                  (canBeRenamed() || canBeDeleted() || canBeSetAsHomepage()) && (
                    <div className="h-px bg-gradient-to-r from-transparent via-slate-300/50 dark:via-slate-600/50 to-transparent my-2" />
                  )}
              </>
            );
          })()}

          {canBeSetAsHomepage() && (
            <DropdownMenuItem
              onClick={handleSetAsHomepage}
              className="flex items-center h-8 px-3 py-1 rounded-lg hover:bg-green-50/80 dark:hover:bg-green-900/20 transition-all duration-200 cursor-pointer group"
              onContextMenu={(e) => {
                e.preventDefault();
                e.stopPropagation();
              }}
            >
              <div className="w-6 h-6 rounded-md bg-green-100 dark:bg-green-900/40 flex items-center justify-center mr-3 group-hover:bg-green-200 dark:group-hover:bg-green-800/60 transition-colors">
                <Home className="w-3.5 h-3.5 text-green-600 dark:text-green-400" />
              </div>
              <span className="text-sm font-medium text-slate-700 dark:text-slate-300">
                Set as Homepage
              </span>
            </DropdownMenuItem>
          )}

          {canBeRenamed() && (
            <DropdownMenuItem
              onClick={handleRenameClick}
              className="flex items-center h-8 px-3 py-1 rounded-lg hover:bg-slate-50/80 dark:hover:bg-slate-700/50 transition-all duration-200 cursor-pointer group"
              onContextMenu={(e) => {
                e.preventDefault();
                e.stopPropagation();
              }}
            >
              <div className="w-6 h-6 rounded-md bg-slate-100 dark:bg-slate-700/60 flex items-center justify-center mr-3 group-hover:bg-slate-200 dark:group-hover:bg-slate-600/80 transition-colors">
                <Edit3 className="w-3.5 h-3.5 text-slate-600 dark:text-slate-400" />
              </div>
              <span className="text-sm font-medium text-slate-700 dark:text-slate-300">
                Rename
              </span>
            </DropdownMenuItem>
          )}

          {canBeDeleted() && (
            <DropdownMenuItem
              onClick={handleDeleteClick}
              className="flex items-center h-8 px-3 py-1 rounded-lg hover:bg-red-50/80 dark:hover:bg-red-900/20 transition-all duration-200 cursor-pointer group"
              onContextMenu={(e) => {
                e.preventDefault();
                e.stopPropagation();
              }}
            >
              <div className="w-6 h-6 rounded-md bg-red-100 dark:bg-red-900/40 flex items-center justify-center mr-3 group-hover:bg-red-200 dark:group-hover:bg-red-800/60 transition-colors">
                <Trash2 className="w-3.5 h-3.5 text-red-600 dark:text-red-400" />
              </div>
              <span className="text-sm font-medium text-red-600 dark:text-red-400">
                Delete
              </span>
            </DropdownMenuItem>
          )}
        </DropdownMenuContent>
      </DropdownMenu>

      {/* Dialog to create item */}
      <Dialog
        open={createDialog.isOpen}
        onOpenChange={(open) => {
          if (!open) {
            setCreateDialog({
              isOpen: false,
              type: null,
              title: "",
              description: "",
            });
            setCreateValidationError(null);
          }
        }}
      >
        <DialogContent className="sm:max-w-[425px] bg-white/95 dark:bg-slate-800/95 backdrop-blur-md border border-slate-200/70 dark:border-slate-700/70 rounded-2xl shadow-xl">
          <DialogHeader>
            <DialogTitle className="text-xl font-semibold bg-gradient-to-r from-slate-900 to-slate-600 dark:from-white dark:to-slate-300 bg-clip-text text-transparent">
              {createDialog.title}
            </DialogTitle>
            <DialogDescription className="text-slate-600 dark:text-slate-400">
              {createDialog.description}
            </DialogDescription>
          </DialogHeader>
          <div className="grid gap-6 py-4">
            <div className="space-y-2">
              <Label
                htmlFor="create-name"
                className="text-sm font-medium text-slate-700 dark:text-slate-300"
              >
                Name
              </Label>
              <Input
                id="create-name"
                value={createName}
                onChange={(e) => {
                  setCreateName(e.target.value);
                  setCreateValidationError(null);
                }}
                className={`bg-white/50 dark:bg-slate-800/50 border rounded-lg focus:border-blue-500/50 dark:focus:border-blue-400/50 focus:ring-blue-500/20 dark:focus:ring-blue-400/20 ${
                  createValidationError 
                    ? "border-red-500/50 dark:border-red-400/50" 
                    : "border-slate-300/50 dark:border-slate-600/50"
                }`}
                placeholder="Item name"
                onKeyDown={(e) => {
                  if (e.key === "Enter") {
                    handleCreateConfirm();
                  }
                }}
              />
              {createValidationError && (
                <p className="text-sm text-red-500 dark:text-red-400 mt-1">
                  {createValidationError}
                </p>
              )}
            </div>
            {createDialog.type === "page" && (
              <div className="space-y-2">
                <Label
                  htmlFor="create-path"
                  className="text-sm font-medium text-slate-700 dark:text-slate-300"
                >
                  Path (optional)
                </Label>
                <Input
                  id="create-path"
                  value={createPath}
                  onChange={(e) => setCreatePath(e.target.value)}
                  className="bg-white/50 dark:bg-slate-800/50 border border-slate-300/50 dark:border-slate-600/50 rounded-lg focus:border-blue-500/50 dark:focus:border-blue-400/50 focus:ring-blue-500/20 dark:focus:ring-blue-400/20"
                  placeholder="Leave empty to use the current hierarchy"
                  onKeyDown={(e) => {
                    if (e.key === "Enter") {
                      handleCreateConfirm();
                    }
                  }}
                />
                <p className="text-xs text-slate-500">
                  If left empty, the path will be generated automatically based
                  on the current folder
                </p>
              </div>
            )}
          </div>
          <DialogFooter className="gap-3">
            <Button
              type="button"
              variant="outline"
              onClick={() =>
                setCreateDialog({
                  isOpen: false,
                  type: null,
                  title: "",
                  description: "",
                })
              }
              className="bg-white/50 dark:bg-slate-800/50 border border-slate-300/50 dark:border-slate-600/50 hover:bg-slate-50 dark:hover:bg-slate-700/50"
            >
              Cancel
            </Button>
            <Button
              type="button"
              onClick={handleCreateConfirm}
              disabled={!createName.trim()}
              className="bg-gradient-to-r from-blue-500 to-indigo-500 hover:from-blue-600 hover:to-indigo-600 text-white border-0 shadow-lg"
            >
              Create
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Dialog to rename */}
      <Dialog
        open={renameDialog.isOpen}
        onOpenChange={(open) => {
          if (!open) {
            setRenameDialog({ isOpen: false, currentName: "" });
            setRenameValidationError(null);
          }
        }}
      >
        <DialogContent className="sm:max-w-[425px] bg-white/95 dark:bg-slate-800/95 backdrop-blur-md border border-slate-200/70 dark:border-slate-700/70 rounded-2xl shadow-xl">
          <DialogHeader>
            <DialogTitle className="text-xl font-semibold bg-gradient-to-r from-slate-900 to-slate-600 dark:from-white dark:to-slate-300 bg-clip-text text-transparent">
              Rename Item
            </DialogTitle>
            <DialogDescription className="text-slate-600 dark:text-slate-400">
              Enter the new name for &ldquo;{renameDialog.currentName}&rdquo;.
            </DialogDescription>
          </DialogHeader>
          <div className="grid gap-6 py-4">
            <div className="space-y-2">
              <Label
                htmlFor="rename-name"
                className="text-sm font-medium text-slate-700 dark:text-slate-300"
              >
                Name
              </Label>
              <Input
                id="rename-name"
                value={renameName}
                onChange={(e) => {
                  setRenameName(e.target.value);
                  setRenameValidationError(null);
                }}
                className={`bg-white/50 dark:bg-slate-800/50 border rounded-lg focus:border-blue-500/50 dark:focus:border-blue-400/50 focus:ring-blue-500/20 dark:focus:ring-blue-400/20 ${
                  renameValidationError 
                    ? "border-red-500/50 dark:border-red-400/50" 
                    : "border-slate-300/50 dark:border-slate-600/50"
                }`}
                placeholder="New name"
                onKeyDown={(e) => {
                  if (e.key === "Enter") {
                    handleRenameConfirm();
                  }
                }}
              />
              {renameValidationError && (
                <p className="text-sm text-red-500 dark:text-red-400 mt-1">
                  {renameValidationError}
                </p>
              )}
            </div>
          </div>
          <DialogFooter className="gap-3">
            <Button
              type="button"
              variant="outline"
              onClick={() => {
                setRenameDialog({ isOpen: false, currentName: "" });
                setRenameValidationError(null);
              }}
              className="bg-white/50 dark:bg-slate-800/50 border border-slate-300/50 dark:border-slate-600/50 hover:bg-slate-50 dark:hover:bg-slate-700/50"
            >
              Cancel
            </Button>
            <Button
              type="button"
              onClick={handleRenameConfirm}
              disabled={!renameName.trim() || renameName.trim() === node.name}
              className="bg-gradient-to-r from-blue-500 to-indigo-500 hover:from-blue-600 hover:to-indigo-600 text-white border-0 shadow-lg"
            >
              Rename
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* AlertDialog to confirm deletion */}
      <AlertDialog
        open={deleteDialog.isOpen}
        onOpenChange={(open) => setDeleteDialog({ isOpen: open })}
      >
        <AlertDialogContent className="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg shadow-lg max-w-md p-0">
          {/* Header with icon and title on the same line */}
          <AlertDialogHeader className="px-4 pt-4">
            <div className="flex items-center space-x-3">
              <div className="text-red-500">
                <Trash2 className="w-6 h-6" />
              </div>
              <AlertDialogTitle className="text-lg font-semibold text-gray-900 dark:text-gray-100">
                Delete &ldquo;{node.name}&rdquo;
              </AlertDialogTitle>
            </div>
          </AlertDialogHeader>

          {/* Divider line */}
          <div className="border-t border-gray-200 dark:border-gray-700"></div>

          {/* Content */}
          <div className="px-6 pt-0">
            <AlertDialogDescription className="text-sm text-gray-600 dark:text-gray-400 mb-4">
              This action cannot be undone. This will permanently delete this
              item and all its contents.
            </AlertDialogDescription>

            <div className="p-3 bg-gray-50 dark:bg-gray-700/50 rounded-md">
              <p className="text-sm font-medium text-gray-900 dark:text-gray-100">
                Warning: This action is irreversible
              </p>
              <p className="text-xs text-gray-600 dark:text-gray-400 mt-1">
                All nested pages, categories, and content will be permanently
                lost.
              </p>
            </div>
          </div>

          {/* Footer with buttons */}
          <AlertDialogFooter className="p-6 pt-0 flex gap-3">
            <AlertDialogCancel className="flex-1 bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-600 h-10 px-4 rounded-md">
              Cancel
            </AlertDialogCancel>
            <AlertDialogAction
              onClick={handleDeleteConfirm}
              className="flex-1 bg-red-500 hover:bg-red-600 text-white h-10 px-4 rounded-md font-medium"
            >
              Delete
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </>
  );
};

export default ContextMenu;
