import React from 'react';
import { BubbleMenu as TiptapBubbleMenu } from '@tiptap/react';
import { Editor } from '@tiptap/react';
import {
	Bold,
	Italic,
	Strikethrough,
	Code,
	List,
	ListOrdered,
	Heading1,
	Heading2,
	Heading3,
	AlignLeft,
	Quote,
	Link,
	HelpCircle,
} from 'lucide-react';

interface BubbleMenuProps {
	editor: Editor;
}

export const BubbleMenu: React.FC<BubbleMenuProps> = ({ editor }) => {
	if (!editor) return null;

	// Check if we're currently in a heading
	const isInHeading = editor.isActive('heading');

	return (
		<TiptapBubbleMenu
			editor={editor}
			tippyOptions={{
				duration: 100,
				placement: 'top',
				animation: 'shift-away',
				theme: 'light-border',
				interactive: true,
				appendTo: () => document.body,
				zIndex: 9999,
			}}
			shouldShow={({ editor, state }) => {
				// Only show when there's a text selection
				const { selection } = state;
				const { empty } = selection;

				// Don't show if selection is empty
				if (empty) return false;

				// Don't show in code blocks
				if (editor.isActive('codeBlock')) return false;

				// Don't show in custom nodes (cards, metadata, etc.)
				const currentNode = state.selection.$from.parent;
				if (
					currentNode.type.name !== 'paragraph' &&
					currentNode.type.name !== 'heading' &&
					currentNode.type.name !== 'listItem' &&
					currentNode.type.name !== 'blockquote'
				) {
					return false;
				}

				return true;
			}}
		>
			<div className='bubble-menu'>
				{/* Text formatting */}
				{/* Only show bold button when not in a heading */}
				{!isInHeading && (
					<button
						onClick={() => editor.chain().focus().toggleBold().run()}
						className={`bubble-menu-button ${
							editor.isActive('bold') ? 'active' : ''
						}`}
						title='Bold (Ctrl+B)'
					>
						<Bold className='w-4 h-4' />
					</button>
				)}

				<button
					onClick={() => editor.chain().focus().toggleItalic().run()}
					className={`bubble-menu-button ${
						editor.isActive('italic') ? 'active' : ''
					}`}
					title='Italic (Ctrl+I)'
				>
					<Italic className='w-4 h-4' />
				</button>

				<button
					onClick={() => editor.chain().focus().toggleStrike().run()}
					className={`bubble-menu-button ${
						editor.isActive('strike') ? 'active' : ''
					}`}
					title='Strikethrough'
				>
					<Strikethrough className='w-4 h-4' />
				</button>

				<button
					onClick={() => editor.chain().focus().toggleCode().run()}
					className={`bubble-menu-button ${
						editor.isActive('code') ? 'active' : ''
					}`}
					title='Inline Code'
				>
					<Code className='w-4 h-4' />
				</button>

				<button
					onClick={() => {
					const { from, to } = editor.state.selection;
					const selectedText = editor.state.doc.textBetween(from, to);
					
					// Check if the selected text is already a link
					let selectedUrl = '';
					if (editor.isActive('link')) {
						const linkMark = editor.getAttributes('link');
						selectedUrl = linkMark.href || '';
					}

					// Trigger link dialog event with selected text and URL
					const event = new CustomEvent('openLinkDialog', {
						detail: { editor, selectedText, selectedUrl },
					});
					window.dispatchEvent(event);
				}}
					className={`bubble-menu-button ${
						editor.isActive('link') ? 'active' : ''
					}`}
					title='Add Link'
				>
					<Link className='w-4 h-4' />
				</button>

				<button
					onClick={() => {
						const { from, to } = editor.state.selection;
						const selectedText = editor.state.doc.textBetween(from, to);

						// Trigger hint dialog event with selected text
						const event = new CustomEvent('openHintDialog', {
							detail: { editor, selectedText },
						});
						window.dispatchEvent(event);
					}}
					className='bubble-menu-button'
					title='Add Hint'
				>
					<HelpCircle className='w-4 h-4' />
				</button>

				<div className='bubble-menu-divider' />

				{/* Block formatting */}
				<button
					onClick={() => editor.chain().focus().setParagraph().run()}
					className={`bubble-menu-button ${
						editor.isActive('paragraph') ? 'active' : ''
					}`}
					title='Paragraph'
				>
					<AlignLeft className='w-4 h-4' />
				</button>

				<button
					onClick={() =>
						editor.chain().focus().toggleHeading({ level: 1 }).run()
					}
					className={`bubble-menu-button ${
						editor.isActive('heading', { level: 1 }) ? 'active' : ''
					}`}
					title='Heading 1'
				>
					<Heading1 className='w-4 h-4' />
				</button>

				<button
					onClick={() =>
						editor.chain().focus().toggleHeading({ level: 2 }).run()
					}
					className={`bubble-menu-button ${
						editor.isActive('heading', { level: 2 }) ? 'active' : ''
					}`}
					title='Heading 2'
				>
					<Heading2 className='w-4 h-4' />
				</button>

				<button
					onClick={() =>
						editor.chain().focus().toggleHeading({ level: 3 }).run()
					}
					className={`bubble-menu-button ${
						editor.isActive('heading', { level: 3 }) ? 'active' : ''
					}`}
					title='Heading 3'
				>
					<Heading3 className='w-4 h-4' />
				</button>

				<div className='bubble-menu-divider' />

				{/* Lists */}
				<button
					onClick={() => editor.chain().focus().toggleBulletList().run()}
					className={`bubble-menu-button ${
						editor.isActive('bulletList') ? 'active' : ''
					}`}
					title='Bullet List'
				>
					<List className='w-4 h-4' />
				</button>

				<button
					onClick={() => editor.chain().focus().toggleOrderedList().run()}
					className={`bubble-menu-button ${
						editor.isActive('orderedList') ? 'active' : ''
					}`}
					title='Numbered List'
				>
					<ListOrdered className='w-4 h-4' />
				</button>

				<button
					onClick={() => editor.chain().focus().toggleBlockquote().run()}
					className={`bubble-menu-button ${
						editor.isActive('blockquote') ? 'active' : ''
					}`}
					title='Quote'
				>
					<Quote className='w-4 h-4' />
				</button>
			</div>
		</TiptapBubbleMenu>
	);
};

export default BubbleMenu;
