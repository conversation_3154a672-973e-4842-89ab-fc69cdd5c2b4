import handleInstallation from '@/utils/create-repo/installation';
import { createClient } from '@/utils/supabase/server';
import { Repository } from '@octokit/webhooks-types';
import { writedocsOctokit } from '@/utils/github/writedocsOctokit';
import { Buffer } from 'buffer';
import { revalidatePath } from 'next/cache';

export const runtime = 'edge';

export async function POST(request: Request): Promise<Response> {
	try {
		console.log('[ONBOARDING API] Starting request');

		const formData = await request.formData();
		const formDataObj = Object.fromEntries(formData.entries());
		const projectName = `${formDataObj.projectName}-docs`;
		const websiteUrl = `${formDataObj.projectName}.writedocs.io`;

		console.log('[ONBOARDING API] Processing:', projectName);

		const supabase = createClient();
		console.log('[ONBOARDING API] Supabase client created');

		// Check if project exists
		const { data: projects, error } = await supabase
			.from('projects')
			.select('*')
			.eq('project_name', projectName);

		if (error) {
			console.error('[ONBOARDING API] Database error:', error);
			return Response.json({ error: 'Database error' }, { status: 500 });
		}

		if (projects.length > 0) {
			console.log('[ONBOARDING API] Project already exists');
			return Response.json(
				{ error: 'Project name already exist' },
				{ status: 403 }
			);
		}

		console.log(
			'[ONBOARDING API] Project name available, creating GitHub repo'
		);

		// Create GitHub client inside the handler
		const octokit = await writedocsOctokit();
		console.log('[ONBOARDING API] GitHub client created');

		// Create GitHub repo
		const { data: repoData } = await octokit.request(
			'POST /repos/{template_owner}/{template_repo}/generate',
			{
				template_owner: 'writedocs',
				template_repo: 'docusaurus-template-3',
				owner: 'writedocs',
				name: projectName,
				description: 'Documentation generated by WriteDocs',
				include_all_branches: false,
				private: true,
				headers: {
					'X-GitHub-Api-Version': '2022-11-28',
				},
			}
		);

		console.log('[ONBOARDING API] GitHub repo created');

		// Deploy project
		await handleInstallation(repoData as Repository);
		console.log('[ONBOARDING API] Project deployed');

		const {
			data: { user },
		} = await supabase.auth.getUser();

		if (!user) {
			console.error('[ONBOARDING API] No user found');
			return Response.json(
				{ error: 'User not authenticated' },
				{ status: 401 }
			);
		}

		// Simple config
		const cleanProjectName = projectName.replace(/-docs$/, '');
		const displayName = cleanProjectName
			.split('-')
			.map((word) => word.charAt(0).toUpperCase() + word.slice(1))
			.join(' ');

		const sidebarRef = `first-steps-${Date.now().toString(36)}`;

		const defaultConfig = {
			websiteName: `${displayName} Documentation`,
			description: `Documentation site for ${displayName}`,
			homepage: 'Welcome/First steps/Editor/How to use',
			images: {
				logo: 'media/logo.png',
				favicon: 'media/favicon.ico',
			},
			navbar: [
				{
					label: 'Welcome',
					dropdown: [
						{
							label: 'First steps',
							sidebarRef: sidebarRef,
						},
					],
				},
			],
			styles: {
				mainColor: '#0029F5',
				darkModeMainColor: '#4f46e5',
				navbarColor: '#a5c6f2',
				navbarDarkModeColor: '#0029F5',
				backgroundDarkModeColor: '#111827',
				logoSize: 'large',
				pagination: true,
			},
			apiFiles: [],
			sidebars: [
				{
					sidebarRef: sidebarRef,
					categories: [
						{
							categoryName: 'Editor',
							pages: [
								'Welcome/First steps/Editor/How to use',
								'Welcome/First steps/Editor/Filetree',
							],
						},
					],
				},
			],
		};

		console.log('[ONBOARDING API] Inserting project');

		// Insert project
		const { data: insertedProjectData, error: projectToDbError } =
			await supabase
				.from('projects')
				.insert({
					project_name: projectName,
					owner_id: user.id,
					website_url: websiteUrl,
					deploy_repo_url: repoData.html_url,
					source_repo_url: repoData.html_url,
					company_name: null,
					configjson: defaultConfig,
				})
				.select('id')
				.single();

		if (projectToDbError) {
			console.error(
				'[ONBOARDING API] Error inserting project:',
				projectToDbError
			);
			return Response.json(
				{ error: 'Failed to create project' },
				{ status: 500 }
			);
		}

		const newProjectId = insertedProjectData.id;
		console.log('[ONBOARDING API] Project created with ID:', newProjectId);

		// Create detailed default pages
		const defaultPages = [
			{
				project_id: newProjectId,
				path: 'Welcome/First steps/Editor/How to use',
				title: 'How to use the Editor',
				content: `---
  title: How to Use the WriteDocs Editor
  description: Guide to using the WriteDocs editor
---

# How to Use the WriteDocs Editor

Welcome to WriteDocs! Our powerful editor makes creating beautiful documentation simple and intuitive.

## Getting Started

The WriteDocs editor is a **rich text editor** that lets you create content visually without needing to know code. Simply click where you want to write and start typing!

## Basic Text Formatting

Select any text to see the **formatting bubble menu** with options for:
- **Bold** text (Ctrl+B)
- *Italic* text (Ctrl+I) 
- ~~Strikethrough~~ text
- \`Inline code\` formatting
- [Links](https://example.com) to other pages or websites

## The Slash Menu - Your Content Toolkit

Type **/** anywhere to open the content menu with 25+ content types:

### Advanced Content Blocks
- **Code Blocks** - Syntax highlighting for 18+ programming languages
- **Tables** - Create and edit tables with drag-to-resize columns
- **Callouts** - Highlight important information in 6 different styles
- **Accordions** - Collapsible content sections
- **Tabs** - Switch between different content areas
- **Steps** - Create numbered instruction sequences
- **Cards** - Visual content blocks with images and links

### Media & Visual Content
- **Images** - Upload and insert images with sizing options
- **Videos** - Embed videos and media content

## Pro Tips

1. **Start with structure** - Use headings to outline your content first
2. **Use callouts** for important information that needs to stand out
3. **Add code examples** - Code blocks make technical content much clearer
4. **Break up long content** - Use tabs or accordions for better organization
5. **Include visuals** - Images and videos make documentation more engaging

The editor auto-saves your work and is designed to be intuitive - just start typing and explore!`,
				is_api_file: false,
			},
			{
				project_id: newProjectId,
				path: 'Welcome/First steps/Editor/Filetree',
				title: 'Working with the File Tree',
				content: `---
  title: Working with the File Tree
  description: Guide to organizing documentation with the file tree
---

# Organizing Your Documentation with the File Tree

The file tree is your command center for organizing and structuring your documentation.

## Understanding the Structure

Your documentation is organized in a **hierarchical system** with different levels:

### Navigation Level
- **Navbar Dropdowns** - Top-level menus in your site's navigation bar
- **Navbar Items** - Individual links that appear in dropdowns or directly in the navbar

### Content Organization Level  
- **Categories** - Main folders that group related content (orange folder icons)
- **Groups** - Sub-folders within categories for further organization (indigo folder icons)
- **Pages** - Your actual documentation content (document icons)
- **Subpages** - Nested pages within groups for detailed topics

## Quick Actions Panel

At the top of the file tree, you'll find **Quick Actions** - your shortcut to creating new content:

- **Navbar Dropdown** - Create new dropdown menus for your navigation
- **Navbar Item** - Add individual navigation links
- **Import API Docs** - Bulk import documentation from API specifications

## Creating New Content

### Right-Click Context Menu
**Right-click on any item or empty space** to see available actions. The menu is smart - it only shows options that make sense for what you clicked on.

## Organizing with Drag and Drop

### Visual Drag and Drop
**Click and drag** any item to move it around:
- **Blue highlights** show where you can drop items
- **Blue lines** indicate if items will go before, after, or inside containers
- **Auto-expansion** - folders open automatically when you drag items over them

## Best Practices

1. **Plan your structure first** - sketch out your organization before creating lots of content
2. **Use clear, descriptive names** that explain the content
3. **Keep hierarchy shallow** - Avoid too many nested levels (3-4 levels maximum)
4. **Group related content** together in the same category or group

The file tree grows with your documentation - start simple and add complexity as needed!`,
				is_api_file: false,
			},
		];

		const { error: pagesError } = await supabase
			.from('project_pages')
			.insert(defaultPages);

		if (pagesError) {
			console.error('[ONBOARDING API] Error inserting pages:', pagesError);
		} else {
			console.log('[ONBOARDING API] Default pages created successfully');
		}

		console.log('[ONBOARDING API] Updating GitHub files');

		// Update GitHub files
		const [owner, repo] = repoData.full_name.split('/');

		try {
			// Wait for the repo to be ready
			await new Promise((resolve) => setTimeout(resolve, 3000));

			// Try to update plan.json
			try {
				const { data: fileData } = await octokit.request(
					'GET /repos/{owner}/{repo}/contents/{path}',
					{ owner, repo, path: 'plan.json', ref: 'main' }
				);

				if ('content' in fileData && 'sha' in fileData) {
					const content = Buffer.from(
						fileData.content as string,
						'base64'
					).toString('utf8');
					const planData = JSON.parse(content);
					planData.projectID = newProjectId;
					planData.algoliaIndex = 'docusaurus_template';
					const newContent = JSON.stringify(planData, null, 2);

					await octokit.request('PUT /repos/{owner}/{repo}/contents/{path}', {
						owner,
						repo,
						path: 'plan.json',
						message: 'feat: add projectID to plan.json',
						content: Buffer.from(newContent).toString('base64'),
						sha: fileData.sha as string,
						branch: 'main',
					});

					console.log('[ONBOARDING API] plan.json updated');
				}
			} catch (planError) {
				console.error('[ONBOARDING API] Error updating plan.json:', planError);
				// Create plan.json if it doesn't exist
				const initialPlan = {
					projectID: newProjectId,
					algoliaIndex: 'docusaurus_template',
				};
				const content = JSON.stringify(initialPlan, null, 2);

				try {
					await octokit.request('PUT /repos/{owner}/{repo}/contents/{path}', {
						owner,
						repo,
						path: 'plan.json',
						message: 'feat: create plan.json with projectID',
						content: Buffer.from(content).toString('base64'),
						branch: 'main',
					});

					console.log('[ONBOARDING API] plan.json created');
				} catch (createError) {
					console.error(
						'[ONBOARDING API] Error creating plan.json:',
						createError
					);
				}
			}

			// Try to update src/utils/plan.js
			try {
				const planJsPath = 'src/utils/plan.js';
				const { data: planJsData } = await octokit.request(
					'GET /repos/{owner}/{repo}/contents/{path}',
					{ owner, repo, path: planJsPath, ref: 'main' }
				);

				if ('content' in planJsData && 'sha' in planJsData) {
					const content = Buffer.from(
						planJsData.content as string,
						'base64'
					).toString('utf8');

					if (content.includes('"projectID": null')) {
						const newContent = content.replace(
							/(["']projectID["']\s*:\s*)null/,
							`$1${newProjectId}`
						);

						await octokit.request('PUT /repos/{owner}/{repo}/contents/{path}', {
							owner,
							repo,
							path: planJsPath,
							message: 'fix: replace projectID null in plan.js',
							content: Buffer.from(newContent).toString('base64'),
							sha: planJsData.sha as string,
							branch: 'main',
						});

						console.log('[ONBOARDING API] plan.js updated');
					}
				}
			} catch (planJsError) {
				console.error('[ONBOARDING API] Error updating plan.js:', planJsError);
			}
		} catch (githubError) {
			console.error(
				'[ONBOARDING API] Error with GitHub operations:',
				githubError
			);
		}

		console.log('[ONBOARDING API] Project creation completed');

		// Force revalidate paths to ensure middleware gets updated data
		revalidatePath('/onboarding');
		revalidatePath('/');

		return Response.json({
			success: true,
			message: 'Project created successfully',
			projectId: newProjectId,
		});
	} catch (error) {
		console.error('[ONBOARDING API] Fatal error:', error);

		if (error instanceof Error) {
			console.error('[ONBOARDING API] Error stack:', error.stack);

			if (error.message.includes('already exist')) {
				return Response.json(
					{ error: 'Project name already exist' },
					{ status: 403 }
				);
			}
		}

		return Response.json({ error: 'Something went wrong' }, { status: 500 });
	}
}
