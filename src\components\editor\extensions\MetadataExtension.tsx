import { Extension } from "@tiptap/core";
import { Plug<PERSON>, <PERSON>lug<PERSON><PERSON><PERSON> } from "prosemirror-state";

export const MetadataExtension = Extension.create({
  name: "metadataExtension",

  addGlobalAttributes() {
    return [
      {
        types: ["metadataNode"],
        attributes: {
          title: {
            default: "",
          },
          description: {
            default: "",
          },
          slug: {
            default: "",
          },
        },
      },
    ];
  },

  addProseMirrorPlugins() {
    return [
      new Plugin({
        key: new PluginKey("metadataProtection"),

        filterTransaction: (transaction, state) => {
          // Check if transaction is trying to delete a metadata node
          let hasMetadataNode = false;

          state.doc.descendants((node) => {
            if (node.type.name === "metadataNode") {
              hasMetadataNode = true;
              return false;
            }
          });

          if (!hasMetadataNode) return true; // No metadata node, allow transaction

          // If after transaction there's no more metadata node, block it
          let hasMetadataAfter = false;
          transaction.doc.descendants((node) => {
            if (node.type.name === "metadataNode") {
              hasMetadataAfter = true;
              return false;
            }
          });

          if (hasMetadataNode && !hasMetadataAfter) {
            // Attempt to delete metadata node detected, blocking transaction
            console.warn("Attempt to delete metadata node blocked");
            return false;
          }

          return true; // Allow other transactions
        },
      }),
    ];
  },

  onUpdate() {
    // Check if a metadata node already exists
    const editor = this.editor;
    let hasMetadataNode = false;

    editor.state.doc.descendants((node) => {
      if (node.type.name === "metadataNode") {
        hasMetadataNode = true;
        return false;
      }
    });

    // If it doesn't exist and the document has HTML content with metadata, insert automatically
    if (!hasMetadataNode) {
      const html = editor.getHTML();

      // Check if HTML contains the new metadata structure
      if (
        html.includes('<head data-type="metadata">') ||
        html.includes("<metadata")
      ) {
        console.log("🔍 MetadataExtension detectou metadata tag:", html);

        // Try to extract from head tag first
        const headMatch = html.match(
          /<head[^>]*data-type="metadata"[^>]*>([\s\S]*?)<\/head>/
        );
        let title = "",
          description = "",
          slug = "";

        if (headMatch) {
          const headContent = headMatch[1];

          const titleMatch = headContent.match(
            /<meta\s+name="title"\s+content="([^"]*)"/
          );
          const descriptionMatch = headContent.match(
            /<meta\s+name="description"\s+content="([^"]*)"/
          );
          const slugMatch = headContent.match(
            /<meta\s+name="slug"\s+content="([^"]*)"/
          );

          title = titleMatch ? titleMatch[1] : "";
          description = descriptionMatch ? descriptionMatch[1] : "";
          slug = slugMatch ? slugMatch[1] : "";
        } else {
          // Try to extract from metadata tag
          const metadataMatch = html.match(/<metadata[^>]*>/);
          if (metadataMatch) {
            const metadataTag = metadataMatch[0];
            const titleMatch = metadataTag.match(/title="([^"]*)"/);
            const descriptionMatch = metadataTag.match(/description="([^"]*)"/);
            const slugMatch = metadataTag.match(/slug="([^"]*)"/);

            title = titleMatch ? titleMatch[1] : "";
            description = descriptionMatch ? descriptionMatch[1] : "";
            slug = slugMatch ? slugMatch[1] : "";
          }
        }

        console.log("🔍 MetadataExtension extraiu:", {
          title,
          description,
          slug,
        });

        // Insert the metadata node at the beginning of the document
        editor.commands.insertContentAt(0, {
          type: "metadataNode",
          attrs: { title, description, slug },
        });
      }
    }
  },
});

export default MetadataExtension;
