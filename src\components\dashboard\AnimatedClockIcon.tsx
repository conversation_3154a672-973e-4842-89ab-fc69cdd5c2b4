import { cn } from "@/lib/utils";
import { CSSProperties, useEffect, useState } from "react";

interface AnimatedClockIconProps {
	isLoading: boolean;
	className?: string;
}

export function AnimatedClockIcon({
	isLoading,
	className,
}: AnimatedClockIconProps) {
	const [time, setTime] = useState({ hours: 0, minutes: 0 });

	// On mount, and when loading stops, update to the current time for the resting position.
	useEffect(() => {
		const now = new Date();
		setTime({
			hours: now.getHours(),
			minutes: now.getMinutes(),
		});
	}, [isLoading]);

	// Calculate rotation based on the current time state.
	// This will be the resting position AND the starting position for the animation.
	const minutesAngle = (time.minutes / 60) * 360;
	const hoursAngle = ((time.hours % 12) / 12) * 360 + (time.minutes / 60) * 30;

	// Create dynamic keyframes that start from the last known position.
	// Using a consistent animation name, we just redefine its keyframes on each render.
	const keyframes = `
      @keyframes spin-hour-from-current {
        from {
          transform: rotate(${hoursAngle}deg);
        }
        to {
          transform: rotate(${hoursAngle + 360}deg);
        }
      }
      @keyframes spin-minute-from-current {
        from {
          transform: rotate(${minutesAngle}deg);
        }
        to {
          transform: rotate(${minutesAngle + 360}deg);
        }
      }
    `;

	// When not loading, the hands point to the current time.
	// When loading begins, the `animation` property takes over.
	// Because the animation's `from` keyframe matches the previous transform, the start is seamless.
	const hourHandStyle: CSSProperties = {
		transformOrigin: "center",
		transition: "transform 0.8s cubic-bezier(0.23, 1, 0.32, 1)",
		transform: isLoading ? undefined : `rotate(${hoursAngle}deg)`,
		animation: isLoading
			? "spin-hour-from-current 12s linear infinite"
			: "none",
	};

	const minuteHandStyle: CSSProperties = {
		transformOrigin: "center",
		transition: "transform 0.8s cubic-bezier(0.23, 1, 0.32, 1)",
		transform: isLoading ? undefined : `rotate(${minutesAngle}deg)`,
		animation: isLoading
			? "spin-minute-from-current 1s linear infinite"
			: "none",
	};

	return (
		<>
			<style>{keyframes}</style>
			<svg
				xmlns='http://www.w3.org/2000/svg'
				width='16'
				height='16'
				fill='currentColor'
				className={cn(className)}
				viewBox='0 0 16 16'
			>
				<path d='M8 16A8 8 0 1 0 8 0a8 8 0 0 0 0 16m7-8A7 7 0 1 1 1 8a7 7 0 0 1 14 0' />

				{/* Hour Hand (slower) */}
				<path
					d='M8 8 V4.5'
					stroke='currentColor'
					strokeWidth='1.2'
					strokeLinecap='round'
					style={hourHandStyle}
				/>

				{/* Minute Hand (faster) */}
				<path
					d='M8 8 V2'
					stroke='currentColor'
					strokeWidth='1'
					strokeLinecap='round'
					style={minuteHandStyle}
				/>
			</svg>
		</>
	);
}
