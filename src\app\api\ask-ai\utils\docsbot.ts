import { DocsBotQuestionsResponse } from "@/app/(dashboard)/[projectId]/ask-ai/types";

/**
 * Fetches questions from the DocsBot API with the given parameters
 */
export async function fetchDocsBotQuestions(
	teamId: string,
	botId: string,
	apiKey: string,
	page: string | number = 0,
	rating?: string | null,
	escalated?: string | null,
	couldAnswer?: string | null,
	startDate?: string | null,
	endDate?: string | null
): Promise<DocsBotQuestionsResponse> {
	// Make request to Docsbot API with pagination
	let apiUrl = `https://docsbot.ai/api/teams/${teamId}/bots/${botId}/questions?page=${page}`;

	// Add filters to the API URL if they exist
	if (rating) {
		apiUrl += `&rating=${rating}`;
	}

	if (escalated) {
		// Ensure the value is literally "true" or "false" as a string
		const escalatedValue = escalated === "true" ? "true" : "false";
		apiUrl += `&escalated=${escalatedValue}`;
	}

	if (couldAnswer) {
		// Ensure the value is literally "true" or "false" as a string
		const couldAnswerValue = couldAnswer === "true" ? "true" : "false";
		apiUrl += `&couldAnswer=${couldAnswerValue}`;
	}

	// Add date parameters if they exist
	if (startDate) {
		apiUrl += `&startDate=${startDate}`;
	}

	if (endDate) {
		apiUrl += `&endDate=${endDate}`;
	}

	const response = await fetch(apiUrl, {
		method: "GET",
		headers: {
			Authorization: `Bearer ${apiKey}`,
			"Content-Type": "application/json",
		},
	});

	if (!response.ok) {
		const errorText = await response.text();
		console.error("Error fetching questions from Docsbot API:", errorText);
		throw new Error(
			`Failed to fetch questions from Docsbot API: ${response.status} - ${errorText}`
		);
	}

	// Parse the response which contains questions and pagination info
	return await response.json();
}

/**
 * Validates the docsbotKey parameter and returns teamId and botId
 */
export function validateDocsBotKey(docsbotKey: string | null): {
	teamId: string;
	botId: string;
} {
	if (!docsbotKey) {
		throw new Error("Missing required parameter: docsbotKey");
	}

	// Extract teamId and botId from docsbotKey
	const [teamId, botId] = docsbotKey.split("/");

	if (!teamId || !botId) {
		throw new Error("Invalid docsbotKey format. Expected format: teamId/botId");
	}

	return { teamId, botId };
}
