import { NextRequest, NextResponse } from "next/server";
import {
	getLatestDeployment,
	retryDeployment,
} from "@/utils/create-repo/cloudflare";

export const runtime = "edge";

interface RetryDeploymentRequest {
	projectName: string;
}

export async function POST(request: NextRequest) {
	try {
		const body = (await request.json()) as RetryDeploymentRequest;
		const { projectName } = body;

		if (!projectName) {
			return NextResponse.json(
				{ error: "projectName is required" },
				{ status: 400 }
			);
		}

		// Get the latest deployment
		const latestDeployment = await getLatestDeployment(projectName);

		if (!latestDeployment) {
			return NextResponse.json(
				{ error: "No deployments found for this project" },
				{ status: 404 }
			);
		}

		// Retry the latest deployment
		const retriedDeployment = await retryDeployment(
			projectName,
			latestDeployment.id
		);

		return NextResponse.json({
			success: true,
			message: "Deployment retry initiated successfully",
			deployment: {
				id: retriedDeployment.id,
				short_id: retriedDeployment.short_id,
				url: retriedDeployment.url,
				environment: retriedDeployment.environment,
				status: retriedDeployment.latest_stage.status,
				created_on: retriedDeployment.created_on,
			},
		});
	} catch (error) {
		console.error("Error retrying deployment:", error);

		let errorMessage = "Failed to retry deployment";
		let statusCode = 500;

		// Handle Axios errors specifically
		if (error && typeof error === "object" && "response" in error) {
			const axiosError = error as {
				response?: {
					status?: number;
					data?: { errors?: Array<{ message?: string }> };
				};
			};
			if (axiosError.response?.status === 404) {
				errorMessage = "Project not found in Cloudflare";
				statusCode = 404;
			} else if (axiosError.response?.status === 429) {
				errorMessage = "Rate limit exceeded, please try again later";
				statusCode = 429;
			} else if (axiosError.response?.data?.errors?.[0]?.message) {
				errorMessage = axiosError.response.data.errors[0].message;
				statusCode = axiosError.response.status || 500;
			}
		}

		return NextResponse.json(
			{
				error: errorMessage,
				details: error instanceof Error ? error.message : "Unknown error",
			},
			{ status: statusCode }
		);
	}
}
