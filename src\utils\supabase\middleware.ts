import { createServerClient } from "@supabase/ssr";
import { NextResponse, type NextRequest } from "next/server";

// Cache simples para evitar queries repetidas
const projectCountCache = new Map<
  string,
  { count: number; projectId?: number; timestamp: number }
>();
const CACHE_TTL = 60 * 1000; // 1 minuto

async function getUserProjectCount(
  supabase: ReturnType<typeof createServerClient>,
  userId: string
) {
  // Verificar cache
  const cached = projectCountCache.get(userId);
  if (cached && Date.now() - cached.timestamp < CACHE_TTL) {
    return cached;
  }

  // Buscar dados atualizados
  const { data, count } = await supabase
    .from("project_user")
    .select("project_id", { count: "exact" })
    .eq("user_id", userId);

  const result = {
    count: count || 0,
    projectId: data && data.length === 1 ? data[0].project_id : undefined,
    timestamp: Date.now(),
  };

  // Atualizar cache
  projectCountCache.set(userId, result);

  return result;
}

export async function updateSession(request: NextRequest) {
  let supabaseResponse = NextResponse.next({
    request,
  });

  const supabase = createServerClient(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
    {
      cookies: {
        getAll() {
          return request.cookies.getAll();
        },
        setAll(cookiesToSet) {
          cookiesToSet.forEach(({ name, value }) =>
            request.cookies.set(name, value)
          );
          supabaseResponse = NextResponse.next({
            request,
          });
          cookiesToSet.forEach(({ name, value, options }) =>
            supabaseResponse.cookies.set(name, value, options)
          );
        },
      },
    }
  );

  // IMPORTANT: Avoid writing any logic between createClient and
  // supabase.auth.getUser(). A simple mistake could make it very hard to debug
  // issues with users being randomly logged out.

  const {
    data: { user },
  } = await supabase.auth.getUser();

  const publicRoutes = [
    "/login",
    "/signup",
    "/reset-password",
    "/update-password",
    "/auth",
    "/invite",
    "/api/stripe/webhook",
  ];

  const isPublicRoute = publicRoutes.some((route) =>
    request.nextUrl.pathname.startsWith(route)
  );

  // If there is no user and it's not a public route, redirect to login
  if (!user && !isPublicRoute) {
    const url = request.nextUrl.clone();
    url.pathname = "/login";
    return NextResponse.redirect(url);
  }

  // If there is a user trying to access login, redirect to dashboard
  if (
    (user && request.nextUrl.pathname.startsWith("/login")) ||
    (user && request.nextUrl.pathname.startsWith("/signup"))
  ) {
    const url = request.nextUrl.clone();
    url.pathname = "/";
    return NextResponse.redirect(url);
  }

  // If there is a user, check projects
  if (user && !isPublicRoute) {
    // Não verificar projetos em rotas de API (exceto as já listadas em publicRoutes)
    if (request.nextUrl.pathname.startsWith("/api")) {
      return supabaseResponse;
    }

    try {
      const projectInfo = await getUserProjectCount(supabase, user.id);
      const url = request.nextUrl.clone();

      // Se está no onboarding e tem projetos, redirecionar para home
      if (
        request.nextUrl.pathname.startsWith("/onboarding") &&
        projectInfo.count > 0
      ) {
        // Se tem apenas 1 projeto, ir direto para ele
        if (projectInfo.count === 1 && projectInfo.projectId) {
          url.pathname = `/${projectInfo.projectId}/dashboard`;
        } else {
          url.pathname = "/";
        }
        return NextResponse.redirect(url);
      }

      // Se não está no onboarding e não tem projetos, ir para onboarding
      if (
        !request.nextUrl.pathname.startsWith("/onboarding") &&
        !request.nextUrl.pathname.startsWith("/new-project") &&
        projectInfo.count === 0
      ) {
        url.pathname = "/onboarding";
        return NextResponse.redirect(url);
      }

      // Redirecionar rotas /[numericProjectId] para /dashboard/[numericProjectId]/dashboard
      const pathname = request.nextUrl.pathname;
      const projectRootRegExp = /^\/(\d+)$/; // Captura o ID do projeto
      const match = pathname.match(projectRootRegExp);

      if (match) {
        const redirectUrl = request.nextUrl.clone();
        redirectUrl.pathname = `/dashboard${pathname}/dashboard`;
        return NextResponse.redirect(redirectUrl);
      }
    } catch (error) {
      // Em caso de erro, apenas logar e continuar sem redirecionar
      console.error("Error checking user projects in middleware:", error);
    }
  }

  // IMPORTANT: You *must* return the supabaseResponse object as it is. If you're
  // creating a new response object with NextResponse.next() make sure to:
  // 1. Pass the request in it, like so:
  //    const myNewResponse = NextResponse.next({ request })
  // 2. Copy over the cookies, like so:
  //    myNewResponse.cookies.setAll(supabaseResponse.cookies.getAll())
  // 3. Change the myNewResponse object to fit your needs, but avoid changing
  //    the cookies!
  // 4. Finally:
  //    return myNewResponse
  // If this is not done, you may be causing the browser and server to go out
  // of sync and terminate the user's session prematurely!

  return supabaseResponse;
}
