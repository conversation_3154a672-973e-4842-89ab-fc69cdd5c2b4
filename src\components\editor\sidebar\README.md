# Sidebar Componentizada - Estrutura de Arquivos

Esta pasta contém todos os componentes e tipos relacionados à sidebar do editor com drag and drop.

## 📁 Arquivos

### `types.ts`

Define todas as interfaces TypeScript utilizadas pelos componentes da sidebar:

- `TreeNode` - Estrutura do nó da árvore
- `TreeItemProps` - Props do componente individual
- `FileTreeProps` - Props da árvore completa

### `FileTreeItem.tsx`

Componente individual de cada item da árvore. Responsável por:

- **Drag and Drop**: Handlers para arrastar e soltar
- **Renderização**: Ícones, nome e estado visual
- **Interação**: Cliques, toggle de expansão
- **Validação**: Previne drops inválidos

### `FileTree.tsx`

Componente container da árvore completa. Responsável por:

- **Renderização da Lista**: Mapeia todos os nós raiz
- **Estado Vazio**: Mostra mensagem quando não há dados
- **Propagação de Props**: Repassa handlers para os itens

### `AddNodeDialog.tsx`

Componente de diálogo para adicionar novos itens. Características:

- **Formulário Modal**: Interface para criar páginas, categorias, grupos
- **Validação**: Campos obrigatórios e opcionais
- **Callback**: Comunica criação de volta ao componente pai

## 🔄 Fluxo de Dados

```
ProjectPagesSidebar.tsx (Container Principal)
     ↓ props & handlers
FileTree.tsx (Lista da Árvore)
     ↓ props & handlers
FileTreeItem.tsx (Item Individual)
     ↓ eventos drag/drop
ProjectPagesSidebar.tsx (Atualiza estado)
```

## 🎯 Vantagens da Componentização

1. **Separação de Responsabilidades**: Cada componente tem uma função específica
2. **Reutilização**: Componentes podem ser usados em outros contextos
3. **Manutenção**: Easier to debug and modify specific functionality
4. **Testabilidade**: Componentes menores são mais fáceis de testar
5. **Performance**: React pode otimizar re-renders por componente

## 🛠️ Como Usar

```tsx
import FileTree from "./sidebar/FileTree";
import type { TreeNode } from "./sidebar/types";

const MyComponent = () => {
  const [tree, setTree] = useState<TreeNode[]>([]);

  return (
    <FileTree
      tree={tree}
      onToggle={handleToggle}
      onDrop={handleDrop}
      draggedItem={draggedItem}
      setDraggedItem={setDraggedItem}
      createPageLink={createPageLink}
      handlePageClick={handlePageClick}
      getItemClasses={getItemClasses}
    />
  );
};
```

## 🔧 Extensibilidade

Para adicionar novas funcionalidades:

1. **Novos Tipos de Nó**: Adicione ao enum `type` em `types.ts`
2. **Novos Ícones**: Modifique `getNodeIcon()` em `FileTreeItem.tsx`
3. **Novos Handlers**: Adicione às interfaces em `types.ts`
4. **Validações Customizadas**: Implemente em `FileTreeItem.tsx`

## 🐛 Correções de Bugs

### Bug de Drop Circular (Corrigido)

**Problema**: Arrastar uma pasta para dentro dela mesma causava o desaparecimento dos arquivos.

**Solução**: Validações implementadas em `handleDrop`:

- Previne drop de um nó em si mesmo
- Previne drop em descendentes usando `isDescendant()`
- Validação adicional para drops "inside"

**Localização**: `src/utils/fileTreeUtils.ts` - função `isDescendant()`
