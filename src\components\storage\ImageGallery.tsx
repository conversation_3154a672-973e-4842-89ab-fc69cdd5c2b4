'use client';

import React, { useState, useEffect, useCallback } from 'react';
import {
	Search,
	Filter,
	Download,
	Eye,
	Grid,
	List,
	Tag,
	Calendar,
} from 'lucide-react';

interface OrganizationImage {
	id: string;
	organization_id: string;
	project_id: number;
	image_name: string;
	image_path: string;
	image_url: string;
	file_size: number;
	content_type: string;
	alt_text: string;
	tags: string[];
	metadata: Record<string, unknown>;
	created_by: string;
	created_at: string;
	updated_at: string;
}

interface ImageGalleryProps {
	projectId: number;
	organizationId: string;
	onImageSelect?: (image: OrganizationImage) => void;
	selectionMode?: boolean;
}

export default function ImageGallery({
	projectId,

	onImageSelect,
	selectionMode = false,
}: ImageGalleryProps) {
	const [images, setImages] = useState<OrganizationImage[]>([]);
	const [loading, setLoading] = useState(true);
	const [searchTerm, setSearchTerm] = useState('');
	const [selectedTags, setSelectedTags] = useState<string[]>([]);
	const [availableTags, setAvailableTags] = useState<string[]>([]);
	const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');
	const [selectedImages, setSelectedImages] = useState<Set<string>>(new Set());

	const loadImages = useCallback(async () => {
		setLoading(true);
		try {
			// Get Supabase client from your context or environment
			const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
			const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY;

			const response = await fetch(
				`${supabaseUrl}/rest/v1/rpc/get_project_images`,
				{
					method: 'POST',
					headers: {
						'Content-Type': 'application/json',
						Authorization: `Bearer ${supabaseAnonKey}`,
						apikey: supabaseAnonKey!,
					},
					body: JSON.stringify({
						p_project_id: projectId,
						p_tags: selectedTags.length > 0 ? selectedTags : null,
						p_search_term: searchTerm || null,
						p_limit: 100,
						p_offset: 0,
					}),
				}
			);

			if (response.ok) {
				const data = (await response.json()) as OrganizationImage[];
				setImages(data);

				// Extract unique tags
				const allTags = data.flatMap((img) => img.tags);
				const uniqueTags = [...new Set(allTags)].sort();
				setAvailableTags(uniqueTags);
			}
		} catch (error) {
			console.error('Error loading images:', error);
		} finally {
			setLoading(false);
		}
	}, [projectId, selectedTags, searchTerm]);

	useEffect(() => {
		loadImages();
	}, [loadImages]);

	const handleImageClick = useCallback(
		(image: OrganizationImage) => {
			if (selectionMode && onImageSelect) {
				onImageSelect(image);
			} else {
				// Open image in modal or new tab
				window.open(image.image_url, '_blank');
			}
		},
		[selectionMode, onImageSelect]
	);

	const toggleImageSelection = useCallback((imageId: string) => {
		setSelectedImages((prev) => {
			const newSet = new Set(prev);
			if (newSet.has(imageId)) {
				newSet.delete(imageId);
			} else {
				newSet.add(imageId);
			}
			return newSet;
		});
	}, []);

	const handleTagFilter = useCallback((tag: string) => {
		setSelectedTags((prev) => {
			if (prev.includes(tag)) {
				return prev.filter((t) => t !== tag);
			} else {
				return [...prev, tag];
			}
		});
	}, []);

	const formatDate = (dateString: string): string => {
		return new Date(dateString).toLocaleDateString('pt-BR', {
			day: '2-digit',
			month: '2-digit',
			year: 'numeric',
			hour: '2-digit',
			minute: '2-digit',
		});
	};

	if (loading) {
		return (
			<div className='flex items-center justify-center h-64'>
				<div className='animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600'></div>
			</div>
		);
	}

	return (
		<div className='space-y-6'>
			{/* Header with controls */}
			<div className='flex flex-col sm:flex-row gap-4 justify-between items-start sm:items-center'>
				<div className='flex items-center space-x-4'>
					<h2 className='text-xl font-semibold'>Galeria de Imagens</h2>
					<span className='text-sm text-gray-500'>
						{images.length} {images.length === 1 ? 'imagem' : 'imagens'}
					</span>
				</div>

				<div className='flex items-center space-x-2'>
					<button
						onClick={() => setViewMode('grid')}
						className={`p-2 rounded ${
							viewMode === 'grid'
								? 'bg-blue-100 text-blue-600'
								: 'text-gray-400 hover:text-gray-600'
						}`}
					>
						<Grid className='w-4 h-4' />
					</button>
					<button
						onClick={() => setViewMode('list')}
						className={`p-2 rounded ${
							viewMode === 'list'
								? 'bg-blue-100 text-blue-600'
								: 'text-gray-400 hover:text-gray-600'
						}`}
					>
						<List className='w-4 h-4' />
					</button>
				</div>
			</div>

			{/* Search and filters */}
			<div className='space-y-4'>
				<div className='relative'>
					<Search className='absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4' />
					<input
						type='text'
						placeholder='Buscar por nome ou texto alternativo...'
						value={searchTerm}
						onChange={(e) => setSearchTerm(e.target.value)}
						className='w-full pl-10 pr-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500'
					/>
				</div>

				{availableTags.length > 0 && (
					<div className='flex flex-wrap gap-2'>
						<span className='text-sm font-medium text-gray-700 dark:text-gray-300 flex items-center'>
							<Filter className='w-4 h-4 mr-1' />
							Tags:
						</span>
						{availableTags.slice(0, 10).map((tag) => (
							<button
								key={tag}
								onClick={() => handleTagFilter(tag)}
								className={`px-3 py-1 text-xs rounded-full border transition-colors ${
									selectedTags.includes(tag)
										? 'bg-blue-100 border-blue-300 text-blue-800'
										: 'bg-gray-100 border-gray-300 text-gray-700 hover:bg-gray-200'
								}`}
							>
								{tag}
							</button>
						))}
						{selectedTags.length > 0 && (
							<button
								onClick={() => setSelectedTags([])}
								className='px-3 py-1 text-xs text-red-600 hover:text-red-800'
							>
								Limpar filtros
							</button>
						)}
					</div>
				)}
			</div>

			{/* Images grid/list */}
			{images.length === 0 ? (
				<div className='text-center py-12'>
					<div className='mx-auto w-24 h-24 text-gray-300 mb-4'>
						<svg
							className='w-full h-full'
							fill='currentColor'
							viewBox='0 0 20 20'
						>
							<path
								fillRule='evenodd'
								d='M4 3a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V5a2 2 0 00-2-2H4zm12 12H4l4-8 3 6 2-4 3 6z'
								clipRule='evenodd'
							/>
						</svg>
					</div>
					<h3 className='text-lg font-medium text-gray-900 dark:text-gray-100 mb-2'>
						Nenhuma imagem encontrada
					</h3>
					<p className='text-gray-500'>
						{searchTerm || selectedTags.length > 0
							? 'Tente ajustar os filtros de busca'
							: 'Faça upload de algumas imagens para começar'}
					</p>
				</div>
			) : (
				<div
					className={
						viewMode === 'grid'
							? 'grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-6 gap-4'
							: 'space-y-4'
					}
				>
					{images.map((image) => (
						<div
							key={image.id}
							className={`group relative border border-gray-200 dark:border-gray-700 rounded-lg overflow-hidden hover:shadow-lg transition-shadow ${
								viewMode === 'grid' ? 'aspect-square' : 'flex items-center p-4'
							}`}
						>
							{viewMode === 'grid' ? (
								<>
									<img
										src={image.image_url}
										alt={image.alt_text || image.image_name}
										className='w-full h-full object-cover cursor-pointer'
										onClick={() => handleImageClick(image)}
									/>
									<div className='absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-30 transition-opacity'>
										<div className='absolute bottom-2 left-2 right-2 text-white text-xs opacity-0 group-hover:opacity-100 transition-opacity'>
											<p className='truncate font-medium'>{image.image_name}</p>
											{image.alt_text && (
												<p className='text-gray-200 truncate'>
													{image.alt_text}
												</p>
											)}
										</div>
									</div>
									{selectionMode && (
										<input
											type='checkbox'
											checked={selectedImages.has(image.id)}
											onChange={() => toggleImageSelection(image.id)}
											className='absolute top-2 left-2 w-4 h-4'
										/>
									)}
								</>
							) : (
								<div className='flex items-center space-x-4 w-full'>
									<img
										src={image.image_url}
										alt={image.alt_text || image.image_name}
										className='w-16 h-16 object-cover rounded cursor-pointer'
										onClick={() => handleImageClick(image)}
									/>
									<div className='flex-1 min-w-0'>
										<h4 className='text-sm font-medium truncate'>
											{image.image_name}
										</h4>
										<p className='text-xs text-gray-500 truncate'>
											{image.alt_text}
										</p>
										<div className='flex items-center space-x-4 text-xs text-gray-400 mt-1'>
											<span className='flex items-center'>
												<Calendar className='w-3 h-3 mr-1' />
												{formatDate(image.created_at)}
											</span>
										</div>
										{image.tags.length > 0 && (
											<div className='flex flex-wrap gap-1 mt-2'>
												{image.tags.slice(0, 3).map((tag) => (
													<span
														key={tag}
														className='inline-flex items-center px-2 py-0.5 text-xs bg-gray-100 text-gray-700 rounded'
													>
														<Tag className='w-2 h-2 mr-1' />
														{tag}
													</span>
												))}
												{image.tags.length > 3 && (
													<span className='text-xs text-gray-500'>
														+{image.tags.length - 3} mais
													</span>
												)}
											</div>
										)}
									</div>
									<div className='flex items-center space-x-2'>
										<button
											onClick={() => handleImageClick(image)}
											className='p-1 text-gray-400 hover:text-blue-600'
											title='Visualizar'
										>
											<Eye className='w-4 h-4' />
										</button>
										<a
											href={image.image_url}
											download={image.image_name}
											className='p-1 text-gray-400 hover:text-green-600'
											title='Download'
										>
											<Download className='w-4 h-4' />
										</a>
									</div>
								</div>
							)}
						</div>
					))}
				</div>
			)}
		</div>
	);
}
