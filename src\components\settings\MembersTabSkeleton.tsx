import React from "react";
import { Skeleton } from "@/components/ui/skeleton";

export const MembersTabSkeleton: React.FC = () => {
	return (
		<div className='space-y-6'>
			{/* Header Skeleton */}
			<div className='flex items-center justify-between'>
				<div className='space-y-1'>
					<Skeleton className='h-6 w-32' />
					<Skeleton className='h-4 w-64' />
				</div>
				<Skeleton className='h-10 w-32' />
			</div>

			{/* Table Skeleton */}
			<div className='border rounded-lg'>
				<div className='p-4'>
					<div className='grid grid-cols-4 gap-4 mb-4'>
						<Skeleton className='h-4 w-16' />
						<Skeleton className='h-4 w-12' />
						<Skeleton className='h-4 w-16' />
						<Skeleton className='h-4 w-12' />
					</div>
					{[1, 2, 3].map((i) => (
						<div key={i} className='grid grid-cols-4 gap-4 py-3 border-t'>
							<Skeleton className='h-4 w-40' />
							<Skeleton className='h-6 w-16' />
							<Skeleton className='h-4 w-24' />
							<Skeleton className='h-8 w-8' />
						</div>
					))}
				</div>
			</div>
		</div>
	);
};
