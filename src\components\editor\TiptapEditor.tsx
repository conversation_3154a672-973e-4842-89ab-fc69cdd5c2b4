'use client';

import { useE<PERSON><PERSON>, EditorContent, type Editor } from '@tiptap/react';
import type { EditorView } from '@tiptap/pm/view';
import StarterKit from '@tiptap/starter-kit';
import TableRow from '@tiptap/extension-table-row';
import TableHeader from '@tiptap/extension-table-header';
import TableCell from '@tiptap/extension-table-cell';
import CustomTable from './extensions/CustomTable';
import Placeholder from '@tiptap/extension-placeholder';
import Link from '@tiptap/extension-link';
import React, { useEffect } from 'react';
import {
	Slash,
	SlashCmd,
	SlashCmdProvider,
	enableKeyboardNavigation,
} from '@harshtalks/slash-tiptap';

import MetadataNode from './extensions/MetadataNode';
import MetadataExtension from './extensions/MetadataExtension';
import CodeBlockNode from './extensions/CodeBlockNode';
import CustomHtmlNode from './extensions/CustomHtmlNode';
import CardNode from './extensions/CardNode';
import <PERSON>ListNode from './extensions/CardListNode';
import AccordionNode from './extensions/AccordionNode';
import AccordionGroupNode from './extensions/AccordionGroupNode';
import CalloutNode from './extensions/CalloutNode';
import ImageNode from './extensions/ImageNode';
import VideoNode from './extensions/VideoNode';
import StepsNode from './extensions/StepsNode';
import TabsNode from './extensions/TabsNode';
import TabIndentExtension from './extensions/TabIndentExtension';
import HintNode from './extensions/HintNode';
import ChecklistNode from './extensions/ChecklistNode';
import ParameterNode from './extensions/ParameterNode';
import APICardListNode from './extensions/APICardListNode';
import MermaidNode from './extensions/MermaidNode';
import './TiptapEditor.css';

import { mdxToHtml } from './utils/mdxToHtml';
import { getContextualSuggestions } from './utils/contextualSuggestions';
import { getCommandIcon } from './utils/commandIcons';
import BubbleMenu from './components/BubbleMenu';
import LinkDialog from './components/LinkDialog';
import HintDialog from './components/HintDialog';
import ShortcutsTip from './components/ShortcutsTip';
import type { TiptapEditorProps } from './types';

const TiptapEditor = ({
	content = '',
	editable = true,
	onUpdate,
}: TiptapEditorProps) => {
	const [isLinkDialogOpen, setIsLinkDialogOpen] = React.useState(false);
	const [linkDialogData, setLinkDialogData] = React.useState<{
		editor: Editor | null;
		selectedText: string;
		selectedUrl: string;
	}>({ editor: null, selectedText: '', selectedUrl: '' });

	const [isHintDialogOpen, setIsHintDialogOpen] = React.useState(false);
	const [hintDialogData, setHintDialogData] = React.useState<{
		editor: Editor | null;
		selectedText: string;
	}>({ editor: null, selectedText: '' });
	// Enhanced keyboard navigation that uses library's navigation and preserves Tab functionality
	const handleKeyDown = React.useCallback(
		(view: EditorView, event: KeyboardEvent) => {
			// First, let the library handle slash menu navigation
			const slashNavResult = enableKeyboardNavigation(event);
			if (slashNavResult) {
				return true; // Library handled the event
			}

			if (event.key === 'Enter') {
				const { state } = view;
				const { selection } = state;
				const { $from } = selection;

				const currentNode = $from.parent;
				const parentNode = $from.node(-1);

				// Check if in list item
				const inList = parentNode && parentNode.type.name === 'listItem';

				if (currentNode.type.name === 'paragraph' && currentNode.textContent.trim() === '') {
					if (inList) {
						// In empty list item, allow Enter to exit list
						return false;
					} else {
						// Block multiple empty lines outside lists
						event.preventDefault();
						return true;
					}
				}
			}

			// For all other cases, let TipTap and our extensions handle the keys
			// This includes Tab for indentation, which is handled by TabIndentExtension
			return false;
		},
		[]
	);
	const editor = useEditor({
		extensions: [
			StarterKit.configure({
				codeBlock: false, // Disable default code block
				hardBreak: false, // Disable hard breaks to prevent <br> tags
				listItem: {
					HTMLAttributes: {
						class: 'editor-list-item',
					},
				},
				bulletList: {
					HTMLAttributes: {
						class: 'editor-bullet-list',
					},
					keepMarks: true,
					keepAttributes: false,
				},
				orderedList: {
					HTMLAttributes: {
						class: 'editor-ordered-list',
					},
					keepMarks: true,
					keepAttributes: false,
				},
			}),
			Placeholder.configure({
				placeholder: 'Press / for commands...',
			}),
			Link.configure({
				openOnClick: false,
				HTMLAttributes: {
					class: 'editor-link',
				},
			}),
			Slash.configure({
				suggestion: {
					items: ({ editor }: { editor: Editor | null }) =>
						getContextualSuggestions(editor),
				},
			}),
			CustomTable.configure({
				resizable: false,
			}),
			TableRow,
			TableHeader,
			TableCell,
			MetadataNode,
			MetadataExtension,
			CodeBlockNode, // Use our custom code block
			CustomHtmlNode, // Custom HTML component
			CardNode,
			CardListNode,
			AccordionNode,
			AccordionGroupNode,
			CalloutNode,
			ImageNode,
			VideoNode,
			StepsNode,
			TabsNode,
			TabIndentExtension,
			HintNode,
			ChecklistNode,
			ParameterNode,
			APICardListNode,
			MermaidNode,
			// HardBreak, // Removed to prevent <br> tags on line breaks
		],
		content: content,
		editable: editable,
		onUpdate: async ({ editor }) => {
			if (onUpdate) {
				// Dynamic import to resolve module issue
				const { htmlToMdx } = await import('./utils/htmlToMdx');

				const originalHtml = editor.getHTML();
				const convertedMdx = await htmlToMdx(originalHtml);

				// Create a modified editor object with MDX output
				const modifiedEditor = {
					...editor,
					getHTML: async () => convertedMdx,
				};
				onUpdate(modifiedEditor as unknown as Editor);
			}
		},
		editorProps: {
			handleDOMEvents: {
				keydown: handleKeyDown,
			},
			attributes: {
				tabindex: '0',
			},
		},
		immediatelyRender: false,
	});

	useEffect(() => {
		const updateContent = async () => {
			if (!editor || !content) {
				return;
			}

			// Dynamic import to resolve module issue
			const { htmlToMdx } = await import('./utils/htmlToMdx');

			// Convert the editor's current HTML content to MDX for a correct comparison
			const currentMdx = await htmlToMdx(editor.getHTML());

			// Only update the editor if the incoming content is genuinely different
			if (currentMdx === content) {
				return;
			}

			// Use the utility function to convert MDX to HTML
			const processedContent = await mdxToHtml(content);

			// Use queueMicrotask to defer execution outside React lifecycle
			queueMicrotask(() => {
				// Set content without triggering another update to prevent loops
				editor.commands.setContent(processedContent, false);
			});
		};
		updateContent();
	}, [content, editor]);

	useEffect(() => {
		return () => {
			editor?.destroy();
		};
	}, [editor]);

	// Handle link dialog events
	useEffect(() => {
		const handleLinkDialog = (event: CustomEvent) => {
			const { editor: eventEditor, selectedText = '', selectedUrl = '' } = event.detail;
			setLinkDialogData({ editor: eventEditor, selectedText, selectedUrl });
			setIsLinkDialogOpen(true);
		};

		window.addEventListener(
			'openLinkDialog',
			handleLinkDialog as EventListener
		);

		return () => {
			window.removeEventListener(
				'openLinkDialog',
				handleLinkDialog as EventListener
			);
		};
	}, []);

	// Handle hint dialog events
	useEffect(() => {
		const handleHintDialog = (event: CustomEvent) => {
			const { editor: eventEditor, selectedText = '' } = event.detail;
			setHintDialogData({ editor: eventEditor, selectedText });
			setIsHintDialogOpen(true);
		};

		window.addEventListener(
			'openHintDialog',
			handleHintDialog as EventListener
		);

		return () => {
			window.removeEventListener(
				'openHintDialog',
				handleHintDialog as EventListener
			);
		};
	}, []);

	const handleLinkConfirm = (url: string, text: string) => {
		const { editor: dialogEditor, selectedText, selectedUrl } = linkDialogData;
		if (dialogEditor) {
			if (selectedText) {
				if (selectedUrl) {
					// Editing existing link
					dialogEditor
						.chain()
						.focus()
						.extendMarkRange('link')
						.setLink({ href: url })
						.insertContent(text)
						.run();
				} else {
					// Replace selected text with new link
					dialogEditor
						.chain()
						.focus()
						.extendMarkRange('link')
						.setLink({ href: url })
						.insertContent(text)
						.run();
				}
			} else {
				// Insert new link
				dialogEditor
					.chain()
					.focus()
					.insertContent(`<a href="${url}">${text}</a>`)
					.run();
			}
		}
		setIsLinkDialogOpen(false);
	};

	const handleHintConfirm = (text: string, hint: string) => {
		const { editor: dialogEditor, selectedText } = hintDialogData;
		if (dialogEditor) {
			if (selectedText) {
				// Replace selected text with hint
				dialogEditor.chain().focus().deleteSelection().run();
				dialogEditor.commands.setHint({ text, hint });
			} else {
				// Insert new hint
				dialogEditor.commands.setHint({ text, hint });
			}
		}
		setIsHintDialogOpen(false);
	};

	// Smart positioning for slash menu
	useEffect(() => {
		if (!editor) return;

		const observeSlashMenu = () => {
			const observer = new MutationObserver((mutations) => {
				mutations.forEach((mutation) => {
					mutation.addedNodes.forEach((node) => {
						if (node.nodeType === Node.ELEMENT_NODE) {
							const element = node as Element;
							const slashMenu =
								element.querySelector('[cmdk-root]') ||
								(element.hasAttribute && element.hasAttribute('cmdk-root')
									? element
									: null);

							if (slashMenu) {
								adjustSlashMenuPosition(slashMenu as HTMLElement);
							}
						}
					});
				});
			});

			observer.observe(document.body, {
				childList: true,
				subtree: true,
			});

			return () => observer.disconnect();
		};

		const adjustSlashMenuPosition = (menu: HTMLElement) => {
			// Small delay to ensure the menu is fully rendered
			setTimeout(() => {
				const rect = menu.getBoundingClientRect();
				const viewportHeight = window.innerHeight;
				const viewportWidth = window.innerWidth;

				const spaceBelow = viewportHeight - rect.bottom;
				const spaceAbove = rect.top;
				const spaceRight = viewportWidth - rect.right;
				const spaceLeft = rect.left;

				// Reset any previous positioning attributes
				menu.removeAttribute('data-position');

				// Check if menu goes below viewport
				if (spaceBelow < 20 && spaceAbove > spaceBelow) {
					menu.setAttribute('data-position', 'top');
				}

				// Check if menu goes beyond right edge
				if (spaceRight < 20 && spaceLeft > 340) {
					// 340 = menu width + some padding
					const currentPosition = menu.getAttribute('data-position') || '';
					menu.setAttribute('data-position', currentPosition + ' left');
				}

				// Ensure z-index is high enough
				menu.style.zIndex = '9999';

				// Add smooth transition
				menu.style.transition =
					'transform 0.2s ease-out, opacity 0.2s ease-out';
			}, 10);
		};

		const cleanup = observeSlashMenu();

		return cleanup;
	}, [editor]);

	// Handle click events on links
	useEffect(() => {
		if (!editor) return;

		const handleClick = (event: MouseEvent) => {
			const target = event.target as HTMLElement;
			
			// Check if clicked element is a link or inside a link
			const linkElement = target.closest('a[href]') as HTMLAnchorElement;
			if (linkElement) {
				// Immediately prevent all default behaviors
				event.preventDefault();
				event.stopPropagation();
				event.stopImmediatePropagation();
				
				// Get link attributes
				const href = linkElement.getAttribute('href') || '';
				const text = linkElement.textContent || '';
				
				// Set cursor position to the link
				const pos = editor.view.posAtDOM(linkElement, 0);
				editor.commands.setTextSelection(pos);
				
				// Open link dialog with current link data
				setLinkDialogData({ 
					editor, 
					selectedText: text, 
					selectedUrl: href 
				});
				setIsLinkDialogOpen(true);
				
				return false;
			}
		};

		// Add click listener to editor content with capture phase
		const editorElement = editor.view.dom;
		editorElement.addEventListener('click', handleClick, true);

		return () => {
			editorElement.removeEventListener('click', handleClick, true);
		};
	}, [editor]);

	// Função para focar o editor quando clicar em áreas vazias
	const handleContainerClick = (e: React.MouseEvent) => {
		if (editor && e.target === e.currentTarget) {
			editor.commands.focus('end');
		}
	};

	return (
		<SlashCmdProvider>
			<div className='prose dark:prose-invert max-w-none h-full overflow-y-auto relative'>
				<div className='max-w-[1024px] mx-auto'>
					<div
						className='min-h-[calc(100vh-110px)] pb-96 cursor-text'
						onClick={handleContainerClick}
					>
						<EditorContent
							editor={editor}
							className='min-h-[calc(100vh-110px)] focus-within:outline-none'
						/>
						{/* Espaçamento adicional para melhor experiência de scroll */}
						<div className='h-64 cursor-text' onClick={handleContainerClick} />
					</div>
					{editor && <BubbleMenu editor={editor} />}
					{editor && (
						<SlashCmd.Root editor={editor}>
							<SlashCmd.Cmd>
								<SlashCmd.Empty>No commands available</SlashCmd.Empty>
								<SlashCmd.List>
									{getContextualSuggestions(editor).map((item) => (
										<SlashCmd.Item
											value={item.title}
											onCommand={(val) => {
												item.command(val);
											}}
											key={item.title}
										>
											<div className='flex items-center w-full'>
												{getCommandIcon(item.title)}
												<span className='text-sm font-medium'>
													{item.title}
												</span>
											</div>
										</SlashCmd.Item>
									))}
								</SlashCmd.List>
							</SlashCmd.Cmd>
						</SlashCmd.Root>
					)}

					<LinkDialog
						isOpen={isLinkDialogOpen}
						onClose={() => setIsLinkDialogOpen(false)}
						onConfirm={handleLinkConfirm}
						initialText={linkDialogData.selectedText}
						initialUrl={linkDialogData.selectedUrl}
					/>

					<HintDialog
						isOpen={isHintDialogOpen}
						onClose={() => setIsHintDialogOpen(false)}
						onConfirm={handleHintConfirm}
						initialText={hintDialogData.selectedText}
					/>
				</div>

				{/* Shortcuts Tip */}
				<ShortcutsTip />
			</div>
		</SlashCmdProvider>
	);
};

export default TiptapEditor;
