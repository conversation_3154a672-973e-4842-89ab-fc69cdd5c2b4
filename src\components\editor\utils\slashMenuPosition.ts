// Smart positioning for slash menu
export const observeSlashMenu = () => {
  const observer = new MutationObserver((mutations) => {
    mutations.forEach((mutation) => {
      mutation.addedNodes.forEach((node) => {
        if (node.nodeType === Node.ELEMENT_NODE) {
          const element = node as Element;
          const slashMenu =
            element.querySelector("[cmdk-root]") ||
            (element.hasAttribute && element.hasAttribute("cmdk-root")
              ? element
              : null);

          if (slashMenu) {
            adjustSlashMenuPosition(slashMenu as HTMLElement);
          }
        }
      });
    });
  });

  observer.observe(document.body, {
    childList: true,
    subtree: true,
  });

  return () => observer.disconnect();
};

const adjustSlashMenuPosition = (menu: HTMLElement) => {
  // Small delay to ensure the menu is fully rendered
  setTimeout(() => {
    const rect = menu.getBoundingClientRect();
    const viewportHeight = window.innerHeight;
    const viewportWidth = window.innerWidth;

    const spaceBelow = viewportHeight - rect.bottom;
    const spaceAbove = rect.top;
    const spaceRight = viewportWidth - rect.right;
    const spaceLeft = rect.left;

    // Reset any previous positioning attributes
    menu.removeAttribute("data-position");

    // Check if menu goes below viewport
    if (spaceBelow < 20 && spaceAbove > spaceBelow) {
      menu.setAttribute("data-position", "top");
    }

    // Check if menu goes beyond right edge
    if (spaceRight < 20 && spaceLeft > 340) {
      // 340 = menu width + some padding
      const currentPosition = menu.getAttribute("data-position") || "";
      menu.setAttribute("data-position", currentPosition + " left");
    }

    // Ensure z-index is high enough
    menu.style.zIndex = "9999";

    // Add smooth transition
    menu.style.transition = "transform 0.2s ease-out, opacity 0.2s ease-out";
  }, 10);
};
