"use client";
import { Provider } from "@supabase/supabase-js";
import { oAuthSignIn } from "@/app/(auth)/login/actions";
import { Button } from "@/components/ui/button";
import { cn } from "@/lib/utils";
import { useState } from "react";
import github from "@/assets/images/providers/github.svg";
import slack from "@/assets/images/providers/slack.svg";
import google from "@/assets/images/providers/google.svg";
import Image from "next/image";

const oAuthConfig = {
	github: {
		name: "github" as Provider,
		displayName: "GitHub",
		icon: (
			<Image src={github} alt={"Github Icon"} priority className='w-5 h-5' />
		),
	},
	google: {
		name: "google" as Provider,
		displayName: "Google",
		icon: (
			<Image src={google} alt={"Google Icon"} priority className='w-5 h-5' />
		),
	},
	slack: {
		name: "slack" as Provider,
		displayName: "Slack",
		icon: <Image src={slack} alt={"Slack Icon"} priority className='w-5 h-5' />,
	},
};

interface OAuthButtonProps {
	operation: "login" | "signup";
	provider: keyof typeof oAuthConfig;
	iconOnly?: boolean;
	className?: string;
}

export function OAuthButtons({
	operation,
	provider,
	iconOnly,
	className,
}: OAuthButtonProps) {
	const config = oAuthConfig[provider];
	const [isLoading, setIsLoading] = useState(false);

	return (
		<Button
			form=''
			className={cn(
				"bg-white hover:bg-gray-50 text-black border border-[#CAD3FF] overflow-hidden relative",
				iconOnly ? "p-2" : "flex gap-2",
				provider === "slack" || provider === "google"
					? "cursor-not-allowed opacity-50"
					: "cursor-pointer",
				className
			)}
			disabled={isLoading || provider === "slack" || provider === "google"}
			onClick={async () => {
				if (provider === "slack" || provider === "google") return;
				try {
					setIsLoading(true);
					const result = await oAuthSignIn(config.name);
					if (result?.url) {
						window.location.href = result.url;
					} else {
						console.error("OAuth URL not found in the result", result);
						setIsLoading(false);
					}
				} catch (error) {
					console.error(`Erro ao autenticar com ${config.displayName}:`, error);
					setIsLoading(false);
				} finally {
				}
			}}
		>
			<div
				className='relative flex items-center justify-center'
				style={{
					width: iconOnly ? "24px" : "24px",
					height: iconOnly ? "24px" : "24px",
					minWidth: "24px",
				}}
			>
				<div
					className={cn(
						"absolute inset-0 flex items-center justify-center transition-all duration-500 ease-in-out transform",
						isLoading
							? "opacity-0 scale-0 -rotate-180"
							: "opacity-100 scale-100 rotate-0 delay-150"
					)}
				>
					{config.icon}
				</div>

				<div
					className={cn(
						"absolute inset-0 flex items-center justify-center transition-all duration-500 ease-in-out transform",
						isLoading ? "opacity-100 scale-100 delay-150" : "opacity-0 scale-0"
					)}
				>
					<div className='animate-spin h-5 w-5 border-2 border-b-transparent rounded-full border-black' />
				</div>
			</div>

			{!iconOnly && (
				<span
					className={cn(
						"transition-all duration-300 ease-in-out ml-2 flex-1 transform",
						isLoading ? "translate-x-1 opacity-70" : "translate-x-0 opacity-100"
					)}
				>
					{isLoading
						? "Processando..."
						: operation === "login"
						? `Continue with ${config.displayName}`
						: `Sign Up with ${config.displayName}`}
				</span>
			)}
		</Button>
	);
}
