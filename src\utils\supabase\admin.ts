import { createClient as createSupabaseClient } from "@supabase/supabase-js";

export async function createAdminClient() {
	// Obter variáveis de ambiente
	const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
	const serviceKey = process.env.NEXT_SUPABASE_SERVICE_ROLE_KEY;

	if (!supabaseUrl) {
		throw new Error(
			"Supabase URL not configured. Please set NEXT_PUBLIC_SUPABASE_URL environment variable"
		);
	}

	if (!serviceKey) {
		throw new Error(
			"Supabase Service Role Key not configured. Please set NEXT_SUPABASE_SERVICE_ROLE_KEY environment variable"
		);
	}

	// Criando um cliente Supabase diretamente com a service role
	return createSupabaseClient(supabaseUrl, serviceKey, {
		auth: {
			autoRefreshToken: false,
			persistSession: false,
		},
	});
}
