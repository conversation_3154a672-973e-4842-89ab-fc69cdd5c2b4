"use client";

import React from "react";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";

interface ModernAlertDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  title: string;
  description: string;
  confirmText?: string;
  cancelText?: string;
  onConfirm: () => void;
  onCancel?: () => void;
  variant?: "danger" | "warning" | "info" | "success";
  icon?: React.ReactNode;
  warningMessage?: {
    title: string;
    description: string;
  };
}

const variantStyles = {
  danger: {
    iconColor: "text-red-500",
    confirmButton: "bg-red-500 hover:bg-red-600",
  },
  warning: {
    iconColor: "text-orange-500",
    confirmButton: "bg-orange-500 hover:bg-orange-600",
  },
  info: {
    iconColor: "text-blue-500",
    confirmButton: "bg-blue-500 hover:bg-blue-600",
  },
  success: {
    iconColor: "text-green-500",
    confirmButton: "bg-green-500 hover:bg-green-600",
  },
};

const defaultIcons = {
  danger: (
    <svg
      className="w-6 h-6"
      fill="none"
      stroke="currentColor"
      viewBox="0 0 24 24"
    >
      <path
        strokeLinecap="round"
        strokeLinejoin="round"
        strokeWidth={2}
        d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"
      />
    </svg>
  ),
  warning: (
    <svg
      className="w-6 h-6"
      fill="none"
      stroke="currentColor"
      viewBox="0 0 24 24"
    >
      <path
        strokeLinecap="round"
        strokeLinejoin="round"
        strokeWidth={2}
        d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.732-.833-2.5 0L4.314 14.5c-.77.833.192 2.5 1.732 2.5z"
      />
    </svg>
  ),
  info: (
    <svg
      className="w-6 h-6"
      fill="none"
      stroke="currentColor"
      viewBox="0 0 24 24"
    >
      <path
        strokeLinecap="round"
        strokeLinejoin="round"
        strokeWidth={2}
        d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
      />
    </svg>
  ),
  success: (
    <svg
      className="w-6 h-6"
      fill="none"
      stroke="currentColor"
      viewBox="0 0 24 24"
    >
      <path
        strokeLinecap="round"
        strokeLinejoin="round"
        strokeWidth={2}
        d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"
      />
    </svg>
  ),
};

export default function ModernAlertDialog({
  open,
  onOpenChange,
  title,
  description,
  confirmText = "Confirm",
  cancelText = "Cancel",
  onConfirm,
  onCancel,
  variant = "info",
  icon,
  warningMessage,
}: ModernAlertDialogProps) {
  const styles = variantStyles[variant];
  const defaultIcon = defaultIcons[variant];

  const handleCancel = () => {
    if (onCancel) {
      onCancel();
    } else {
      onOpenChange(false);
    }
  };

  const handleConfirm = () => {
    onConfirm();
    onOpenChange(false);
  };

  return (
    <AlertDialog open={open} onOpenChange={onOpenChange}>
      <AlertDialogContent className="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg shadow-lg max-w-md p-0">
        {/* Header com ícone e título na mesma linha */}
        <AlertDialogHeader className="p-6 pb-4">
          <div className="flex items-center space-x-3">
            <div className={styles.iconColor}>{icon || defaultIcon}</div>
            <AlertDialogTitle className="text-lg font-semibold text-gray-900 dark:text-gray-100">
              {title}
            </AlertDialogTitle>
          </div>
        </AlertDialogHeader>

        {/* Linha divisória */}
        <div className="border-t border-gray-200 dark:border-gray-700"></div>

        {/* Conteúdo */}
        <div className="p-6 pt-4">
          <AlertDialogDescription className="text-sm text-gray-600 dark:text-gray-400 mb-4">
            {description}
          </AlertDialogDescription>

          {warningMessage && (
            <div className="p-3 bg-gray-50 dark:bg-gray-700/50 rounded-md">
              <p className="text-sm font-medium text-gray-900 dark:text-gray-100">
                {warningMessage.title}
              </p>
              <p className="text-xs text-gray-600 dark:text-gray-400 mt-1">
                {warningMessage.description}
              </p>
            </div>
          )}
        </div>

        {/* Footer com botões */}
        <AlertDialogFooter className="p-6 pt-0 flex gap-3">
          <AlertDialogCancel
            onClick={handleCancel}
            className="flex-1 bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-600 h-10 px-4 rounded-md"
          >
            {cancelText}
          </AlertDialogCancel>
          <AlertDialogAction
            onClick={handleConfirm}
            className={`flex-1 ${styles.confirmButton} text-white h-10 px-4 rounded-md font-medium`}
          >
            {confirmText}
          </AlertDialogAction>
        </AlertDialogFooter>
      </AlertDialogContent>
    </AlertDialog>
  );
}
