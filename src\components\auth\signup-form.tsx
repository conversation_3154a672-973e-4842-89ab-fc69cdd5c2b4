"use client";

import { useEffect, useState } from "react";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { PasswordInput } from "@/components/auth/password-input";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
} from "@/components/ui/form";
import { zodResolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import { z } from "zod";
import { signup, checkEmailExists } from "@/app/(auth)/signup/actions";
import { useToast } from "@/components/ToastProvider";
import { OTPVerification } from "@/components/auth/otp-verification";
import { passwordSchema, emailSchema, nameSchema } from "@/lib/schemas";

const signUpSchema = z.object({
  name: nameSchema,
  email: emailSchema,
  password: passwordSchema,
});

export function SignUpForm() {
  const [isSigningUp, setIsSigningUp] = useState(false);
  const [showOTP, setShowOTP] = useState(false);
  const [email, setEmail] = useState("");
  const [isEmailAvailable, setIsEmailAvailable] = useState(false);
  const [checkingEmail, setCheckingEmail] = useState(false);
  const { addToast } = useToast();
  const [formData, setFormData] = useState<{ name: string; email: string }>();

  const form = useForm<z.infer<typeof signUpSchema>>({
    resolver: zodResolver(signUpSchema),
    defaultValues: {
      name: "",
      email: "",
      password: "",
    },
  });

  useEffect(() => {
    const password = form.watch("password");
    const TOAST_ID = "password-requirements";

    const checks = [
      { test: password.length >= 8, message: "At least 8 characters" },
      { test: /[A-Z]/.test(password), message: "At least 1 capital letter" },
      { test: /[a-z]/.test(password), message: "At least 1 lower case letter" },
      { test: /[0-9]/.test(password), message: "At least 1 number" },
      {
        test: /[!@#$%^&*\-_]/.test(password),
        message: "At least 1 special character (!@#$%^&*_-)",
      },
    ];

    if (password) {
      const failedChecks = checks.filter((check) => !check.test);
      const allValid = failedChecks.length === 0;

      if (allValid) {
        addToast(
          "✓ All requirements met!",
          "success",
          "Password Requirements",
          TOAST_ID
        );
      } else {
        const message = failedChecks
          .map((check) => `• ${check.message}`)
          .join("\n");
        addToast(message, "info", "Password Requirements", TOAST_ID);
      }
    }
  }, [form.watch("password"), addToast]);

  useEffect(() => {
    const { errors } = form.formState;
    Object.entries(errors).forEach(([field, error]) => {
      if (error?.message) {
        addToast(error.message, "error", `Invalid ${field}`);
      }
    });
  }, [form.formState.errors, addToast]);

  useEffect(() => {
    const emailValue = form.watch("email");
    const TOAST_ID = "email-check";
    const emailIsValidFormat = z.string().email().safeParse(emailValue).success;

    const checkEmail = async () => {
      setCheckingEmail(true);
      form.clearErrors("email");
      try {
        const exists = await checkEmailExists(emailValue);
        if (exists) {
          form.setError("email", {
            type: "manual",
            message: "This email is already registered",
          });
          addToast(
            "This email is already registered",
            "error",
            "Email",
            TOAST_ID
          );
          setIsEmailAvailable(false);
        } else {
          setIsEmailAvailable(true);
        }
      } catch (error) {
        console.error("Error checking email:", error);
      } finally {
        setCheckingEmail(false);
      }
    };

    if (emailValue && emailIsValidFormat) {
      const timeoutId = setTimeout(checkEmail, 500);
      return () => clearTimeout(timeoutId);
    } else {
      setIsEmailAvailable(false);
      setCheckingEmail(false);
    }
  }, [form.watch("email")]);

  async function onSubmit(values: z.infer<typeof signUpSchema>) {
    const TOAST_ID = "email-check";
    setIsSigningUp(true);
    setFormData({ name: values.name, email: values.email });

    if (checkingEmail) {
      addToast("Please wait while we verify your email", "info");
      setIsSigningUp(false);
      return;
    }

    try {
      const exists = await checkEmailExists(values.email);
      if (exists) {
        form.setError("email", {
          type: "manual",
          message: "This email is already registered",
        });
        addToast(
          "This email is already registered",
          "error",
          "Email",
          TOAST_ID
        );
        setIsEmailAvailable(false);
        setIsSigningUp(false);
        return;
      }

      if (!exists) {
        const formData = new FormData();
        Object.entries(values).forEach(([key, value]) => {
          formData.append(key, value);
        });

        await signup(formData);
        setEmail(values.email);
        setShowOTP(true);
      }
    } catch (error) {
      console.error("Error verifying email:", error);
      addToast("Error verifying email", "error");
    } finally {
      setIsSigningUp(false);
    }
  }

  if (showOTP) {
    return (
      <div className="fixed inset-0 z-50 flex items-center justify-center bg-black/50 backdrop-blur-sm">
        <OTPVerification
          email={email}
          onBack={() => {
            setShowOTP(false);
            if (formData) {
              form.setValue("name", formData.name);
              form.setValue("email", formData.email);
            }
          }}
        />
      </div>
    );
  }

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className="grid gap-3">
        <FormField
          control={form.control}
          name="name"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Name</FormLabel>
              <FormControl>
                <Input
                  type="text"
                  placeholder="Name"
                  className={
                    form.formState.errors.name &&
                    "border-red-500 focus-visible:ring-red-500"
                  }
                  {...field}
                />
              </FormControl>
            </FormItem>
          )}
        />
        <FormField
          control={form.control}
          name="email"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Email</FormLabel>
              <FormControl>
                <Input
                  type="email"
                  placeholder="<EMAIL>"
                  className={
                    checkingEmail
                      ? "animate-pulse border-yellow-500 focus-visible:ring-yellow-500 transition-colors"
                      : form.formState.errors.email
                      ? "border-red-500 focus-visible:ring-red-500 transition-colors"
                      : isEmailAvailable
                      ? "border-green-500 focus-visible:ring-green-500 transition-colors"
                      : "transition-colors"
                  }
                  {...field}
                />
              </FormControl>
            </FormItem>
          )}
        />
        <FormField
          control={form.control}
          name="password"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Password</FormLabel>
              <FormControl>
                <PasswordInput
                  placeholder="Your Password"
                  className={
                    form.formState.errors.password &&
                    "border-red-500 focus-visible:ring-red-500"
                  }
                  {...field}
                />
              </FormControl>
            </FormItem>
          )}
        />
        <Button
          type="submit"
          className="my-1.5 bg-wd-blue hover:bg-wd-blueDark"
        >
          {isSigningUp ? "Signing up..." : "Sign Up"}
        </Button>
      </form>
    </Form>
  );
}
