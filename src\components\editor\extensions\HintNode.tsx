import { Node, mergeAttributes } from '@tiptap/core';
import { ReactNodeView<PERSON><PERSON>er, NodeViewProps } from '@tiptap/react';
import React from 'react';
import { NodeViewWrapper } from '@tiptap/react';

declare module '@tiptap/core' {
	interface Commands<ReturnType> {
		hintNode: {
			/**
			 * Insert hint with the specified attributes.
			 */
			setHint: (attributes: HintAttrs) => ReturnType;
		};
	}
}

interface HintAttrs {
	text: string;
	hint: string;
}

const HintComponent: React.FC<NodeViewProps> = ({ node }) => {
	const { text, hint } = node.attrs;
	const [showTooltip, setShowTooltip] = React.useState(false);

	// Calculate tooltip width based on text length and hint length
	const textLength = text.length;
	const hintLength = hint.length;

	const getTooltipWidth = () => {
		// Use the longer of the two as base, with minimum and maximum limits
		const baseLength = Math.max(textLength, Math.min(hintLength, 50)); // Max 50 chars for single line
		const minWidth = Math.max(textLength * 8, 120); // At least as wide as the text
		const maxWidth = Math.min(baseLength * 8, 400); // Max 400px

		return Math.max(minWidth, maxWidth);
	};

	return (
		<NodeViewWrapper className='hint-node inline-block'>
			<span
				className='hint-text cursor-help underline decoration-dotted decoration-blue-500 hover:bg-blue-50 dark:hover:bg-blue-900/20 px-1 py-0.5 rounded transition-colors relative'
				data-hint={hint}
				onMouseEnter={() => setShowTooltip(true)}
				onMouseLeave={() => setShowTooltip(false)}
				style={{
					textDecoration: 'underline',
					textDecorationStyle: 'dotted',
					textDecorationColor: '#3b82f6',
				}}
			>
				{text}
				{showTooltip && hint && (
					<span
						className='hint-tooltip'
						style={{
							width: `${getTooltipWidth()}px`,
							whiteSpace: hintLength <= 50 ? 'nowrap' : 'normal',
						}}
					>
						{hint}
					</span>
				)}
			</span>
		</NodeViewWrapper>
	);
};

export const HintNode = Node.create({
	name: 'hintNode',

	group: 'inline',

	inline: true,

	atom: false,

	addAttributes() {
		return {
			text: {
				default: '',
				parseHTML: (element) =>
					element.getAttribute('data-text') || element.textContent || '',
				renderHTML: (attributes) => ({
					'data-text': attributes.text,
				}),
			},
			hint: {
				default: '',
				parseHTML: (element) =>
					element.getAttribute('data-hint') ||
					element.getAttribute('hint') ||
					'',
				renderHTML: (attributes) => ({
					'data-hint': attributes.hint,
					hint: attributes.hint,
				}),
			},
		};
	},

	parseHTML() {
		return [
			{
				tag: 'span[data-type="hint"]',
				getAttrs: (element) => {
					const hint =
						element.getAttribute('data-hint') ||
						element.getAttribute('hint') ||
						'';
					const text =
						element.getAttribute('data-text') || element.textContent || '';
					return { hint, text };
				},
			},
			{
				tag: 'hint',
				getAttrs: (element) => {
					const hint = element.getAttribute('hint') || '';
					const text = element.textContent || '';
					return { hint, text };
				},
			},
		];
	},

	renderHTML({ HTMLAttributes, node }) {
		const { text, hint } = node.attrs;
		return [
			'span',
			mergeAttributes(HTMLAttributes, {
				'data-type': 'hint',
				'data-text': text,
				'data-hint': hint,
				hint: hint,
				class: 'hint-node',
			}),
			text || 0,
		];
	},

	addNodeView() {
		return ReactNodeViewRenderer(HintComponent);
	},

	addCommands() {
		return {
			setHint:
				(attributes: HintAttrs) =>
				({ commands }) => {
					return commands.insertContent({
						type: this.name,
						attrs: attributes,
					});
				},
		};
	},
});

export default HintNode;
