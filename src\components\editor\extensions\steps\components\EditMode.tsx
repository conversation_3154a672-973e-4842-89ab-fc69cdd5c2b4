import React, { useState, useCallback, useMemo } from "react";
import { Edit3, Save, X, Plus, Trash2 } from "lucide-react";
import { Button } from "@/components/ui/button";
import { useToast } from "@/components/ToastProvider";
import { EditModeProps, EditModeStepProps, FlatStep } from "../types";
import {
  flattenSteps,
  reconstructSteps,
  getDescendantIds,
  moveStepWithDescendants,
  moveStepToEndAsRoot,
} from "../utils";

const EditModeStep: React.FC<EditModeStepProps> = ({
  step,
  index,
  onDragStart,
  onDragOver,
  onDrop,
  onDragEnd,
  isDragging,
  isDragOver,
  onEdit,
  onDelete,
  descendantCount = 0,
}) => {
  if (!step || !step.id || typeof step.level !== "number") {
    console.warn("Invalid step data in EditModeStep:", step);
    return null;
  }

  const indentation = (step.level || 0) * 20;

  const getDropPreview = (
    stepLevel: number
  ): { type: "child" | "sibling" | "root"; message: string } => {
    if (stepLevel < 4) {
      return {
        type: "child",
        message: `<PERSON> become child of "${step.title || `Step ${index + 1}`}"`,
      };
    } else {
      return {
        type: "sibling",
        message: `Will become sibling (max depth reached)`,
      };
    }
  };

  return (
    <div className="relative">
      {isDragOver && (
        <>
          <div className="absolute -top-2 left-0 right-0 h-1 bg-gradient-to-r from-transparent via-blue-500 to-transparent rounded-full z-10 opacity-80" />
          <div className="absolute -top-8 left-0 right-0 z-20">
            <div className="flex items-center justify-center">
              <div className="bg-blue-600 text-white px-3 py-1 rounded-full text-xs font-medium shadow-lg">
                {getDropPreview(step.level).message}
              </div>
            </div>
          </div>
        </>
      )}

      <div
        className={`
					flex items-center gap-3 p-3 bg-white dark:bg-slate-800 border border-gray-200 dark:border-gray-700 
					rounded-lg cursor-move transition-all duration-200 group relative
					${
            isDragging
              ? "opacity-60 scale-98 shadow-lg bg-blue-50 dark:bg-blue-900/20 border-blue-300 dark:border-blue-600"
              : "hover:bg-gray-50 dark:hover:bg-slate-700"
          }
					${
            isDragOver
              ? "bg-blue-50/50 dark:bg-blue-900/10 border-blue-300 dark:border-blue-600 transform scale-102"
              : ""
          }
				`}
        style={{ marginLeft: `${indentation}px` }}
        draggable
        onDragStart={(e) => onDragStart(e, index)}
        onDragOver={(e) => onDragOver(e, index)}
        onDrop={(e) => onDrop(e, index)}
        onDragEnd={(e) => onDragEnd(e)}
      >
        {isDragOver && (
          <div className="absolute left-0 top-0 bottom-0 w-1 bg-gradient-to-b from-blue-400 via-blue-500 to-blue-600 rounded-l-lg"></div>
        )}

        <div className="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 transition-colors duration-200">
          <svg
            className="w-4 h-4"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M4 8h16M4 16h16"
            />
          </svg>
        </div>

        <div className="flex-1 min-w-0">
          <div className="flex items-center gap-2">
            <span className="text-xs font-medium text-gray-500 dark:text-gray-400">
              {step.titleSize.toUpperCase()}
            </span>
            <span className="text-sm font-semibold text-gray-900 dark:text-gray-100 truncate">
              {step.title || `Step ${index + 1}`}
            </span>
            {descendantCount > 0 && (
              <span className="inline-flex items-center px-1.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-300">
                +{descendantCount} sub-step{descendantCount !== 1 ? "s" : ""}
              </span>
            )}
            {isDragOver && (
              <span className="inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-300 animate-pulse">
                📥 Drop Target
              </span>
            )}
          </div>
          <div className="text-xs text-gray-600 dark:text-gray-400 truncate mt-1">
            Level {step.level} {step.parentId ? "• Sub-step" : "• Root step"}
            {descendantCount > 0 && (
              <span className="text-blue-600 dark:text-blue-400 font-medium ml-2">
                • Will move with children
              </span>
            )}
            {isDragOver && (
              <span className="text-green-600 dark:text-green-400 font-medium ml-2 animate-pulse">
                • Will accept as child step
              </span>
            )}
          </div>
        </div>

        <div className="flex items-center gap-1 opacity-0 group-hover:opacity-100 transition-opacity duration-200">
          <Button
            variant="ghost"
            size="sm"
            onClick={(e) => {
              e.stopPropagation();
              onEdit(step);
            }}
            className="h-7 px-2 text-xs hover:bg-blue-100 dark:hover:bg-blue-900/30 text-blue-600 dark:text-blue-400"
          >
            <Edit3 className="w-3 h-3" />
          </Button>
          <Button
            variant="ghost"
            size="sm"
            onClick={(e) => {
              e.stopPropagation();
              onDelete(step.id);
            }}
            className="h-7 px-2 text-xs hover:bg-red-100 dark:hover:bg-red-900/30 text-red-600 dark:text-red-400"
          >
            <Trash2 className="w-3 h-3" />
          </Button>
        </div>
      </div>

      {isDragOver && (
        <div className="absolute -bottom-2 left-0 right-0 h-1 bg-gradient-to-r from-transparent via-blue-500 to-transparent rounded-full z-10 opacity-80" />
      )}
    </div>
  );
};

export const EditMode: React.FC<EditModeProps> = ({
  steps,
  onSave,
  onCancel,
  onEditStep,
}) => {
  const [flatSteps, setFlatSteps] = useState<FlatStep[]>(() => {
    try {
      if (!Array.isArray(steps)) {
        console.warn("Invalid steps data in EditMode:", steps);
        return [];
      }
      return flattenSteps(steps);
    } catch (error) {
      console.error("Error flattening steps in EditMode:", error);
      return [];
    }
  });
  const [draggedIndex, setDraggedIndex] = useState<number | null>(null);
  const [dragOverIndex, setDragOverIndex] = useState<number | null>(null);
  const { addToast } = useToast();

  const stepsWithDescendants = useMemo(() => {
    return flatSteps.map((step) => {
      const descendantIds = getDescendantIds(step, flatSteps);
      return {
        ...step,
        descendantCount: descendantIds.length,
        hasDescendants: descendantIds.length > 0,
      };
    });
  }, [flatSteps]);

  const handleDragStart = useCallback(
    (e: React.DragEvent, index: number) => {
      e.stopPropagation();
      setDraggedIndex(index);
      e.dataTransfer.effectAllowed = "move";
      e.dataTransfer.setData("text/plain", index.toString());

      const step = flatSteps[index];
      const descendantCount = getDescendantIds(step, flatSteps).length;
      if (descendantCount > 0) {
        addToast(
          `Moving step with ${descendantCount} sub-step${
            descendantCount !== 1 ? "s" : ""
          }`,
          "info"
        );
      }
    },
    [flatSteps, addToast]
  );

  const handleDragOver = useCallback(
    (e: React.DragEvent, targetIndex: number) => {
      e.preventDefault();
      e.stopPropagation();
      e.dataTransfer.dropEffect = "move";
      setDragOverIndex(targetIndex);
    },
    []
  );

  const handleDrop = useCallback(
    (e: React.DragEvent, targetIndex: number) => {
      e.preventDefault();
      e.stopPropagation();
      setDragOverIndex(null);

      if (draggedIndex === null || draggedIndex === targetIndex) {
        setDraggedIndex(null);
        return;
      }

      const draggedStep = flatSteps[draggedIndex];
      const originalLevel = draggedStep.level;

      let targetStep: FlatStep | null = null;

      if (targetIndex < flatSteps.length && targetIndex >= 0) {
        const potentialParent = flatSteps[targetIndex];

        if (potentialParent.id !== draggedStep.id) {
          const draggedDescendants = getDescendantIds(draggedStep, flatSteps);
          if (!draggedDescendants.includes(potentialParent.id)) {
            targetStep = potentialParent;
          }
        }
      }

      if (!targetStep && targetIndex > 0) {
        const stepsExcludingDragged = flatSteps.filter(
          (s) => s.id !== draggedStep.id
        );
        const adjustedTargetIndex = Math.min(
          targetIndex - 1,
          stepsExcludingDragged.length - 1
        );

        if (
          adjustedTargetIndex >= 0 &&
          stepsExcludingDragged[adjustedTargetIndex]
        ) {
          targetStep = stepsExcludingDragged[adjustedTargetIndex];
        }
      }

      const newFlatSteps = moveStepWithDescendants(
        flatSteps,
        draggedIndex,
        targetIndex,
        false,
        targetStep
      );

      const movedStep = newFlatSteps.find((step) => step.id === draggedStep.id);
      const newLevel = movedStep?.level || 0;

      setFlatSteps(newFlatSteps);
      setDraggedIndex(null);

      if (newLevel > originalLevel) {
        addToast(
          `"${draggedStep.title}" is now a child of "${
            targetStep?.title || "target"
          }"`,
          "success"
        );
      } else if (newLevel < originalLevel) {
        addToast(`"${draggedStep.title}" moved to a higher level`, "success");
      } else {
        addToast(`"${draggedStep.title}" reordered at same level`, "success");
      }
    },
    [flatSteps, draggedIndex, addToast]
  );

  const handleDragEnd = useCallback((e?: React.DragEvent) => {
    if (e) {
      e.stopPropagation();
    }
    setDraggedIndex(null);
    setDragOverIndex(null);
  }, []);

  const handleDelete = useCallback(
    (stepId: string) => {
      const stepIndex = flatSteps.findIndex((step) => step.id === stepId);
      if (stepIndex === -1) return;

      const step = flatSteps[stepIndex];
      const descendantIds = getDescendantIds(step, flatSteps);

      if (descendantIds.length > 0) {
        if (
          !confirm(
            `This will delete the step and its ${
              descendantIds.length
            } sub-step${descendantIds.length !== 1 ? "s" : ""}. Continue?`
          )
        ) {
          return;
        }
      }

      const newFlatSteps = flatSteps.filter(
        (s) => s.id !== stepId && !descendantIds.includes(s.id)
      );

      setFlatSteps(newFlatSteps);
      addToast(
        descendantIds.length > 0
          ? `Step and ${descendantIds.length} sub-steps deleted`
          : "Step deleted successfully",
        "info"
      );
    },
    [flatSteps, addToast]
  );

  const handleSave = useCallback(() => {
    try {
      const validFlatSteps = flatSteps.filter(
        (step) => step && step.id && typeof step.level === "number"
      );
      const reconstructed = reconstructSteps(validFlatSteps);
      onSave(reconstructed);
      addToast("Steps order saved successfully", "success");
    } catch (error) {
      console.error("Error saving steps order:", error);
      addToast("Error saving steps order", "error");
    }
  }, [flatSteps, onSave, addToast]);

  const handleAddStep = useCallback(() => {
    const newStep: FlatStep = {
      id: Date.now().toString(),
      title: "New Step",
      content: "<p>Content for the new step.</p>",
      titleSize: "h3",
      level: 0,
      subSteps: [],
    };
    setFlatSteps((prev) => [...prev, newStep]);
    addToast("New step added", "success");
  }, [addToast]);

  return (
    <div className="my-4 p-4 bg-white dark:bg-slate-800 border-2 border-blue-400/50 dark:border-blue-600/50 rounded-lg shadow-lg">
      <div className="flex items-center justify-between mb-4">
        <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100">
          Edit Steps Order
        </h3>
        <div className="flex items-center gap-2">
          <Button
            onClick={handleAddStep}
            size="sm"
            className="h-8 px-3 text-xs bg-green-600 hover:bg-green-700 text-white"
          >
            <Plus className="w-3 h-3 mr-1" />
            Add Step
          </Button>
          <Button
            variant="ghost"
            size="sm"
            onClick={onCancel}
            className="h-8 px-3 text-xs hover:bg-red-100/50 dark:hover:bg-red-900/30 text-red-600 dark:text-red-400"
          >
            <X className="w-3 h-3 mr-1" />
            Cancel
          </Button>
          <Button
            variant="default"
            size="sm"
            onClick={handleSave}
            className="h-8 px-3 text-xs bg-blue-600 hover:bg-blue-700 text-white"
          >
            <Save className="w-3 h-3 mr-1" />
            Save Order
          </Button>
        </div>
      </div>

      <div className="mb-4 p-3 bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg">
        <p className="text-sm text-blue-800 dark:text-blue-200">
          <strong>Drag and drop</strong> to reorder steps:
        </p>
        <ul className="list-disc list-inside text-xs text-blue-700 dark:text-blue-300 mt-2 space-y-1">
          <li>Parent steps move with all their children</li>
          <li>
            <strong>Any step dropped on another step becomes its child</strong>
          </li>
          <li>Visual indicators show where the step will be placed</li>
          <li>
            Drop in the &quot;Drop here to move to end&quot; zone to make any
            step a root-level step
          </li>
        </ul>
      </div>

      <div className="space-y-2">
        {stepsWithDescendants
          .filter((step) => step && step.id && typeof step.level === "number")
          .map((step, index) => (
            <EditModeStep
              key={step.id}
              step={step}
              index={index}
              onDragStart={handleDragStart}
              onDragOver={handleDragOver}
              onDrop={handleDrop}
              onDragEnd={handleDragEnd}
              isDragging={draggedIndex === index}
              isDragOver={
                dragOverIndex === index &&
                draggedIndex !== null &&
                draggedIndex !== index
              }
              onEdit={onEditStep}
              onDelete={handleDelete}
              descendantCount={step.descendantCount}
            />
          ))}

        {draggedIndex !== null && (
          <div
            className="h-12 flex items-center justify-center border-2 border-dashed border-blue-300 dark:border-blue-500 rounded-lg bg-blue-50/50 dark:bg-blue-900/10 transition-all duration-200 hover:bg-blue-100/50 dark:hover:bg-blue-900/20"
            onDragOver={(e) => {
              e.preventDefault();
              e.stopPropagation();
              setDragOverIndex(flatSteps.length);
            }}
            onDragLeave={() => setDragOverIndex(null)}
            onDrop={(e) => {
              e.preventDefault();
              e.stopPropagation();
              setDragOverIndex(null);

              if (draggedIndex === null) return;

              const newFlatSteps = moveStepToEndAsRoot(flatSteps, draggedIndex);
              setFlatSteps(newFlatSteps);
              setDraggedIndex(null);

              addToast("Step moved to end as root-level step", "success");
            }}
          >
            <div className="flex items-center space-x-2 text-blue-600 dark:text-blue-400">
              <svg
                className="w-4 h-4"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M19 14l-7 7m0 0l-7-7m7 7V3"
                />
              </svg>
              <span className="text-sm font-medium">
                Drop here to move to end as root step
              </span>
            </div>
          </div>
        )}

        {flatSteps.length === 0 && (
          <div className="text-center py-8 text-gray-500 dark:text-gray-400">
            No steps yet. Click &quot;Add Step&quot; to get started.
          </div>
        )}
      </div>
    </div>
  );
};
