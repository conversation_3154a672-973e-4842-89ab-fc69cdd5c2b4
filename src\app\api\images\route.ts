import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@/utils/supabase/server';

export const runtime = 'edge';

// GET /api/images - List images for a project/organization
export async function GET(request: NextRequest) {
	try {
		const { searchParams } = new URL(request.url);
		const projectId = searchParams.get('projectId');
		const organizationId = searchParams.get('organizationId');
		const tags = searchParams.get('tags')?.split(',').filter(Boolean);
		const searchTerm = searchParams.get('searchTerm');
		const limit = parseInt(searchParams.get('limit') || '100');
		const offset = parseInt(searchParams.get('offset') || '0');

		if (!projectId || !organizationId) {
			return NextResponse.json(
				{ error: 'Project ID and Organization ID are required' },
				{ status: 400 }
			);
		}

		const supabase = createClient();

		// Get user session for authentication
		const {
			data: { user },
			error: userError,
		} = await supabase.auth.getUser();

		if (userError || !user) {
			return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
		}

		// Build query
		let query = supabase
			.from('organization_images')
			.select('*')
			.eq('project_id', parseInt(projectId))
			.eq('organization_id', organizationId)
			.neq('deleting', true) // Exclude images marked for deletion
			.order('created_at', { ascending: false })
			.range(offset, offset + limit - 1);

		// Add search filter if provided
		if (searchTerm) {
			query = query.ilike('image_name', `%${searchTerm}%`);
		}

		// Add tags filter if provided
		if (tags && tags.length > 0) {
			query = query.overlaps('tags', tags);
		}

		const { data: images, error } = await query;

		if (error) {
			console.error('Error loading images:', error);
			return NextResponse.json(
				{ error: 'Failed to load images', details: error.message },
				{ status: 500 }
			);
		}

		return NextResponse.json({
			success: true,
			data: images || [],
			pagination: {
				offset,
				limit,
				total: images?.length || 0,
			},
		});
	} catch (error) {
		console.error('Images API error:', error);
		return NextResponse.json(
			{
				success: false,
				error: 'Internal server error',
				details: error instanceof Error ? error.message : 'Unknown error',
			},
			{ status: 500 }
		);
	}
}

// POST /api/images - Create/upload new image (if needed)
export async function POST(request: NextRequest) {
	try {
		const supabase = createClient();

		// Get user session for authentication
		const {
			data: { user },
			error: userError,
		} = await supabase.auth.getUser();

		if (userError || !user) {
			return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
		}

		const body = (await request.json()) as {
			projectId: string;
			organizationId: string;
			imageName: string;
			imagePath: string;
			imageUrl?: string;
			fileSize?: number;
			contentType?: string;
			altText?: string;
			tags?: string[];
			metadata?: Record<string, unknown>;
		};
		const {
			projectId,
			organizationId,
			imageName,
			imagePath,
			imageUrl,
			fileSize,
			contentType,
			altText,
			tags,
			metadata,
		} = body;

		if (!projectId || !organizationId || !imageName || !imagePath) {
			return NextResponse.json(
				{
					error:
						'Project ID, Organization ID, image name, and image path are required',
				},
				{ status: 400 }
			);
		}

		const { data: image, error } = await supabase
			.from('organization_images')
			.insert({
				organization_id: organizationId,
				project_id: parseInt(projectId),
				image_name: imageName,
				image_path: imagePath,
				image_url: imageUrl,
				file_size: fileSize,
				content_type: contentType,
				alt_text: altText,
				tags: tags || [],
				metadata: metadata || {},
				created_by: user.id,
				created_at: new Date().toISOString(),
				updated_at: new Date().toISOString(),
			})
			.select()
			.single();

		if (error) {
			console.error('Error creating image:', error);
			return NextResponse.json(
				{ error: 'Failed to create image', details: error.message },
				{ status: 500 }
			);
		}

		return NextResponse.json({
			success: true,
			data: image,
		});
	} catch (error) {
		console.error('Images POST API error:', error);
		return NextResponse.json(
			{
				success: false,
				error: 'Internal server error',
				details: error instanceof Error ? error.message : 'Unknown error',
			},
			{ status: 500 }
		);
	}
}
