"use client";

import { useState } from "react";
import {
	BotQuestion,
	DocsBotPagination,
} from "@/app/(dashboard)/[projectId]/ask-ai/types";
import QuestionDetailModal from "@/components/ask-ai/QuestionDetailModal";
import {
	HeaderComponent,
	FiltersComponent,
	TableComponent,
	CardComponent,
	PaginationComponent,
} from "./components";
import { Button } from "@/components/ui/button";
import { DownloadIcon } from "lucide-react";
import { useProject } from "@/contexts";

interface AskAILogsComponentProps {
	questions: BotQuestion[];
	isLoading: boolean;
	pagination: DocsBotPagination | null;
	currentPage: number;
	onPageChange: (pageNumber: number) => void;
	filters?: {
		rating: string;
		escalation: string;
		couldAnswer: string;
	};
	dateRange?: {
		startDate: Date | undefined;
		endDate: Date | undefined;
	};
	onRatingFilterChange?: (value: string) => void;
	onEscalationFilterChange?: (value: string) => void;
	onCouldAnswerFilterChange?: (value: string) => void;
	onDateRangeChange?: (
		startDate: Date | undefined,
		endDate: Date | undefined
	) => void;
}

export default function AskAILogsComponent({
	questions,
	isLoading,
	pagination,
	currentPage,
	onPageChange,
	filters = { rating: "all", escalation: "all", couldAnswer: "all" },
	dateRange = { startDate: undefined, endDate: undefined },
	onRatingFilterChange = () => {},
	onEscalationFilterChange = () => {},
	onCouldAnswerFilterChange = () => {},
	onDateRangeChange = () => {},
}: AskAILogsComponentProps) {
	const [selectedQuestion, setSelectedQuestion] = useState<BotQuestion | null>(
		null
	);
	const [isExporting, setIsExporting] = useState(false);
	const { selectedProject } = useProject();

	const handleQuestionClick = (question: BotQuestion) => {
		setSelectedQuestion(question);
	};

	const closeModal = () => {
		setSelectedQuestion(null);
	};

	const exportToXLSX = async () => {
		if (isExporting || questions.length === 0 || !selectedProject) {
			return;
		}

		try {
			setIsExporting(true);

			const requestBody = {
				questions: questions,
				projectName: selectedProject.project_name,
			};

			const response = await fetch("/api/ask-ai/export-logs", {
				method: "POST",
				headers: {
					"Content-Type": "application/json",
				},
				body: JSON.stringify(requestBody),
			});

			if (!response.ok) {
				throw new Error(`Export failed: ${response.statusText}`);
			}

			const blob = await response.blob();
			const url = window.URL.createObjectURL(blob);

			const contentDisposition = response.headers.get("Content-Disposition");
			const filenameMatch = contentDisposition?.match(/filename="(.+)"/);
			const filename = filenameMatch
				? filenameMatch[1]
				: `${selectedProject.project_name}_ask-ai-logs.xlsx`;

			const a = document.createElement("a");
			a.href = url;
			a.download = filename;
			document.body.appendChild(a);
			a.click();

			document.body.removeChild(a);
			window.URL.revokeObjectURL(url);
		} catch (error) {
			console.error("Error exporting XLSX:", error);
			// Optionally, show a toast notification for the error
		} finally {
			setTimeout(() => {
				setIsExporting(false);
			}, 2000);
		}
	};

	return (
		<div className='relative min-h-[60vh]'>
			{/* Header Section */}
			<div className='mb-6'>
				<div className='flex flex-col space-y-4'>
					<HeaderComponent
						title='Questions'
						description='A log of questions you or users have asked your bot in the last 90 days.'
					/>

					{/* Filters and Pagination Section */}
					<div className='flex flex-col sm:flex-row flex-wrap justify-between items-start sm:items-end gap-2 sm:gap-4'>
						<div className='w-full sm:w-auto flex flex-wrap items-end gap-x-4 gap-y-2'>
							<FiltersComponent
								filters={filters}
								dateRange={{
									startDate: dateRange.startDate,
									endDate: dateRange.endDate,
								}}
								onRatingFilterChange={onRatingFilterChange}
								onEscalationFilterChange={onEscalationFilterChange}
								onCouldAnswerFilterChange={onCouldAnswerFilterChange}
								onDateRangeChange={onDateRangeChange}
								isLoading={isLoading}
							/>

							{/* XLSX Export Button */}
							<Button
								variant='outline'
								size='sm'
								className='sm:mt-0 whitespace-nowrap self-end'
								onClick={exportToXLSX}
								disabled={isLoading || questions.length === 0 || isExporting}
							>
								<DownloadIcon className='mr-2 h-4 w-4' />
								{isExporting ? "Exporting..." : "Export XLSX"}
							</Button>
						</div>

						{/* Pagination - top right */}
						<div className='flex items-end ml-auto mt-2 sm:mt-0'>
							{questions.length > 0 && (
								<PaginationComponent
									pagination={pagination}
									currentPage={currentPage}
									onPageChange={onPageChange}
									isLoading={isLoading}
								/>
							)}
						</div>
					</div>
				</div>
			</div>

			<div>
				{/* Table view for larger screens */}
				<div className='hidden lg:block w-full transition-all duration-300'>
					<TableComponent
						questions={questions}
						isLoading={isLoading}
						onQuestionClick={handleQuestionClick}
					/>
				</div>

				{/* Card view for mobile and tablet screens */}
				<div className='lg:hidden transition-all duration-300'>
					<CardComponent
						questions={questions}
						isLoading={isLoading}
						onQuestionClick={handleQuestionClick}
					/>
				</div>

				{/* Bottom pagination - centered */}
				<div className='flex justify-center mt-6'>
					{questions.length > 0 && (
						<PaginationComponent
							pagination={pagination}
							currentPage={currentPage}
							onPageChange={onPageChange}
							isLoading={isLoading}
						/>
					)}
				</div>
			</div>

			{/* Question Detail Modal */}
			{selectedQuestion && (
				<QuestionDetailModal
					question={selectedQuestion}
					isOpen={!!selectedQuestion}
					onClose={closeModal}
				/>
			)}
		</div>
	);
}
