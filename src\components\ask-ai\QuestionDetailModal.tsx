"use client";

import { format } from "date-fns";
import {
	BotQuestion,
	DocumentSource,
} from "@/app/(dashboard)/[projectId]/ask-ai/types";
import {
	Dialog,
	DialogPortal,
	DialogOverlay,
	DialogTitle,
	DialogClose,
	DialogHeader,
} from "@/components/ui/dialog";
import * as DialogPrimitive from "@radix-ui/react-dialog";
import { cn } from "@/lib/utils";
import {
	CheckCircle2,
	MinusCircle,
	ThumbsUp,
	ThumbsDown,
	ExternalLink,
	Calendar,
	LifeBuoy,
	X,
	Link as LinkIcon,
	ChevronDown,
	ChevronUp,
	Minus,
} from "lucide-react";
import { marked } from "marked";
import { useEffect, useState, forwardRef } from "react";
// import "highlight.js/styles/atom-one-dark.css";
import {
	Tooltip,
	TooltipContent,
	TooltipProvider,
	TooltipTrigger,
} from "@/components/ui/tooltip";

// Custom DialogContent component without the automatic close button
const CustomDialogContent = forwardRef<
	React.ElementRef<typeof DialogPrimitive.Content>,
	React.ComponentPropsWithoutRef<typeof DialogPrimitive.Content>
>(({ className, children, ...props }, ref) => (
	<DialogPortal>
		<DialogOverlay />
		<DialogPrimitive.Content
			ref={ref}
			className={cn(
				"fixed left-[50%] top-[50%] z-50 grid w-full max-w-lg translate-x-[-50%] translate-y-[-50%] gap-4 border bg-background p-6 shadow-lg duration-200 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[state=closed]:slide-out-to-left-1/2 data-[state=closed]:slide-out-to-top-[48%] data-[state=open]:slide-in-from-left-1/2 data-[state=open]:slide-in-from-top-[48%] sm:rounded-lg",
				className
			)}
			{...props}
		>
			{children}
		</DialogPrimitive.Content>
	</DialogPortal>
));
CustomDialogContent.displayName = "CustomDialogContent";

// Basic marked configuration
marked.use({
	breaks: true,
	gfm: true,
});

interface QuestionDetailModalProps {
	question: BotQuestion;
	isOpen: boolean;
	onClose: () => void;
}

export default function QuestionDetailModal({
	question,
	isOpen,
	onClose,
}: QuestionDetailModalProps) {
	const [parsedAnswer, setParsedAnswer] = useState("");
	const [expandedSources, setExpandedSources] = useState<{
		[key: string]: boolean;
	}>({});

	// Function to toggle the expansion of a source
	const toggleSourceExpansion = (sourceId: string, index: number) => {
		setExpandedSources((prev) => ({
			...prev,
			[`${sourceId}-${index}`]: !prev[`${sourceId}-${index}`],
		}));
	};

	// Process markdown when the question changes
	useEffect(() => {
		if (question?.answer) {
			try {
				// Convert markdown to HTML
				const html = marked.parse(question.answer) as string;
				setParsedAnswer(html);
			} catch (err) {
				console.error("Error processing markdown:", err);
				setParsedAnswer(question.answer.replace(/\n/g, "<br />"));
			}
		}
	}, [question]);

	// Format date to a readable format MM/dd/yyyy, HH:mm:ss
	const formatDate = (dateString: string) => {
		try {
			return format(new Date(dateString), "MM/dd/yyyy, HH:mm:ss");
		} catch {
			return dateString;
		}
	};

	// Function to get all available feedbacks for the question
	const getAllFeedbacks = () => {
		const feedbacks = [];

		// Add rating feedback if available
		if (question.rating === 1) {
			feedbacks.push({
				type: "positive",
				icon: <ThumbsUp className='text-green-500' size={20} />,
				tooltip: "Helpful",
				className: "text-green-500",
			});
		} else if (question.rating === -1) {
			feedbacks.push({
				type: "negative",
				icon: <ThumbsDown className='text-red-500' size={20} />,
				tooltip: "Not Helpful",
				className: "text-red-500",
			});
		}

		// Add escalation feedback if available
		if (question.escalation) {
			feedbacks.push({
				type: "escalation",
				icon: <LifeBuoy className='text-blue-500' size={20} />,
				tooltip: "Escalated to Support",
				className: "text-blue-500",
			});
		}

		// If no feedback was given
		if (feedbacks.length === 0) {
			feedbacks.push({
				type: "none",
				icon: <Minus className='text-gray-400' size={20} />,
				tooltip: "No Rating",
				className: "text-gray-500",
			});
		}

		return feedbacks;
	};

	// Render all feedbacks
	const renderAllFeedbacks = () => {
		const feedbacks = getAllFeedbacks();

		return (
			<TooltipProvider>
				<div className='flex gap-3'>
					{feedbacks.map((feedback, index) => (
						<Tooltip key={index}>
							<TooltipTrigger asChild>
								<div
									className={`rounded-full p-1 hover:bg-gray-100 cursor-help transition-colors`}
								>
									{feedback.icon}
								</div>
							</TooltipTrigger>
							<TooltipContent
								side='bottom'
								className='bg-[#f0f0ff] border border-[#cad3ff] p-2'
							>
								<p>{feedback.tooltip}</p>
							</TooltipContent>
						</Tooltip>
					))}
				</div>
			</TooltipProvider>
		);
	};

	// Process source content with markdown
	const parseSourceContent = (content: string) => {
		try {
			return marked.parse(content) as string;
		} catch {
			return content;
		}
	};

	return (
		<Dialog open={isOpen} onOpenChange={(open) => !open && onClose()}>
			<CustomDialogContent className='max-w-4xl h-auto max-h-[90vh] flex flex-col overflow-hidden'>
				<DialogHeader className='flex-shrink-0 flex flex-row items-center justify-between mb-4'>
					<DialogTitle className='text-xl'>Question Details</DialogTitle>
					<DialogClose
						className='rounded-full p-1.5 hover:bg-gray-100'
						onClick={onClose}
					>
						<X className='h-5 w-5' />
					</DialogClose>
				</DialogHeader>

				<div className='flex-grow overflow-y-auto pr-2 -mr-2'>
					{/* User question */}
					<div className='bg-gray-50 p-4 rounded-lg mb-6'>
						<div className='flex justify-between items-start mb-2'>
							<span className='text-blue-600 font-semibold'>User Question</span>
							<div className='flex items-center gap-2 text-sm text-gray-500'>
								<Calendar size={14} />
								<time dateTime={question.createdAt}>
									{formatDate(question.createdAt)}
								</time>
							</div>
						</div>
						<h2 className='text-lg font-medium mb-2'>
							{question.standaloneQuestion || question.question}
						</h2>

						<div className='flex flex-col sm:flex-row gap-4 mt-4'>
							<div className='flex items-center gap-3'>
								<span className='text-sm text-gray-500'>Status:</span>
								<TooltipProvider>
									<Tooltip>
										<TooltipTrigger asChild>
											<div className={`flex items-center gap-2 cursor-help`}>
												{question.couldAnswer ? (
													<CheckCircle2 className='text-green-500' size={20} />
												) : (
													<MinusCircle className='text-red-500' size={20} />
												)}
											</div>
										</TooltipTrigger>
										<TooltipContent
											side='bottom'
											className='bg-[#f0f0ff] border border-[#cad3ff] p-2'
										>
											<p>
												{question.couldAnswer
													? "The AI was able to answer this question successfully"
													: "The AI was unable to answer this question"}
											</p>
										</TooltipContent>
									</Tooltip>
								</TooltipProvider>
							</div>

							<div className='flex items-center gap-3 align-top'>
								<span className='text-sm text-gray-500'>Feedback:</span>
								{renderAllFeedbacks()}
							</div>

							{question.metadata?.referrer && (
								<div className='flex items-center gap-3'>
									<span className='text-sm text-gray-500'>Referrer:</span>
									<a
										href={question.metadata.referrer}
										target='_blank'
										rel='noopener noreferrer'
										className='flex items-center gap-1 text-sm text-blue-500 hover:underline'
									>
										<LinkIcon size={14} />
										<span className='truncate max-w-[200px]'>
											{question.metadata.referrer}
										</span>
									</a>
								</div>
							)}
						</div>
					</div>

					{/* Answer section */}
					<div className='mb-6'>
						<h3 className='text-blue-600 font-semibold mb-3'>Answer</h3>
						<div className='border border-gray-200 rounded-lg p-4 bg-white'>
							<div className='[&_ol]:pl-5 [&_ul]:pl-5 [&_li]:mb-2 [&_p]:mb-4 [&_h2]:text-xl [&_h2]:font-semibold [&_h2]:mb-2 [&_h2]:mt-6 [&_h3]:text-lg [&_h3]:font-medium [&_h3]:mb-2 [&_h3]:mt-6 [&_a]:text-blue-500 [&_a]:underline'>
								<div
									className='prose prose-sm prose-headings:mt-4 prose-headings:mb-2 prose-p:my-2'
									dangerouslySetInnerHTML={{
										__html: parsedAnswer.replace(
											/<pre><code>([\s\S]*?)<\/code><\/pre>/g,
											(_, content) =>
												`<pre class="bg-[#0c1021] text-white p-3 rounded-md mb-4 text-sm font-mono overflow-auto w-full"><code>${content}</code></pre>`
										),
									}}
								/>
							</div>
						</div>
					</div>

					{/* Sources section */}
					<div>
						<h3 className='text-blue-600 font-semibold mb-3'>Sources</h3>
						<div className='border border-gray-200 rounded-lg overflow-hidden'>
							{question.sources && question.sources.length > 0 ? (
								<div className='divide-y divide-gray-200'>
									{question.sources.map(
										(source: DocumentSource, index: number) => (
											<div
												key={`${source.sourceId}-${index}`}
												className={`p-3 ${
													source.used ? "bg-green-50" : "bg-white"
												}`}
											>
												<div className='flex justify-between items-center mb-1.5'>
													<h4 className='font-medium text-gray-800 text-sm'>
														{source.title || "Untitled Source"}
													</h4>
													{source.used && (
														<span className='px-2 py-0.5 text-xs font-semibold rounded-full bg-green-100 text-green-800'>
															Used
														</span>
													)}
												</div>

												{source.url && (
													<a
														href={source.url}
														target='_blank'
														rel='noopener noreferrer'
														className='flex items-center gap-1 text-xs text-blue-500 hover:underline mb-1.5'
													>
														<ExternalLink size={12} />
														<span className='truncate'>{source.url}</span>
													</a>
												)}

												<div className='relative'>
													<div
														className={`mt-1 text-xs text-gray-600 prose prose-sm max-w-none overflow-hidden transition-all duration-200 ease-out ${
															expandedSources[`${source.sourceId}-${index}`]
																? "max-h-[2000px] opacity-100 transform scale-100"
																: "max-h-[60px] opacity-90 transform scale-[0.99]"
														} [&_pre]:bg-[#0c1021] [&_pre]:text-white [&_pre]:p-3 [&_pre]:rounded-md [&_pre]:text-xs [&_pre]:font-mono [&_pre]:overflow-auto [&_pre]:w-full [&_p]:my-1.5 [&_ul]:my-1.5 [&_ol]:my-1.5 [&_li]:my-0.5 [&_h3]:text-sm [&_h4]:text-sm`}
														dangerouslySetInnerHTML={{
															__html: parseSourceContent(
																source.content
															).replace(
																/<pre><code>([\s\S]*?)<\/code><\/pre>/g,
																(_, content) =>
																	`<pre class="bg-[#0c1021] text-white p-2 rounded-md mb-2 text-xs font-mono overflow-auto w-full"><code>${content}</code></pre>`
															),
														}}
													/>

													<button
														onClick={() =>
															toggleSourceExpansion(source.sourceId, index)
														}
														className='w-full flex items-center justify-center text-xs font-medium text-blue-600 hover:text-blue-800 mt-2 transition-colors bg-gray-50 py-1.5 px-3 rounded-md border border-gray-200 hover:bg-gray-100 shadow-sm'
													>
														{expandedSources[`${source.sourceId}-${index}`] ? (
															<>
																<ChevronUp size={16} className='mr-1' />
																<span>See less</span>
															</>
														) : (
															<>
																<ChevronDown size={16} className='mr-1' />
																<span>See more</span>
															</>
														)}
													</button>
												</div>
											</div>
										)
									)}
								</div>
							) : (
								<div className='p-4 text-center text-gray-500'>
									No sources available
								</div>
							)}
						</div>
					</div>
				</div>
			</CustomDialogContent>
		</Dialog>
	);
}
