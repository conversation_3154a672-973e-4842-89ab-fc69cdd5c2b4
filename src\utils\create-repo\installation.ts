import { deployToCloudflare } from "./cloudflare";
// import { deployToNetlify } from "./netlify";
import { Repository } from "@octokit/webhooks-types";

async function handleInstallation(repo: Repository) {
	try {
		const repoName = repo.full_name.split("/")[1];
		const writedocsRepo = repo.full_name.replace("/", "-");
		console.log(repoName, writedocsRepo);
		console.log("Deploying to Cloudflare");
		const project = await deployToCloudflare(repoName, writedocsRepo);
		return project;
		// await deployToNetlify(repoName, writedocsRepo);
		// const org = payload.installation.account.login;
		// const title = org
		//   ? `:checked: Repository from *${org}* organization connected `
		//   : ":checked: Repository connected";
		// await slackNotifier(
		//   title,
		//   `*<https://github.com/writedocs/${writedocsRepo}>*`
		// );
	} catch (error) {
		console.error(error);
		// const title = "❌ [REPOSITORY_INSTALLATION_ERROR]";
		// await slackNotifier(
		//   title,
		//   `Error on ${payload.installation.account.login} organization \n\n${error.message}`
		// );
		return null;
	}
}

export default handleInstallation;
