"use client";

import { <PERSON>bar<PERSON>ogo } from "./sidebar-logo";

import {
	Sidebar,
	SidebarContent,
	SidebarFooter,
	SidebarGroup,
	SidebarGroupContent,
	SidebarMenu,
	SidebarRail,
} from "@/components/ui/sidebar";

// Imports for footer components
import { DocsDropdownMenuItem } from "./docs-dropdown-menu-item";
import { ProjectsDropdownMenuItem } from "./projects-dropdown-menu-item";
import { UserDropdownMenuItem } from "./user-dropdown-menu-item";

export function HomeSidebar() {
	// Footer elements are global and can be kept
	const footerElements = [
		{ id: "docs", component: DocsDropdownMenuItem, props: {} },
		{ id: "projects", component: ProjectsDropdownMenuItem, props: {} },
		{
			id: "user",
			component: UserDropdownMenuItem,
			props: { className: "mt-auto" },
		},
	];

	return (
		<Sidebar collapsible='icon'>
			<SidebarRail />
			<div className='flex flex-col h-full bg-gradient-to-b from-info-300 to-wd-blueDark/10'>
				<SidebarContent>
					<SidebarLogo />
					{/* No project-specific menu items needed for the home page */}
					<SidebarGroup>
						<SidebarGroupContent>
							<SidebarMenu className='flex flex-col items-center gap-y-2 py-2'>
								{/* Placeholder for any potential global links, or can be left empty */}
							</SidebarMenu>
						</SidebarGroupContent>
					</SidebarGroup>
				</SidebarContent>
				<SidebarFooter>
					<SidebarMenu className='flex flex-col flex-grow gap-y-2 py-2'>
						{footerElements.map((item) => {
							const ItemComponent = item.component;
							return <ItemComponent key={item.id} {...item.props} />;
						})}
					</SidebarMenu>
				</SidebarFooter>
			</div>
		</Sidebar>
	);
}
