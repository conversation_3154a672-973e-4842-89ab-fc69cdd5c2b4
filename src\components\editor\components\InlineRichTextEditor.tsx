import React, { useEffect, useCallback } from "react";
import { useEditor, EditorContent } from "@tiptap/react";
import StarterKit from "@tiptap/starter-kit";
import Placeholder from "@tiptap/extension-placeholder";
import "./InlineRichTextEditor.css";

interface InlineRichTextEditorProps {
  content: string;
  onChange: (content: string) => void;
  placeholder?: string;
  maxLength?: number;
  className?: string;
  disabled?: boolean;
}

export const InlineRichTextEditor: React.FC<InlineRichTextEditorProps> = ({
  content,
  onChange,
  placeholder = "Enter text...",
  maxLength,
  className = "",
  disabled = false,
}) => {
  const editor = useEditor({
    extensions: [
      StarterKit.configure({
        // Disable some features for inline editor
        heading: false,
        codeBlock: false,
        blockquote: false,
        horizontalRule: false,
        bulletList: false,
        orderedList: false,
        listItem: false,
        // Keep basic formatting
        bold: {},
        italic: {},
        strike: {},
        code: {},
        // Single paragraph mode
        paragraph: {
          HTMLAttributes: {
            class: "inline-editor-paragraph",
          },
        },
      }),
      Placeholder.configure({
        placeholder,
      }),
    ],
    content: content,
    editable: !disabled,
    editorProps: {
      attributes: {
        class: `inline-rich-editor ${className}`,
      },
      handleDOMEvents: {
        keydown: (view, event) => {
          // Prevent Enter key from creating new paragraphs (keep inline)
          if (event.key === "Enter" && !event.shiftKey) {
            event.preventDefault();
            return true;
          }

          // Handle character limit
          if (maxLength && view.state.doc.textContent.length >= maxLength) {
            if (
              event.key.length === 1 && // Single character
              !event.ctrlKey &&
              !event.metaKey &&
              !event.altKey
            ) {
              event.preventDefault();
              return true;
            }
          }

          return false;
        },
      },
    },
    onUpdate: ({ editor }) => {
      const html = editor.getHTML();
      onChange(html);
    },
    onCreate: ({ editor }) => {
      // Focus the editor after creation if needed
      if (content === "" || content === "<p></p>") {
        setTimeout(() => {
          editor.commands.focus();
        }, 0);
      }
    },
  });

  useEffect(() => {
    if (!editor) return;

    // Update content if it changes externally
    const currentContent = editor.getHTML();
    if (currentContent !== content && content !== editor.getHTML()) {
      editor.commands.setContent(content, false);
    }
  }, [content, editor]);

  useEffect(() => {
    if (!editor) return;

    // Update editable state
    editor.setEditable(!disabled);
  }, [disabled, editor]);

  const handleKeyDown = useCallback((e: React.KeyboardEvent) => {
    // Prevent event bubbling for certain keys to avoid form submission
    if (e.key === "Enter" && !e.shiftKey) {
      e.preventDefault();
      e.stopPropagation();
    }
  }, []);

  if (!editor) {
    return (
      <div
        className={`inline-rich-editor-placeholder ${className}`}
        style={{ minHeight: "2.5rem" }}
      >
        <div className="animate-pulse bg-gray-200 dark:bg-gray-700 rounded h-6"></div>
      </div>
    );
  }

  return (
    <div
      className={`inline-rich-editor-container ${className}`}
      onKeyDown={handleKeyDown}
    >
      <EditorContent editor={editor} className="inline-rich-editor-content" />
      {maxLength && (
        <div className="inline-rich-editor-counter">
          <span
            className={`text-xs ${
              editor.state.doc.textContent.length > maxLength * 0.9
                ? "text-orange-500 dark:text-orange-400"
                : editor.state.doc.textContent.length >= maxLength
                ? "text-red-500 dark:text-red-400"
                : "text-gray-500 dark:text-gray-400"
            }`}
          >
            {editor.state.doc.textContent.length}/{maxLength}
          </span>
        </div>
      )}

      {/* Floating toolbar for formatting */}
      <div className="inline-rich-editor-toolbar">
        <button
          type="button"
          onClick={() => editor.chain().focus().toggleBold().run()}
          className={`toolbar-button ${
            editor.isActive("bold") ? "active" : ""
          }`}
          title="Bold (Ctrl+B)"
        >
          <strong>B</strong>
        </button>
        <button
          type="button"
          onClick={() => editor.chain().focus().toggleItalic().run()}
          className={`toolbar-button ${
            editor.isActive("italic") ? "active" : ""
          }`}
          title="Italic (Ctrl+I)"
        >
          <em>I</em>
        </button>
        <button
          type="button"
          onClick={() => editor.chain().focus().toggleStrike().run()}
          className={`toolbar-button ${
            editor.isActive("strike") ? "active" : ""
          }`}
          title="Strikethrough"
        >
          <span style={{ textDecoration: "line-through" }}>S</span>
        </button>
        <button
          type="button"
          onClick={() => editor.chain().focus().toggleCode().run()}
          className={`toolbar-button ${
            editor.isActive("code") ? "active" : ""
          }`}
          title="Code"
        >
          &lt;/&gt;
        </button>
      </div>
    </div>
  );
};

export default InlineRichTextEditor;
