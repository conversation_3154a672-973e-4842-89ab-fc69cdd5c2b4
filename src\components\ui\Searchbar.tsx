'use client';

import React, { useState, useMemo } from 'react';
import { Search } from 'lucide-react';
import { Input } from './input';

interface SearchbarProps {
  children: React.ReactNode;
  placeholder?: string;
  className?: string;
}

export const Searchbar: React.FC<SearchbarProps> = ({ 
  children, 
  placeholder = "Search table...",
  className = ""
}) => {
  const [searchTerm, setSearchTerm] = useState('');

  // Function to extract text content from React nodes
  const extractTextContent = (node: React.ReactNode): string => {
    if (typeof node === 'string') return node;
    if (typeof node === 'number') return node.toString();
    if (React.isValidElement(node)) {
      const props = node.props as { children?: React.ReactNode };
      if (props.children) {
        return extractTextContent(props.children);
      }
    }
    if (Array.isArray(node)) {
      return node.map(extractTextContent).join(' ');
    }
    return '';
  };

  // Function to filter table rows based on search term
  const filteredChildren = useMemo(() => {
    if (!searchTerm.trim()) return children;

    const filterTableRows = (element: React.ReactElement): React.ReactElement => {
      const props = element.props as { children?: React.ReactNode };

      if (element.type === 'table') {
        const filteredTable = React.cloneElement(element, props,
          React.Children.map(props.children, (child) => {
            if (React.isValidElement(child)) {
              return filterTableRows(child);
            }
            return child;
          })
        );
        return filteredTable;
      }

      if (element.type === 'tbody' || element.type === 'thead') {
        const filteredBody = React.cloneElement(element, props,
          React.Children.map(props.children, (child) => {
            if (React.isValidElement(child) && child.type === 'tr') {
              // Extract text content from the row
              const childProps = child.props as { children?: React.ReactNode };
              const rowText = extractTextContent(childProps.children).toLowerCase();
              const searchLower = searchTerm.toLowerCase();

              // Show row if it contains the search term
              if (rowText.includes(searchLower)) {
                return child;
              }
              return null;
            }
            return child;
          })
        );
        return filteredBody;
      }

      // For other elements, recursively process children
      if (props.children) {
        return React.cloneElement(element, props,
          React.Children.map(props.children, (child) => {
            if (React.isValidElement(child)) {
              return filterTableRows(child);
            }
            return child;
          })
        );
      }

      return element;
    };

    if (React.isValidElement(children)) {
      return filterTableRows(children);
    }

    return children;
  }, [children, searchTerm]);

  return (
    <div className={`searchbar-container ${className}`}>
      {/* Search Input */}
      <div className="mb-4">
        <div className="relative">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
          <Input
            type="text"
            placeholder={placeholder}
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="pl-10"
          />
        </div>
      </div>
      
      {/* Table Content */}
      <div className="searchbar-content">
        {filteredChildren}
      </div>
    </div>
  );
};

export default Searchbar;
