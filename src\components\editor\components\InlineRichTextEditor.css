/* Inline Rich Text Editor Styles */

.inline-rich-editor-container {
  position: relative;
  width: 100%;
}

.inline-rich-editor-content {
  position: relative;
  width: 100%;
}

.inline-rich-editor-content .ProseMirror {
  padding: 8px 12px;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  background: white;
  color: #374151;
  font-size: 14px;
  line-height: 1.5;
  min-height: 40px;
  max-height: 120px;
  overflow-y: auto;
  outline: none;
  transition: all 0.2s ease;
  resize: none;
}

.inline-rich-editor-content .ProseMirror:focus {
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.dark .inline-rich-editor-content .ProseMirror {
  background: #374151;
  border-color: #4b5563;
  color: white;
}

.dark .inline-rich-editor-content .ProseMirror:focus {
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.2);
}

/* Paragraph styling */
.inline-rich-editor-content .inline-editor-paragraph {
  margin: 0;
  padding: 0;
}

/* Placeholder styling */
.inline-rich-editor-content .ProseMirror p.is-editor-empty:first-child::before {
  content: attr(data-placeholder);
  float: left;
  color: #9ca3af;
  pointer-events: none;
  height: 0;
  font-style: italic;
}

.dark
  .inline-rich-editor-content
  .ProseMirror
  p.is-editor-empty:first-child::before {
  color: #6b7280;
}

/* Character counter */
.inline-rich-editor-counter {
  position: absolute;
  bottom: -20px;
  right: 0;
  font-size: 11px;
  line-height: 1;
}

/* Toolbar styling */
.inline-rich-editor-toolbar {
  position: absolute;
  top: -40px;
  left: 0;
  display: flex;
  gap: 2px;
  background: white;
  border: 1px solid #e5e7eb;
  border-radius: 6px;
  padding: 4px;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
  opacity: 0;
  visibility: hidden;
  transition: all 0.2s ease;
  z-index: 50;
}

.dark .inline-rich-editor-toolbar {
  background: #374151;
  border-color: #4b5563;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.3);
}

.inline-rich-editor-container:focus-within .inline-rich-editor-toolbar {
  opacity: 1;
  visibility: visible;
}

.toolbar-button {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 28px;
  height: 28px;
  border: none;
  background: transparent;
  border-radius: 4px;
  cursor: pointer;
  font-size: 12px;
  font-weight: 500;
  color: #6b7280;
  transition: all 0.15s ease;
}

.toolbar-button:hover {
  background: #f3f4f6;
  color: #374151;
}

.toolbar-button.active {
  background: #3b82f6;
  color: white;
}

.dark .toolbar-button {
  color: #9ca3af;
}

.dark .toolbar-button:hover {
  background: #4b5563;
  color: #e5e7eb;
}

.dark .toolbar-button.active {
  background: #3b82f6;
  color: white;
}

/* Formatting styles */
.inline-rich-editor-content .ProseMirror strong {
  font-weight: 600;
}

.inline-rich-editor-content .ProseMirror em {
  font-style: italic;
}

.inline-rich-editor-content .ProseMirror code {
  background: #f3f4f6;
  border-radius: 3px;
  padding: 1px 4px;
  font-family: "Monaco", "Menlo", "Ubuntu Mono", monospace;
  font-size: 85%;
  color: #d63384;
}

.dark .inline-rich-editor-content .ProseMirror code {
  background: #4b5563;
  color: #f472b6;
}

/* Loading placeholder */
.inline-rich-editor-placeholder {
  padding: 8px 12px;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  background: white;
}

.dark .inline-rich-editor-placeholder {
  background: #374151;
  border-color: #4b5563;
}

/* Responsive adjustments */
@media (max-width: 640px) {
  .inline-rich-editor-toolbar {
    position: fixed;
    top: auto;
    bottom: 20px;
    left: 50%;
    transform: translateX(-50%);
    box-shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.1);
  }

  .inline-rich-editor-counter {
    bottom: -24px;
    right: 0;
  }
}

/* Focus states for accessibility */
.toolbar-button:focus {
  outline: 2px solid #3b82f6;
  outline-offset: 1px;
}

.inline-rich-editor-content .ProseMirror:focus {
  outline: none;
}

/* Scrollbar styling */
.inline-rich-editor-content .ProseMirror::-webkit-scrollbar {
  width: 4px;
}

.inline-rich-editor-content .ProseMirror::-webkit-scrollbar-track {
  background: transparent;
}

.inline-rich-editor-content .ProseMirror::-webkit-scrollbar-thumb {
  background: #d1d5db;
  border-radius: 2px;
}

.dark .inline-rich-editor-content .ProseMirror::-webkit-scrollbar-thumb {
  background: #4b5563;
}

.inline-rich-editor-content .ProseMirror::-webkit-scrollbar-thumb:hover {
  background: #9ca3af;
}

.dark .inline-rich-editor-content .ProseMirror::-webkit-scrollbar-thumb:hover {
  background: #6b7280;
}
