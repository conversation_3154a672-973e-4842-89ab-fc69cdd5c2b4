import { Node } from '@tiptap/core';
import { <PERSON><PERSON><PERSON><PERSON>w<PERSON>rapper, ReactNodeViewRenderer } from '@tiptap/react';
import React, { useState, useCallback, useRef, useEffect } from 'react';
import { Edit3, Save, X, Image as ImageIcon, Info, Search } from 'lucide-react';
import { Button } from '@/components/ui/button';
import type { NodeViewProps } from '@tiptap/react';
import { ImageSelector } from '../ImageSelector';
import { RichTextInput } from '@/components/ui/RichTextInput';
import { useProject } from '@/contexts';
import { useToast } from '@/components/ToastProvider';
import { getPhosphorIcon } from '../utils/phosphorIconRenderer';

// Utility function to parse image name and remove timestamp
const parseImageName = (imagePath: string): string => {
	if (!imagePath) return 'Selected image';

	const fileName = imagePath.split('/').pop() || '';
	// Remove timestamp pattern: numbers_filename.extension -> filename.extension
	const withoutTimestamp = fileName.replace(/^\d+_/, '');

	return withoutTimestamp || fileName || 'Selected image';
};

// Interface for card attributes
interface CardAttrs {
	title: string;
	description: string;
	link: string;
	icon?: string;
	iconType?: 'thin' | 'light' | 'regular' | 'bold' | 'fill' | 'duotone';
	iconSize?: string;
	image?: string;
}


// React component for the card
const CardComponent: React.FC<NodeViewProps> = ({
	node,
	updateAttributes,
	selected,
}) => {
	const [isEditing, setIsEditing] = useState(false);
	const [showTooltip, setShowTooltip] = useState(false);
	const [showImageSelector, setShowImageSelector] = useState(false);
	const tooltipTimeoutRef = useRef<NodeJS.Timeout | null>(null);
	const { selectedProject } = useProject();
	const { addToast } = useToast();
	const [formData, setFormData] = useState<CardAttrs>({
		title: node.attrs.title || '',
		description: node.attrs.description || '',
		link: node.attrs.link || '',
		icon: node.attrs.icon || '',
		iconType: node.attrs.iconType || 'regular',
		iconSize: node.attrs.iconSize || '48px',
		image: node.attrs.image || '',
	});

	const handleSave = useCallback(() => {
		// Validate required fields
		if (!formData.title.trim()) {
			addToast('Title is required', 'warning');
			return;
		}

		// Create a clean copy of formData to avoid mutation issues
		const cleanFormData = {
			...formData,
			// Ensure description is properly formatted
			description: formData.description || '<p></p>',
		};

		updateAttributes(cleanFormData);
		setIsEditing(false);
		addToast('Card updated successfully', 'success', 'Success');
	}, [formData, updateAttributes, addToast]);

	const handleCancel = useCallback(() => {
		setFormData({
			title: node.attrs.title || '',
			description: node.attrs.description || '',
			link: node.attrs.link || '',
			icon: node.attrs.icon || '',
			iconType: node.attrs.iconType || 'regular',
			iconSize: node.attrs.iconSize || '48px',
			image: node.attrs.image || '',
		});
		setIsEditing(false);
	}, [node.attrs]);

	const handleInputChange = useCallback(
		(field: keyof CardAttrs, value: string) => {
			setFormData((prev) => {
				const newData = { ...prev, [field]: value };
				// Clear image when icon is set, and vice versa
				if (field === 'icon' && value.trim()) {
					newData.image = '';
				} else if (field === 'image' && value.trim()) {
					newData.icon = '';
				}
				return newData;
			});
		},
		[]
	);

	const handleCardClick = useCallback(() => {
		// Removed redirection - cards are now non-clickable for links
		// This prevents unwanted navigation when clicking on cards in the editor
	}, []);

	const handleTooltipMouseEnter = useCallback(() => {
		// Clear any existing timeout
		if (tooltipTimeoutRef.current) {
			clearTimeout(tooltipTimeoutRef.current);
			tooltipTimeoutRef.current = null;
		}
		setShowTooltip(true);
	}, []);

	const handleTooltipMouseLeave = useCallback(() => {
		// Set a delay before hiding the tooltip
		tooltipTimeoutRef.current = setTimeout(() => {
			setShowTooltip(false);
		}, 300); // 300ms delay
	}, []);

	// Cleanup timeout on unmount or when entering edit mode
	useEffect(() => {
		return () => {
			if (tooltipTimeoutRef.current) {
				clearTimeout(tooltipTimeoutRef.current);
			}
		};
	}, []);

	// Hide tooltip when entering edit mode
	const handlePhosphorLinkClick = useCallback((e: React.MouseEvent) => {
		e.preventDefault();
		e.stopPropagation();
		window.open('https://phosphoricons.com', '_blank', 'noopener,noreferrer');
	}, []);

	const handleImageSelect = useCallback(
		(imageUrl: string) => {
			handleInputChange('image', imageUrl);
			setShowImageSelector(false);
		},
		[handleInputChange]
	);

	const handleOpenImageSelector = useCallback(() => {
		setShowImageSelector(true);
	}, []);

	if (!isEditing) {
		return (
			<NodeViewWrapper
				className='card-node'
				as='div'
				data-drag-handle=''
				contentEditable={false}
			>
				<div
					className={`
          my-4 bg-white dark:bg-slate-800 border border-gray-200 dark:border-gray-700
          rounded-lg shadow-sm transition-all duration-200
          ${selected ? 'ring-2 ring-blue-400/50' : ''}
          ${node.attrs.link ? 'border-blue-300 dark:border-blue-600' : ''}
          relative group overflow-hidden
        `}
					onClick={handleCardClick}
				>
					{/* Edit button - only visible on hover */}
					<Button
						variant='ghost'
						size='sm'
						onClick={(e) => {
							e.stopPropagation();
							setIsEditing(true);
						}}
						className='absolute top-2 right-2 h-6 px-2 text-xs opacity-0 group-hover:opacity-100 transition-opacity duration-200 bg-white/80 dark:bg-slate-800/80 hover:bg-gray-100 dark:hover:bg-slate-700 z-10'
					>
						<Edit3 className='w-3 h-3 mr-1' />
						Edit
					</Button>

					{/* Image Banner - Full Width */}
					{formData.image && (
						<div className='w-full p-2.5 pb-0'>
							<img
								src={formData.image}
								alt={formData.title}
								width={400}
								height={150}
								className='w-full h-[150px] object-cover rounded-lg'
								onError={(e) => {
									e.currentTarget.style.display = 'none';
								}}
							/>
						</div>
					)}

					{/* Content Container */}
					<div className='p-4'>
						<div className='flex flex-col items-start'>
							{/* Icon (only when no image) */}
							{formData.icon && !formData.image && (
								<div className='flex-shrink-0 mb-3'>
									{getPhosphorIcon(
										formData.icon || '',
										formData.iconType || 'regular',
										formData.iconSize || '48px'
									)}
								</div>
							)}

							{/* Content */}
							<div className='flex-1 min-w-0'>
								<p className='text-md font-semibold text-gray-900 dark:text-gray-100'>
									{node.attrs.title || 'Card Title'}
								</p>
								{node.attrs.description && (
									<div
										className='text-sm text-gray-600 dark:text-gray-400 leading-snug prose-sm'
										dangerouslySetInnerHTML={{
											__html: node.attrs.description || '',
										}}
									/>
								)}
							</div>
						</div>
					</div>
				</div>
			</NodeViewWrapper>
		);
	}

	return (
		<NodeViewWrapper
			className='card-node'
			as='div'
			data-drag-handle=''
			contentEditable={false}
		>
			<div
				className={`
        my-4 p-4 bg-white dark:bg-slate-800 border-2 border-blue-400/50 dark:border-blue-600/50 
        rounded-lg shadow-lg transition-all duration-300 ease-in-out
      `}
			>
				{/* Header */}
				<div className='flex items-center justify-between mb-4'>
					<h4 className='text-sm font-semibold text-gray-900 dark:text-gray-100'>
						Edit Card
					</h4>
					<div className='flex items-center space-x-2'>
						<Button
							variant='ghost'
							size='sm'
							onClick={handleCancel}
							className='h-6 px-2 text-xs hover:bg-red-100/50 dark:hover:bg-red-900/30 text-red-600 dark:text-red-400'
						>
							<X className='w-3 h-3 mr-1' />
							Cancel
						</Button>
						<Button
							variant='default'
							size='sm'
							onClick={handleSave}
							className='h-6 px-2 text-xs bg-blue-600 hover:bg-blue-700 text-white'
						>
							<Save className='w-3 h-3 mr-1' />
							Save
						</Button>
					</div>
				</div>

				{/* Edit Form */}
				<div className='space-y-4'>
					{/* Title */}
					<div>
						<label className='block text-xs font-medium text-gray-700 dark:text-gray-300 mb-1'>
							Title *
						</label>
						<input
							type='text'
							value={formData.title}
							onChange={(e) => handleInputChange('title', e.target.value)}
							placeholder='Enter card title'
							className='w-full px-3 py-2 text-sm bg-white dark:bg-slate-700 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:text-white'
							required
						/>
					</div>

					{/* Description */}
					<div>
						<label className='block text-xs font-medium text-gray-700 dark:text-gray-300 mb-1'>
							Description
						</label>
						<RichTextInput
							value={formData.description}
							onChange={(html) => handleInputChange('description', html)}
							placeholder="Brief explanation of the card's purpose..."
							variant='default'
							className='w-full'
						/>
					</div>

					{/* Link */}
					<div>
						<label className='block text-xs font-medium text-gray-700 dark:text-gray-300 mb-1'>
							Link
						</label>
						<input
							type='url'
							value={formData.link}
							onChange={(e) => handleInputChange('link', e.target.value)}
							placeholder='https://example.com or /path'
							className='w-full px-3 py-2 text-sm bg-white dark:bg-slate-700 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:text-white'
						/>
					</div>

					{/* Icon or Image */}
					<div className='grid grid-cols-1 md:grid-cols-2 gap-4'>
						<div className='flex flex-col h-full'>
							<div className='flex items-center space-x-1 mb-1'>
								<label className='block text-xs font-medium text-gray-700 dark:text-gray-300'>
									Icon Name
								</label>
								<div
									className='relative inline-block'
									onMouseEnter={handleTooltipMouseEnter}
									onMouseLeave={handleTooltipMouseLeave}
								>
									<Info className='w-3 h-3 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 cursor-help' />
									{showTooltip && (
										<div className='absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 px-3 py-2 bg-gray-900 dark:bg-gray-700 text-white text-xs rounded-lg z-50 max-w-xs text-center'>
											We use Phosphor Icons. Visit{' '}
											<a
												href='https://phosphoricons.com'
												target='_blank'
												rel='noopener noreferrer'
												className='font-semibold text-blue-300 hover:text-blue-200 underline'
												onClick={handlePhosphorLinkClick}
												onMouseDown={handlePhosphorLinkClick}
											>
												phosphoricons.com
											</a>{' '}
											to see all available icons and copy the name here.
										</div>
									)}
								</div>
							</div>
							<div className='flex-1'>
								<input
									type='text'
									value={formData.icon}
									onChange={(e) => handleInputChange('icon', e.target.value)}
									placeholder='e.g. house, user, gear, book, heart'
									className='w-full h-10 px-3 py-2 text-sm bg-white dark:bg-slate-700 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:text-white'
								/>
								{formData.image && (
									<p className='text-xs text-gray-500 dark:text-gray-400 mt-1'>
										Clear the image field to use an icon
									</p>
								)}
							</div>
						</div>
						<div className='flex flex-col h-full'>
							<label className='block text-xs font-medium text-gray-700 dark:text-gray-300 mb-1'>
								Image
							</label>
							<div className='flex-1'>
								{formData.image ? (
									<div className='space-y-2'>
										{/* Selected Image Preview */}
										<div className='flex items-center space-x-3 p-2 bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-lg'>
											<img
												src={formData.image}
												alt='Selected image'
												width={32}
												height={32}
												className='object-cover rounded'
												onError={(e) => {
													e.currentTarget.style.display = 'none';
												}}
											/>
											<div className='flex-1 min-w-0'>
												<p className='text-xs font-medium text-green-700 dark:text-green-300 truncate'>
													{parseImageName(formData.image)}
												</p>
												<p className='text-xs text-green-600 dark:text-green-400'>
													Image selected
												</p>
											</div>
											<Button
												type='button'
												variant='ghost'
												size='sm'
												onClick={() => handleInputChange('image', '')}
												disabled={!!formData.icon}
												className='text-red-500 hover:text-red-700 hover:bg-red-50 dark:hover:bg-red-900/20 h-6 w-6 p-0'
											>
												<X className='w-3 h-3' />
											</Button>
										</div>
										{/* Change Image Button */}
										<Button
											type='button'
											variant='outline'
											size='sm'
											onClick={handleOpenImageSelector}
											disabled={!!formData.icon}
											className='w-full h-8 flex items-center justify-center space-x-2 border-green-200 dark:border-green-800 text-green-700 dark:text-green-300 hover:bg-green-50 dark:hover:bg-green-900/20'
										>
											<Search className='w-3 h-3' />
											<span className='text-xs'>Change</span>
										</Button>
									</div>
								) : (
									<Button
										type='button'
										variant='outline'
										size='sm'
										onClick={handleOpenImageSelector}
										disabled={!!formData.icon}
										className={`w-full h-10 flex items-center justify-center space-x-2 border-2 border-dashed transition-all ${
											formData.icon
												? 'border-gray-200 dark:border-gray-600 text-gray-400 dark:text-gray-500 cursor-not-allowed bg-gray-50 dark:bg-gray-800'
												: 'border-blue-300 dark:border-blue-600 text-blue-600 dark:text-blue-400 hover:border-blue-400 dark:hover:border-blue-500 hover:bg-blue-50 dark:hover:bg-blue-900/20'
										}`}
									>
										<ImageIcon className='w-4 h-4' />
										<span className='text-sm font-medium'>Browse Images</span>
									</Button>
								)}
								{formData.icon && (
									<p className='text-xs text-gray-500 dark:text-gray-400 mt-1'>
										Clear the icon field to select an image
									</p>
								)}
							</div>
						</div>
					</div>

					{/* Icon Settings */}
					{formData.icon && (
						<div className='grid grid-cols-1 md:grid-cols-2 gap-4'>
							<div>
								<label className='block text-xs font-medium text-gray-700 dark:text-gray-300 mb-1'>
									Icon Type
								</label>
								<select
									value={formData.iconType}
									onChange={(e) =>
										handleInputChange('iconType', e.target.value)
									}
									className='w-full px-3 py-2 text-sm bg-white dark:bg-slate-700 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:text-white'
								>
									<option value='thin'>Thin</option>
									<option value='light'>Light</option>
									<option value='regular'>Regular</option>
									<option value='bold'>Bold</option>
									<option value='fill'>Fill</option>
									<option value='duotone'>Duotone</option>
								</select>
							</div>
							<div>
								<label className='block text-xs font-medium text-gray-700 dark:text-gray-300 mb-1'>
									Icon Size
								</label>
								<input
									type='text'
									value={formData.iconSize}
									onChange={(e) =>
										handleInputChange('iconSize', e.target.value)
									}
									placeholder='48px'
									className='w-full px-3 py-2 text-sm bg-white dark:bg-slate-700 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:text-white'
								/>
							</div>
						</div>
					)}
				</div>
			</div>

			{/* Image Selector Modal */}
			<ImageSelector
				isOpen={showImageSelector}
				onClose={() => setShowImageSelector(false)}
				onSelectImage={handleImageSelect}
				projectId={selectedProject?.id}
				organizationId={selectedProject?.owner_id}
			/>
		</NodeViewWrapper>
	);
};

// Tiptap extension definition
export const CardNode = Node.create({
	name: 'cardNode',

	group: 'block',

	atom: true,

	draggable: false,

	selectable: true,

	addAttributes() {
		return {
			title: {
				default: '',
				parseHTML: (element) =>
					element.getAttribute('title') || element.getAttribute('data-title'),
				renderHTML: (attributes) => {
					if (!attributes.title) return {};
					return { title: attributes.title };
				},
			},
			description: {
				default: '',
				parseHTML: (element) =>
					element.getAttribute('description') ||
					element.getAttribute('data-description'),
				renderHTML: (attributes) => {
					if (!attributes.description) return {};
					return { description: attributes.description };
				},
			},
			link: {
				default: '',
				parseHTML: (element) =>
					element.getAttribute('link') || element.getAttribute('data-link'),
				renderHTML: (attributes) => {
					if (!attributes.link) return {};
					return { link: attributes.link };
				},
			},
			icon: {
				default: '',
				parseHTML: (element) =>
					element.getAttribute('icon') || element.getAttribute('data-icon'),
				renderHTML: (attributes) => {
					if (!attributes.icon) return {};
					return { icon: attributes.icon };
				},
			},
			iconType: {
				default: 'regular',
				parseHTML: (element) =>
					element.getAttribute('iconType') ||
					element.getAttribute('data-icon-type'),
				renderHTML: (attributes) => {
					if (!attributes.iconType) return {};
					return { iconType: attributes.iconType };
				},
			},
			iconSize: {
				default: '48px',
				parseHTML: (element) =>
					element.getAttribute('iconSize') ||
					element.getAttribute('data-icon-size'),
				renderHTML: (attributes) => {
					if (!attributes.iconSize) return {};
					return { iconSize: attributes.iconSize };
				},
			},
			image: {
				default: '',
				parseHTML: (element) =>
					element.getAttribute('image') || element.getAttribute('data-image'),
				renderHTML: (attributes) => {
					if (!attributes.image) return {};
					return { image: attributes.image };
				},
			},
		};
	},

	parseHTML() {
		return [
			{
				tag: 'div[data-type="card"]',
			},
		];
	},

	renderHTML({ HTMLAttributes }) {
		return [
			'div',
			{
				...HTMLAttributes,
				'data-type': 'card',
				class: 'card-node',
			},
		];
	},

	addNodeView() {
		return ReactNodeViewRenderer(CardComponent);
	},
});

export default CardNode;
