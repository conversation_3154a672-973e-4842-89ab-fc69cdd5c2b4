import { unified } from 'unified';
import rehypeParse from 'rehype-parse';
import type { Options as RehypeRemarkOptions } from 'rehype-remark';
import rehypeRemark from 'rehype-remark';
import remarkStringify from 'remark-stringify';
import remarkMdx from 'remark-mdx';
import remarkFrontmatter from 'remark-frontmatter';
import remarkGfm from 'remark-gfm';
import { visit } from 'unist-util-visit';
import type {
	Root as HastRoot,
	Element as HastElement,
	Properties,
	Parent as HastParent,
	Node as HastNode,
	Text as HastText,
	RootContent,
	ElementContent,
} from 'hast';
import { toHtml } from 'hast-util-to-html';
import type { VFile } from 'vfile';
import type {
	BlockContent,
	DefinitionContent,
	Root as MdastRoot,
	Node as MdastNode,
} from 'mdast';
import { validateHtmlForMdx } from './conversionValidators';

// MDX-specific node types
interface MdxJsxFlowElement extends MdastNode {
	type: 'mdxJsxFlowElement';
	name: string;
	attributes?: Array<{
		type: 'mdxJsxAttribute';
		name: string;
		value?: string | { type: 'mdxJsxAttributeValueExpression'; value: string };
	}>;
	children?: MdastNode[];
}

interface TextNode extends MdastNode {
	type: 'text';
	value: string;
}

// ===== TYPES & CONSTANTS =====

interface TransformContext {
	frontmatter: Record<string, string>;
	componentMap: Map<string, HastElement>;
}

interface StepData {
	title?: string;
	content?: string;
	titleSize?: string;
	subSteps?: StepData[];
}

interface CardData {
	title?: string;
	description?: string;
	link?: string;
	icon?: string;
	'icon-type'?: string;
	'icon-size'?: string;
	image?: string;
	[key: string]: unknown; // Para propriedades adicionais não mapeadas
}

const SELF_CLOSING_TAGS = new Set([
	'br',
	'hr',
	'img',
	'input',
	'meta',
	'link',
	'area',
	'base',
	'col',
	'embed',
	'source',
	'track',
	'wbr',
]);

const FORMATTING_TAGS = new Set([
	'strong',
	'em',
	's',
	'del',
	'b',
	'i',
	'u',
	'strike',
]);

const COMPONENT_TAGS = new Set([
	'card',
	'cardlist',
	'api-card-list',
	'accordion',
	'accordion-group',
	'callout',
	'mermaid',
	'steps',
	'tabs',
	'image',
	'video',
	'custom-html',
	'hint',
	'checklist',
	'parameter',
]);

// ===== UTILITY FUNCTIONS =====

const decodeHtmlEntities = (text: string): string => {
	return text
		.replace(/&lt;/g, '<')
		.replace(/&gt;/g, '>')
		.replace(/&quot;/g, '"')
		.replace(/&#39;/g, "'")
		.replace(/&amp;/g, '&');
};

const isEmptyNode = (node: HastElement): boolean => {
	return (
		node.children.length === 0 ||
		node.children.every((c) => c.type === 'text' && !c.value.trim())
	);
};

const processRichTextParagraphs = (html: string): string => {
	// Remove consecutive empty paragraphs instead of converting to breaks
	let processed = html.replace(
		/(<p class="rich-text-paragraph">\s*<\/p>\s*)+/g,
		''
	);

	// Handle paragraphs with literal line breaks inside them
	// Convert literal line breaks inside paragraphs to <br> tags for consistent processing
	processed = processed.replace(
		/<p class="rich-text-paragraph">([^<]*?)\n([^<]*?)<\/p>/g,
		(_, before, after) => {
			// Split by line breaks and create separate paragraphs
			const lines = (before + '\n' + after)
				.split('\n')
				.filter((line) => line.trim());
			return lines
				.map((line) => `<p class="rich-text-paragraph">${line.trim()}</p>`)
				.join('');
		}
	);

	// Also handle regular paragraphs with line breaks
	processed = processed.replace(
		/<p>([^<]*?)\n([^<]*?)<\/p>/g,
		(_, before, after) => {
			// Split by line breaks and create separate paragraphs
			const lines = (before + '\n' + after)
				.split('\n')
				.filter((line) => line.trim());
			return lines
				.map((line) => `<p class="rich-text-paragraph">${line.trim()}</p>`)
				.join('');
		}
	);

	// Now handle non-empty paragraphs
	// Split into segments to process paragraph by paragraph
	const segments = processed.split(/(<p class="rich-text-paragraph">.*?<\/p>)/);
	const result: string[] = [];

	for (const segment of segments) {
		if (segment.match(/<p class="rich-text-paragraph">/)) {
			// This is a paragraph segment - preserve as paragraph HTML
			if (segment.trim()) {
				result.push(segment);
			}
		} else if (segment.trim()) {
			// This is content outside paragraphs (like <br /> tags)
			result.push(segment);
		}
	}

	return result.join('\n\n').trim();
};

const createJsxAttributes = (
	properties: Properties = {}
): Array<{
	type: 'mdxJsxAttribute';
	name: string;
	value?: string | { type: 'mdxJsxAttributeValueExpression'; value: string };
}> => {
	const attributes = Object.entries(properties).map(([key, value]) => {
		// Handle boolean attributes (like 'required')
		if (key === 'required' && value === 'true') {
			return {
				type: 'mdxJsxAttribute' as const,
				name: key,
				// No value for boolean attributes
			};
		}

		return {
			type: 'mdxJsxAttribute' as const,
			name: key,
			value:
				key === 'cols'
					? {
							type: 'mdxJsxAttributeValueExpression' as const,
							value: String(value),
						}
					: String(value),
		};
	});

	return attributes;
};

// ===== ENHANCED CONTENT PROCESSOR =====

class EnhancedContentProcessor {
	/**
	 * Processa conteúdo HTML complexo que pode conter componentes aninhados e code blocks
	 */
	static processComplexContent(htmlContent: string): HastNode[] {
		if (!htmlContent?.trim()) return [];

		const decoded = decodeHtmlEntities(
			htmlContent.replace(/\\"/g, '"').replace(/\\n/g, '\n')
		);

		try {
			const tree = unified()
				.use(rehypeParse, { fragment: true })
				.parse(decoded);

			// Clean up excessive paragraphs before processing
			this.cleanupExcessiveParagraphs(tree);

			// Processa a árvore recursivamente para transformar componentes aninhados
			this.transformNestedComponents(tree);

			return tree.children;
		} catch {
			return [{ type: 'text', value: decoded } as HastText];
		}
	}

	/**
	 * Remove parágrafos excessivos que podem ter sido criados durante conversões
	 */
	private static cleanupExcessiveParagraphs(tree: HastRoot): void {
		visit(tree, 'element', (node: HastElement, index, parent) => {
			if (!parent || typeof index !== 'number') return;

			// Remove empty paragraphs instead of converting to <br>
			if (
				node.tagName === 'p' &&
				(node.children.length === 0 ||
					node.children.every(
						(child) => child.type === 'text' && !child.value.trim()
					))
			) {
				if ('children' in parent) {
					parent.children.splice(index, 1);
					return ['skip', index];
				}
			}

			// Remove consecutive empty paragraphs
			if (node.tagName === 'p' && index > 0 && 'children' in parent) {
				const prevSibling = parent.children[index - 1];
				if (
					prevSibling?.type === 'element' &&
					prevSibling.tagName === 'p' &&
					(prevSibling.children.length === 0 ||
						prevSibling.children.every(
							(child) => child.type === 'text' && !child.value.trim()
						))
				) {
					parent.children.splice(index - 1, 1);
					return ['skip', index - 1];
				}
			}
		});
	}

	/**
	 * Transforma componentes aninhados recursivamente
	 */
	private static transformNestedComponents(tree: HastRoot): void {
		visit(tree, 'element', (node: HastElement) => {
			// Detecta componentes por data-type ou tag name
			const dataType = node.properties?.['dataType'];
			const isComponent =
				COMPONENT_TAGS.has(node.tagName) ||
				(dataType && COMPONENT_TAGS.has(String(dataType)));

			if (isComponent) {
				// Aplica transformação específica do componente
				this.applyComponentTransformation(
					node,
					dataType ? String(dataType) : node.tagName
				);
			}

			// Processa code blocks especiais
			if (
				node.tagName === 'pre' &&
				node.properties?.['dataType'] === 'codeBlock'
			) {
				this.transformCodeBlock(node);
			}
		});
	}

	/**
	 * Aplica transformação específica para cada tipo de componente
	 */
	private static applyComponentTransformation(
		node: HastElement,
		componentType: string
	): void {
		switch (componentType) {
			case 'card':
				ComponentTransformers.card(node);
				break;
			case 'cardlist':
				ComponentTransformers.cardList(node);
				break;
			case 'api-card-list':
				// This case needs special handling in the main transform plugin
				break;
			case 'accordion':
				ComponentTransformers.accordion(node);
				break;
			case 'accordion-group':
				ComponentTransformers.accordionGroup(node);
				break;
			case 'callout':
				ComponentTransformers.callout(node);
				break;
			case 'mermaid':
				ComponentTransformers.mermaid(node);
				break;
			case 'steps':
				ComponentTransformers.steps(node);
				break;
			case 'tabs':
				ComponentTransformers.tabs(node);
				break;
			case 'image':
				ComponentTransformers.image(node);
				break;
			case 'video':
				ComponentTransformers.video(node);
				break;
			case 'hint':
				ComponentTransformers.hint(node);
				break;
			case 'custom-html':
				ComponentTransformers.customHtml(node);
				break;
			case 'checklist':
				ComponentTransformers.checklist(node);
				break;
			case 'parameter':
				ComponentTransformers.parameter(node);
				break;
		}
	}

	/**
	 * Transforma code blocks preservando syntax highlighting
	 */
	private static transformCodeBlock(node: HastElement): void {
		const codeElement = node.children.find(
			(child) => child.type === 'element' && child.tagName === 'code'
		) as HastElement | undefined;

		if (codeElement) {
			const language =
				node.properties?.['dataLanguage'] ||
				String(codeElement.properties?.className || '').replace(
					'language-',
					''
				) ||
				'text';

			// Preserva a estrutura para o processor principal
			node.properties = {
				...node.properties,
				dataType: 'codeBlock',
				dataLanguage: language,
			};
		}
	}

	/**
	 * Processa descrição simples (sem componentes aninhados)
	 */
	static processSimpleDescription(htmlContent: string): string {
		if (!htmlContent?.trim()) return '';

		const decoded = decodeHtmlEntities(htmlContent);

		if (!decoded.includes('<')) {
			return decoded.trim();
		}

		try {
			const tree = unified()
				.use(rehypeParse, { fragment: true })
				.parse(decoded);

			// For simple descriptions, preserve paragraph structure by using a different approach
			return this.extractTextContentWithParagraphs(tree.children);
		} catch {
			return decoded.replace(/<[^>]+>/g, '').trim();
		}
	}

	/**
	 * Extrai conteúdo de texto preservando a estrutura de parágrafos
	 */
	private static extractTextContentWithParagraphs(
		children: (RootContent | ElementContent)[]
	): string {
		const parts: string[] = [];

		for (let i = 0; i < children.length; i++) {
			const child = children[i];

			if (child.type === 'text') {
				parts.push(child.value);
			} else if (child.type === 'element') {
				switch (child.tagName) {
					case 'p':
						const content = this.extractTextContent(child.children);
						if (content.trim()) {
							// Add paragraph spacing only if there are already parts
							if (parts.length > 0) {
								parts.push('\n\n' + content);
							} else {
								parts.push(content);
							}
						}
						break;
					case 'br':
						parts.push('\n');
						break;
					case 'code':
						const code = this.extractTextContent(child.children);
						parts.push(`\`${code}\``);
						break;
					default:
						if (FORMATTING_TAGS.has(child.tagName)) {
							// For formatting tags, preserve the HTML to maintain spacing
							parts.push(toHtml(child));
						} else {
							parts.push(this.extractTextContent(child.children));
						}
				}
			}
		}

		// Join parts preserving the newlines that were added for paragraph spacing
		return parts
			.join('')
			.replace(/\n{3,}/g, '\n\n')
			.trim();
	}

	/**
	 * Processa conteúdo de Card sem adicionar parágrafos desnecessários
	 */
	static processCardContent(htmlContent: string): HastNode[] {
		if (!htmlContent?.trim()) return [];

		const decoded = decodeHtmlEntities(
			htmlContent.replace(/\\"/g, '"').replace(/\\n/g, '\n')
		);

		try {
			const tree = unified()
				.use(rehypeParse, { fragment: true })
				.parse(decoded);

			// Primeiro, limpa parágrafos excessivos
			this.cleanupExcessiveParagraphs(tree);

			// Para Cards, detecta e corrige parágrafos duplos aninhados
			this.fixNestedParagraphs(tree);

			// Para Cards, se há apenas um parágrafo com class="rich-text-paragraph",
			// extraia seu conteúdo diretamente para evitar <p><p></p></p>
			if (
				tree.children.length === 1 &&
				tree.children[0].type === 'element' &&
				tree.children[0].tagName === 'p'
			) {
				const paragraph = tree.children[0] as HastElement;

				// Se é um parágrafo rich-text, extraia o conteúdo
				if (
					paragraph.properties?.class === 'rich-text-paragraph' ||
					String(paragraph.properties?.className || '').includes(
						'rich-text-paragraph'
					)
				) {
					return paragraph.children;
				}

				// Se é um parágrafo simples sem conteúdo complexo, extraia o conteúdo
				const hasComplexContent = paragraph.children.some(
					(child) =>
						child.type === 'element' &&
						!FORMATTING_TAGS.has(child.tagName) &&
						child.tagName !== 'br' &&
						child.tagName !== 'code'
				);

				if (!hasComplexContent) {
					return paragraph.children;
				}
			}

			// Processa componentes aninhados
			this.transformNestedComponents(tree);

			return tree.children;
		} catch {
			return [{ type: 'text', value: decoded } as HastText];
		}
	}

	/**
	 * Corrige parágrafos duplos aninhados como <p><p>...</p></p>
	 */
	private static fixNestedParagraphs(tree: HastRoot): void {
		visit(tree, 'element', (node: HastElement, index, parent) => {
			if (!parent || typeof index !== 'number') return;

			// Detecta <p><p class="rich-text-paragraph">...</p></p>
			if (
				node.tagName === 'p' &&
				node.children.length === 1 &&
				node.children[0].type === 'element' &&
				node.children[0].tagName === 'p'
			) {
				const innerParagraph = node.children[0] as HastElement;

				// Se o parágrafo interno tem class="rich-text-paragraph", promova-o
				if (
					innerParagraph.properties?.class === 'rich-text-paragraph' ||
					String(innerParagraph.properties?.className || '').includes(
						'rich-text-paragraph'
					)
				) {
					if ('children' in parent) {
						parent.children.splice(index, 1, innerParagraph);
						return ['skip', index];
					}
				}
			}
		});
	}

	private static extractTextContent(
		children: (RootContent | ElementContent)[]
	): string {
		const parts: string[] = [];

		for (let i = 0; i < children.length; i++) {
			const child = children[i];

			if (child.type === 'text') {
				parts.push(child.value);
			} else if (child.type === 'element') {
				switch (child.tagName) {
					case 'p':
						const content = this.extractTextContent(child.children);
						if (content.trim()) {
							// Add paragraph spacing only if there are already parts
							if (parts.length > 0) {
								parts.push('\n\n' + content);
							} else {
								parts.push(content);
							}
						}
						break;
					case 'br':
						parts.push('\n');
						break;
					case 'code':
						const code = this.extractTextContent(child.children);
						parts.push(`\`${code}\``);
						break;
					default:
						if (FORMATTING_TAGS.has(child.tagName)) {
							// For formatting tags, preserve the HTML to maintain spacing
							parts.push(toHtml(child));
						} else {
							parts.push(this.extractTextContent(child.children));
						}
				}
			}
		}

		// Join parts preserving the newlines that were added for paragraph spacing
		return parts
			.join('')
			.replace(/\n{3,}/g, '\n\n')
			.trim();
	}
}

// ===== ENHANCED COMPONENT TRANSFORMERS =====

class ComponentTransformers {
	static metadata(
		node: HastElement,
		context: TransformContext,
		parent: HastParent,
		index: number
	): void {
		const { title, description, slug, ...otherProps } = node.properties || {};

		// Only include non-empty values in frontmatter
		if (title && String(title).trim() !== '') {
			context.frontmatter.title = String(title);
		}

		if (description && String(description).trim() !== '') {
			context.frontmatter.description = String(description);
		}

		if (slug && String(slug).trim() !== '') {
			context.frontmatter.slug = String(slug);
		}

		// Handle other properties (like route for API files)
		Object.entries(otherProps).forEach(([key, value]) => {
			if (
				!key.startsWith('data') &&
				key !== 'dataType' &&
				value != null &&
				String(value).trim() !== ''
			) {
				context.frontmatter[key] = String(value);
			}
		});

		if ('children' in parent) {
			parent.children.splice(index, 1);
		}
	}

	static card(node: HastElement): void {
		const props = node.properties || {};
		const newProps: Properties = {};

		// Extract description before processing attributes
		const descriptionValue = props['data-description'] || props.description;

		// Process attributes, avoiding empty values and data-component
		Object.entries(props).forEach(([key, value]) => {
			const cleanKey = key.replace(/^data-?/, '').toLowerCase();
			if (cleanKey !== 'component' && value && String(value).trim() !== '') {
				// Convert kebab-case and specific cases to camelCase for JSX attributes
				let camelKey = cleanKey.replace(/-([a-z])/g, (_, letter) =>
					letter.toUpperCase()
				);

				// Handle specific icon cases that don't use hyphens
				if (camelKey === 'icontype') camelKey = 'iconType';
				if (camelKey === 'iconsize') camelKey = 'iconSize';

				// Don't include description as attribute - it will be content
				if (camelKey !== 'description') {
					newProps[camelKey] = String(value);
				}
			}
		});

		// Process card content - handle description as children content
		let cardContent: HastElement['children'] = [];

		// First check for data-description attribute (standalone cards)
		if (descriptionValue && String(descriptionValue).trim() !== '') {
			const descriptionHtml = String(descriptionValue);

			// Remove HTML entity encoding
			const cleanHtml = descriptionHtml
				.replace(/&quot;/g, '"')
				.replace(/&#x22;/g, '"')
				.replace(/&lt;/g, '<')
				.replace(/&gt;/g, '>')
				.replace(/&amp;/g, '&');

			// Process rich text paragraphs properly
			const processedHtml = processRichTextParagraphs(cleanHtml);

			if (processedHtml.trim()) {
				try {
					const contentTree = unified()
						.use(rehypeParse, { fragment: true })
						.parse(processedHtml);
					cardContent = contentTree.children as HastElement['children'];
				} catch (error) {
					console.warn('❌ Card content parse failed:', error);
					cardContent = [{ type: 'text', value: processedHtml }];
				}
			}
		}
		// Then check for existing children (cards inside CardList)
		else if (node.children.length > 0) {
			// Process HTML content that comes as text children
			const textContent = node.children
				.filter((child) => child.type === 'text')
				.map((child) => (child as { value: string }).value)
				.join('');

			if (textContent.trim()) {
				// Decode HTML entities in text content
				const decodedContent = textContent
					.replace(/&lt;/g, '<')
					.replace(/&gt;/g, '>')
					.replace(/&quot;/g, '"')
					.replace(/&amp;/g, '&');

				// Process the HTML content
				const processedHtml = processRichTextParagraphs(decodedContent);

				if (processedHtml.trim()) {
					try {
						const contentTree = unified()
							.use(rehypeParse, { fragment: true })
							.parse(processedHtml);
						cardContent = contentTree.children as HastElement['children'];
					} catch {
						cardContent = [{ type: 'text', value: processedHtml }];
					}
				}
			} else {
				// Use existing children as-is
				cardContent = node.children;
			}
		}

		node.properties = newProps;
		node.children = cardContent;
		node.tagName = 'Card';
	}

	static cardList(node: HastElement): void {
		node.tagName = 'CardList';

		const props = node.properties || {};
		const cols = props.cols || props['data-cols'];

		// Set the cols property if it exists
		const newProps: Properties = {};
		if (cols) {
			newProps.cols = String(cols);
		}

		// Process cards from data-cards JSON attribute (legacy format)
		const dataCards = props['data-cards'];
		if (dataCards && typeof dataCards === 'string') {
			try {
				const cardsData = JSON.parse(dataCards);
				if (Array.isArray(cardsData)) {
					// Create Card elements as children
					node.children = cardsData.map((cardData: CardData) => {
						const cardProps: Properties = {};

						// Add card properties, avoiding empty values
						if (cardData.title) cardProps.title = cardData.title;
						if (cardData.link) cardProps.link = cardData.link;
						if (cardData.icon) cardProps.icon = cardData.icon;
						if (cardData['icon-type'] && cardData['icon-type'] !== 'regular')
							cardProps.iconType = cardData['icon-type'];
						if (cardData['icon-size'] && cardData['icon-size'] !== '48px')
							cardProps.iconSize = cardData['icon-size'];
						if (cardData.image) cardProps.image = cardData.image;

						// Process description as content
						let cardContent: HastElement['children'] = [];
						if (cardData.description) {
							// Clean up the description HTML
							const cleanDescription = processRichTextParagraphs(
								String(cardData.description)
									.replace(/&quot;/g, '"')
									.replace(/&#x22;/g, '"')
									.replace(/&lt;/g, '<')
									.replace(/&gt;/g, '>')
									.replace(/&amp;/g, '&')
							);

							if (cleanDescription.trim()) {
								try {
									const contentTree = unified()
										.use(rehypeParse, { fragment: true })
										.parse(cleanDescription);
									cardContent = contentTree.children as HastElement['children'];
								} catch {
									cardContent = [{ type: 'text', value: cleanDescription }];
								}
							}
						}

						return {
							type: 'element' as const,
							tagName: 'Card',
							properties: cardProps,
							children: cardContent,
						};
					});
				}
			} catch (error) {
				console.warn('Failed to parse cards data:', error);
			}
		} else {
			// Process existing child cards (current format)
			node.children.forEach((child) => {
				if (
					child.type === 'element' &&
					(child.tagName === 'card' ||
						child.properties?.['dataType'] === 'card')
				) {
					// Process each card individually
					this.card(child);
				}
			});
		}

		node.properties = newProps;
	}

	static apiCardList(node: HastElement): HastElement[] {
		// For API cards, we don't need a wrapper - just convert to individual APICard components
		const props = node.properties || {};
		const dataCards = props['data-cards'] || props['dataCards'];

		if (dataCards && typeof dataCards === 'string') {
			try {
				// The JSON might have unescaped quotes in HTML descriptions, let's fix it
				let cleanedDataCards = dataCards;

				// First decode HTML entities if they exist
				cleanedDataCards = cleanedDataCards
					.replace(/&quot;/g, '"')
					.replace(/&lt;/g, '<')
					.replace(/&gt;/g, '>')
					.replace(/&amp;/g, '&');

				// Fix the specific issue with class="rich-text-paragraph" in descriptions
				cleanedDataCards = cleanedDataCards.replace(
					/"description":"<p class="rich-text-paragraph">([^<]*)<\/p>"/g,
					(_, content) => {
						return `"description":"${content}"`;
					}
				);

				const cardsData = JSON.parse(cleanedDataCards);
				if (Array.isArray(cardsData)) {
					// Create APICard elements as children
					return cardsData.map((cardData: Record<string, unknown>) => {
						// Build properties in the expected order: title, description, link, type
						const cardProps: Properties = {};

						// Fix title (trim extra space)
						if (typeof cardData.title === 'string') {
							cardProps.title = cardData.title.trim();
						}

						// Add description
						if (cardData.description) {
							// Clean up the description HTML
							const cleanDescription = processRichTextParagraphs(
								String(cardData.description)
									.replace(/&quot;/g, '"')
									.replace(/&#x22;/g, '"')
									.replace(/&lt;/g, '<')
									.replace(/&gt;/g, '>')
									.replace(/&amp;/g, '&')
							);

							if (cleanDescription.trim()) {
								// For API cards, we just want the plain text description
								cardProps.description = cleanDescription;
							}
						}

						// Generate link from title if not provided
						if (typeof cardData.link === 'string') {
							cardProps.link = cardData.link;
						} else if (typeof cardData.title === 'string') {
							// Generate slug from title
							const slug = cardData.title
								.trim()
								.toLowerCase()
								.replace(/\s+/g, '-')
								.replace(/[^a-z0-9-]/g, '');
							cardProps.link = `/${slug}`;
						}

						// Add type (convert to lowercase)
						if (typeof cardData.type === 'string') {
							cardProps.type = cardData.type.toLowerCase();
						}

						return {
							type: 'element' as const,
							tagName: 'APICard',
							properties: cardProps,
							children: [], // APICard uses attributes only
						};
					});
				}
			} catch (error) {
				console.warn('Failed to parse API cards data:', error);
			}
		}

		// Return empty array if no cards to process
		return [];
	}

	static accordion(node: HastElement): void {
		const props = node.properties || {};
		const newProps: Properties = {};

		if (props.title) {
			newProps.title = String(props.title);
		}

		// Use the same processing as Card to preserve paragraph structure
		let accordionContent: HastElement['children'] = [];

		// First check for description attribute (standalone accordions)
		const descriptionValue = props.description;
		if (descriptionValue && String(descriptionValue).trim() !== '') {
			const descriptionHtml = String(descriptionValue);

			// Remove HTML entity encoding
			const cleanHtml = descriptionHtml
				.replace(/&quot;/g, '"')
				.replace(/&#x22;/g, '"')
				.replace(/&lt;/g, '<')
				.replace(/&gt;/g, '>')
				.replace(/&amp;/g, '&');

			// Process rich text paragraphs properly
			const processedHtml = processRichTextParagraphs(cleanHtml);

			if (processedHtml.trim()) {
				try {
					const contentTree = unified()
						.use(rehypeParse, { fragment: true })
						.parse(processedHtml);
					accordionContent = contentTree.children as HastElement['children'];
				} catch (error) {
					console.warn('❌ Accordion content parse failed:', error);
					accordionContent = [{ type: 'text', value: processedHtml }];
				}
			}
		}
		// Then check for existing children (accordions inside AccordionGroup)
		else if (node.children.length > 0) {
			const textContent = toHtml(node.children);

			if (textContent.trim()) {
				// Decode HTML entities in text content
				const decodedContent = textContent
					.replace(/&lt;/g, '<')
					.replace(/&gt;/g, '>')
					.replace(/&quot;/g, '"')
					.replace(/&amp;/g, '&');

				// Process the HTML content
				const processedHtml = processRichTextParagraphs(decodedContent);

				if (processedHtml.trim()) {
					try {
						const contentTree = unified()
							.use(rehypeParse, { fragment: true })
							.parse(processedHtml);
						accordionContent = contentTree.children as HastElement['children'];
					} catch {
						accordionContent = [{ type: 'text', value: processedHtml }];
					}
				}
			} else {
				// Use existing children as-is
				accordionContent = node.children;
			}
		}

		node.properties = newProps;
		node.children = accordionContent;
		node.tagName = 'Accordion';
	}

	static accordionGroup(node: HastElement): void {
		node.tagName = 'AccordionGroup';
		node.properties = {};

		const dataAccordions = node.properties?.['dataAccordions'];
		if (dataAccordions && typeof dataAccordions === 'string') {
			try {
				const accordions = JSON.parse(dataAccordions);
				if (Array.isArray(accordions)) {
					node.children = accordions.map((accordion) => {
						const accordionNode: HastElement = {
							type: 'element',
							tagName: 'Accordion',
							properties: { title: accordion.title || '' },
							children: [],
						};

						if (accordion.description) {
							// Use the same processing as Card and Accordion to preserve paragraph structure
							const descriptionHtml = String(accordion.description);

							// Remove HTML entity encoding
							const cleanHtml = descriptionHtml
								.replace(/&quot;/g, '"')
								.replace(/&#x22;/g, '"')
								.replace(/&lt;/g, '<')
								.replace(/&gt;/g, '>')
								.replace(/&amp;/g, '&');

							// Process rich text paragraphs properly
							const processedHtml = processRichTextParagraphs(cleanHtml);

							if (processedHtml.trim()) {
								try {
									const contentTree = unified()
										.use(rehypeParse, { fragment: true })
										.parse(processedHtml);
									accordionNode.children =
										contentTree.children as HastElement['children'];
								} catch (error) {
									console.warn(
										'❌ AccordionGroup accordion content parse failed:',
										error
									);
									accordionNode.children = [
										{ type: 'text', value: processedHtml },
									];
								}
							}
						}

						return accordionNode;
					});
				}
			} catch (error) {
				console.warn('Failed to parse accordion data:', error);
			}
		}
	}

	static callout(node: HastElement): void {
		const props = node.properties || {};
		const newProps: Properties = {};

		Object.entries(props).forEach(([key, value]) => {
			if (key === 'type' && value !== 'callout') {
				newProps.type = String(value);
			} else if (key === 'title') {
				newProps.title = String(value);
			}
		});

		// Processa conteúdo complexo
		if (props.description) {
			const complexContent = EnhancedContentProcessor.processComplexContent(
				String(props.description)
			);
			node.children = complexContent as HastElement['children'];
		} else if (node.children.length > 0) {
			const childrenHtml = toHtml(node.children);
			if (childrenHtml.trim()) {
				const complexContent =
					EnhancedContentProcessor.processComplexContent(childrenHtml);
				node.children = complexContent as HastElement['children'];
			}
		}

		node.properties = newProps;
		node.tagName = 'Callout';
	}

	static mermaid(node: HastElement): void {
		const props = node.properties || {};
		const code = props['dataCode'] || props['data-code'] || '';

		if (code) {
			// Convert to standard Mermaid code block structure
			node.tagName = 'pre';
			node.properties = {
				dataType: 'mermaidBlock',
				dataLanguage: 'mermaid',
			};
			node.children = [
				{
					type: 'element',
					tagName: 'code',
					properties: {
						className: ['language-mermaid'],
					},
					children: [
						{
							type: 'text',
							value: String(code),
						},
					],
				},
			];
		}
	}

	static steps(node: HastElement): void {
		const props = node.properties || {};

		if (props['dataSteps']) {
			try {
				const stepsData = JSON.parse(String(props['dataSteps']));
				if (Array.isArray(stepsData)) {
					node.children = this.processStepsRecursively(stepsData);
				}
			} catch (error) {
				console.warn('Error parsing steps data:', error);
			}
		}

		node.properties = {};
		node.tagName = 'Steps';
	}

	private static processStepsRecursively(steps: StepData[]): HastElement[] {
		const elements: HastElement[] = [];

		for (const step of steps) {
			const titleSize = step.titleSize || 'h3';

			// Add title
			elements.push({
				type: 'element',
				tagName: titleSize,
				properties: {},
				children: [{ type: 'text', value: step.title || '' }],
			});

			// Processa conteúdo complexo que pode conter componentes
			if (step.content) {
				const complexContent = EnhancedContentProcessor.processComplexContent(
					step.content
				);
				elements.push(
					...complexContent.filter(
						(node): node is HastElement => node.type === 'element'
					)
				);
			}

			// Add sub-steps recursivamente
			if (step.subSteps && step.subSteps.length > 0) {
				const subStepTitleSize = step.subSteps[0]?.titleSize || 'h4';
				elements.push({
					type: 'element',
					tagName: 'Steps',
					properties: { titleSize: subStepTitleSize },
					children: this.processStepsRecursively(step.subSteps),
				});
			}
		}

		return elements;
	}

	static tabs(node: HastElement): void {
		const props = node.properties || {};

		if (props['dataTabs']) {
			try {
				const tabsData = JSON.parse(String(props['dataTabs']));
				if (Array.isArray(tabsData)) {
					node.children = tabsData.map((tab) => {
						const tabElement: HastElement = {
							type: 'element',
							tagName: 'Tab',
							properties: {
								value: tab.value || '',
								...(tab.label &&
									tab.label !== tab.value && { label: tab.label }),
								...(tab.default && { default: 'true' }),
							},
							children: [],
						};

						// Processa conteúdo complexo do tab
						if (tab.content) {
							const complexContent =
								EnhancedContentProcessor.processComplexContent(tab.content);
							tabElement.children = complexContent as HastElement['children'];
						}

						return tabElement;
					});
				}
			} catch (error) {
				console.warn('Error parsing tabs data:', error);
			}
		}

		node.properties = {};
		node.tagName = 'Tabs';
	}

	static image(node: HastElement): void {
		const props = node.properties || {};
		const newProps: Properties = {};

		const imgElement = node.children.find(
			(child) => child.type === 'element' && child.tagName === 'img'
		) as HastElement | undefined;

		if (imgElement?.properties) {
			const imgProps = imgElement.properties;
			if (imgProps.src) newProps.src = String(imgProps.src);
			if (imgProps['dataSrcDark'])
				newProps.srcDark = String(imgProps['dataSrcDark']);
			if (imgProps.alt) newProps.alt = String(imgProps.alt);
		}

		if (props['dataSize']) {
			newProps.size = String(props['dataSize']);
		}

		node.properties = newProps;
		node.tagName = 'Image';
		node.children = [];
	}

	static video(node: HastElement): void {
		const props = node.properties || {};
		const newProps: Properties = {};

		const iframeElement = node.children.find(
			(child) => child.type === 'element' && child.tagName === 'iframe'
		) as HastElement | undefined;

		if (iframeElement?.properties) {
			const iframeProps = iframeElement.properties;
			if (iframeProps.src) newProps.src = String(iframeProps.src);
			if (iframeProps.title) newProps.alt = String(iframeProps.title);
		}

		if (props['dataWidth']) {
			newProps.width = String(props['dataWidth']);
		}

		node.properties = newProps;
		node.tagName = 'Video';
		node.children = [];
	}

	static customHtml(node: HastElement): void {
		// For Custom HTML, we want to preserve the HTML exactly as is
		// Use attribute format to avoid MDX parsing issues
		const props = node.properties || {};
		const htmlContent =
			props['data-html'] ||
			node.children
				.filter((child) => child.type === 'text')
				.map((child) => (child as { value: string }).value)
				.join('');

		// Processing CustomHtml component

		node.tagName = 'CustomHtml';

		// Use html attribute to avoid parsing issues with children
		if (htmlContent) {
			// Escape quotes and newlines for safe attribute value
			const escapedContent = String(htmlContent)
				.replace(/\\/g, '\\\\')
				.replace(/"/g, '\\"')
				.replace(/\n/g, '\\n')
				.replace(/\r/g, '\\r');

			node.properties = { html: escapedContent };
		} else {
			node.properties = {};
		}

		node.children = []; // No children, content is in attribute

		// CustomHtml transformation complete
	}

	static hint(node: HastElement): void {
		node.tagName = 'Hint';
		const { hint, text, dataHint, dataText } = node.properties || {};

		// Extract hint and text from various possible attributes
		const hintValue = hint || dataHint || '';
		const textValue = text || dataText || '';

		// Only keep the hint attribute, remove all other attributes that shouldn't be in MDX
		node.properties = {
			hint: hintValue,
		};

		// Set the text content as children if available
		if (textValue) {
			node.children = [{ type: 'text', value: String(textValue) }];
		}
	}

	static checklist(node: HastElement): void {
		node.tagName = 'Checklist';
		const dataItems = node.properties?.['dataItems'];

		if (dataItems && typeof dataItems === 'string') {
			try {
				const jsonString = (dataItems as string).replace(/&quot;/g, '"');
				const items = JSON.parse(jsonString);

				// Create a proper list structure with individual list items
				const children: ElementContent[] = [];

				// Add initial newline for proper spacing
				children.push({ type: 'text', value: '\n' } as HastText);

				// Create a ul element with li children
				const listElement: HastElement = {
					type: 'element',
					tagName: 'ul',
					properties: {},
					children: items.map(
						(item: { text: string }) =>
							({
								type: 'element',
								tagName: 'li',
								properties: {},
								children: [{ type: 'text', value: item.text } as HastText],
							}) as HastElement
					),
				};

				children.push(listElement);

				// Add final newline
				children.push({ type: 'text', value: '\n' } as HastText);

				node.children = children;
			} catch (e) {
				console.error('Failed to parse checklist JSON:', e);
				node.children = [];
			}
		}

		node.properties = {};
	}

	static parameter(node: HastElement): void {
		const dataParameter = node.properties?.['dataParameter'];

		if (dataParameter && typeof dataParameter === 'string') {
			try {
				// Fix unescaped quotes in the JSON string
				// This regex matches the description field and captures everything until the closing quote followed by } or ,
				const fixedDataParameter = dataParameter.replace(
					/"description":"(.*?)"(?=\}|,)/g,
					(_match, content) => {
						// Escape internal quotes
						const escapedContent = content.replace(/([^\\])"/g, '$1\\"');
						return `"description":"${escapedContent}"`;
					}
				);

				// Parse the JSON data from data-parameter attribute
				const paramData = JSON.parse(fixedDataParameter);

				// Create properties for the Parameter component
				const newProps: Properties = {
					name: paramData.name || '',
					type: paramData.type || '',
				};

				// Add 'required' attribute only if it's true
				if (paramData.required === true) {
					newProps.required = 'true';
				}

				// Add 'default' attribute if it has a value
				if (paramData.default && paramData.default.trim() !== '') {
					newProps.default = paramData.default;
				}

				// Process the description content
				if (paramData.description) {
					// First, let's try to preserve the original HTML structure better
					// by processing it more carefully
					let descriptionHtml = paramData.description;

					// Decode HTML entities
					descriptionHtml = descriptionHtml
						.replace(/&quot;/g, '"')
						.replace(/&lt;/g, '<')
						.replace(/&gt;/g, '>')
						.replace(/&amp;/g, '&');

					// Parse the HTML to extract just the content inside the paragraph
					try {
						const tree = unified()
							.use(rehypeParse, { fragment: true })
							.parse(descriptionHtml);

						// Find the paragraph with rich-text-paragraph class
						const paragraph = tree.children.find((child) => {
							return (
								child.type === 'element' &&
								child.tagName === 'p' &&
								(child.properties?.class === 'rich-text-paragraph' ||
									String(child.properties?.className || '').includes(
										'rich-text-paragraph'
									))
							);
						}) as HastElement;

						if (paragraph && paragraph.children) {
							// Process children to ensure proper spacing
							const processedChildren: HastNode[] = [];

							paragraph.children.forEach((child, index) => {
								processedChildren.push(child);

								// Add space after formatting elements if the next element exists
								// and is not already a text node starting with space
								if (
									child.type === 'element' &&
									FORMATTING_TAGS.has(child.tagName) &&
									index < paragraph.children.length - 1
								) {
									const nextChild = paragraph.children[index + 1];

									// If next child is an element, we need a space between them
									if (nextChild.type === 'element') {
										processedChildren.push({
											type: 'text',
											value: ' ',
										} as HastText);
									}
									// If next child is text but doesn't start with space, add space
									else if (
										nextChild.type === 'text' &&
										(nextChild as HastText).value &&
										!(nextChild as HastText).value.startsWith(' ') &&
										(nextChild as HastText).value.trim() !== ''
									) {
										// Insert a space before the next text node
										processedChildren.push({
											type: 'text',
											value: ' ',
										} as HastText);
									}
								}
							});

							node.children = processedChildren as HastElement['children'];
						}
					} catch (error) {
						console.warn('Failed to parse parameter description HTML:', error);
						// Fallback to simple text content
						node.children = [{ type: 'text', value: descriptionHtml }];
					}
				}

				node.properties = newProps;
				node.tagName = 'Parameter';
			} catch (error) {
				console.warn('Failed to parse parameter data:', error);
			}
		}
	}
}

// ===== MAIN TRANSFORM PLUGIN =====

const enhancedTransformPlugin = () => {
	return (tree: HastRoot, file: VFile) => {
		const context: TransformContext = {
			frontmatter: {},
			componentMap: new Map(),
		};

		// Track processed nodes to prevent infinite recursion
		const processedNodes = new WeakSet();

		visit(tree, 'element', (node, index, parent) => {
			if (!parent || typeof index !== 'number') return;

			// Skip if already processed
			if (processedNodes.has(node)) return;

			switch (node.tagName) {
				case 'metadata':
					ComponentTransformers.metadata(node, context, parent, index);
					return ['skip', index];

				case 'head':
					if (node.properties?.['dataType'] === 'metadata') {
						visit(node, 'element', (child) => {
							if (
								child.tagName === 'meta' &&
								child.properties?.name &&
								child.properties?.content &&
								String(child.properties.content).trim() !== ''
							) {
								context.frontmatter[String(child.properties.name)] = String(
									child.properties.content
								);
							}
						});
						if ('children' in parent) {
							parent.children.splice(index, 1);
						}
						return ['skip', index];
					}
					break;

				case 'card':
					ComponentTransformers.card(node);
					break;

				case 'cardlist':
					ComponentTransformers.cardList(node);
					break;

				case 'div':
					const dataType =
						node.properties?.['dataType'] || node.properties?.['data-type'];
					switch (dataType) {
						case 'card':
							ComponentTransformers.card(node);
							break;
						case 'api-card-list':
							// Handle API card list specially - replace with multiple APICard elements
							const apiCards = ComponentTransformers.apiCardList(node);
							if (apiCards.length > 0 && 'children' in parent) {
								parent.children.splice(index, 1, ...apiCards);
								return ['skip', index + apiCards.length - 1];
							}
							break;
						case 'accordion':
							ComponentTransformers.accordion(node);
							break;
						case 'accordion-group':
							ComponentTransformers.accordionGroup(node);
							break;
						case 'callout':
							ComponentTransformers.callout(node);
							break;
						case 'mermaid':
							ComponentTransformers.mermaid(node);
							break;
						case 'steps':
							ComponentTransformers.steps(node);
							break;
						case 'tabs':
							ComponentTransformers.tabs(node);
							break;
						case 'image':
							ComponentTransformers.image(node);
							break;
						case 'video':
							ComponentTransformers.video(node);
							break;
						case 'custom-html':
							ComponentTransformers.customHtml(node);
							break;
						case 'checklist':
							ComponentTransformers.checklist(node);
							break;
						case 'parameter':
							ComponentTransformers.parameter(node);
							break;
					}
					break;

				case 'span':
					const spanDataType = node.properties?.['dataType'];
					if (spanDataType === 'custom-html') {
						ComponentTransformers.customHtml(node);
					} else if (spanDataType === 'hint') {
						ComponentTransformers.hint(node);
					}
					break;

				case 'hint':
					ComponentTransformers.hint(node);
					break;

				case 'p':
					if (isEmptyNode(node)) {
						// Remove empty paragraphs instead of converting to <br>
						if ('children' in parent) {
							parent.children.splice(index, 1);
						}
						return ['skip', index];
					}
					break;

				case 'table':
					// Mark this node as processed
					processedNodes.add(node);

					// Check if table has searchbar attribute
					const hasSearchbar =
						node.properties?.['dataSearchbar'] === 'true' ||
						node.properties?.['data-searchbar'] === 'true' ||
						node.properties?.['searchbar'] === true;

					// Prevent infinite recursion by checking if we're already inside a Searchbar
					let isInsideSearchbar = false;
					if (parent && 'tagName' in parent && parent.tagName === 'Searchbar') {
						isInsideSearchbar = true;
					}

					visit(node, 'element', (el, elIndex, elParent) => {
						if (
							['table', 'tbody', 'thead', 'tfoot', 'tr', 'th', 'td'].includes(
								el.tagName
							)
						) {
							// Preserve searchbar attribute for table element
							if (el.tagName === 'table' && hasSearchbar) {
								el.properties = { 'data-searchbar': 'true' };
							} else {
								el.properties = {};
							}
						}

						if (
							el.tagName === 'colgroup' &&
							elParent &&
							typeof elIndex === 'number'
						) {
							elParent.children.splice(elIndex, 1);
							return ['skip', elIndex];
						}

						if (el.tagName === 'th' || el.tagName === 'td') {
							const newChildren: HastElement['children'] = [];
							el.children.forEach((child) => {
								if (child.type === 'element' && child.tagName === 'p') {
									if (isEmptyNode(child)) {
										// Skip empty paragraphs in table cells
										return;
									} else {
										newChildren.push(...child.children);
									}
								} else {
									newChildren.push(child);
								}
							});
							el.children = newChildren;
						}
					});

					// If table has searchbar and we're not already inside a Searchbar, wrap it
					if (
						hasSearchbar &&
						!isInsideSearchbar &&
						parent &&
						typeof index === 'number' &&
						'children' in parent
					) {
						const searchbarElement: HastElement = {
							type: 'element',
							tagName: 'Searchbar',
							properties: {},
							children: [node],
						};
						// Mark the new Searchbar element as processed
						processedNodes.add(searchbarElement);
						parent.children.splice(index, 1, searchbarElement);
						return ['skip', index];
					}
					break;
			}
		});

		if (Object.keys(context.frontmatter).length > 0) {
			file.data.frontmatter = context.frontmatter;
		}
	};
};

// ===== EXPORT =====

export const htmlToMdx = async (html: string): Promise<string> => {
	const validation = validateHtmlForMdx(html);
	if (!validation.isValid) {
		throw new Error(`Invalid HTML input: ${validation.errors.join(', ')}`);
	}

	if (validation.warnings.length > 0) {
		console.warn('⚠️ Conversion warnings:', validation.warnings);
	}

	const rehypeRemarkOptions: RehypeRemarkOptions = {
		handlers: {
			// Preserve formatting tags as HTML
			...Object.fromEntries(
				Array.from(FORMATTING_TAGS).map((tag) => [
					tag,
					(_h: unknown, node: HastElement) => ({
						type: 'html',
						value: toHtml(node),
					}),
				])
			),

			// Handle tables
			table: (_h: unknown, node: HastElement) => {
				visit(node, 'element', (el, index, parent) => {
					if (
						el.tagName === 'colgroup' &&
						parent &&
						typeof index === 'number'
					) {
						parent.children.splice(index, 1);
						return ['skip', index];
					}
				});
				return { type: 'html', value: toHtml(node) };
			},

			// Handle breaks - convert to line breaks instead of HTML
			br: () => ({ type: 'break' }),

			// Handle code blocks
			pre: (_h: unknown, node: HastElement) => {
				const codeElement = node.children.find(
					(child) => child.type === 'element' && child.tagName === 'code'
				) as HastElement | undefined;

				// Handle Mermaid code blocks
				if (codeElement && node.properties?.['dataType'] === 'mermaidBlock') {
					const extractText = (el: HastElement): string => {
						return el.children
							.map((child) => {
								if (child.type === 'text') return child.value;
								if (child.type === 'element') return extractText(child);
								return '';
							})
							.join('');
					};

					return {
						type: 'code',
						lang: 'mermaid',
						value: extractText(codeElement),
					};
				}

				// Handle regular code blocks
				if (codeElement && node.properties?.['dataType'] === 'codeBlock') {
					const language =
						node.properties['dataLanguage'] ||
						(codeElement.properties?.className as string)?.replace(
							'language-',
							''
						) ||
						'text';

					const extractText = (el: HastElement): string => {
						return el.children
							.map((child) => {
								if (child.type === 'text') return child.value;
								if (child.type === 'element') return extractText(child);
								return '';
							})
							.join('');
					};

					return {
						type: 'code',
						lang: String(language),
						value: extractText(codeElement),
					};
				}

				return { type: 'html', value: toHtml(node) };
			},

			// MDX Components
			...Object.fromEntries(
				[
					'Card',
					'CardList',
					'APICard',
					'Accordion',
					'AccordionGroup',
					'Callout',
					'Steps',
					'Tabs',
					'Tab',
					'Image',
					'Video',
					'CustomHtml',
					'Hint',
					'Checklist',
					'Parameter',
					'Searchbar',
				].map((componentName) => [
					componentName,
					(h: { all: (node: HastElement) => unknown[] }, node: HastElement) => {
						const attributes = createJsxAttributes(node.properties);
						const children = h.all(node) as (
							| BlockContent
							| DefinitionContent
						)[];

						// Special handling for Hint component as inline
						if (componentName === 'Hint') {
							return {
								type: 'mdxJsxTextElement' as const,
								name: componentName,
								attributes,
								children,
							};
						}

						// Special handling for APICard to ensure proper multiline formatting
						if (componentName === 'APICard') {
							return {
								type: 'mdxJsxFlowElement' as const,
								name: componentName,
								attributes,
								children: [],
							};
						}

						return {
							type: 'mdxJsxFlowElement' as const,
							name: componentName,
							attributes,
							children:
								componentName === 'Image' || componentName === 'CustomHtml'
									? []
									: children,
						};
					},
				])
			),
		},
	};

	const addFrontmatterPlugin = () => (tree: MdastRoot, file: VFile) => {
		const frontmatter = file.data.frontmatter as Record<string, string>;
		if (frontmatter && Object.keys(frontmatter).length > 0) {
			tree.children.unshift({
				type: 'yaml',
				value: Object.entries(frontmatter)
					.map(([key, value]) => `${key}: "${value}"`)
					.join('\n'),
			});
		}
	};

	// Type guard functions
	const isMdxJsxFlowElement = (node: MdastNode): node is MdxJsxFlowElement => {
		return node.type === 'mdxJsxFlowElement';
	};

	const isTextNode = (node: MdastNode): node is TextNode => {
		return node.type === 'text';
	};

	// Plugin to fix Parameter component formatting
	const fixParameterFormattingPlugin = () => (tree: MdastRoot) => {
		visit(tree, (node: MdastNode) => {
			// Type guard to check if it's an MDX component
			if (isMdxJsxFlowElement(node) && node.name === 'Parameter') {
				// Ensure children have no extra indentation
				if (node.children && node.children.length > 0) {
					// Process children to preserve spaces around formatting elements

					const processChildren = (children: MdastNode[]): MdastNode[] => {
						const result: MdastNode[] = [];

						for (let i = 0; i < children.length; i++) {
							const child = children[i];

							if (isTextNode(child)) {
								// Preserve spaces but normalize excessive whitespace
								let value = child.value.replace(/\s+/g, ' ');

								// Don't trim leading space if previous element was non-text
								if (i > 0 && !isTextNode(children[i - 1])) {
									value = value.replace(/^\s+/, ' ');
								}

								// Don't trim trailing space if next element is non-text
								if (i < children.length - 1 && !isTextNode(children[i + 1])) {
									value = value.replace(/\s+$/, ' ');
								}

								if (value) {
									result.push({ type: 'text', value } as TextNode);
								}
							} else {
								// Non-text nodes (like inlineCode, html) are preserved as-is
								result.push(child);
							}
						}

						return result;
					};

					// Process the children to preserve spacing
					const processedChildren = processChildren(node.children);

					// Add proper newlines for MDX formatting

					const finalChildren: MdastNode[] = [];

					if (processedChildren.length > 0) {
						// Add leading newline
						finalChildren.push({ type: 'text', value: '\n' } as TextNode);

						// Add processed children
						finalChildren.push(...processedChildren);

						// Add trailing newline
						finalChildren.push({ type: 'text', value: '\n' } as TextNode);
					}

					node.children = finalChildren;
				}
			}
		});
	};

	const file = await unified()
		.use(rehypeParse, { fragment: true })
		.use(enhancedTransformPlugin)
		.use(rehypeRemark, rehypeRemarkOptions)
		.use(remarkFrontmatter, ['yaml'])
		.use(remarkMdx)
		.use(remarkGfm)
		.use(addFrontmatterPlugin)
		.use(fixParameterFormattingPlugin)
		.use(remarkStringify, {
			bullet: '-',
			fence: '`',
			fences: true,
			incrementListMarker: false,
			emphasis: '*',
			strong: '*',
			rule: '-',
			ruleRepetition: 3,
			ruleSpaces: false,
			listItemIndent: 'one',
			handlers: {
				// Custom handler for breaks to avoid <br /> tags
				break: () => '\n',
			},
			join: [
				(left, right) =>
					left.type === 'mdxJsxFlowElement' ||
					right.type === 'mdxJsxFlowElement'
						? 1
						: null,
				// Preserve line breaks between paragraphs in MDX components
				(left, right) =>
					left.type === 'paragraph' && right.type === 'paragraph' ? 1 : null,
			],
		})
		.process(html);

	let mdx = String(file);

	// Enhanced cleanup to prevent excessive paragraph tags and line breaks
	mdx = mdx
		.replace(/&#x20;/g, ' ')
		.replace(/&#x([0-9A-Fa-f]+);/g, (match, hex) => {
			const code = parseInt(hex, 16);
			return code === 32 ||
				(code >= 65 && code <= 90) ||
				(code >= 97 && code <= 122)
				? String.fromCharCode(code)
				: match;
		})
		.replace(/\\([<>*\[\]])/g, '$1')
		.replace(/[ \t]+\n/g, '\n')
		.replace(/\n{4,}/g, '\n\n\n')
		// Remove excessive empty paragraphs that might be generated
		.replace(/<p>\s*<\/p>/g, '')
		.replace(/\n\s*\n\s*\n/g, '\n\n')
		// Fix double paragraph patterns in Card content
		.replace(
			/<p class="rich-text-paragraph"><p class="rich-text-paragraph">/g,
			'<p class="rich-text-paragraph">'
		)
		.replace(/<\/p><\/p>/g, '</p>')
		// Format APICard components as multiline
		.replace(/<APICard ([^>]+) \/>/g, (_, attrs) => {
			// Parse attributes and format them multiline
			const attrPairs = attrs.match(/(\w+)="([^"]*)"/g) || [];
			const formattedAttrs = attrPairs
				.map((attr: string) => `${attr}`)
				.join('\n');
			return `<APICard\n${formattedAttrs}\n/>`;
		})
		// Clean up spaces around components
		.replace(/\n\s*(<[A-Z][^>]*>)/g, '\n\n$1')
		.replace(/(<\/[A-Z][^>]*>)\s*\n/g, '$1\n\n')
		// Remove <br /> tags that might have been generated and replace with line breaks
		.replace(/<br\s*\/?>/g, '\n')
		// Clean up excessive line breaks
		.replace(/\n{3,}/g, '\n\n');

	// Normalize self-closing tags
	Array.from(SELF_CLOSING_TAGS).forEach((tag) => {
		const pattern = new RegExp(`<${tag}(\\s[^>]*)?(?<!/)>`, 'gi');
		mdx = mdx.replace(pattern, (_match, attrs) =>
			attrs ? `<${tag}${attrs} />` : `<${tag} />`
		);
	});

	// Final cleanup to ensure proper spacing
	mdx = mdx
		.replace(/\n{3,}/g, '\n\n')
		.replace(/^\n+/, '')
		.replace(/\n+$/, '\n')
		// Remove indentation from list items inside Checklist components
		.replace(/(<Checklist>[\s\S]*?<\/Checklist>)/g, (match) => {
			return match.replace(/^\s+(-\s)/gm, '$1');
		})
		// Fix Parameter component formatting
		.replace(/(<Parameter[^>]*>)\s*\n\s*/g, '$1\n')
		.replace(/\n\s*(<\/Parameter>)/g, '\n$1')
		// Remove extra blank lines between Parameter components
		.replace(/(<\/Parameter>)\n\n+(<Parameter)/g, '$1\n$2')
		// Fix spacing issues in Parameter components - preserve spaces between formatting elements
		.replace(/(<Parameter[^>]*>[\s\S]*?<\/Parameter>)/g, (match) => {
			// Fix spacing between formatting elements - comprehensive approach
			return (
				match
					// Remove excessive newlines and spaces between any formatting elements
					.replace(
						/(<\/(?:strong|em|s|del|b|i|u|strike)>)\s*\n\s*(<(?:strong|em|s|del|b|i|u|strike|code)[^>]*>)/g,
						'$1 $2'
					)
					.replace(
						/(<\/(?:strong|em|s|del|b|i|u|strike)>)\s*\n\s*(`[^`]+`)/g,
						'$1 $2'
					)
					.replace(
						/(`[^`]+`)\s*\n\s*(<(?:strong|em|s|del|b|i|u|strike)[^>]*>)/g,
						'$1 $2'
					)
					// Handle nested elements like <strong><em><s>text</s></em></strong>
					.replace(
						/(<\/(?:strong|em|s|del|b|i|u|strike)>)\s*\n\s*(<\/(?:strong|em|s|del|b|i|u|strike)>)/g,
						'$1$2'
					)
					// Handle text nodes between elements - more comprehensive
					.replace(
						/(<\/(?:strong|em|s|del|b|i|u|strike|code)>)\s*\n\s+([a-zA-Z])/g,
						'$1 $2'
					)
					.replace(
						/([a-zA-Z])\s*\n\s*(<(?:strong|em|s|del|b|i|u|strike|code)[^>]*>)/g,
						'$1 $2'
					)
					// Handle specific case: "word\n`code`" -> "word `code`"
					.replace(/([a-zA-Z])\s*\n\s*(`[^`]+`)/g, '$1 $2')
					// Handle specific case: "`code`\nword" -> "`code` word"
					.replace(/(`[^`]+`)\s*\n\s*([a-zA-Z])/g, '$1 $2')
					// Ensure proper spacing between all formatting elements
					.replace(
						/(<\/(?:strong|em|s|del|b|i|u|strike)>)(<(?:strong|em|s|del|b|i|u|strike|code)[^>]*>)/g,
						'$1 $2'
					)
					.replace(/(<\/(?:strong|em|s|del|b|i|u|strike)>)(`[^`]+`)/g, '$1 $2')
					.replace(
						/(`[^`]+`)(<(?:strong|em|s|del|b|i|u|strike)[^>]*>)/g,
						'$1 $2'
					)
					// Clean up the Parameter structure
					.replace(/(<Parameter[^>]*>)\s*\n\s*/g, '$1\n')
					.replace(/\s*\n\s*(<\/Parameter>)/g, '\n$1')
					// Remove excessive whitespace within Parameter content
					.replace(/\n\s*\n\s*/g, '\n')
			);
		});

	console.log('✅ Initial HTML:\n', html);

	console.log('✅ Final MDX output:\n', mdx);

	return mdx;
};
