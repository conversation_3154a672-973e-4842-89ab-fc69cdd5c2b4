"use client";

import { useEffect, useState, Suspense } from "react";
import { useSearchParams, useRouter } from "next/navigation";
import { createClient } from "@/utils/supabase/client";
import { Button } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";

function InviteContent() {
	const searchParams = useSearchParams();
	const router = useRouter();
	const [loading, setLoading] = useState(true);
	const [error, setError] = useState<string | null>(null);

	useEffect(() => {
		const processInvite = async () => {
			try {
				const token = searchParams.get("token");
				const redirect = searchParams.get("redirect");
				const projectId = searchParams.get("projectId");
				const supabase = createClient();

				if (!token) {
					setError("No invitation token provided");
					setLoading(false);
					return;
				}

				// Verificar se o usuário está logado
				const {
					data: { user },
					error: userError,
				} = await supabase.auth.getUser();

				if (userError) {
					console.error("Error getting user:", userError);
				}

				// Buscar o convite e informações do projeto
				const { data: invite, error: inviteError } = await supabase
					.from("project_invites")
					.select("*, projects:project_id(*)")
					.eq("id", token)
					.single();

				if (inviteError) {
					console.error("Error fetching invite:", inviteError);
					setError("Failed to fetch invitation details");
					setLoading(false);
					return;
				}

				if (!invite) {
					setError("Invitation not found or already used");
					setLoading(false);
					return;
				}

				// Se o usuário não estiver logado e tiver uma conta
				if (!user && redirect === "dashboard") {
					const redirectUrl = projectId
						? `/invite?token=${token}&redirect=dashboard&projectId=${projectId}`
						: `/invite?token=${token}&redirect=dashboard`;
					router.push(`/login?redirect=${redirectUrl}`);
					return;
				}

				// Se o usuário não estiver logado e não tiver uma conta
				if (!user && !redirect) {
					const redirectUrl = projectId
						? `/invite?token=${token}&redirect=dashboard&projectId=${projectId}`
						: `/invite?token=${token}&redirect=dashboard`;
					router.push(`/signup?redirect=${redirectUrl}`);
					return;
				}

				// Se o usuário estiver logado, aceitar o convite usando a função RPC
				if (user) {
					const { error: acceptError } = await supabase.rpc(
						"accept_project_invite",
						{
							p_invite_id: parseInt(token),
							p_project_id: invite.project_id,
							p_user_id: user.id,
							p_user_email: user.email,
							p_role: "Editor",
						}
					);

					if (acceptError) {
						console.error("Error accepting invite:", acceptError);
						setError(
							"Failed to accept invitation: Invitation not found or already used"
						);
						setLoading(false);
						return;
					}

					// Redirecionar para o dashboard do projeto específico
					if (projectId || invite.project_id) {
						const targetProjectId = projectId || invite.project_id;
						router.push(`/${targetProjectId}/dashboard`);
					} else {
						// Fallback para a página inicial se não tiver projectId
						router.push("/");
					}
				}
			} catch (err) {
				console.error("Error processing invite:", err);
				setError(
					"Failed to accept invitation: Invitation not found or already used"
				);
			}
			setLoading(false);
		};

		processInvite();
	}, [searchParams, router]);

	if (loading) {
		return (
			<Card className='flex flex-col items-center justify-center w-full max-w-md mx-auto border-0 shadow-lg h-fit min-h-[350px] min-w-[450px]'>
				<CardContent className='flex flex-col items-center p-8 text-center space-y-4'>
					<div className='animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-blue-600'></div>
					<p className='text-sm text-gray-500'>Processing invitation...</p>
				</CardContent>
			</Card>
		);
	}

	if (error) {
		return (
			<Card className='flex flex-col items-center justify-center w-full max-w-md mx-auto bg-white shadow-sm border border-gray-100 h-fit min-h-[350px] min-w-[450px]'>
				<CardContent className='flex flex-col items-center p-12 text-center'>
					<div className='mb-6'>
						<svg
							className='w-8 h-8 text-red-500'
							fill='currentColor'
							viewBox='0 0 24 24'
						>
							<path d='M12 2L1 21h22L12 2zm0 3.99L19.53 19H4.47L12 5.99zM11 16h2v2h-2v-2zm0-6h2v4h-2v-4z' />
						</svg>
					</div>
					<h2 className='text-xl font-semibold text-red-500 mb-4'>Error</h2>
					<p className='text-gray-500 text-sm mb-8 leading-relaxed max-w-xs'>
						Failed to accept invitation: Invitation not found or already used
					</p>
					<Button
						onClick={() => router.push("/")}
						className='bg-blue-600 hover:bg-blue-700 text-white px-6 py-2 rounded-md'
					>
						Return to Home
					</Button>
				</CardContent>
			</Card>
		);
	}

	return null;
}

// Componente principal que envolve o conteúdo com Suspense
export default function InvitePage() {
	return (
		<Suspense
			fallback={
				<div className='flex flex-col items-center justify-center bg-[#f0f0ff] h-fit'>
					<Card className='w-[400px] p-6 bg-white'>
						<CardContent className='flex flex-col items-center space-y-4 pt-0'>
							<div className='animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-blue-600'></div>
							<p className='text-sm text-gray-500'>Loading...</p>
						</CardContent>
					</Card>
				</div>
			}
		>
			<InviteContent />
		</Suspense>
	);
}
