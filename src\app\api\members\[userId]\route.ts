import { NextRequest, NextResponse } from "next/server";
import { createServerClient } from "@supabase/ssr";
import { createAdminClient } from "@/utils/supabase/admin";
import { cookies } from "next/headers";

export const runtime = 'edge';

export async function DELETE(
	request: NextRequest,
	{ params }: { params: Promise<{ userId: string }> }
) {
	try {
		const { searchParams } = new URL(request.url);
		const projectId = searchParams.get("projectId");
		const { userId } = await params;

		if (!projectId || !userId) {
			return NextResponse.json(
				{ error: "Project ID and User ID are required" },
				{ status: 400 }
			);
		}

		// Convert projectId to number since it's stored as integer in database
		const projectIdNumber = parseInt(projectId, 10);
		if (isNaN(projectIdNumber)) {
			return NextResponse.json(
				{ error: "Invalid project ID format" },
				{ status: 400 }
			);
		}

		// Authenticate the user with regular client
		const cookieStore = await cookies();
		const supabase = createServerClient(
			process.env.NEXT_PUBLIC_SUPABASE_URL!,
			process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
			{
				cookies: {
					get(name: string) {
						return cookieStore.get(name)?.value;
					},
				},
			}
		);

		// Get current user
		const {
			data: { user },
			error: userError,
		} = await supabase.auth.getUser();

		if (userError || !user) {
			return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
		}

		// Use admin client for data operations
		const adminClient = await createAdminClient();

		// Security check: Get current user's role in the project
		const { data: currentUserProject, error: currentUserError } =
			await adminClient
				.from("project_user")
				.select("role")
				.eq("project_id", projectIdNumber)
				.eq("user_id", user.id)
				.single();

		if (currentUserError || !currentUserProject) {
			return NextResponse.json({ error: "Forbidden" }, { status: 403 });
		}

		// Get target member info
		const { data: targetMember, error: targetMemberError } = await adminClient
			.from("project_user")
			.select("role, is_creator, email")
			.eq("project_id", projectIdNumber)
			.eq("user_id", userId)
			.single();

		if (targetMemberError || !targetMember) {
			return NextResponse.json(
				{ error: "Target user not found in this project" },
				{ status: 404 }
			);
		}

		// Permission checks
		if (targetMember.is_creator) {
			return NextResponse.json(
				{ error: "Cannot remove the project creator" },
				{ status: 403 }
			);
		}

		const isCurrentUser = userId === user.id;
		const currentUserRole = currentUserProject.role;

		// Self-removal (leave project) - any member can leave
		if (isCurrentUser) {
			// Allow leaving unless they are the creator
			if (targetMember.is_creator) {
				return NextResponse.json(
					{ error: "Project creator cannot leave the project" },
					{ status: 403 }
				);
			}
		} else {
			// Removing another member - check permissions
			if (currentUserRole === "Editor") {
				return NextResponse.json(
					{ error: "Editors cannot remove other members" },
					{ status: 403 }
				);
			}

			if (currentUserRole === "Admin") {
				// Admins can only remove Editors and other Admins, not Owners
				if (targetMember.role === "Owner") {
					return NextResponse.json(
						{ error: "Admins cannot remove Owners" },
						{ status: 403 }
					);
				}
			}
			// Owners can remove anyone except creator (already checked above)
		}

		// Remove the member
		const { error: deleteError } = await adminClient
			.from("project_user")
			.delete()
			.eq("project_id", projectIdNumber)
			.eq("user_id", userId);

		if (deleteError) {
			console.error("❌ Error removing member:", deleteError);
			return NextResponse.json(
				{ error: "Failed to remove member" },
				{ status: 500 }
			);
		}

		return NextResponse.json({
			message: "Member removed successfully",
		});
	} catch (error) {
		console.error("💥 Error in remove member API:", error);
		return NextResponse.json(
			{ error: "Internal server error" },
			{ status: 500 }
		);
	}
}

export async function PATCH(
	request: NextRequest,
	{ params }: { params: Promise<{ userId: string }> }
) {
	try {
		const body = (await request.json()) as {
			role?: string;
			projectId?: string;
		};
		const { role, projectId } = body;
		const { userId } = await params;

		// Validate required parameters
		if (!projectId || !userId || !role) {
			return NextResponse.json(
				{ error: "Project ID, User ID and role are required" },
				{ status: 400 }
			);
		}

		// Convert and validate projectId
		const projectIdNumber = parseInt(projectId, 10);
		if (isNaN(projectIdNumber) || projectIdNumber <= 0) {
			return NextResponse.json(
				{ error: "Invalid project ID format" },
				{ status: 400 }
			);
		}

		// Validate role
		const validRoles = ["Owner", "Admin", "Editor"];
		if (!validRoles.includes(role)) {
			return NextResponse.json(
				{
					error: `Invalid role. Must be one of: ${validRoles.join(", ")}`,
				},
				{ status: 400 }
			);
		}

		// Validate userId format (should be UUID)
		const uuidRegex =
			/^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i;
		if (!uuidRegex.test(userId)) {
			return NextResponse.json(
				{ error: "Invalid user ID format" },
				{ status: 400 }
			);
		}

		// Authenticate the user with regular client
		const cookieStore = await cookies();
		const supabase = createServerClient(
			process.env.NEXT_PUBLIC_SUPABASE_URL!,
			process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
			{
				cookies: {
					get(name: string) {
						return cookieStore.get(name)?.value;
					},
				},
			}
		);

		// Get current user
		const {
			data: { user },
			error: userError,
		} = await supabase.auth.getUser();

		if (userError || !user) {
			return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
		}

		// Use admin client for data operations
		const adminClient = await createAdminClient();

		// Security check: Get current user's role in the project
		const { data: currentUserProject, error: currentUserError } =
			await adminClient
				.from("project_user")
				.select("role")
				.eq("project_id", projectIdNumber)
				.eq("user_id", user.id)
				.single();

		if (currentUserError || !currentUserProject) {
			return NextResponse.json({ error: "Forbidden" }, { status: 403 });
		}

		// Get target member info
		const { data: targetMember, error: targetMemberError } = await adminClient
			.from("project_user")
			.select("role, is_creator, email")
			.eq("project_id", projectIdNumber)
			.eq("user_id", userId)
			.single();

		if (targetMemberError || !targetMember) {
			return NextResponse.json(
				{ error: "Target user not found in this project" },
				{ status: 404 }
			);
		}

		// Permission checks
		if (targetMember.is_creator) {
			return NextResponse.json(
				{ error: "Cannot change the role of the project creator" },
				{ status: 403 }
			);
		}

		const currentUserRole = currentUserProject.role;

		// Cannot change own role
		if (userId === user.id) {
			return NextResponse.json(
				{ error: "You cannot change your own role" },
				{ status: 403 }
			);
		}

		// Permission hierarchy checks
		if (currentUserRole === "Editor") {
			return NextResponse.json(
				{ error: "Editors cannot change member roles" },
				{ status: 403 }
			);
		}

		if (currentUserRole === "Admin") {
			// Admins can only manage Editors and other Admins
			if (targetMember.role === "Owner") {
				return NextResponse.json(
					{ error: "Admins cannot change the role of Owners" },
					{ status: 403 }
				);
			}
			// Admins cannot promote to Owner
			if (role === "Owner") {
				return NextResponse.json(
					{ error: "Admins cannot promote members to Owner" },
					{ status: 403 }
				);
			}
		}
		// Owners can change any role except creator (already checked above)

		// Check if role is actually changing
		if (targetMember.role === role) {
			return NextResponse.json(
				{ error: "Member already has this role" },
				{ status: 400 }
			);
		}

		// Update the member role
		const { error: updateError } = await adminClient
			.from("project_user")
			.update({ role })
			.eq("project_id", projectIdNumber)
			.eq("user_id", userId);

		if (updateError) {
			console.error("❌ Error updating member role:", updateError);
			return NextResponse.json(
				{ error: "Failed to update member role" },
				{ status: 500 }
			);
		}

		return NextResponse.json({
			message: "Member role updated successfully",
			data: {
				old_role: targetMember.role,
				new_role: role,
			},
		});
	} catch (error) {
		console.error("💥 Unexpected error in update member role API:", error);
		return NextResponse.json(
			{ error: "Internal server error" },
			{ status: 500 }
		);
	}
}
