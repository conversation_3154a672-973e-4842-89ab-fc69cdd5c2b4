"use client";

import * as React from "react";
import { format } from "date-fns";
import { Calendar as CalendarIcon, CalendarDays } from "lucide-react";

import { cn } from "@/lib/utils";
import { Button } from "./button";
import { Calendar as CalendarComponent } from "./calendar";
import { Popover, PopoverContent, PopoverTrigger } from "./popover";

export interface DateRangePickerProps {
	dateRange: {
		from: Date | undefined;
		to: Date | undefined;
	};
	onDateRangeChange: (from: Date | undefined, to: Date | undefined) => void;
	disabled?: boolean;
	isDisabledDate?: (date: Date) => boolean;
	placeholder?: string;
	align?: "center" | "start" | "end";
	className?: string;
}

export function DateRangePicker({
	dateRange,
	onDateRangeChange,
	disabled = false,
	isDisabledDate,
	placeholder = "Select date range",
	align = "start",
	className,
}: DateRangePickerProps) {
	const [isOpen, setIsOpen] = React.useState(false);
	const [internalRange, setInternalRange] = React.useState<{
		from: Date | undefined;
		to: Date | undefined;
	}>({ from: dateRange.from, to: dateRange.to });
	const [interactionCount, setInteractionCount] = React.useState(0);
	const [hasFromDate, setHasFromDate] = React.useState(false);

	// Synchronize internal state with external props
	React.useEffect(() => {
		setInternalRange({ from: dateRange.from, to: dateRange.to });
	}, [dateRange.from, dateRange.to]);

	// Reset interaction state when popover opens - always start fresh
	const handleOpenChange = React.useCallback(
		(open: boolean) => {
			if (!disabled) {
				setIsOpen(open);
				if (open) {
					// Always reset to clean state when opening
					setInternalRange({ from: undefined, to: undefined });
					setInteractionCount(0);
					setHasFromDate(false);
				}
			}
		},
		[disabled]
	);

	const handleDateSelection = React.useCallback(
		(range: { from?: Date; to?: Date } | undefined) => {
			if (!range) {
				// Clear selection
				setInternalRange({ from: undefined, to: undefined });
				setInteractionCount(0);
				setHasFromDate(false);
				onDateRangeChange(undefined, undefined);
				return;
			}

			const { from, to } = range;

			// Always update internal state for visual feedback
			setInternalRange({ from, to });

			// Count interactions based on date selection state
			if (from && !hasFromDate) {
				// First interaction: from date selected
				setInteractionCount(1);
				setHasFromDate(true);
			} else if (from && to && hasFromDate && interactionCount === 1) {
				// Second interaction: both dates selected
				setInteractionCount(2);

				// Configure times: start of day for 'from' and end of day for 'to'
				const fromDate = new Date(from);
				fromDate.setHours(0, 0, 0, 0);

				const toDate = new Date(to);
				toDate.setHours(23, 59, 59, 999);

				onDateRangeChange(fromDate, toDate);
				setIsOpen(false);
			} else if (!range) {
				// Reset if range is cleared
				setInteractionCount(0);
				setHasFromDate(false);
			}
		},
		[onDateRangeChange, hasFromDate, interactionCount]
	);

	// Format date to a readable format MM/dd/yyyy
	const formatDisplayDate = (date: Date | undefined) => {
		if (!date) return "";
		return format(date, "MM/dd/yyyy");
	};

	const displayText = React.useMemo(() => {
		if (internalRange.from && internalRange.to) {
			return `${formatDisplayDate(internalRange.from)} - ${formatDisplayDate(
				internalRange.to
			)}`;
		} else if (internalRange.from) {
			return `${formatDisplayDate(internalRange.from)} - Select end date`;
		}
		return placeholder;
	}, [internalRange.from, internalRange.to, placeholder]);

	return (
		<div className={className}>
			<Popover open={isOpen && !disabled} onOpenChange={handleOpenChange}>
				<PopoverTrigger asChild>
					<Button
						variant='outline'
						className={cn(
							"w-full justify-start text-left font-normal h-8 px-3",
							!internalRange.from && "text-muted-foreground",
							disabled && "opacity-70 cursor-not-allowed"
						)}
						disabled={disabled}
					>
						<CalendarIcon
							className={cn(
								"mr-2 h-4 w-4",
								internalRange.from && internalRange.to && "text-wd-blue"
							)}
						/>
						<span className='truncate'>{displayText}</span>
					</Button>
				</PopoverTrigger>
				<PopoverContent className='w-auto p-0' align={align}>
					<div className='space-y-4 p-3'>
						<div className='flex justify-between gap-4'>
							<Button
								variant='ghost'
								size='sm'
								onClick={() => {
									const to = new Date();
									to.setHours(23, 59, 59, 999);
									const from = new Date();
									from.setDate(to.getDate() - 7);
									from.setHours(0, 0, 0, 0);
									setInternalRange({ from, to });
									onDateRangeChange(from, to);
									setIsOpen(false);
								}}
								className='flex-1'
							>
								<CalendarDays className='mr-1 h-4 w-4 text-wd-primary-btncolor' />
								Last 7 days
							</Button>
							<Button
								variant='ghost'
								size='sm'
								onClick={() => {
									const to = new Date();
									to.setHours(23, 59, 59, 999);
									const from = new Date();
									from.setDate(to.getDate() - 30);
									from.setHours(0, 0, 0, 0);
									setInternalRange({ from, to });
									onDateRangeChange(from, to);
									setIsOpen(false);
								}}
								className='flex-1'
							>
								<CalendarDays className='mr-1 h-4 w-4 text-wd-blue' />
								Last 30 days
							</Button>
							<Button
								variant='ghost'
								size='sm'
								onClick={() => {
									const to = new Date();
									to.setHours(23, 59, 59, 999);
									const from = new Date();
									from.setDate(to.getDate() - 90);
									from.setHours(0, 0, 0, 0);
									setInternalRange({ from, to });
									onDateRangeChange(from, to);
									setIsOpen(false);
								}}
								className='flex-1'
							>
								<CalendarDays className='mr-1 h-4 w-4 text-wd-blue' />
								Last 90 days
							</Button>
						</div>
						<CalendarComponent
							initialFocus
							mode='range'
							defaultMonth={internalRange.from}
							min={0}
							selected={{
								from: internalRange.from,
								to: internalRange.to,
							}}
							onSelect={(range) => {
								handleDateSelection(range);
							}}
							disabled={isDisabledDate}
							numberOfMonths={2}
						/>
					</div>
				</PopoverContent>
			</Popover>
		</div>
	);
}
