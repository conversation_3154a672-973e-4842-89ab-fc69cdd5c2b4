// Data interface for TreeNode
export interface TreeNodeData {
  type: string;
  // Common properties
  index?: number;
  sidebarIndex?: number;
  categoryIndex?: number;
  groupIndex?: number;

  // Category specific
  categoryName?: string;

  // Group specific
  groupName?: string;
  page?: string;

  // Page/subpage specific
  path?: string;

  // Navbar specific
  label?: string;
  sidebarRef?: string;
  dropdownIndex?: number;

  // Navbar structure preservation
  navbar?: unknown[];

  // Root node marker
  root?: boolean;
}

export interface TreeNode {
  id: string;
  name: string;
  type: "navbar" | "sidebar" | "category" | "page" | "subpage" | "group" | "root";
  children?: TreeNode[];
  isExpanded?: boolean;
  parentId?: string;

  data?: TreeNodeData;
  path?: string;
}

export interface TreeItemProps {
  node: TreeNode;
  level: number;
  fullTree: TreeNode[];
  onToggle: (id: string) => void;
  onDrop: (
    draggedId: string,
    targetId: string,
    position: "inside" | "before" | "after"
  ) => void;
  draggedItem: string | null;
  setDraggedItem: (id: string | null) => void;
  createPageLink: (pagePath: string) => string;
  handlePageClick: (href: string) => void;
  getItemClasses: (href: string) => string;
  // Context menu handlers
  onCreateItem: (
    parentNode: TreeNode,
    type: "category" | "group" | "page" | "navbar-item" | "navbar-dropdown",
    name: string,
    path?: string
  ) => void;
  onRenameItem: (node: TreeNode, newName: string) => void;
  onDeleteItem: (node: TreeNode) => void;
  onSetAsHomepage?: (node: TreeNode) => void;
  // Inline editing
  editingNodeId: string | null;
  setEditingNodeId: (id: string | null) => void;
  // Homepage
  homepagePath?: string;
}

export interface FileTreeProps {
  tree: TreeNode[];
  onToggle: (id: string) => void;
  onDrop: (
    draggedId: string,
    targetId: string,
    position: "inside" | "before" | "after"
  ) => void;
  draggedItem: string | null;
  setDraggedItem: (id: string | null) => void;
  createPageLink: (pagePath: string) => string;
  handlePageClick: (href: string) => void;
  getItemClasses: (href: string) => string;
  // Context menu handlers
  onCreateItem: (
    parentNode: TreeNode,
    type: "category" | "group" | "page" | "navbar-item" | "navbar-dropdown",
    name: string,
    path?: string
  ) => void;
  onRenameItem: (node: TreeNode, newName: string) => void;
  onDeleteItem: (node: TreeNode) => void;
  onSetAsHomepage?: (node: TreeNode) => void;
  // Inline editing
  editingNodeId: string | null;
  setEditingNodeId: (id: string | null) => void;
  // Homepage
  homepagePath?: string;
  onCustomHomepageClick?: () => void;
}
