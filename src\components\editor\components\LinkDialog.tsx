import React, { useState, useEffect } from "react";
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  Di<PERSON>Footer,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";

interface LinkDialogProps {
  isOpen: boolean;
  onClose: () => void;
  onConfirm: (url: string, text: string) => void;
  initialText?: string;
  initialUrl?: string;
}

export const LinkDialog: React.FC<LinkDialogProps> = ({
  isOpen,
  onClose,
  onConfirm,
  initialText = "",
  initialUrl = "",
}) => {
  const [url, setUrl] = useState("");
  const [text, setText] = useState(initialText);

  // Sincronizar texto e URL quando initialText, initialUrl ou isOpen mudarem
  useEffect(() => {
    if (isOpen) {
      setText(initialText);
      setUrl(initialUrl);
    }
  }, [initialText, initialUrl, isOpen]);

  // Função para normalizar URLs
  const normalizeUrl = (inputUrl: string): string => {
    const trimmedUrl = inputUrl.trim();
    
    // Se começar com /, é um link relativo - manter como está
    if (trimmedUrl.startsWith('/')) {
      return trimmedUrl;
    }

    // Se começar com #, é um link ancora - manter como está
    if (trimmedUrl.startsWith('#')) {
      return trimmedUrl;
    }
    
    // Se já tem protocolo (http:// ou https://), manter como está
    if (trimmedUrl.startsWith('http://') || trimmedUrl.startsWith('https://')) {
      return trimmedUrl;
    }
    
    // Se não tem protocolo, adicionar https://
    return `https://${trimmedUrl}`;
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (url.trim() && text.trim()) {
      const normalizedUrl = normalizeUrl(url);
      onConfirm(normalizedUrl, text.trim());
      handleClose();
    }
  };

  const handleClose = () => {
    setUrl("");
    setText("");
    onClose();
  };

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle>Add Link</DialogTitle>
          <DialogDescription>
            Enter the URL and text for your link.
          </DialogDescription>
        </DialogHeader>
        <form onSubmit={handleSubmit}>
          <div className="grid gap-4 py-4">
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="url" className="text-right">
                URL
              </Label>
              <Input
                id="url"
                type="text"
                placeholder="https://example.com or /page or #header"
                value={url}
                onChange={(e) => setUrl(e.target.value)}
                className="col-span-3"
                autoFocus
                required
              />
            </div>
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="text" className="text-right">
                Text
              </Label>
              <Input
                id="text"
                placeholder="Link text"
                value={text}
                onChange={(e) => setText(e.target.value)}
                className="col-span-3"
                required
              />
            </div>
          </div>
          <DialogFooter>
            <Button type="button" variant="outline" onClick={handleClose}>
              Cancel
            </Button>
            <Button type="submit" disabled={!url.trim() || !text.trim()}>
              Add Link
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  );
};

export default LinkDialog;
