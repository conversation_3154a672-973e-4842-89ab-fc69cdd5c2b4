"use client";

import React from "react";
import { Skeleton } from "@/components/ui/skeleton";

const FileTreeSkeleton = () => {
  const renderSkeletonItem = (level = 0) => (
    <div
      className="flex items-center gap-2 py-1 px-2"
      style={{ paddingLeft: `${level * 16 + 8}px` }}
    >
      <Skeleton className="h-4 w-4 rounded-sm flex-shrink-0" />
      <Skeleton className="h-5 w-3/4 rounded-sm" />
    </div>
  );

  return (
    <div className="space-y-0.5 p-2 animate-pulse">
      {renderSkeletonItem(0)}
      {renderSkeletonItem(1)}
      {renderSkeletonItem(1)}
      {renderSkeletonItem(2)}
      {renderSkeletonItem(0)}
      {renderSkeletonItem(0)}
      {renderSkeletonItem(1)}
      <div className="pt-4">
        {renderSkeletonItem(0)}
        {renderSkeletonItem(1)}
        {renderSkeletonItem(0)}
      </div>
    </div>
  );
};

export default FileTreeSkeleton;
