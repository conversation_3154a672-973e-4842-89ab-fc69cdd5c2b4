import { ThumbsUp, ThumbsDown, Minus, LifeBuoy } from "lucide-react";

interface RatingDisplayProps {
  rating: number;
  escalation: boolean;
  size?: number;
}

export const RatingDisplay = ({ rating, escalation, size = 22 }: RatingDisplayProps) => {
  if (escalation) {
    return <LifeBuoy className="text-wd-blue" size={size} />;
  } else if (rating === 1) {
    return <ThumbsUp className="text-green-500" size={size} />;
  } else if (rating === -1) {
    return <ThumbsDown className="text-destructive" size={size} />;
  } else {
    return <Minus className="text-wd-font-color" size={size} />;
  }
}; 