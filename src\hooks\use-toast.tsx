import * as React from "react";

type ToastProps = {
	title?: string;
	description?: string;
	variant?: "default" | "destructive";
};

type ToastState = {
	toasts: (ToastProps & { id: string })[];
};

type ToastAction =
	| { type: "ADD_TOAST"; toast: ToastProps }
	| { type: "REMOVE_TOAST"; id: string };

const toastReducer = (state: ToastState, action: ToastAction) => {
	switch (action.type) {
		case "ADD_TOAST":
			return {
				...state,
				toasts: [
					...state.toasts,
					{ ...action.toast, id: Math.random().toString() },
				],
			};
		case "REMOVE_TOAST":
			return {
				...state,
				toasts: state.toasts.filter((toast) => toast.id !== action.id),
			};
		default:
			return state;
	}
};

const ToastContext = React.createContext<{
	toasts: (ToastProps & { id: string })[];
	toast: (props: ToastProps) => void;
	dismiss: (id: string) => void;
}>({
	toasts: [],
	toast: () => {},
	dismiss: () => {},
});

export function ToastProvider({ children }: { children: React.ReactNode }) {
	const [state, dispatch] = React.useReducer(toastReducer, { toasts: [] });

	const toast = React.useCallback((props: ToastProps) => {
		dispatch({ type: "ADD_TOAST", toast: props });
	}, []);

	const dismiss = React.useCallback((id: string) => {
		dispatch({ type: "REMOVE_TOAST", id });
	}, []);

	return (
		<ToastContext.Provider value={{ toasts: state.toasts, toast, dismiss }}>
			{children}
			<div className='fixed bottom-4 right-4 z-50 space-y-2'>
				{state.toasts.map((toast) => (
					<div
						key={toast.id}
						className={`rounded-lg border p-4 shadow-md ${
							toast.variant === "destructive"
								? "border-red-200 bg-red-50 text-red-900"
								: "border-gray-200 bg-white text-gray-900"
						}`}
					>
						{toast.title && <div className='font-semibold'>{toast.title}</div>}
						{toast.description && (
							<div className='text-sm'>{toast.description}</div>
						)}
						<button
							onClick={() => dismiss(toast.id)}
							className='absolute top-2 right-2 text-gray-400 hover:text-gray-600'
						>
							×
						</button>
					</div>
				))}
			</div>
		</ToastContext.Provider>
	);
}

export function useToast() {
	const context = React.useContext(ToastContext);
	if (!context) {
		throw new Error("useToast must be used within a ToastProvider");
	}
	return context;
}
