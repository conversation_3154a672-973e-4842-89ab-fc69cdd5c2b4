import { useState, useEffect, useCallback } from "react";
import { useToast } from "@/hooks/use-toast";
import { ProcessedProduct, StripePrice, StripeCustomer } from "./types";
import { useProject } from "@/contexts/ProjectContext/ProjectContextProvider";

// Type for the API response structure
interface ApiPrice {
	id: string;
	unit_amount: number | null;
	currency: string;
	recurring: {
		interval: "month" | "year";
		interval_count: number;
	} | null;
	product: {
		id: string;
		name: string;
		description: string | null;
		active: boolean;
		marketing_features?: Array<{ name?: string }>;
		metadata: Record<string, string>;
		default_price?: string;
	};
	active: boolean;
}

interface ApiProduct {
	id: string;
	name: string;
	description: string | null;
	active: boolean;
	display_prices: {
		monthly: ApiPrice | null;
		yearly: ApiPrice | null;
	};
	features?: string[] | null;
}

// Função reutilizável para redirecionar ao Portal do Cliente Stripe
export async function redirectToCustomerPortal(
	customerId: string,
	projectId?: string
) {
	console.log("🔗 [REDIRECT_PORTAL] Iniciando redirecionamento:", {
		customerId,
		projectId,
	});

	// Call API to create a customer portal session
	const response = await fetch("/api/stripe/customer-portal", {
		method: "POST",
		headers: {
			"Content-Type": "application/json",
		},
		body: JSON.stringify({
			customerId: customerId,
			projectId: projectId,
		}),
	});

	if (!response.ok) {
		console.error("❌ [REDIRECT_PORTAL] Erro na resposta:", response.status);
		throw new Error("Failed to create customer portal session");
	}

	const data = (await response.json()) as { url: string };

	console.log("✅ [REDIRECT_PORTAL] URL recebida:", data.url);

	// Redirect to the Stripe Customer Portal in the same tab
	window.location.href = data.url;
}

// Hook para gerenciar o cliente Stripe associado ao projeto atual
export function useStripeCustomer() {
	const [customer, setCustomer] = useState<StripeCustomer | null>(null);
	const [isLoading, setIsLoading] = useState(true);
	const { selectedProject } = useProject();
	const { toast } = useToast();

	// Verifica se o plano pode ser assinado novamente com base no status atual
	const canResubscribe = useCallback(
		(priceId: string): boolean => {
			if (!customer || !customer.subscription_id) return true;
			if (customer.price_id !== priceId) return true;
			const resubscribableStatuses = [
				"canceled",
				"incomplete_expired",
				"unpaid",
				"inactive",
			];
			return resubscribableStatuses.includes(customer.status);
		},
		[customer]
	);

	const fetchCustomer = useCallback(async () => {
		if (!selectedProject?.id) {
			setIsLoading(false);
			return;
		}
		try {
			setIsLoading(true);
			const response = await fetch(
				`/api/stripe/customer?projectId=${selectedProject.id}`
			);
			if (!response.ok) {
				if (response.status === 401) {
					setCustomer(null);
					return;
				}
				throw new Error(
					`Failed to fetch customer: ${response.statusText} (status: ${response.status})`
				);
			}
			const data = (await response.json()) as {
				customer: StripeCustomer | null;
			};
			setCustomer(data.customer);
		} catch (fetchError: unknown) {
			let errorMessage = "Could not load subscription information";
			if (fetchError instanceof Error) {
				errorMessage = fetchError.message;
			} else if (
				typeof fetchError === "object" &&
				fetchError !== null &&
				"message" in fetchError &&
				typeof (fetchError as { message: unknown }).message === "string"
			) {
				errorMessage = (fetchError as { message: string }).message;
			}
			toast({
				title: "Error Fetching Customer",
				description: errorMessage,
				variant: "destructive",
			});
			setCustomer(null);
		} finally {
			setIsLoading(false);
		}
	}, [selectedProject?.id, toast]);

	useEffect(() => {
		fetchCustomer();
	}, [fetchCustomer]);

	return {
		customer,
		isLoading,
		fetchCustomer,
		hasSubscription: !!customer?.subscription_id,
		subscriptionStatus: customer?.status || "inactive",
		paymentMethod: customer?.lastPayment?.payment_method_last4
			? `${customer.lastPayment.payment_method_brand || ""} •••• ${
					customer.lastPayment.payment_method_last4
			  }`
			: customer?.payment_method_last4
			? `•••• ${customer.payment_method_last4}`
			: null,
		priceId: customer?.price_id,
		billingInterval: customer?.billingInterval || "Monthly",
		amount: customer?.amount,
		lastPayment: customer?.lastPayment,
		lastInvoice: customer?.lastInvoice,
		canResubscribe,
	};
}

export function useStripePlans() {
	const [products, setProducts] = useState<ProcessedProduct[]>([]);
	const [isLoading, setIsLoading] = useState(true);
	const [isAnnual, setIsAnnual] = useState(false);
	const [hasAdminPermission, setHasAdminPermission] = useState(false);
	const { toast } = useToast();
	const { selectedProject } = useProject();

	const handleSetIsAnnual = useCallback((value: boolean) => {
		setIsAnnual(value);
	}, []);

	// Since products now come pre-processed from the API, we don't need complex processing
	const convertApiResponseToProcessedProducts = useCallback(
		(apiProducts: ApiProduct[]): ProcessedProduct[] => {
			return apiProducts.map((apiProduct) => {
				// Convert API response to ProcessedProduct format
				const processedProduct: ProcessedProduct = {
					id: apiProduct.id,
					name: apiProduct.name,
					description: apiProduct.description,
					active: apiProduct.active,
					_temp_default_price_id: null,
					default_price: null,
					prices: [],
					features: apiProduct.features || null,
					display_prices: {
						monthly: null,
						yearly: null,
					},
				};

				// Helper function to convert ApiPrice to StripePrice
				const convertApiPriceToStripePrice = (
					apiPrice: ApiPrice
				): StripePrice => ({
					id: apiPrice.id,
					unit_amount: apiPrice.unit_amount || 0,
					currency: apiPrice.currency,
					recurring: apiPrice.recurring
						? {
								interval: apiPrice.recurring.interval,
								interval_count: apiPrice.recurring.interval_count,
								trial_period_days: null,
								usage_type: "licensed",
						  }
						: null,
					product: {
						id: apiPrice.product.id,
						name: apiPrice.product.name,
						description: apiPrice.product.description,
						active: apiPrice.product.active,
						images: [],
						metadata: apiPrice.product.metadata,
						marketing_features: apiPrice.product.marketing_features?.map(
							(f) => ({ name: f.name || undefined })
						),
					},
					active: apiPrice.active,
				});

				// Convert display_prices from API format
				if (apiProduct.display_prices.monthly) {
					processedProduct.display_prices.monthly =
						convertApiPriceToStripePrice(apiProduct.display_prices.monthly);
					processedProduct.prices.push(processedProduct.display_prices.monthly);
				}

				if (apiProduct.display_prices.yearly) {
					processedProduct.display_prices.yearly = convertApiPriceToStripePrice(
						apiProduct.display_prices.yearly
					);
					processedProduct.prices.push(processedProduct.display_prices.yearly);
				}

				// Set default_price to one of the display prices
				processedProduct.default_price =
					processedProduct.display_prices.monthly ||
					processedProduct.display_prices.yearly;

				return processedProduct;
			});
		},
		[]
	);

	const loadProducts = useCallback(async () => {
		try {
			setIsLoading(true);
			const response = await fetch("/api/stripe/products");
			if (!response.ok) {
				throw new Error(
					`Error fetching products: ${response.statusText} (status: ${response.status})`
				);
			}
			const apiProducts = (await response.json()) as ApiProduct[];

			console.log("📦 [USE_STRIPE_PLANS] Dados recebidos da API:", {
				totalProducts: apiProducts.length,
				products: apiProducts.map((p) => ({
					id: p.id,
					name: p.name,
					hasMonthly: !!p.display_prices?.monthly,
					hasYearly: !!p.display_prices?.yearly,
				})),
				timestamp: new Date().toISOString(),
			});

			if (Array.isArray(apiProducts)) {
				const processedProducts =
					convertApiResponseToProcessedProducts(apiProducts);
				setProducts(processedProducts);

				console.log("📦 [USE_STRIPE_PLANS] Produtos processados:", {
					totalProcessed: processedProducts.length,
					productsDetails: processedProducts.map((p) => ({
						id: p.id,
						name: p.name,
						hasMonthlyDisplay: !!p.display_prices.monthly,
						hasYearlyDisplay: !!p.display_prices.yearly,
						totalPrices: p.prices.length,
					})),
				});
			} else {
				throw new Error("Invalid response format from /api/stripe/products");
			}
		} catch (loadError: unknown) {
			toast({
				title: "Error Loading Plans",
				description:
					loadError instanceof Error
						? loadError.message
						: "Could not load available plans",
				variant: "destructive",
			});
		} finally {
			setIsLoading(false);
		}
	}, [convertApiResponseToProcessedProducts, toast]);

	const getSelectedPrice = useCallback(
		(product: ProcessedProduct) => {
			const interval = isAnnual ? "year" : "month";
			if (interval === "year") {
				return product.display_prices.yearly || product.default_price;
			} else {
				return product.display_prices.monthly || product.default_price;
			}
		},
		[isAnnual]
	);

	const getFilteredAndSortedProducts = useCallback(() => {
		const interval = isAnnual ? "year" : "month";

		// Filter products that have the desired interval
		const filteredProducts = products.filter((product) => {
			if (interval === "year") {
				return product.display_prices.yearly !== null;
			} else {
				return product.display_prices.monthly !== null;
			}
		});

		// If no products found for the specific interval, return all available products
		const productsToDisplay =
			filteredProducts.length > 0
				? filteredProducts
				: products.filter(
						(product) =>
							product.display_prices.monthly !== null ||
							product.display_prices.yearly !== null
				  );

		// Sort by price
		return [...productsToDisplay].sort((a, b) => {
			const priceA = getSelectedPrice(a);
			const priceB = getSelectedPrice(b);
			if (!priceA) return 1;
			if (!priceB) return -1;
			if (priceA.unit_amount === undefined || priceA.unit_amount === null)
				return 1;
			if (priceB.unit_amount === undefined || priceB.unit_amount === null)
				return -1;
			return (priceA.unit_amount || 0) - (priceB.unit_amount || 0);
		});
	}, [products, isAnnual, getSelectedPrice]);

	useEffect(() => {
		if (selectedProject?.role) {
			setHasAdminPermission(
				selectedProject.role === "Owner" || selectedProject.role === "Admin"
			);
		} else {
			setHasAdminPermission(false);
		}
	}, [selectedProject]);

	useEffect(() => {
		loadProducts();
	}, [loadProducts]);

	return {
		products,
		isLoading,
		isAnnual,
		setIsAnnual: handleSetIsAnnual,
		hasAdminPermission,
		loadProducts,
		getFilteredAndSortedProducts,
		selectedProject,
	};
}
