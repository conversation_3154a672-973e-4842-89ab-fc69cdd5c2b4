import { AlertCircle } from "lucide-react";
import { Card, CardContent } from "@/components/ui/card";

export function NoAnalyticsData() {
	return (
		<Card className='w-full border-dashed border-yellow-500/50 bg-yellow-500/5 shadow-none'>
			<CardContent className='flex flex-col items-center justify-center p-12 text-center'>
				<AlertCircle className='h-12 w-12 text-yellow-500 mb-4' />
				<h3 className='text-xl font-semibold mb-2'>Analytics Data Not Found</h3>
				{/* <p className='text-muted-foreground max-w-lg'>
					We couldn&apos;t find an Analytics in Cloudflare.
				</p> */}
				<p className='text-muted-foreground max-w-lg mt-2'>
					Please contact the support team for assistance.
				</p>
			</CardContent>
		</Card>
	);
}
