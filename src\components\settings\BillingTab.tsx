import { Suspense, lazy, useMemo, useEffect } from "react";
import { IntervalToggle } from "./plans/IntervalToggle";
import { useStripePlans, useStripeCustomer } from "./plans/hooks";
import { Skeleton } from "@/components/ui/skeleton";

// Lazy load heavy components
const ManagePlan = lazy(() =>
	import("./plans/ManagePlan").then((module) => ({
		default: module.ManagePlan,
	}))
);
const PlanList = lazy(() =>
	import("./plans/PlanList").then((module) => ({ default: module.PlanList }))
);
const NoPlansState = lazy(() =>
	import("./plans/NoPlansState").then((module) => ({
		default: module.NoPlansState,
	}))
);

// Fallback component for loading
const LoadingSkeleton = () => (
	<div className='space-y-8 max-w-7xl mx-auto px-4 sm:px-6'>
		{/* Skeleton for ManagePlan */}
		<div className='bg-white rounded-lg border border-gray-200 mb-8 mx-auto'>
			<div className='p-6'>
				<Skeleton className='w-40 h-6 mb-6' /> {/* Title "Manage Plan" */}
				<div className='grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-5 gap-6 items-center'>
					<div>
						<Skeleton className='w-16 h-4 mb-1' /> {/* AMOUNT */}
						<Skeleton className='w-20 h-6' />
					</div>
					<div>
						<Skeleton className='w-16 h-4 mb-1' /> {/* BILLED */}
						<Skeleton className='w-20 h-6' />
					</div>
					<div>
						<div className='flex flex-col justify-center items-center w-fit'>
							<Skeleton className='w-16 h-4 mb-1' /> {/* STATUS */}
							<Skeleton className='w-20 h-6 rounded-full' />
						</div>
					</div>
					<div>
						<Skeleton className='w-32 h-4 mb-1' /> {/* NEXT PAYMENT */}
						<Skeleton className='w-36 h-6' />
					</div>
					<div>
						<Skeleton className='w-32 h-4 mb-1' /> {/* PAYMENT METHOD */}
						<div className='flex items-center space-x-3'>
							<Skeleton className='w-8 h-6' /> {/* Logo */}
							<Skeleton className='w-20 h-4' /> {/* Number */}
						</div>
					</div>
				</div>
				{/* Invoice button */}
				<div className='mt-4'>
					<Skeleton className='w-32 h-5' />
				</div>
				{/* Separator */}
				<div className='mt-6 border-t border-gray-200'></div>
				{/* Informational text */}
				<div className='mt-4'>
					<Skeleton className='w-72 h-5' />
				</div>
			</div>
		</div>

		{/* Skeleton for interval toggle */}
		<div className='flex justify-center my-6'>
			<div className='bg-gray-100 rounded-full p-1 flex w-56'>
				<Skeleton className='w-24 h-8 rounded-full' />
				<div className='flex-1'></div>
				<Skeleton className='w-24 h-8 rounded-full' />
			</div>
		</div>

		{/* Skeleton for plan cards */}
		<div className='grid gap-6 grid-cols-1 lg:grid-cols-2 xl:grid-cols-3'>
			{[1, 2, 3].map((i) => (
				<div
					key={i}
					className='border rounded-lg p-6 bg-white h-full flex flex-col'
				>
					{/* Title and description */}
					<div className='pb-3'>
						<Skeleton className='w-2/3 h-6 mb-1' /> {/* Title */}
						<Skeleton className='w-full h-4' /> {/* Description */}
					</div>

					{/* Price */}
					<div className='py-3'>
						<div className='mb-2'>
							<Skeleton className='w-1/2 h-8' /> {/* Price */}
							<Skeleton className='w-28 h-4 mt-1' /> {/* per month/year */}
						</div>

						{/* Separator */}
						<div className='border-t border-gray-200 my-2'></div>

						{/* Features */}
						<div className='space-y-2 flex-grow my-2'>
							<Skeleton className='w-24 h-4 mb-1' /> {/* INCLUDES */}
							{[1, 2, 3, 4, 5].map((j) => (
								<div key={j} className='flex items-start'>
									<Skeleton className='w-4 h-4 mr-2 flex-shrink-0 rounded-sm' />{" "}
									{/* Check icon */}
									<Skeleton className='w-full h-4' /> {/* Feature text */}
								</div>
							))}
						</div>
					</div>

					{/* Botão */}
					<div className='pt-2'>
						<Skeleton className='w-full h-10 rounded-md' />{" "}
						{/* Subscribe button */}
					</div>
				</div>
			))}
		</div>
	</div>
);

export const BillingTab = () => {
	const {
		products,
		isAnnual,
		setIsAnnual,
		hasAdminPermission,
		loadProducts,
		getFilteredAndSortedProducts,
		selectedProject,
		isLoading: isLoadingProducts,
	} = useStripePlans();

	const {
		isLoading: isLoadingCustomer,
		customer,
		priceId: customerPriceId,
		billingInterval,
		hasSubscription,
		subscriptionStatus,
	} = useStripeCustomer();

	console.log("💳 [BILLING_TAB] Componente carregado com dados:", {
		hasProducts: products.length > 0,
		totalProducts: products.length,
		isAnnual,
		hasAdminPermission,
		selectedProjectId: selectedProject?.id,
		isLoadingProducts,
		isLoadingCustomer,
		hasCustomer: !!customer,
		customerPriceId,
		billingInterval,
		hasSubscription,
		subscriptionStatus,
		timestamp: new Date().toISOString(),
	});

	// Update isAnnual based on the customer's billingInterval
	useEffect(() => {
		if (billingInterval) {
			setIsAnnual(billingInterval === "Annual");
		}
	}, [billingInterval, setIsAnnual]);

	// Get sorted and filtered products
	const sortedProducts = useMemo(() => {
		const result = getFilteredAndSortedProducts();
		return result;
	}, [getFilteredAndSortedProducts]);

	// Create a more efficient isCurrentPlan function that does not depend on nested hooks
	const isCurrentPlan = useMemo(() => {
		return (priceId: string) => {
			return customerPriceId === priceId;
		};
	}, [customerPriceId]);

	const isLoading = isLoadingProducts || isLoadingCustomer;

	// If loading, show skeleton
	if (isLoading) {
		return <LoadingSkeleton />;
	}

	return (
		<div className='space-y-6 max-w-7xl mx-auto px-4 sm:px-6'>
			{customer && (
				<Suspense fallback={<Skeleton className='w-full h-40' />}>
					<ManagePlan
						customer={customer}
						hasAdminPermission={hasAdminPermission}
					/>
				</Suspense>
			)}

			{products.length === 0 ? (
				<Suspense fallback={<Skeleton className='w-full h-40' />}>
					<NoPlansState loadProducts={loadProducts} />
				</Suspense>
			) : (
				<>
					<IntervalToggle isAnnual={isAnnual} setIsAnnual={setIsAnnual} />

					<Suspense fallback={<Skeleton className='w-full h-60' />}>
						<PlanList
							products={sortedProducts}
							isAnnual={isAnnual}
							hasAdminPermission={hasAdminPermission}
							selectedProject={selectedProject}
							isCurrentPlan={isCurrentPlan}
							customerId={customer?.id}
							hasSubscription={hasSubscription}
							subscriptionStatus={subscriptionStatus}
						/>
					</Suspense>
				</>
			)}
		</div>
	);
};
